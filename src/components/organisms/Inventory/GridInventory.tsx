import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Note1, <PERSON><PERSON><PERSON>, UserEdit } from "iconsax-react";
import OrganizationEmptyState from "../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../atoms/CustomTable/CustomTable";
import { debounce } from "../../shared/helpers";
import { FaEdit, FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../shared/hooks";
import { FiPlus } from "react-icons/fi";
import { IoGrid } from "react-icons/io5";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import Loader from "../../atoms/Loader";
import { PiTrash } from "react-icons/pi";
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";
import { Form, Formik } from "formik";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";
import {
  getAllBranchesAtom,
  getDepartmentsAtom,
} from "../../../recoil/atom/organizationAtom";
import useUpdateRecoilAtom from "../../shared/hooks/updateRecoilAtom";
import {
  deleteExpense,
  getExpenseCategories,
  getExpenses,
} from "../../../api/expense";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import { getVendors } from "../../../api/vendor";
import { getVendorsAtom } from "../../../recoil/atom/vendors";
import moment from "moment";
import {
  getExpenseCategoryAtom,
  getExpensesAtom,
} from "../../../recoil/atom/expense";
import { FaEye, FaList, FaListCheck, FaTrash } from "react-icons/fa6";
import StatisticsCard from "../../atoms/Cards/StatisticsCard";
import CustomGrid from "../../atoms/CustomGrid";

import img1 from "../../../assets/sample/fabio-oyXis2kALVg-unsplash.jpg";
import img2 from "../../../assets/sample/florian-olivo-4hbJ-eymZ1o-unsplash.jpg";
import img3 from "../../../assets/sample/karl-pawlowicz-QUHuwyNgSA0-unsplash.jpg";
import { MdGridView } from "react-icons/md";
import InventoryItemCard from "../../atoms/Cards/InventoryItemCard";
import CustomPagination from "../../atoms/CustomPagination";
import { getInventory } from "../../../api/inventory";
import { getInventoriesAtom } from "../../../recoil/atom/inventory";

function GridInventory() {
  const navigate = useNavigate();
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [branchName, setBranchName] = useState<string>("");
  const [branchId, setBranchId] = useState<string>("");
  const [departmentName, setDepartmentName] = useState<string>("");
  const [departmentId, setDepartmentId] = useState<string>("");
  const [vendorName, setVendorName] = useState<string>("");
  const [vendorId, setVendorId] = useState<string>("");
  const [categoryName, setCategoryName] = useState<string>("");
  const [categoryId, setCategoryId] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");

  const [pageNumber, setPageNumber] = useState<number>(1);
  const [, setExpensesAtom] = useRecoilState(getExpensesAtom);
  const [rowId, setRowId] = useState(0);
  const getEntireBranchesValue = useRecoilValue(getAllBranchesAtom);
  const expenses = useRecoilValue(getExpensesAtom);
  const [, setCategoriesAtom] = useRecoilState(getExpenseCategoryAtom);
  const [, setVendorsAtom] = useRecoilState(getVendorsAtom);
  const categoriesValue = useRecoilValue(getExpenseCategoryAtom);
  const vendorValue = useRecoilValue(getVendorsAtom);
  const getDepartments = useRecoilValue(getDepartmentsAtom);
  const [, setInventoryAtom] = useRecoilState(getInventoriesAtom);
  const { fetchEntireBranches, fetchDepartments } = useUpdateRecoilAtom();

  const inventoryValue = useRecoilValue(getInventoriesAtom);

  const debounceSearch = useRef(
    debounce((q) => fetchInventory(q), 2000)
  ).current;

  const branches =
    getEntireBranchesValue?.data?.map((branch) => ({
      text: branch.name,
      value: branch.id,
    })) || [];

    const fetchInventory = (q?) => {
      setIsFetching(true);
      getInventory(
        { page: pageNumber, search: q || "" }
      ).then((res) => {
        if (res.success) {
          setIsFetching(false);
          setInventoryAtom(res.data);
        }
      });
    };

  const fetchExpenseCategories = () => {
    getExpenseCategories().then((res) => {
      if (res.success) {
        setCategoriesAtom(res.data);
      }
    });
  };

  const fetchVendors = () => {
    getVendors().then((res) => {
      if (res.success) {
        setVendorsAtom(res.data);
      }
    });
  };

  const handleDeleteExpense = (id) => {
    setIsLoading(true);
    deleteExpense(id).then((res) => {
      setIsLoading(false);
      if (res.success) {
        setShowDropdown(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchInventory();
      }
    });
  };

  useEffect(() => {
    fetchInventory(searchQuery);
  }, [pageNumber, branchId, categoryId, departmentId, vendorId, selectedDate]);

  const allInventories = [
    {
      title: "Inventory count",
      value: 1,
      icon: <NoteAdd size={24} />,
      valueText: "Available",
      color: "bg-accent-green-light",
    },
    {
      title: "Low stock count",
      value: 2,
      icon: <FaListCheck size={24} />,
      valueText: "stock low",
      color: "bg-accent-blue-light",
    },
    {
      title: "Total inventory cost",
      value: 3,
      icon: <Note1 size={24} />,
      valueText: "Amount",
      color: "bg-[#F4EDF9]",
    },
    {
      title: "Total inventory quantity",
      value: 4,
      icon: <DollarCircle size={24} />,
      valueText: "available",
      color: "bg-accent-orange-light",
    },
  ];

  type SampleDataType = {
    img: string;
    name: string;
    costprice: string;
    sellingprice: string;
    color: string;
  };

  const sampleData: SampleDataType[] = [
    {
      img: img1,
      name: "Product Test 1",
      costprice: "500",
      sellingprice: "1000",
      color: "red",
    },
    {
      img: img2,
      name: "Product Test 2",
      costprice: "200",
      sellingprice: "400",
      color: "blue",
    },
    {
      img: img3,
      name: "Product Test 3",
      costprice: "300",
      sellingprice: "600",
      color: "green",
    },
    {
      img: img1,
      name: "Product Test 4",
      costprice: "400",
      sellingprice: "800",
      color: "red",
    },
    {
      img: img2,
      name: "Product Test 5",
      costprice: "100",
      sellingprice: "200",
      color: "blue",
    },
    {
      img: img3,
      name: "Product Test 6",
      costprice: "600",
      sellingprice: "1200",
      color: "green",
    },
  ];

  useEffect(() => {
    fetchEntireBranches();
    fetchExpenseCategories();
    fetchVendors();
  }, []);

  useEffect(() => {
    fetchDepartments(branchId);
  }, [branchId]);

  if (isFetching && !searchQuery) {
    return (
      <div>
        <Loader />
      </div>
    );
  }

  return (
    <>
      <div>
        <div>
          {/* <h1 className="font-poppins font-semibold text-24">Asset Management</h1> */}
          <div className="mt-8 grid grid-cols-4 gap-5">
            {allInventories?.map((item, index) => (
              <div key={index}>
                <StatisticsCard
                  backgroundColor={item.color}
                  key={index}
                  title={item.title}
                  value={item.value}
                  icon={item.icon}
                  valueText={item.valueText}
                />
              </div>
            ))}
          </div>
        </div>

        <div className="pt-10 flex justify-end">
          <CustomButton
            className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
            isTransparent={true}
            handleClick={() => {
              navigate("/inventory/add-inventory");
            }}
            leftIcon={<FiPlus className="ml-3" size={20} />}
            title="Add inventory"
          />
        </div>

        <div className="mt-8 flex justify-end">
          <div className="flex gap-3 ">
            <div
              onClick={() => navigate(`/inventory`)}
              className="mt-1 text-neutral-normal cursor-pointer h-12 w-12 bg-white rounded-lg flex justify-center items-center"
            >
              <FaList size={14} />
            </div>
            <div className="mt-1 text-purple-normal cursor-pointer h-12 w-12 bg-purple-light rounded-lg flex justify-center items-center">
              <MdGridView size={18} />
            </div>
          </div>
        </div>

        <div className="bg-[#F5F5F5] my-10 px-4 py-[23px]">
          {inventoryValue?.data?.length > 0 ||
          searchQuery ||
          branchId ||
          vendorId ||
          departmentId ||
          categoryId ||
          selectedDate ||
          sampleData ? (
            <>
              <div className="">
                <h1 className="font-poppins-medium text-purple-normal-active mb-4">
                  Inventory Information
                </h1>
                <div className="bg-purple-200 h-[1px] w-[100%] mb-5"></div>
              </div>
              <div className="py-6 px-4 bg-[#f4f4f4] rounded-[10px]">
                {inventoryValue?.data?.length > 0 ? (
                  <div className="grid grid-cols-2 lg:grid-cols-4 television:flex television:flex-wrap gap-x-5 gap-y-4">
                    {inventoryValue?.data?.map((data, index) => (
                      <div key={index}>
                        <InventoryItemCard route="/inventory" data={data} />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex justify-center items-center py-[120px]">
                    <OrganizationEmptyState text="No record found. When there is a record, they will appear here." />
                  </div>
                )}

                {/* {teamMembers?.meta && teamMembers?.meta.last_page > 1 && (
                  <div className="flex justify-center mt-9 mb-5">
                    <CustomPagination
                      pageCount={Math.ceil(
                        teamMembers?.meta?.total / teamMembers?.meta.per_page
                      )}
                      onChange={(pageNumber) => {
                        handlePageChange(pageNumber);
                        setPageNumber(pageNumber);
                      }}
                      forcePage={teamMembers?.meta.current_page}
                    />
                  </div>
                )} */}
              </div>
              {/* <div className="grid grid-cols-6 gap-4 ">
                {sampleData.map(
                  ({ img, name, costprice, sellingprice, color }, index) => (
                    <div
                      key={index}
                      className="flex flex-col py-5 rounded items-center mb-5 mt-5 gap-3 bg-purple-100"
                    >
                      <div
                        className={`bg-${color}-400 w-[200px] h-[200px] rounded-md`}
                      >
                        <img
                          src={img}
                          alt={name}
                          className="object-cover w-full h-full rounded"
                        />
                      </div>

                      <div className="flex flex-col items-center gap-1">
                        <p>{name}</p>
                        <p>Cost price: ${costprice}</p>
                        <p>Selling price: ${sellingprice}</p>
                        <div className="flex gap-10">
                          <div className="flex gap-2 items-center cursor-pointer">
                            <p>view</p>
                            <span>
                              <Eye size={15} />
                            </span>
                          </div>
                          <div className="flex gap-2 items-center">
                            <span className="cursor-pointer">
                              <UserEdit size={15} />
                            </span>
                            <span className="cursor-pointer text-red-500">
                              <PiTrash size={15} />
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                )}
              </div> */}
            </>
          ) : (
            <div className="flex justify-center items-center py-[120px]">
              <OrganizationEmptyState
                buttonTitle="Add New Inventory"
                handleClick={() => navigate("/inventory/add-inventory")}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default GridInventory;
