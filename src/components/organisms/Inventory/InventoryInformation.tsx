import React, { useEffect, useRef, useState } from 'react'
import GoBack from '../../atoms/Ui/GoBack'
import { Formik, Form } from 'formik';
import { useLocation, useNavigate } from 'react-router-dom';
import { AiOutlineEdit, AiOutlineStop } from 'react-icons/ai';
import { BiSave } from 'react-icons/bi';
import { ButtonProperties } from '../../shared/helpers';
import CustomButton from '../../atoms/CustomButton/CustomButton';
import FormikCustomInput from '../../atoms/CustomInput/FormikCustomInput';
import FormikCustomSelect from '../../atoms/CustomInput/FormikCustomSelect';
import FormikSelectCurrency from '../../atoms/CustomInput/FormikSelectCurrency';
import { FaPlus } from 'react-icons/fa6';
import { useRecoilState, useRecoilValue } from 'recoil';
import useUpdateRecoil<PERSON>tom from '../../shared/hooks/updateRecoilAtom';
import { getAllBranchesAtom, getDepartmentsAtom } from '../../../recoil/atom/organizationAtom';
import { getSingleInventoryAtom } from '../../../recoil/atom/inventory';
import { loggedUserAtom } from '../../../recoil/atom/authAtom';
import { getVendorsAtom } from '../../../recoil/atom/vendors';
import { getExpenseCategoryAtom } from '../../../recoil/atom/expense';
import { addVariantInventory, deleteVaraintInventory, getSingleInventory, restockVaraintInventory, updateInventory } from '../../../api/inventory';
import { getVendors } from '../../../api/vendor';
import { getExpenseCategories } from '../../../api/expense';
import { clustarkToast } from '../../atoms/Toast';
import { NotificationTypes } from '../../shared/helpers/enums';
import Loader from '../../atoms/Loader';
import CustomTable from '../../atoms/CustomTable/CustomTable';
import { FiPlus } from 'react-icons/fi';
import CustomModal from '../../atoms/CustomModal/CustomModal';
import CurrencyTag from '../../atoms/Ui/CurrencyTag';
import { MdOutlineAdd } from 'react-icons/md';

const InventoryInformation = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const fileInputRef = useRef<HTMLInputElement>(null);
    const { isEdit, data } = location.state || {};
    const [edit, setEdit] = useState(false);
    const [isFetching, setIsFetching] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [variantLoading, setVariantLoading] = useState(false);
    const [variantModal, setVariantModal] = useState(false);
    const [restockModal, setRestockModal] = useState(false);
    const [restockLoading, setRestockLoading] = useState(false);
  const [, setDragging] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [departmentId, setDepartmentId] = useState("");
    const [variantId, setVariantId] = useState("");
    const [vendorId, setVendorId] = useState("");
    const [branchId, setBranchId] = useState("");
    const [, setCategoriesAtom] = useRecoilState(getExpenseCategoryAtom);
    const [, setVendorsAtom] = useRecoilState(getVendorsAtom);
    const categoriesValue = useRecoilValue(getExpenseCategoryAtom);
    const vendorValue = useRecoilValue(getVendorsAtom);
    const { fetchEntireBranches, fetchDepartments } = useUpdateRecoilAtom();
    const getEntireBranchesValue = useRecoilValue(getAllBranchesAtom);
    const getDepartmentsValue = useRecoilValue(getDepartmentsAtom);
    const [singleInventoryAtom, setSingleInventoryAtom] = useRecoilState(getSingleInventoryAtom);
    const getLoggedUser = useRecoilValue(loggedUserAtom);
    const defaultCurrency = getLoggedUser.businesses[0]?.default_currency;

    const initialValues = {
        branch: singleInventoryAtom?.branch?.name || "",
        department: singleInventoryAtom?.department?.name || "",
        vendor: singleInventoryAtom?.vendor?.name || "",
        category: singleInventoryAtom?.category || "",
        name: singleInventoryAtom?.name || "",
        quantity: singleInventoryAtom?.quantity || "",
        costPrice: singleInventoryAtom?.cost_price || "",
        sellingPrice: singleInventoryAtom?.selling_price || "",
        unit: singleInventoryAtom?.unit || "",
        status: singleInventoryAtom?.status || "",
        type: singleInventoryAtom?.variant_type || "",
        cost_currency: singleInventoryAtom?.cost_currency || defaultCurrency,
        selling_currency: singleInventoryAtom?.selling_currency || defaultCurrency,
      };

    
  const branches = getEntireBranchesValue?.data?.map((branch) => ({
    text: branch.name,
    value: branch.id,
  }));

  const departments = getDepartmentsValue?.data?.map((item) => ({
    text: item.name,
    value: item.id,
  }));

  const categories = categoriesValue.map((item) => ({
    text: item.name,
    value: item.id,
  }));

  const units = [
    { text: "Pieces", value: "Pieces" },
    { text: "Boxes", value: "Boxes" },
    { text: "Packs", value: "Packs" },
    { text: "Cartons", value: "Cartons" },
    { text: "Sets", value: "Sets" },
    { text: "Bundles", value: "Bundles" },
    { text: "Yard", value: "Yard" },
  ];

  const types = [
    { text: "single", value: "single" },
    { text: "multiple", value: "multiple" },
  ];

  const fetchSingleInventory = () => {
    setIsFetching(true);
    getSingleInventory(data?.id).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setSingleInventoryAtom(res.data);
      }
    });
  };

  const fetchExpenseCategories = () => {
    getExpenseCategories().then((res) => {
      if (res.success) {
        setCategoriesAtom(res.data);
      }
    });
  };

  const fetchVendors = () => {
    getVendors().then((res) => {
      if (res.success) {
        setVendorsAtom(res.data);
      }
    });
  };

  const vendors = vendorValue?.data?.map((item) => ({
    text: item.name,
    value: item.id,
  }));

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
  const file = event.target.files?.[0];
  if (
    file &&
    (file.type === "image/jpeg" || file.type === "image/png") &&
    file.size <= 900 * 1024
  ) {
    setSelectedFile(file);
  } else {
    clustarkToast(
      NotificationTypes.ERROR,
      "Image must be a .jpg or .png and less than 900kb."
    );
  }
  if (fileInputRef.current) {
    fileInputRef.current.value = '';
  }
};

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  useEffect(() => {
    fetchExpenseCategories();
    fetchEntireBranches();
    fetchVendors();
  }, []);

  useEffect(() => {
    fetchDepartments(branchId || data?.branch_id);
  }, [branchId]);

  const handleSubmit = (values) => {
    const dataPayload = {
      name: values.name,
      selling_price: values.sellingPrice,
      category: values.category,
      cost_price: values.costPrice,
      quantity: values.quantity,
      vendor_id: vendorId,
      branch_id: branchId,
      department_id: departmentId,
      variant_type: values.type,
      unit: values.unit,
      status: values.status,
      attachment: selectedFile,
    };

      updateInventory({...dataPayload, inventory_id: data?.id}).then((response) => {
        setIsLoading(false);
        if (response?.success) {
          clustarkToast(NotificationTypes.SUCCESS, response.message);
          fetchSingleInventory();
        }
      });
      setIsLoading(true);
     
  };

  const handleVariantAdd = (values) => {
    setVariantLoading(true);
    addVariantInventory({...values, inventory_id: data?.id}).then((res) => {
      if(res.success) {
        setVariantLoading(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        setVariantModal(false);
        fetchSingleInventory();
      }
    })

  };

  const handleRemoveVariant = (id) => {
    deleteVaraintInventory(id).then((res) => {
      if(res.success) {
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchSingleInventory();
      }
    }).catch(() => setVariantLoading(false));

  };

  const handleRestockVariant = (values) => {
    setVariantLoading(true);
    restockVaraintInventory({inventory_variant_id: variantId, quantity: values.quantity}).then((res) => {
      if(res.success) {
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        setRestockModal(false);
        fetchSingleInventory();
        setVariantLoading(false);
      }
    }).catch(() => setVariantLoading(false));

  };
  

  const columns = [
    {
      Header: "Name",
      accessor: "name",
    },

    {
      Header: "Quantity",
      accessor: "quantity",
    },
    {
      Header: "Unit",
      accessor: "unit",
    },
    {
      Header: "Cost Price",
      accessor: "cost_price",
      Cell: (row: any) => (
        <p>
          {`${row.cell?.value?.toLocaleString()}` || "--"}
          {row.cell.row.original.currency ||
            (defaultCurrency && (
              <CurrencyTag
                currency={row.cell.row.original.currency || defaultCurrency}
              />
            ))}
        </p>
      ),
    },
    {
      Header: "Selling Price",
      accessor: "selling_price",
      Cell: (row: any) => (
        <p>
          {`${row.cell?.value?.toLocaleString()}` || "--"}
          {row.cell.row.original.currency ||
            (defaultCurrency && (
              <CurrencyTag
                currency={row.cell.row.original.currency || defaultCurrency}
              />
            ))}
        </p>
      ),
    },
    {
      Header: "",
      accessor: "id",
      Cell: (row) => (
        <div className="flex gap-4 justify-between">
          <CustomButton leftIcon={<MdOutlineAdd className='mb-1'/>} isTransparent className=" !h-[30px] " handleClick={() => {setRestockModal(true); setVariantId(row?.cell?.row?.original?.id)}} title='Restock'/>
          <CustomButton leftIcon={<AiOutlineStop className='mb-1'/>} className="border !border-alert-text-error !text-alert-text-error !h-[30px] hover:bg-white rounded-md" handleClick={() => {handleRemoveVariant(row?.cell?.row?.original?.id)}} title='Remove'/>
        </div>
      )
    }
  ];

  useEffect(() =>{
    if(data?.id) {
      fetchSingleInventory();
    } else {
        navigate("/inventory")
    }
  }, [data]);

  if(isFetching){
    return <div><Loader/></div>
  }


  return (
    <div>
        <GoBack />
      <div className="mt-6">
        <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
          <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
            Inventory Information
          </h1>
        </div>

        <div className="bg-[#F5F5F5] px-6 pt-[40px] pb-[60px] rounded-bl-[10px] rounded-br-[10px]">
          <Formik
            initialValues={initialValues}
            onSubmit={handleSubmit}
            enableReinitialize
          >
            {({ values, setFieldValue }) => (
              <Form>
                <div className='bg-white pl-10 pr-16 py-6 rounded-lg '>
                <div className="flex justify-end">
                  {data && (
                    <>
                      {isEdit || edit ? (
                        <CustomButton
                          leftIcon={<BiSave size={20} />}
                          type="submit"
                          isLoading={isLoading}
                          title="Save"
                          handleClick={() => {}}
                          className="!w-[108px]"
                          variant={ButtonProperties.VARIANT.primary.name}
                        />
                      ) : (
                        <div
                          onClick={() => {
                            setEdit(true);
                          }}
                          className="!w-[108px] bg-purple-normal text-white text-center py-3 rounded flex justify-center gap-2"
                        >
                          <AiOutlineEdit size={18} /> Edit
                        </div>
                      )}
                    </>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-8 mt-8">
                  <div>
                    <FormikCustomInput
                      label="Name *"
                      id="name"
                      name="name"
                      placeholder="enter inventory name"
                      type="text"
                      inputClassName=""
                      value={values.name}
                      disabled={!edit && !isEdit && data}
                    />
                  </div>

                  <div>
                    <FormikCustomInput
                      label="Quantity *"
                      id="quantity"
                      name="quantity"
                      placeholder="enter quantity"
                      type="text"
                      inputClassName=""
                      value={values.quantity}
                      disabled={!edit && !isEdit && data}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-8 mt-8">
                  <div>
                    <FormikCustomSelect
                      label="Branch"
                      options={branches}
                      name="branch"
                      placeholder="Select branch"
                      value={values.branch}
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("branch", item.value);
                        setBranchId(item.value);
                      }}
                      disabled={!edit && !isEdit && data}
                    />
                  </div>

                  <div>
                    <FormikCustomSelect
                      label="Department"
                      options={departments}
                      name="department"
                      placeholder="Select department"
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("department", item.value);
                        setDepartmentId(item.value);
                      }}
                      disabled={(!edit && !isEdit && data) || !branchId}
                      value={values.department}
                    />
                  </div>
                </div>

                <div className=" grid grid-cols-2 gap-8 mt-8">
                  <div>
                    <FormikSelectCurrency
                      label="Cost Price *"
                      name="cost_currency"
                      value={values.cost_currency}
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("cost_currency", item.value);
                      }}
                      amountValue={values.costPrice}
                      handleAmount={(costPrice) =>
                        setFieldValue("costPrice", costPrice)
                      }
                      disabled={!edit && !isEdit && data}
                    />
                  </div>
                  <div>
                    <FormikSelectCurrency
                      label="Selling Price *"
                      name="selling_currency"
                      value={values.selling_currency}
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("selling_currency", item.value);
                      }}
                      amountValue={values.sellingPrice}
                      handleAmount={(sellingPrice) =>
                        setFieldValue("sellingPrice", sellingPrice)
                      }
                      disabled={!edit && !isEdit && data}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-8 mt-8">
                  <div className="relative">
                    <FormikCustomSelect
                      label="Vendor"
                      options={vendors}
                      name="vendor"
                      placeholder="Select vendor"
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("vendor", item.value);
                        setVendorId(item.value);
                      }}
                      disabled={!edit && !isEdit && data}
                      value={values.vendor}
                    />
                  </div>

                  <div>
                    <FormikCustomSelect
                      label="Product category *"
                      options={categories}
                      name="category"
                      placeholder="Select Category"
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("category", item.text);
                        // setCategoryId(item.text);
                      }}
                      value={values.category}
                      disabled={!edit && !isEdit && data}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-8 mt-8">
                  <div className="relative">
                    <FormikCustomSelect
                      label="Units *"
                      options={units}
                      name="units"
                      value={values.unit}
                      placeholder="select unit"
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("unit", item.value);
                      }}
                      disabled={!edit && !isEdit && data}
                    />
                  </div>

                  <div>
                    <FormikCustomSelect
                      label="Type *"
                      options={types}
                      name="types"
                      placeholder="Select type"
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("type", item.value);
                      }}
                      disabled
                      value={values.type}
                    />

                  
                  </div>
                </div>

                <div className=" mt-8">
                  <div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      className="hidden"
                      onChange={handleFileChange}
                    />
                    <label className="text-neutral-dark" htmlFor="reason">
                      Attachment upload(optional)
                    </label>
                    {!edit && !isEdit ? (
                      singleInventoryAtom?.images && singleInventoryAtom?.images[0]?.attachment ? (
                        <p className="border-2 border-dashed bg-[#B2BBC6] bg-opacity-10 h-[178px] rounded mt-4 flex justify-center items-center ">
                          File attached &nbsp;{" "}
                          <a
                            className="text-purple-normal"
                            href={singleInventoryAtom?.images[0]?.attachment}
                            target="_blank"
                          >
                            View file
                          </a>
                        </p>
                      ) : (
                        <p className="border-2 border-dashed bg-[#B2BBC6] bg-opacity-10 h-[78px] rounded mt-4 flex justify-center items-center">
                          No file attached
                        </p>
                      )
                    ) : (
                      <div className="border-2 border-dashed bg-[#B2BBC6] bg-opacity-10 h-[178px] rounded mt-4 flex justify-center items-center">
                        <div
                          onDragOver={(e) => {
                            e.preventDefault();
                            setDragging(true);
                          }}
                          onDragLeave={() => setDragging(false)}
                          onDrop={handleDrop}
                        >
                          <div className="flex justify-center items-center">
                            <svg
                              width="40"
                              height="40"
                              viewBox="0 0 40 40"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M31.6654 21.6675C31.2233 21.6675 30.7994 21.8431 30.4869 22.1556C30.1743 22.4682 29.9987 22.8921 29.9987 23.3341V23.9675L27.532 21.5008C26.6611 20.6367 25.4839 20.1518 24.257 20.1518C23.0302 20.1518 21.853 20.6367 20.982 21.5008L19.8154 22.6675L15.682 18.5341C14.7989 17.6935 13.6263 17.2246 12.407 17.2246C11.1878 17.2246 10.0152 17.6935 9.13203 18.5341L6.66536 21.0008V11.6675C6.66536 11.2254 6.84096 10.8015 7.15352 10.489C7.46608 10.1764 7.89 10.0008 8.33203 10.0008H19.9987C20.4407 10.0008 20.8646 9.82521 21.1772 9.51265C21.4898 9.20009 21.6654 8.77617 21.6654 8.33414C21.6654 7.89211 21.4898 7.46819 21.1772 7.15563C20.8646 6.84307 20.4407 6.66747 19.9987 6.66747H8.33203C7.00595 6.66747 5.73418 7.19425 4.7965 8.13194C3.85882 9.06962 3.33203 10.3414 3.33203 11.6675V31.6675C3.33203 32.9936 3.85882 34.2653 4.7965 35.203C5.73418 36.1407 7.00595 36.6675 8.33203 36.6675H28.332C29.6581 36.6675 30.9299 36.1407 31.8676 35.203C32.8052 34.2653 33.332 32.9936 33.332 31.6675V23.3341C33.332 22.8921 33.1564 22.4682 32.8439 22.1556C32.5313 21.8431 32.1074 21.6675 31.6654 21.6675ZM8.33203 33.3341C7.89 33.3341 7.46608 33.1585 7.15352 32.846C6.84096 32.5334 6.66536 32.1095 6.66536 31.6675V25.7175L11.4987 20.8841C11.7436 20.6508 12.0688 20.5206 12.407 20.5206C12.7453 20.5206 13.0705 20.6508 13.3154 20.8841L18.5987 26.1675L25.7654 33.3341H8.33203ZM29.9987 31.6675C29.9963 31.9865 29.8911 32.2963 29.6987 32.5508L22.182 25.0008L23.3487 23.8341C23.4682 23.7122 23.6108 23.6153 23.7682 23.5492C23.9256 23.483 24.0946 23.4489 24.2654 23.4489C24.4361 23.4489 24.6051 23.483 24.7625 23.5492C24.9199 23.6153 25.0625 23.7122 25.182 23.8341L29.9987 28.6841V31.6675ZM37.8487 7.1508L32.8487 2.1508C32.6902 1.99907 32.5033 1.88013 32.2987 1.8008C31.8929 1.63411 31.4378 1.63411 31.032 1.8008C30.8274 1.88013 30.6405 1.99907 30.482 2.1508L25.482 7.1508C25.1682 7.46464 24.9919 7.8903 24.9919 8.33414C24.9919 8.77797 25.1682 9.20363 25.482 9.51747C25.7959 9.83131 26.2215 10.0076 26.6654 10.0076C27.1092 10.0076 27.5349 9.83131 27.8487 9.51747L29.9987 7.3508V16.6675C29.9987 17.1095 30.1743 17.5334 30.4869 17.846C30.7994 18.1585 31.2233 18.3341 31.6654 18.3341C32.1074 18.3341 32.5313 18.1585 32.8439 17.846C33.1564 17.5334 33.332 17.1095 33.332 16.6675V7.3508L35.482 9.51747C35.637 9.67368 35.8213 9.79767 36.0244 9.88229C36.2275 9.9669 36.4453 10.0105 36.6654 10.0105C36.8854 10.0105 37.1032 9.9669 37.3063 9.88229C37.5094 9.79767 37.6938 9.67368 37.8487 9.51747C38.0049 9.36253 38.1289 9.1782 38.2135 8.9751C38.2981 8.772 38.3417 8.55416 38.3417 8.33414C38.3417 8.11412 38.2981 7.89627 38.2135 7.69318C38.1289 7.49008 38.0049 7.30574 37.8487 7.1508Z"
                                fill="#B2BBC6"
                              />
                            </svg>
                          </div>
                          <p
                            onClick={triggerFileInput}
                            className="text-neutral-normal text-center mt-5"
                          >
                            <span className="font-poppins-medium text-purple-normal cursor-pointer">
                              Click here
                            </span>
                            &nbsp; or Drag and Drop file to upload
                          </p>
                          <p className="text-purple-normal font-semibold text-center mt-5">
                            {selectedFile && selectedFile.name}
                            {singleInventoryAtom?.images && singleInventoryAtom?.images[0]?.attachment && (
                              <p className="mt-4 flex justify-center items-center ">
                                File attached &nbsp;{" "}
                                <a
                                  className="text-purple-normal"
                                  href={singleInventoryAtom?.images[0]?.attachment}
                                  target="_blank"
                                >
                                  View file
                                </a>
                              </p>
                            )}
                          </p>
                        </div>
                      </div>
                    )}

                  
                  </div>
                </div>
              
              </div>
              </Form>
            )}
          </Formik>


          {singleInventoryAtom?.variant_type === "multiple" && (
            <div className='mt-10 bg-white rounded-bl-[10px] rounded-br-[10px] rounded-tl-[10px] rounded-tr-[10px]'>
              <div className="pt-10 flex justify-between px-8">
                <h1 className="font-poppins-medium text-20 text-purple-normal-hover place-content-center">
                Inventory Variant
                </h1>
                <CustomButton
                  leftIcon={<FiPlus size={20} />}
                  type="submit"
                  isLoading={isLoading}
                  title="Add Variant Type"
                  handleClick={() => {setVariantModal(true)}}
                  className="!w-[168px]"
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </div>

              <CustomTable hideSearch data={singleInventoryAtom?.inventory_variants} columns={columns || []} />

            </div>
          )}

          <CustomModal visibility={variantModal} toggleVisibility={setVariantModal}> 
          <div>
          <Formik
            initialValues={{
              name: "",
              quantity: "",
              unit: "",
              cost_price: "",
              selling_price: "",
            }}
            onSubmit={handleVariantAdd}
          >
            {({ values, setFieldValue }) => (
              <Form>
                <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
                  <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                    Add Inventory Variant
                  </h1>
                </div>
                <div className="px-6 py-10">
                    <div className="">
                      <FormikCustomInput
                        label="Name "
                        id={`name`}
                        name={`name`}
                        placeholder="name"
                        type="text"
                        inputClassName="!bg-transparent"
                        onChange={(e) =>
                          setFieldValue("name", e.target.value)
                        }
                        required
                      />
                    </div>
                  <div className="grid grid-cols-2 gap-8 mt-8">
                  <div className="">
                          <FormikCustomSelect
                            label="Unit *"
                            options={units}
                            name="unit"
                            value={values.unit}
                            parentContainer=""
                            placeholder="select unit"
                            onChange={(item: {
                              value: string;
                              text: string;
                            }) => {
                              setFieldValue("unit", item.value);
                            }}
                            required
                          />
                        </div>
                    <div className="">
                      <FormikCustomInput
                        label="Quantity"
                        id={`quantity`}
                        name={`quantity`}
                        placeholder=""
                        type="number"
                        inputClassName="!bg-transparent"
                        onChange={(e) =>
                          setFieldValue("quantity", e.target.value)
                        }
                        required
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-8 mt-8">
                  <div className="">
                          <FormikCustomInput
                            label={`Selling Price`}
                            id={`selling_price`}
                            name={`selling_price`}
                            placeholder="0"
                            type="number"
                            inputClassName="!bg-transparent"
                            onChange={(e) =>
                              setFieldValue("selling_price", e.target.value)
                            }
                            required
                          />
                        </div>
                        <div>
                          <FormikCustomInput
                            label="Cost Price"
                            id={`cost_price`}
                            name={`cost_price`}
                            placeholder="0"
                            type="number"
                            inputClassName="!bg-transparent "
                            onChange={(e) =>
                              setFieldValue("cost_price", e.target.value)
                            }
                            required
                          />
                        </div>
                  </div>
                  <div className="mt-10 flex justify-end">
                    <CustomButton
                      type="submit"
                      title="Submit"
                      handleClick={() => {}}
                      isLoading={variantLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                    />
                  </div>
                </div>
              </Form>
            )}
          </Formik>
        </div>
          </CustomModal>

          <CustomModal visibility={restockModal} toggleVisibility={setRestockModal}>
              <div>
                <Formik
            initialValues={{
              quantity: "",
            }}
            onSubmit={handleRestockVariant}
          >
            {() => (
              <Form>
                <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
                  <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                    Add Inventory Variant
                  </h1>
                </div>
                <div className="px-6 py-10">
                    <div className="">
                      <FormikCustomInput
                        label="Quantity"
                        id={`quantity`}
                        name={`quantity`}
                        placeholder=""
                        type="number"
                        inputClassName="!bg-transparent"
                        required
                      />
                    </div>
                  <div className="mt-10 flex justify-end">
                    <CustomButton
                      type="submit"
                      title="Submit"
                      handleClick={() => {}}
                      isLoading={variantLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                    />
                  </div>
                </div>
              </Form>
            )}
          </Formik>
                
              </div>
          </CustomModal>

        </div>
        </div>
    </div>
  )
}

export default InventoryInformation;