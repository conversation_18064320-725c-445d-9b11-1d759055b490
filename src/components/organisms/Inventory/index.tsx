
import { useParams, useSearchParams } from "react-router-dom"
import { useRecoilState } from "recoil";
import { getTeamMembersAtom } from "../../../recoil/atom/teams";
import { useEffect, useRef } from "react";
import { debounce } from "../../shared/helpers";
import GridInventory from "./GridInventory";
import ListInventory from "./ListInventory";

const Inventory = () => {
  const [searchParams, _] = useSearchParams();
  const departmentQuery: any = searchParams.get("department");
  const roleQuery: any = searchParams.get("role");
  const locationQuery: any = searchParams.get("location");
  const searchQuery: any = searchParams.get("q");
  const pageQuery: any = searchParams.get("page");
  const { view } = useParams();
  const [, setTeamAtoms] = useRecoilState(getTeamMembersAtom);
  // const debounceSearch = useRef(debounce((q) => fetchTeamMembers(q), 2000)).current;


  // const fetchTeamMembers = (q) => {
  //   getTeamMembers({search: q, location: locationQuery, role: roleQuery || "", department: departmentQuery || "", page: pageQuery || ""}).then((res) => {
  //     if(res.success) {
  //       setTeamAtoms(res.data);
  //     }
  //   })
  // }

  // useEffect(() => {
  //   fetchTeamMembers(searchQuery);
  // }, [roleQuery, departmentQuery, locationQuery, pageQuery, searchQuery]);

  return (
    <div>
      <h1 className="font-poppins font-semibold text-24">Inventory</h1>
     {view === "grid" ? <GridInventory/> : <ListInventory/>}
    </div>
  )
}

export default Inventory;