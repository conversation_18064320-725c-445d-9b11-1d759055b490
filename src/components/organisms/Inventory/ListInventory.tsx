import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Note1, <PERSON><PERSON><PERSON>, UserEdit } from "iconsax-react";
import OrganizationEmptyState from "../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../atoms/CustomTable/CustomTable";
import { debounce } from "../../shared/helpers";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../shared/hooks";
import { FiPlus } from "react-icons/fi";
import { IoGrid } from "react-icons/io5";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import Loader from "../../atoms/Loader";
import { PiTrash } from "react-icons/pi";
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";
import { Form, Formik } from "formik";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";
import {
  getAllBranchesAtom,
  getDepartmentsAtom,
} from "../../../recoil/atom/organizationAtom";
import useUpdateRecoilAtom from "../../shared/hooks/updateRecoilAtom";
import {
  deleteExpense,
  getExpenseCategories,
  getExpenses,
} from "../../../api/expense";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import { getVendors } from "../../../api/vendor";
import { getVendorsAtom } from "../../../recoil/atom/vendors";
import moment from "moment";
import {
  getExpenseCategoryAtom,
  getExpensesAtom,
} from "../../../recoil/atom/expense";
import { FaList, FaListCheck } from "react-icons/fa6";
import StatisticsCard from "../../atoms/Cards/StatisticsCard";
import { MdGridView } from "react-icons/md";
import { deleteInventory, getInventory } from "../../../api/inventory";
import { getInventoriesAtom } from "../../../recoil/atom/inventory";
import { loggedUserAtom } from "../../../recoil/atom/authAtom";
import CurrencyTag from "../../atoms/Ui/CurrencyTag";
function ListInventory({ debounceSearchs }: any) {
  const navigate = useNavigate();
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  // const [branchName, setBranchName] = useState<string>("");
  // const [branchId, setBranchId] = useState<string>("");
  // const [departmentName, setDepartmentName] = useState<string>("");
  // const [departmentId, setDepartmentId] = useState<string>("");
  // const [vendorName, setVendorName] = useState<string>("");
  // const [vendorId, setVendorId] = useState<string>("");
  // const [categoryName, setCategoryName] = useState<string>("");
  // const [categoryId, setCategoryId] = useState<string>("");
  // const [selectedDate, setSelectedDate] = useState<string>("");

  const [pageNumber, setPageNumber] = useState<number>(1);
  const [, setInventoryAtom] = useRecoilState(getInventoriesAtom);
  const [rowId, setRowId] = useState(0);
  // const getEntireBranchesValue = useRecoilValue(getAllBranchesAtom);
  const inventoryValue = useRecoilValue(getInventoriesAtom);
  const [, setCategoriesAtom] = useRecoilState(getExpenseCategoryAtom);
  const [, setVendorsAtom] = useRecoilState(getVendorsAtom);
  // const categoriesValue = useRecoilValue(getExpenseCategoryAtom);
  // const vendorValue = useRecoilValue(getVendorsAtom);
  // const getDepartments = useRecoilValue(getDepartmentsAtom);
  const { fetchEntireBranches, fetchDepartments } = useUpdateRecoilAtom();
  const getLoggedUser = useRecoilValue(loggedUserAtom);

  const defaultCurrency = getLoggedUser.businesses[0].default_currency;

  const debounceSearch = useRef(
    debounce((q) => fetchInventory(q), 2000)
  ).current;

  // const branches =
  //   getEntireBranchesValue?.data?.map((branch) => ({
  //     text: branch.name,
  //     value: branch.id,
  //   })) || [];

  const fetchInventory = (q?) => {
    setIsFetching(true);
    getInventory(
      { page: pageNumber, search: q || "" }
    ).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setInventoryAtom(res.data);
      }
    });
  };

  const fetchExpenseCategories = () => {
    getExpenseCategories().then((res) => {
      if (res.success) {
        setCategoriesAtom(res.data);
      }
    });
  };

  const fetchVendors = () => {
    getVendors().then((res) => {
      if (res.success) {
        setVendorsAtom(res.data);
      }
    });
  };

  // const categories = categoriesValue.map((item) => ({
  //   text: item.name,
  //   value: item.id,
  // }));

  // const vendors = vendorValue?.data?.map((item) => ({
  //   text: item.name,
  //   value: item.id,
  // }));

  // const departments = getDepartments?.data?.map((item) => ({
  //   text: item.name,
  //   value: item.id,
  // }));

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setShowDeleteWarn(false);
    setRowId(0);
  });

  const handleDeleteInventory = (id) => {
    setIsLoading(true);
    deleteInventory(id).then((res) => {
      setIsLoading(false);
      if (res.success) {
        setShowDropdown(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchInventory();
        setShowDeleteWarn(false);
      }
    });
  };

  useEffect(() => {
    fetchInventory(searchQuery);
  }, [pageNumber]);

  const allInventories = [
    {
      title: "Inventory count",
      value: 1,
      icon: <NoteAdd size={24} />,
      valueText: "Available",
      color: "bg-accent-green-light",
    },
    {
      title: "Low stock count",
      value: 2,
      icon: <FaListCheck size={24} />,
      valueText: "stock low",
      color: "bg-accent-blue-light",
    },
    {
      title: "Total inventory cost",
      value: 3,
      icon: <Note1 size={24} />,
      valueText: "Amount",
      color: "bg-[#F4EDF9]",
    },
    {
      title: "Total inventory quantity",
      value: 4,
      icon: <DollarCircle size={24} />,
      valueText: "available",
      color: "bg-accent-orange-light",
    },
  ];

  useEffect(() => {
    fetchEntireBranches();
    fetchExpenseCategories();
    fetchVendors();
  }, []);

  // useEffect(() => {
  //   fetchDepartments(branchId);
  // }, [branchId]);

  if (isFetching && !searchQuery) {
    return (
      <div>
        <Loader />
      </div>
    );
  }

  const columns = [
    {
      Header: "Inventory name",
      accessor: "name",
      Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
    },
    {
      Header: "Inventory category",
      accessor: "category",
      Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
    },
    {
      Header: "Cost Price",
      accessor: "cost_price",
      Cell: (row: any) => (
        <p>
          {`${row.cell?.value?.toLocaleString()}` || "--"}
          {row.cell.row.original.currency ||
            (defaultCurrency && (
              <CurrencyTag
                currency={row.cell.row.original.currency || defaultCurrency}
              />
            ))}
        </p>
      ),
    },

    {
      Header: "Selling price",
      accessor: "selling_price",
      Cell: (row: any) => (
        <p>
          {`${row.cell?.value?.toLocaleString()}` || "--"}
          {row.cell.row.original.currency ||
            (defaultCurrency && (
              <CurrencyTag
                currency={row.cell.row.original.currency || defaultCurrency}
              />
            ))}
        </p>
      ),
    },
    {
      Header: "Quantity",
      accessor: "quantity",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Unit",
      accessor: "unit",
      Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
    },
    {
      Header: "Type",
      accessor: "variant_type",
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() =>
                    navigate(`/inventory/inventory-information`, {
                      state: { data: row.cell.row.original },
                    })
                  }
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>
                <li
                  onClick={() => {
                    navigate(`/inventory/inventory-information`, {
                      state: { isEdit: true, data: row.cell.row.original },
                    });
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <UserEdit size={18} />
                  Edit
                </li>
                {showDeleteWarn ? (
                  <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                    <div className="flex gap-3">
                      <PiTrash size={18} />
                      Are you sure?
                    </div>
                    <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                      <CustomButton
                        isLoading={isLoading}
                        title="Yes"
                        handleClick={() =>
                          handleDeleteInventory(row.cell.row.original.id)
                        }
                        className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer"
                      />
                      <span
                        onClick={() => {
                          setShowDeleteWarn(false);
                        }}
                        className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                      >
                        No
                      </span>
                    </div>
                  </li>
                ) : (
                  <li
                    onClick={() => setShowDeleteWarn(true)}
                    className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer"
                  >
                    <PiTrash size={18} />
                    Delete
                  </li>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];



  return (
    <>
      <div>
        <div>
          {/* <h1 className="font-poppins font-semibold text-24">Asset Management</h1> */}
          <div className="mt-8 grid grid-cols-4 gap-5">
            {allInventories?.map((item, index) => (
              <div key={index}>
                <StatisticsCard
                  backgroundColor={item.color}
                  key={index}
                  title={item.title}
                  value={item.value}
                  icon={item.icon}
                  valueText={item.valueText}
                />
              </div>
            ))}
          </div>
        </div>

        <div className="pt-10 flex justify-end">
          <CustomButton
            className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
            isTransparent={true}
            handleClick={() => {
              navigate("/inventory/add-inventory");
            }}
            leftIcon={<FiPlus className="ml-3" size={20} />}
            title="Add new inventory"
          />
        </div>

        <div className=" flex justify-end mt-8">
          <div className="flex gap-3 ">
            <div className="mt-1 text-purple-normal h-12 w-12 bg-purple-light cursor-pointer rounded-lg flex justify-center items-center">
              <FaList size={14} />
            </div>
            <div
              onClick={() => navigate(`/inventory-grid`)}
              className="mt-1 text-neutral-normal h-12 w-12 bg-white cursor-pointer rounded-lg flex justify-center items-center"
            >
              <MdGridView size={18} />
            </div>
          </div>
        </div>

        <div className="bg-[#F5F5F5] my-10 px-4 py-[23px]">
          {inventoryValue?.data?.length > 0 ||
          searchQuery ? (
            <CustomTable
              data={inventoryValue?.data || []}
              meta={inventoryValue?.meta || {}}
              columns={columns}
              // customFilter={
              //   <div className="pb-4 absolute w-[50rem] bg-white right-0">
              //     <div>
              //       <h1 className="border-b border-neutral-light pl-4 py-3">
              //         Modify this view
              //       </h1>
              //       <Formik
              //         initialValues={{
              //           branch: branchName || "",
              //           department: departmentName || "",
              //           vendor: vendorName || "",
              //           category: categoryName || "",
              //           date: selectedDate || "",
              //         }}
              //         onSubmit={() => {}}
              //       >
              //         {({ setFieldValue, values }) => (
              //           <Form>
              //             <div className="flex px-4 gap-5 py-[17.5px] border-b">
              //               <p className="whitespace-nowrap">Filter by:</p>
              //               <div className="flex gap-2">
              //                 <div>
              //                   <FormikCustomInput
              //                     id="date"
              //                     name="date"
              //                     type="date"
              //                     inputClassName="!h-7"
              //                     onChange={(e) => {
              //                       setSearchQuery("");
              //                       setSelectedDate(e.target.value);
              //                       setFieldValue("date", e.target.value);
              //                     }}
              //                     value={values.date}
              //                   />
              //                 </div>
              //                 <div>
              //                   <FormikCustomSelect
              //                     parentContainer="!h-7"
              //                     placeholder="Expense Category"
              //                     optionsParentClassName="!capitalize"
              //                     options={categories}
              //                     name="category"
              //                     onChange={(item: {
              //                       value: string;
              //                       text: string;
              //                     }) => {
              //                       setSearchQuery("");
              //                       setCategoryId(item.value);
              //                       setCategoryName(item.text);
              //                       setFieldValue("category", item.text);
              //                     }}
              //                     value={values.category}
              //                   />
              //                 </div>
              //                 <div className="">
              //                   <FormikCustomSelect
              //                     parentContainer="!h-7"
              //                     placeholder="Select Branch"
              //                     optionsParentClassName="!capitalize"
              //                     options={branches}
              //                     name="branch"
              //                     onChange={(item: {
              //                       value: string;
              //                       text: string;
              //                     }) => {
              //                       setSearchQuery("");
              //                       setBranchId(item.value);
              //                       setBranchName(item.text);
              //                       setFieldValue("branch", item.text);
              //                     }}
              //                     value={values.branch}
              //                   />
              //                 </div>
              //                 <div>
              //                   <FormikCustomSelect
              //                     parentContainer="!h-7"
              //                     placeholder="Select Department"
              //                     optionsParentClassName="!capitalize"
              //                     options={departments}
              //                     name="department"
              //                     onChange={(item: {
              //                       value: string;
              //                       text: string;
              //                     }) => {
              //                       setSearchQuery("");
              //                       setDepartmentId(item.value);
              //                       setDepartmentName(item.text);
              //                       setFieldValue("department", item.text);
              //                     }}
              //                     value={values.department}
              //                     disabled={!branchId}
              //                   />
              //                 </div>
              //                 <div>
              //                   <FormikCustomSelect
              //                     parentContainer="!h-7"
              //                     placeholder="Select Vendor"
              //                     optionsParentClassName="!capitalize"
              //                     options={vendors}
              //                     name="vendor"
              //                     onChange={(item: {
              //                       value: string;
              //                       text: string;
              //                     }) => {
              //                       setSearchQuery("");
              //                       setVendorId(item.value);
              //                       setVendorName(item.text);
              //                       setFieldValue("vendor", item.text);
              //                     }}
              //                     value={values.vendor}
              //                   />
              //                 </div>
              //               </div>
              //             </div>
              //             <div className="flex justify-end items-end place-content-end">
              //               <p
              //                 className=" pt-3 pr-3 cursor-pointer"
              //                 onClick={() => {
              //                   setBranchId("");
              //                   setBranchName("");
              //                   setDepartmentId("");
              //                   setDepartmentName("");
              //                   setCategoryId("");
              //                   setCategoryName("");
              //                   setVendorId("");
              //                   setVendorName("");
              //                   setSearchQuery("");
              //                   setSelectedDate("");
              //                 }}
              //               >
              //                 Clear Filter
              //               </p>
              //             </div>
              //           </Form>
              //         )}
              //       </Formik>
              //     </div>
              //   </div>
              // }
              handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
              handleSearch={(search) => {
                setSearchQuery(search);
                debounceSearch(search);
              }}
              header={
                <div>
                  <h1 className="font-poppins-medium text-purple-normal-active mb-4">
                    Inventory Information
                  </h1>
                </div>
              }
            />
          ) : (
            <div className="flex justify-center items-center py-[120px]">
              <OrganizationEmptyState
                buttonTitle="Add New Inventory"
                handleClick={() => navigate("/inventory/add-inventory")}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default ListInventory;
