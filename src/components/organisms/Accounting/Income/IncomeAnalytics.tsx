import React, { useEffect, useState } from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ChartOptions,
} from "chart.js";
import { ArrowDown2 } from "iconsax-react";
import CustomSelectWithOverlay from "../../../atoms/CustomInput/CustomSelectWithOverlay";
import StatsCard from "../../../atoms/Cards/StatsCard";
import { PiTrendUp } from "react-icons/pi";
import moment from "moment";
import {
  getHighestIncomeBranch,
  getIncomeChartTrend,
  getIncomeExpenseSummary,
  getRevenueSUmmary,
} from "../../../../api/accountingAnalytics";
import {
  highestIncomeBranchAtom,
  incomeChartTrendAtom,
  incomeExpenseSummaryAtom,
  revenueSummaryAtom,
} from "../../../../recoil/atom/accountAnalytics";
import { useRecoilState, useRecoilValue } from "recoil";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const IncomeAnalytics = () => {
  const [selectedYear, setSelectedYear] = useState(moment().year().toString());
  const [selectedMonth, setSelectedMonth] = useState(
    (moment().month() + 1).toString()
  );
  const [selectedChartYear, setSelectedChartYear] = useState(
    moment().year().toString()
  );
  const [chartLoading, setChartLoading] = useState(false);
  const [, setIncomeSummary] = useRecoilState(revenueSummaryAtom);
  const incomeSummaryValue = useRecoilValue(revenueSummaryAtom);
  const [, setHighestBranchIncome] = useRecoilState(highestIncomeBranchAtom);
  const [, setIncomeExpenseSummary] = useRecoilState(incomeExpenseSummaryAtom);

  const [, setIncomeChartTrend] = useRecoilState(incomeChartTrendAtom);
  const incomeChartTrendValue = useRecoilValue(incomeChartTrendAtom);
  const highestIncomeBranchValue = useRecoilValue(highestIncomeBranchAtom);
  const incomeExpenseSummaryValue = useRecoilValue(incomeExpenseSummaryAtom);

  const fetchIncomeTrendChart = async () => {
    setChartLoading(true);
    try {
      const startDate = moment(`${selectedChartYear}-01`, "YYYY-MM")
        .startOf("month")
        .format("YYYY-MM-DD hh:mm:ss");
      const endDate = moment(`${selectedChartYear}-12`, "YYYY-MM")
        .endOf("month")
        .format("YYYY-MM-DD hh:mm:ss");

      const res = await getIncomeChartTrend({
        from: startDate || "",
        to: endDate || "",
      });

      if (res.success) {
        setIncomeChartTrend(res?.data?.income || res?.data || []);
      } else {
        setIncomeChartTrend([]);
      }
    } catch (error) {
      setIncomeChartTrend([]);
    } finally {
      setChartLoading(false);
    }
  };

  useEffect(() => {
    fetchIncomeTrendChart();
  }, [selectedChartYear]);

  const fetchIncomeSummary = () => {
    const startDate =
      selectedMonth === "0"
        ? moment(`${selectedYear}-01`, "YYYY-MM")
            .startOf("month")
            .format("YYYY-MM-DD hh:mm:ss")
        : moment(`${selectedMonth} ${selectedYear}`, "MM YYYY")
            .startOf("month")
            .format("YYYY-MM-DD hh:mm:ss");
    const endDate =
      selectedMonth === "0"
        ? moment(`${selectedYear}-12`, "YYYY-MM")
            .startOf("month")
            .format("YYYY-MM-DD hh:mm:ss")
        : moment(`${selectedMonth} ${selectedYear}`, "MM YYYY")
            .endOf("month")
            .format("YYYY-MM-DD hh:mm:ss");
    getRevenueSUmmary({ from: startDate || "", to: endDate || "" }).then(
      (res) => {
        if (res.success) {
          setIncomeSummary(res.data);
        }
      }
    );
  };

  const fetchExpenseIncomeSummary = () => {
    getIncomeExpenseSummary().then((res) => {
      if (res.success) {
        setIncomeExpenseSummary(res.data);
      }
    });
  };

  const fetchHighestIncomeByBranch = () => {
    const startDate = selectedMonth === "0" ? moment(`${selectedYear}-01`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${selectedMonth} ${selectedYear}`, "MM YYYY").startOf("month").format("YYYY-MM-DD hh:mm:ss");
    const endDate = selectedMonth === "0" ? moment(`${selectedYear}-12`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${selectedMonth} ${selectedYear}`, "MM YYYY").endOf("month").format("YYYY-MM-DD hh:mm:ss");
    getHighestIncomeBranch({ from: startDate || "", to: endDate || "" }).then(
      (res) => {
        if (res.success) {
          setHighestBranchIncome(res.data);
        }
      }
    );
  };

  useEffect(() => {
    fetchIncomeSummary();
    fetchExpenseIncomeSummary();
    fetchHighestIncomeByBranch();
  }, [selectedYear, selectedMonth]);

  console.log(incomeExpenseSummaryValue)

  const revenueCards = [
    {
      title: "Total income",
      value: incomeExpenseSummaryValue?.total_income?.toLocaleString() || 0,
      icon: <PiTrendUp size={16} />,
      iconBgColor: "bg-[#E8FDEF]",
      iconColor: "text-[#049270]",
      tooltipContent: "Total income",
      showCurrency: true,
      currencyCode: "NGN",
      valueSize: "!text-24 text-[#049270]",
      cardBorder: "!border-[#049270]",
    },
    {
      title: "Highest revenue branch",
      value: highestIncomeBranchValue[0]?.branch_name || "--",
      icon: <PiTrendUp size={16} />,
      iconBgColor: "bg-[#E8FDEF]",
      iconColor: "text-[#049270]",
      tooltipContent: "Highest revenue branch",
      showCurrency: false,
      currencyCode: "NGN",
      valueSize: "!text-24 text-[#049270]",
      cardBorder: "!border-[#049270]",
    },
    {
      title: "Highest earnings",
      value: incomeSummaryValue?.highestIncome
        ? parseInt(
            incomeSummaryValue?.highestIncome[0]?.total || 0
          )?.toLocaleString()
        : 0,
      icon: <PiTrendUp size={16} />,
      iconBgColor: "bg-orange-100",
      iconColor: "text-orange-600",
      tooltipContent: incomeSummaryValue?.highestIncome[0]?.name || "Highest earnings",
      showCurrency: true,
      currencyCode: "NGN",
    },
    {
      title: "Lowest earnings",
      value: incomeSummaryValue?.lowestIncome
        ? parseInt(
            incomeSummaryValue?.lowestIncome[0]?.total || 0
          )?.toLocaleString()
        : 0,
      icon: <PiTrendUp size={16} />,
      iconBgColor: "bg-blue-100",
      iconColor: "text-blue-600",
      tooltipContent: incomeSummaryValue?.lowestIncome[0]?.name || "Lowest earnings",
      showCurrency: true,
      currencyCode: "NGN",
    },
    {
      title: "Highest earning category",
      value: incomeSummaryValue?.highestIncomeSource
        ? parseInt(
            incomeSummaryValue?.highestIncomeSource[0]?.total || 0
          )?.toLocaleString()
        : 0,
      icon: <PiTrendUp size={16} />,
      iconBgColor: "bg-orange-100",
      iconColor: "text-orange-600",
      tooltipContent: incomeSummaryValue?.highestIncomeSource[0]?.name || "Highest earning category",
      showCurrency: true,
      currencyCode: "NGN",
      showInfoDots: true,
    },
    {
      title: "Lowest earning category",
      value: incomeSummaryValue?.lowestIncomeSource
        ? parseInt(
            incomeSummaryValue?.lowestIncomeSource[0]?.total || 0
          )?.toLocaleString()
        : 0,
      icon: <PiTrendUp size={16} />,
      iconBgColor: "bg-purple-100",
      iconColor: "text-purple-600",
      tooltipContent: incomeSummaryValue?.lowestIncomeSource[0]?.name || "Lowest earning category",
      showCurrency: true,
      currencyCode: "NGN",
      showInfoDots: true,
    },
    {
      title: "Top performing vendor",
      value: incomeSummaryValue?.highestIncomeCustomer
        ? parseInt(
            incomeSummaryValue?.highestIncomeCustomer[0]?.total || 0
          )?.toLocaleString()
        : 0,
      icon: <PiTrendUp size={16} />,
      iconBgColor: "bg-yellow-100",
      iconColor: "text-yellow-600",
      tooltipContent: incomeSummaryValue?.highestIncomeCustomer[0]?.name || "Top performing vendor",
      showCurrency: true,
      currencyCode: "NGN",
      showInfoDots: true,
    },
    {
      title: "Least profitable vendor",
      value: incomeSummaryValue?.lowestIncomeCustomer
        ? parseInt(
            incomeSummaryValue?.lowestIncomeCustomer[0]?.total || 0
          )?.toLocaleString()
        : 0,
      icon: <PiTrendUp size={16} />,
      iconBgColor: "bg-pink-100",
      iconColor: "text-pink-600",
      tooltipContent: incomeSummaryValue?.lowestIncomeCustomer[0]?.name || "Least profitable vendor",
      showCurrency: true,
      currencyCode: "NGN",
      showInfoDots: true,
    },
  ];

  const yearOptions = (() => {
    const currentYear = moment().year();
    const years: { text: string; value: string }[] = [];

    for (let year = currentYear - 2; year <= currentYear; year++) {
      years.push({
        text: year.toString(),
        value: year.toString(),
      });
    }

    return years.reverse();
  })();

  const monthOptions = [
    { text: "All", value: "0" },
    { text: "January", value: "1" },
    { text: "February", value: "2" },
    { text: "March", value: "3" },
    { text: "April", value: "4" },
    { text: "May", value: "5" },
    { text: "June", value: "6" },
    { text: "July", value: "7" },
    { text: "August", value: "8" },
    { text: "September", value: "9" },
    { text: "October", value: "10" },
    { text: "November", value: "11" },
    { text: "December", value: "12" },
  ];

  // Process API data for chart
  const processChartData = () => {
    // Always show all 12 months
    const allMonths = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    // Create data array with 0 for all months initially
    const monthlyData = new Array(12).fill(0);

    if (incomeChartTrendValue && incomeChartTrendValue.length > 0) {
      // Map API data to correct month positions
      incomeChartTrendValue.forEach(
        (item: { month: string; total: string }) => {
          const monthIndex = moment(item.month).month(); // 0-based month index
          monthlyData[monthIndex] = parseFloat(item.total);
        }
      );
    }

    // Create point radius array - show dot on highest value (only if > 0)
    const maxValue = Math.max(...monthlyData);
    const pointRadius = monthlyData.map((value: number) =>
      value > 0 && value === maxValue ? 6 : 0
    );

    return {
      labels: allMonths,
      datasets: [
        {
          data: monthlyData,
          fill: true,
          backgroundColor: "#E8FDEF", // Green gradient for income
          borderColor: "#27836D",
          borderWidth: 2,
          tension: 0.4,
          pointRadius,
          pointHoverRadius: 6,
          pointBackgroundColor: "#1E40AF", // Blue color for the dot
          pointBorderColor: "#fff",
          pointBorderWidth: 2,
        },
      ],
    };
  };

  const chartData = processChartData();

  const getYAxisMax = () => {
    if (!incomeChartTrendValue || incomeChartTrendValue.length === 0) {
      return 5000;
    }
    const maxValue = Math.max(
      ...incomeChartTrendValue.map((item: { month: string; total: string }) =>
        Number(item.total)
      )
    );
    if (maxValue === 0) {
      return 5000;
    }

    // Add 20% padding to the max value and round to nearest thousand
    const paddedMax = maxValue * 1.2;
    return Math.ceil(paddedMax / 1000) * 1000;
  };

  const yAxisMax = getYAxisMax();
  const stepSize = Math.max(Math.floor(yAxisMax / 5), 1000); // Dynamic step size

  const chartOptions: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: "#fff",
        titleColor: "#000",
        bodyColor: "#000",
        borderWidth: 1,
        displayColors: false,
        callbacks: {
          label: function (context) {
            return `₦${context.parsed.y.toLocaleString()}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: "#6B7280",
          font: {
            size: 12,
          },
        },
        border: {
          display: false,
        },
      },
      y: {
        beginAtZero: true,
        max: yAxisMax,
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
        ticks: {
          color: "#6B7280",
          font: {
            size: 12,
          },
          stepSize: stepSize,
        },
        border: {
          display: false,
        },
      },
    },
    elements: {
      point: {
        hoverRadius: 8,
      },
    },
  };

  return (
    <div className=" p-6">
      <div className="grid grid-cols-5 gap-8 mb-8">
        <div className="col-span-3">
          <div className="flex justify-between items-center bg-white px-4 py-2 mb-4 rounded-lg">
            <h2 className="text-lg font-medium text-gray-900">Income trend</h2>
            <div className="flex items-center gap-2">
              <CustomSelectWithOverlay
                options={yearOptions}
                value={selectedChartYear}
                onChange={(option: { text: string; value: string }) =>
                  setSelectedChartYear(option.value)
                }
                parentContainer="w-20"
                className="text-sm border-neutral-gray text-neutral-gray"
                showOverlay={true}
              />
              <ArrowDown2
                size={16}
                className="text-neutral-gray -ml-6 pointer-events-none"
              />
            </div>
          </div>
          <div
            style={{ height: "470px", position: "relative" }}
            className="py-4 px-5 bg-white"
          >
            {chartLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-pulse text-gray-500">
                  Loading chart data...
                </div>
              </div>
            ) : (
              <Line data={chartData} options={chartOptions} />
            )}
          </div>
        </div>
        <div className="space-y-4 col-span-2">
          <div className="flex justify-between items-center bg-white px-4 py-2 rounded-lg">
            <h3 className="text-16 font-medium text-gray-900">Revenue data</h3>
            <div className="flex items-center gap-2">
              <CustomSelectWithOverlay
                options={monthOptions}
                value={selectedMonth}
                onChange={(option: { text: string; value: string }) =>
                  setSelectedMonth(option.value)
                }
                parentContainer="w-20"
                className="text-sm border-neutral-gray text-neutral-gray"
                showOverlay={true}
              />
              <ArrowDown2
                size={16}
                className="text-neutral-gray -ml-6 pointer-events-none"
              />
              <CustomSelectWithOverlay
                options={yearOptions}
                value={selectedYear}
                onChange={(option: { text: string; value: string }) =>
                  setSelectedYear(option.value)
                }
                parentContainer="w-20"
                className="text-sm border-neutral-gray text-neutral-gray ml-2"
                showOverlay={true}
              />
              <ArrowDown2
                size={16}
                className="text-neutral-gray -ml-6 pointer-events-none"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 py-4 px-5 bg-white">
            {revenueCards.map((card, index) => (
              <StatsCard
                key={index}
                title={card.title}
                value={card.value}
                icon={card.icon}
                iconBgColor={card.iconBgColor}
                iconColor={card.iconColor}
                tooltipContent={card.tooltipContent}
                showCurrency={card.showCurrency}
                currencyCode={card.currencyCode}
                valueSize={card.valueSize}
                cardBorder={card.cardBorder}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default IncomeAnalytics;
