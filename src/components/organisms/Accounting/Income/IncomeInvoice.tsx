import { Copy } from "iconsax-react";
import React, { useEffect, useRef, useState } from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { FiDownload } from "react-icons/fi";
import { ButtonProperties, getNameInitials } from "../../../shared/helpers";
import { useLocation } from "react-router-dom";
import moment from "moment";
import GoBack from "../../../atoms/Ui/GoBack";
import {
  handleCopyPdfLink,
  handleDownloadPdf,
} from "../../../shared/hooks/handlePdf";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { useRecoilValue } from "recoil";
import logo from "../../../../assets/images/logo.png";
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";
import { defaultBusinessAtom } from "../../../../recoil/atom/organizationAtom";

const IncomeInvoice = () => {
  const isMounted = useRef(false);
  const printRef = useRef<HTMLDivElement>(null);
  const location = useLocation();
  const { data } = location.state || {};
  const [isLoading, setIsLoading] = useState(false);
  const { fetchDefaultBusiness } = useUpdateRecoilAtom();
  const getDefaultBusinessValue = useRecoilValue(defaultBusinessAtom);

  const copyLink = async () => {
    const link = await handleCopyPdfLink(printRef);
    if (link) {
      clustarkToast(NotificationTypes.SUCCESS, "Link copied to clipboard!");
    }
  };

  useEffect(() => {
    if (isMounted.current) return;
    isMounted.current = true;
    fetchDefaultBusiness();
  }, []);

  return (
    <div>
      <GoBack />
      <div className="mt-10 flex justify-between px-4 py-3 text-purple-dark bg-purple-light">
        <h1 className="text-18 font-poppins-medium ">Income invoice</h1>
        <div className="flex justify-center gap-4">
          <CustomButton
            className="!w-[150px] !h-6"
            leftIcon={<Copy size={18} />}
            title="Copy Link"
            handleClick={copyLink}
            isLoading={isLoading}
            variant={ButtonProperties.VARIANT.primary.name}
          />
          <CustomButton
            className="!w-[150px] !h-6"
            leftIcon={<FiDownload size={18} />}
            title="Download invoice"
            handleClick={() => handleDownloadPdf(printRef, "invoice")}
            variant={ButtonProperties.VARIANT.primary.name}
          />
        </div>
      </div>
      <div className="bg-white p-10" ref={printRef}>
        <div className="flex justify-between py-4 px-6 rounded-lg bg-purple-normal text-white">
          <div className="flex gap-4">
            {getDefaultBusinessValue?.avatar ? (
              <img
                src={getDefaultBusinessValue?.avatar}
                alt="Profile"
                className="w-24 h-24 object-cover rounded-full  border"
              />
            ) : (
              <div className="py-5 px-6 bg-white rounded-full flex justify-center items-center place-content-center">

                <p className="  text-purple-normal font-poppins-medium font-bold text-2xl  place-content-center">
                  {getDefaultBusinessValue?.name ? getNameInitials(getDefaultBusinessValue?.name) : ""}
                </p>
              </div>
            )}
            <div className="flex justify-center items-center">
              <div>
                <p className="text-24 font-poppins-medium capitalize">
                  {getDefaultBusinessValue?.name}
                </p>
                <p className="text-14 font-poppins-light">
                  {getDefaultBusinessValue?.business_email}
                </p>
              </div>
            </div>
          </div>
          <div className="flex justify-center items-center">
            <div>
              <p className="capitalize">{getDefaultBusinessValue?.address}</p>
              <p>{moment(data?.date).format("DD-MMM-YYYY")}</p>
            </div>
          </div>
        </div>
        <div></div>
        <div>
          <div className="flex justify-between gap-10 py-8 border-b">
            <div>
              <p className="font-bold">To</p>
              <p className="capitalize">{data?.vendor?.name || "John Doe"}</p>
              <p className="capitalize">{data?.vendor?.address || "01 Txz, Houston Texas"}</p>
              <p>{data?.vendor?.email || "<EMAIL>"}</p>
              <p>{data?.vendor?.phone || "******* 8374 984"}</p>
            </div>
            <div className="row-span-">
              <p>
                Amount:
                <span className="font-bold ml-1">
                  {data?.currency} {data?.amount?.toLocaleString()}
                </span>
              </p>
              <p>
                Date Issued:
                <span className="ml-1">
                  {moment(data?.date).format("DD/MM/YYYY")}
                </span>
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-10 py-8 capitalize"></div>
          <div className="py-4 border-b">
            <h1>Summary</h1>
            <p>Your expense breakdown</p>
          </div>
        </div>

        <div className="py-8">
          <table className="w-full text-left">
            <thead>
              <tr className="border-b">
                <th className="py-2">Name</th>
                <th className="py-2">Category</th>
                <th className="py-2">Branch</th>
                <th className="py-2">Vendor</th>
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="py-2 capitalize">{data?.name || "--"}</td>
                <td className="py-2 capitalize">
                  {data?.expenseCategory?.name || "--"}
                </td>
                <td className="py-2 capitalize">
                  {data?.branch?.name || "--"}
                </td>
                <td className="py-2 capitalize">
                  {data?.vendor?.name || "--"}
                </td>
                <td className="py-2 capitalize">
                  {data?.currency} {data?.amount?.toLocaleString()}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div className="text-right">
          <div>
            <div className="pr-4">
              <h1 className="mt-1">
                Subtotal :
                <span>
                  {data?.currency} {data?.amount?.toLocaleString()}
                </span>
              </h1>
              {/* <h1 className='mt-1'>Discount : <span>0.00</span></h1>
                        <h1 className='mt-1'>VAT : <span>0.00</span></h1> */}
            </div>
            <hr />
            <div className="mt-2">
              <h1 className="bg-purple-light text-purple-normal font-poppins-medium text-20 py-3 pr-4">
                Total :
                <span>
                  {data?.currency} {data?.amount?.toLocaleString()}
                </span>
              </h1>
            </div>
          </div>
        </div>
        <div className="flex justify-center items-center mt-20">
          <div>
            <div className="flex justify-center items-center">
              <h1 className="mt-[16px] font-poppins-medium text-20 text-purple-normal">
                <img src={logo} alt="logo" />
              </h1>
            </div>
            <div>
              <p>ArkHR, Ipent 4 extension, Lokogoma, Ap Abuja</p>
              <p className="text-center">+234 903 901 1682</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IncomeInvoice;
