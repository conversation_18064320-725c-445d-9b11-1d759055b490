import React, { useEffect, useState } from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from "chart.js";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import ReportSkeleton from "../../../atoms/Skeleton/AccountingReportSkeleton";
import CustomSelectWithOverlay from "../../../atoms/CustomInput/CustomSelectWithOverlay";
import { ArrowDown2 } from "iconsax-react";
import moment from "moment";
import StatisticsCard from "../../../atoms/Cards/StatisticsCard";

// Import and initialize 3D module
import "highcharts/highcharts-3d";
import { IoCubeOutline } from "react-icons/io5";
import { MdOutlineDoNotDisturbOn } from "react-icons/md";
import { useRecoilState, useRecoilValue } from "recoil";
import { expenseChartTrendAtom, highestExpenseBranchAtom, highestIncomeBranchAtom, incomeChartTrendAtom, incomeExpenseSummaryAtom, top10ExpenseCategoryAtom, top10ExpenseVendorAtom, top10IncomeCustomersAtom, top10IncomeSourceAtom } from "../../../../recoil/atom/accountAnalytics";
import { getExpenseChartTrend, getHighestExpenseBranch, getHighestIncomeBranch, getIncomeChartTrend, getIncomeExpenseSummary, getTop10ExpenseCategory, getTop10ExpenseVendor, getTop10IncomeCustomers, getTop10IncomeSources } from "../../../../api/accountingAnalytics";
import { currencySymbol } from "../../../shared/helpers";
import SearchCategoryIcon from "../../../../assets/icons/SearchCategoryIcon";
import CurrencyTag from "../../../atoms/Ui/CurrencyTag";
import OpenSquareArrow from "../../../../assets/icons/OpenSquareArrow";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);



const Report = () => {
  const [chartLoading, setChartLoading] = useState(false);
  const [donutChartLoading, setDonutChartLoading] = useState(false);
  const [tableLoadingState, setTableLoadingState] = useState({
    expenseVendorLoading: false,
    expenseCategoryLoading: false,
    incomeCustomerLoading: false,
    incomeSourceLoading: false,
  })
  const [isFetching, setIsFetching] = useState(true);
  const [selectedChartYear, setSelectedChartYear] = useState(moment().year().toString());
  const [selectedMonth, setSelectedMonth] = useState((moment().month() + 1).toString());
  const [selectedYear, setSelectedYear] = useState(moment().year().toString());
  const [expenseVendorSelectedMonth, setExpenseVendorSelectedMonth] = useState((moment().month() + 1).toString());
  const [expenseVendorSelectedYear, setExpenseVendorSelectedYear] = useState(moment().year().toString());
  const [expenseCategorySelectedMonth, setExpenseCategorySelectedMonth] = useState((moment().month() + 1).toString());
  const [expenseCategorySelectedYear, setExpenseCategorySelectedYear] = useState(moment().year().toString());
  const [incomeCustomerSelectedMonth, setIncomeCustomerSelectedMonth] = useState((moment().month() + 1).toString());
  const [incomeCustomerSelectedYear, setIncomeCustomerSelectedYear] = useState(moment().year().toString());
  const [incomeSourceSelectedMonth, setIncomeSourceSelectedMonth] = useState((moment().month() + 1).toString());
  const [incomeSourceSelectedYear, setIncomeSourceSelectedYear] = useState(moment().year().toString());
  const [, setIncomeExpenseSummary] = useRecoilState(incomeExpenseSummaryAtom);
  const [, setIncomeChartTrend] = useRecoilState(incomeChartTrendAtom);
  const [, setExpenseChartTrend] = useRecoilState(expenseChartTrendAtom);
  const [, setHighestBranchExpense] = useRecoilState(highestExpenseBranchAtom);
  const [, setHighestBranchIncome] = useRecoilState(highestIncomeBranchAtom);
  const incomeChartTrendValue = useRecoilValue(incomeChartTrendAtom);
  const expenseChartTrendValue = useRecoilValue(expenseChartTrendAtom);
  const incomeExpenseSummaryValue = useRecoilValue(incomeExpenseSummaryAtom);
  const highestExpenseBranchValue = useRecoilValue(highestExpenseBranchAtom);
  const highestIncomeBranchValue = useRecoilValue(highestIncomeBranchAtom);
  const [, setTop10ExpenseCategory] = useRecoilState(top10ExpenseCategoryAtom);
  const [, setTop10ExpenseVendor] = useRecoilState(top10ExpenseVendorAtom);
  const top10ExpenseCategory = useRecoilValue(top10ExpenseCategoryAtom);
  const [, setTop10IncomeCustomers] = useRecoilState(top10IncomeCustomersAtom);
  const [, setTop10IncomeSources] = useRecoilState(top10IncomeSourceAtom);
  const top10ExpenseVendor = useRecoilValue(top10ExpenseVendorAtom);
  const top10IncomeCustomers = useRecoilValue(top10IncomeCustomersAtom);
  const top10IncomeSources = useRecoilValue(top10IncomeSourceAtom);


  const fetchExpenseIncomeSummary = () => {
    setIsFetching(true);
    getIncomeExpenseSummary().then((res) => {
      if (res.success) {
        setIncomeExpenseSummary(res.data);
      };
      setIsFetching(false);
    });
  };

  const fetchIncomeTrendChart = async () => {
    setChartLoading(true);
    try {
      const startDate = moment(`${selectedChartYear}-01`, "YYYY-MM")
        .startOf("month")
        .format("YYYY-MM-DD hh:mm:ss");
      const endDate = moment(`${selectedChartYear}-12`, "YYYY-MM")
        .endOf("month")
        .format("YYYY-MM-DD hh:mm:ss");

      const res = await getIncomeChartTrend({
        from: startDate || "",
        to: endDate || "",
      });

      if (res.success) {
        setIncomeChartTrend(res?.data?.income || res?.data || []);
      } else {
        setIncomeChartTrend([]);
      }
    } catch (error) {
      setIncomeChartTrend([]);
    } finally {
      setChartLoading(false);
    }
  };

  const fetchExpenseTrendChart = async () => {
    setChartLoading(true);
    try {
      const startDate = moment(`${selectedChartYear}-01`, "YYYY-MM")
        .startOf("month")
        .format("YYYY-MM-DD hh:mm:ss");
      const endDate = moment(`${selectedChartYear}-12`, "YYYY-MM")
        .endOf("month")
        .format("YYYY-MM-DD hh:mm:ss");
      const res = await getExpenseChartTrend({
        from: startDate || "",
        to: endDate || "",
      });

      if (res.success) {
        setExpenseChartTrend(res.data);
      } else {
        setExpenseChartTrend([]);
      }
    } catch (error) {
      setExpenseChartTrend([]);
    } finally {
      setChartLoading(false);
    }
  };

  const fetchTop10ExpenseVendor = () => {
    setTableLoadingState({ ...tableLoadingState, expenseVendorLoading: true })
    const startDate = expenseVendorSelectedMonth === "0" ? moment(`${expenseVendorSelectedYear}-01`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${expenseVendorSelectedMonth} ${expenseVendorSelectedYear}`, "MM YYYY").startOf("month").format("YYYY-MM-DD hh:mm:ss");
    const endDate = expenseVendorSelectedMonth === "0" ? moment(`${expenseVendorSelectedYear}-12`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${expenseVendorSelectedMonth} ${expenseVendorSelectedYear}`, "MM YYYY").endOf("month").format("YYYY-MM-DD hh:mm:ss");
    getTop10ExpenseVendor({ from: startDate || "", to: endDate || "" }).then((res) => {
      if (res.success) {
        setTableLoadingState({ ...tableLoadingState, expenseVendorLoading: false })
        setTop10ExpenseVendor(res.data);
      }
    });
  };

  const fetchTop10ExpenseCategory = () => {
    setTableLoadingState({ ...tableLoadingState, expenseCategoryLoading: true })
    const startDate = expenseCategorySelectedMonth === "0" ? moment(`${expenseCategorySelectedYear}-01`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${expenseCategorySelectedMonth} ${expenseCategorySelectedYear}`, "MM YYYY").startOf("month").format("YYYY-MM-DD hh:mm:ss");
    const endDate = expenseCategorySelectedMonth === "0" ? moment(`${expenseCategorySelectedYear}-12`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${expenseCategorySelectedMonth} ${expenseCategorySelectedYear}`, "MM YYYY").endOf("month").format("YYYY-MM-DD hh:mm:ss");
    getTop10ExpenseCategory({ from: startDate || "", to: endDate || "" }).then((res) => {
      if (res.success) {
        setTableLoadingState({ ...tableLoadingState, expenseCategoryLoading: false });
        setTop10ExpenseCategory(res.data);
      }
    });
  };

  const fetchTop10IncomeCustomers = () => {
    setTableLoadingState({ ...tableLoadingState, incomeCustomerLoading: true })
    const startDate = incomeCustomerSelectedMonth === "0" ? moment(`${incomeCustomerSelectedYear}-01`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${incomeCustomerSelectedMonth} ${incomeCustomerSelectedYear}`, "MM YYYY").startOf("month").format("YYYY-MM-DD hh:mm:ss");
    const endDate = incomeCustomerSelectedMonth === "0" ? moment(`${incomeCustomerSelectedYear}-12`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${incomeCustomerSelectedMonth} ${incomeCustomerSelectedYear}`, "MM YYYY").endOf("month").format("YYYY-MM-DD hh:mm:ss");
    getTop10IncomeCustomers({ from: startDate || "", to: endDate || "" }).then((res) => {
      if (res.success) {
        setTableLoadingState({ ...tableLoadingState, incomeCustomerLoading: false });
        setTop10IncomeCustomers(res.data);
      }
    })
  };

  const fetchTop10IncomeSources = () => {
    setTableLoadingState({ ...tableLoadingState, incomeSourceLoading: true });
    const startDate = incomeSourceSelectedMonth === "0" ? moment(`${incomeSourceSelectedYear}-01`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${incomeSourceSelectedMonth} ${incomeSourceSelectedYear}`, "MM YYYY").startOf("month").format("YYYY-MM-DD hh:mm:ss");
    const endDate = incomeSourceSelectedMonth === "0" ? moment(`${incomeSourceSelectedYear}-12`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${incomeSourceSelectedMonth} ${incomeSourceSelectedYear}`, "MM YYYY").endOf("month").format("YYYY-MM-DD hh:mm:ss");
    getTop10IncomeSources({ from: startDate || "", to: endDate || "" }).then((res) => {
      if (res.success) {
        setTableLoadingState({ ...tableLoadingState, incomeSourceLoading: false });
        setTop10IncomeSources(res.data);
      }
    })
  };

  useEffect(() => {
    fetchTop10ExpenseVendor();
  }, [expenseVendorSelectedMonth, expenseVendorSelectedYear]);

  useEffect(() => {
    fetchTop10ExpenseCategory();
  }, [expenseCategorySelectedMonth, expenseCategorySelectedYear]);

  useEffect(() => {
    fetchTop10IncomeCustomers();
  }, [incomeCustomerSelectedMonth, incomeCustomerSelectedYear]);

  useEffect(() => {
    fetchTop10IncomeSources();
  }, [incomeSourceSelectedMonth, incomeSourceSelectedYear]);

  useEffect(() => {
    fetchExpenseIncomeSummary();
  }, []);

  useEffect(() => {
    fetchIncomeTrendChart();
    fetchExpenseTrendChart();
  }, [selectedChartYear]);

  const fetchHighestExpenseByBranch = () => {
    setDonutChartLoading(true)
    const startDate = selectedMonth === "0" ? moment(`${selectedYear}-01`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${selectedMonth} ${selectedYear}`, "MM YYYY").startOf("month").format("YYYY-MM-DD hh:mm:ss");
    const endDate = selectedMonth === "0" ? moment(`${selectedYear}-12`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${selectedMonth} ${selectedYear}`, "MM YYYY").endOf("month").format("YYYY-MM-DD hh:mm:ss");
    getHighestExpenseBranch({ from: startDate || "", to: endDate || "" }).then(
      (res) => {
        if (res.success) {
          setDonutChartLoading(false);
          setHighestBranchExpense(res.data);
        }
      }
    );
  };

  const fetchHighestIncomeByBranch = () => {
    setDonutChartLoading(true);
    const startDate = selectedMonth === "0" ? moment(`${selectedYear}-01`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${selectedMonth} ${selectedYear}`, "MM YYYY").startOf("month").format("YYYY-MM-DD hh:mm:ss");
    const endDate = selectedMonth === "0" ? moment(`${selectedYear}-12`, "YYYY-MM").startOf("month").format("YYYY-MM-DD hh:mm:ss") : moment(`${selectedMonth} ${selectedYear}`, "MM YYYY").endOf("month").format("YYYY-MM-DD hh:mm:ss");
    getHighestIncomeBranch({ from: startDate || "", to: endDate || "" }).then(
      (res) => {
        if (res.success) {
          setDonutChartLoading(false);
          setHighestBranchIncome(res.data);
        }
      }
    );
  };

  useEffect(() => {
    fetchHighestExpenseByBranch();
    fetchHighestIncomeByBranch();
  }, [selectedYear, selectedMonth]);

  const expenseChartOptions = {
    chart: {
      type: "pie",
      options3d: {
        enabled: true,
        alpha: 45,
        beta: 0,
      },
      backgroundColor: 'transparent',
      height: 120,
      width: null,
      // reflow: true
    },
    title: {
      text: "",
      style: {
        font: {
          size: 12,
          family: "Cabin",
        },
      },
    },
    plotOptions: {
      pie: {
        innerSize: "50%",
        depth: 45,
        dataLabels: {
          enabled: true,
        },
      },
    },
    series: [
      {
        name: "Data",
        data: highestExpenseBranchValue.map(item => [item.branch_name, parseInt(item.total)])
      },
    ],
    responsive: {
    rules: [{
      condition: {
        maxWidth: 100
      },
      chartOptions: {
        plotOptions: {
          pie: {
            dataLabels: {
              enabled: false
            }
          }
        }
      }
    }]
  },
    colors: ["#EE5858", "#F6ADAD"],
    credits: {
      enabled: false,
    },
  };

  const incomeChartOptions = {
    chart: {
      type: "pie",
      options3d: {
        enabled: true,
        alpha: 45,
      },
      backgroundColor: "transparent",
      height: 120,
      width: null, 
      // reflow: true,
    },
    title: {
      text: "",
    },
    plotOptions: {
      pie: {
        innerSize: "50%",
        depth: 45,
        dataLabels: {
          enabled: true,
        },
      },
    },
    series: [
      {
        name: "Revenue",
        data: highestIncomeBranchValue.map(item => [item.branch_name, parseInt(item.total)])
      },
    ],
    colors: ["#3730A3", "#6FD195"],
    responsive: {
    rules: [{
      condition: {
        maxWidth: 100
      },
      chartOptions: {
        plotOptions: {
          pie: {
            dataLabels: {
              enabled: false
            }
          }
        }
      }
    }]
  },
    credits: {
      enabled: false,
    },
  };


  const yearOptions = (() => {
    const currentYear = moment().year();
    const years: { text: string; value: string }[] = [];

    for (let year = currentYear - 2; year <= currentYear; year++) {
      years.push({
        text: year.toString(),
        value: year.toString(),
      });
    }

    return years.reverse();
  })();

  const monthOptions = [
    { text: "All", value: "0" },
    { text: "January", value: "1" },
    { text: "February", value: "2" },
    { text: "March", value: "3" },
    { text: "April", value: "4" },
    { text: "May", value: "5" },
    { text: "June", value: "6" },
    { text: "July", value: "7" },
    { text: "August", value: "8" },
    { text: "September", value: "9" },
    { text: "October", value: "10" },
    { text: "November", value: "11" },
    { text: "December", value: "12" },
  ];

  if (isFetching) {
    return (
      <div>
        <ReportSkeleton />
      </div>
    );
  }

  const processChartData = () => {
    const allMonths = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    const incomeData = new Array(12).fill(0);
    const expenseData = new Array(12).fill(0);

    incomeChartTrendValue?.forEach((item: { month: string; total: string }) => {
      const monthIndex = moment(item.month).month();
      incomeData[monthIndex] = parseFloat(item.total);
    });

    expenseChartTrendValue?.forEach(
      (item: { month: string; total: string }) => {
        const monthIndex = moment(item.month).month();
        expenseData[monthIndex] = parseFloat(item.total);
      }
    );

    const incomePointRadius = incomeData.map((value: number) =>
      value > 0 ? 6 : 0
    );
    const expensePointRadius = expenseData.map((value: number) =>
      value > 0 ? 6 : 0
    );

    return {
      labels: allMonths,
      datasets: [
        {
          label: "Income",
          data: incomeData,
          // fill: true,
          // backgroundColor: "rgba(232, 253, 239, 0.5)",
          borderColor: "#27836D",
          borderWidth: 2,
          tension: 0.4,
          pointRadius: incomePointRadius,
          // pointRadius: 0,
          pointHoverRadius: 6,
          pointBackgroundColor: "#27836D",
          pointBorderColor: "#fff",
          pointBorderWidth: 2,
        },
        {
          label: "Expenses",
          data: expenseData,
          // fill: true,
          // backgroundColor: "rgba(255, 230, 230, 0.4)",
          borderColor: "#D14343",
          borderWidth: 2,
          tension: 0.4,
         pointRadius: expensePointRadius,
          pointHoverRadius: 6,
          pointBackgroundColor: "#D14343",
          pointBorderColor: "#fff",
          pointBorderWidth: 2,
        },
      ],
    };
  };

  const chartData = processChartData();

  const getYAxisMax = () => {
    const allTotals = [
      ...incomeChartTrendValue.map((i) => +i.total),
      ...expenseChartTrendValue.map((i) => +i.total),
    ];

    const maxValue = Math.max(...allTotals, 0);
    const padded = maxValue * 1.2;
    return Math.ceil(padded / 1000) * 1000 || 5000;
  };

  const yAxisMax = getYAxisMax();
  const stepSize = Math.max(Math.floor(yAxisMax / 5), 1000);

  const chartOptions: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: "#fff",
        titleColor: "#374151",
        bodyColor: "#374151",
        borderWidth: 1,
        displayColors: false,
        callbacks: {
          label: (context) =>
            `${context.dataset.label}: ₦${context.parsed.y.toLocaleString()}`,
        },
        titleFont: {
          size: 14,
          family: "Cabin",
        },
        bodyFont: {
          size: 12,
          family: "Cabin",
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: "#6B7280",
          font: {
            size: 12,
            family: "Cabin",
          },
        },
        border: {
          display: false,
        },
      },
      y: {
        beginAtZero: true,
        max: yAxisMax,
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
        ticks: {
          color: "#6B7280",
          font: {
            size: 12,
            family: "Cabin",
          },
          stepSize,
        },
        border: {
          display: false,
        },
      },
    },
    elements: {
      point: {
        hoverRadius: 6,
      },
    },
  };

  const accountStats = [
    {
      title: "Current balance",
      value: incomeExpenseSummaryValue?.balance?.toLocaleString() || 0,
      icon: <IoCubeOutline className="rotate-90" size={24} />,
      valueText: "",
      color: "#EEF2FFCC",
      iconBackgroundColor: '#3730A399',
      parentContainer: "!shadow-none",
      currency: <CurrencyTag currency={currencySymbol.NGN_PREFIX} />,
    },
    {
      title: "Total income",
      value: incomeExpenseSummaryValue?.total_income?.toLocaleString() || 0,
      icon: <OpenSquareArrow />
      ,
      valueText: "",
      color: "#FDFFE8CC",
      iconBackgroundColor: '#B3AA0199',
      parentContainer: "!shadow-none",
      currency: <CurrencyTag currency={currencySymbol.NGN_PREFIX} />,
    },
    {
      title: "Total expense",
      value: incomeExpenseSummaryValue?.total_expense?.toLocaleString() || 0,
      icon: <MdOutlineDoNotDisturbOn size={24} />,
      valueText: "",
      color: "#FFEDEDCC",
      iconBackgroundColor: '#A3000099',
      parentContainer: "!shadow-none",
      currency: <CurrencyTag currency={currencySymbol.NGN_PREFIX} />,
    },
  ];

  return (
    <div>
      <div className="bg-white py-8 px-6 rounded-[10px] ">
        {/* <h1 className="text-16 font-poppins-medium">Reports</h1> */}
        <div className="grid grid-cols-3 gap-5 mt-2 ">
          {accountStats?.map((item, index) => (
            <div key={index}>
              <StatisticsCard
                backgroundColor={item.color}
                key={index}
                title={item.title}
                value={item.value}
                icon={item.icon}
                valueText={item.valueText}
                iconBackgroundColor={item.iconBackgroundColor}
                parentContainer={item.parentContainer}
                currency={item.currency}
              />
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-6 gap-6 my-8">
        <div className="col-span-4 rounded-xl flex flex-col">
          <div className="bg-white py-2 px-4 flex justify-between">
            <h2 className="font-medium text-16 flex justify-center items-center">
              Income and expense trend
            </h2>
            <div className="flex space-x-2">

              <div className="flex items-center gap-2">
                <CustomSelectWithOverlay
                  options={yearOptions}
                  value={selectedChartYear}
                  onChange={(option: { text: string; value: string }) =>
                    setSelectedChartYear(option.value)
                  }
                  parentContainer="w-20"
                  className="text-sm border-neutral-gray text-neutral-gray"
                  showOverlay={true}
                />
                <ArrowDown2
                  size={16}
                  className="text-neutral-gray -ml-6 pointer-events-none"
                />
              </div>
            </div>
          </div>
          <div
            style={{ height: "366px", position: "relative" }}
            className="mt-2 bg-white p-6 flex items-center justify-center "
          >
            <div className="w-full">
              {chartLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-pulse text-gray-500">
                    Loading chart data...
                  </div>
                </div>
              ) : (
                <div className="h-[316px] py-3">
                  <div className="flex justify-end pb-3 text-[#949494]">
                     <p className="w-3.5 border h-fit mt-2 mr-2 border-[#68BDA9]"></p>
                    <div className="">INCOME</div>
                    <p className="w-3.5 border h-fit mt-2 ml-5 mr-2 border-[#F41F22]"></p>
                    <p>EXPENSE</p>
                  </div>
                  <Line data={chartData} options={chartOptions} />
                </div>
              )}
            </div>
          </div>
        </div>
        {/* Data: Highest expense/revenue by branch */}
        <div className=" col-span-2 rounded-xl flex flex-col">
          <div className="bg-white py-2 px-4 flex justify-between">
            <h2 className="font-medium text-16 flex justify-center items-center">
              Data
            </h2>
            <div className="flex space-x-2">
              <div className="flex items-center gap-2">
                <CustomSelectWithOverlay
                  options={monthOptions}
                  value={selectedMonth}
                  onChange={(option: { text: string; value: string }) =>
                    setSelectedMonth(option.value)
                  }
                  parentContainer="w-20"
                  className="text-sm border-neutral-gray text-neutral-gray"
                  showOverlay={true}
                />
                <ArrowDown2
                  size={16}
                  className="text-neutral-gray -ml-6 pointer-events-none"
                />
              </div>
              <div className="flex items-center gap-2">
                <CustomSelectWithOverlay
                  options={yearOptions}
                  value={selectedYear}
                  onChange={(option: { text: string; value: string }) =>
                    setSelectedYear(option.value)
                  }
                  parentContainer="w-20"
                  className="text-sm border-neutral-gray text-neutral-gray"
                  showOverlay={true}
                />
                <ArrowDown2
                  size={16}
                  className="text-neutral-gray -ml-6 pointer-events-none"
                />
              </div>
            </div>
          </div>
          <div className="bg-white mt-2 py-6 px-4 h-full">
            <div className="border p-4">
              <span className="font-medium text-neutral-dark mb-3 block">
                Highest expense by branch
              </span>
              <div>
                <div className="w-full">
                  {donutChartLoading ? (
                    <div className="flex items-center justify-center h-20">
                      <div className="animate-pulse flex items-center justify-center text-gray-500 h-20">
                        Loading chart data...
                      </div>
                    </div>
                  ) : highestExpenseBranchValue.length === 0 ?
                    <div className="flex items-center justify-center text-gray-500 h-20">
                      No data available
                    </div>
                    : (
                      <div className="w-full max-w-md h-28 relative">
                        <HighchartsReact                      
                          highcharts={Highcharts}
                          options={expenseChartOptions}
                        />
                      </div>
                    )}

                </div>
              </div>
            </div>
            <div className="border mt-3 p-4">
              <span className=" font-medium text-neutral-dark mb-3 block">
                Highest revenue by branch
              </span>
              <div className="">
                <div className="w-full relative">
                  {donutChartLoading ? (
                    <div className="flex items-center justify-center h-20">
                      <div className="animate-pulse flex items-center justify-center text-gray-500 h-20">
                        Loading chart data...
                      </div>
                    </div>
                  ) : highestIncomeBranchValue.length === 0 ?
                    <div className="flex items-center justify-center text-gray-500 h-20">
                      No data available
                    </div>
                    : (
                      <div className="w-full max-w-md relative h-28">

                        <HighchartsReact
                          highcharts={Highcharts}
                          options={incomeChartOptions}
                        />
                      </div>
                    )}

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Lower Grid: Expense */}
      <div className="my-8">
        <div className="grid grid-cols-2 gap-6 my-8">
          {/* Top expense vendor */}
          <div>
            <div className="bg-white py-2 px-4 flex justify-between">
              <h2 className="font-medium text-16 flex justify-center items-center">
                Top expense vendor
              </h2>
              <div className="flex space-x-2">
                <div className="flex items-center gap-2">
                  <CustomSelectWithOverlay
                    options={monthOptions}
                    value={expenseVendorSelectedMonth}
                    onChange={(option: { text: string; value: string }) =>
                      setExpenseVendorSelectedMonth(option.value)
                    }
                    parentContainer="w-20"
                    className="text-sm border-neutral-gray text-neutral-gray"
                    showOverlay={true}
                  />
                  <ArrowDown2
                    size={16}
                    className="text-neutral-gray -ml-6 pointer-events-none"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <CustomSelectWithOverlay
                    options={yearOptions}
                    value={expenseVendorSelectedYear}
                    onChange={(option: { text: string; value: string }) =>
                      setExpenseVendorSelectedYear(option.value)
                    }
                    parentContainer="w-20"
                    className="text-sm border-neutral-gray text-neutral-gray"
                    showOverlay={true}
                  />
                  <ArrowDown2
                    size={16}
                    className="text-neutral-gray -ml-6 pointer-events-none"
                  />
                </div>
              </div>
            </div>
            <div className="mt-2">

              {tableLoadingState.expenseVendorLoading ? (
                <div className="flex items-center justify-center h-full bg-white">
                  <div className="animate-pulse flex items-center justify-center text-gray-500 h-40">
                    Loading data...
                  </div>
                </div>
              ) : (
                <>
                  {top10ExpenseVendor?.length > 0 ? (
                    <div className=" bg-white my-2 px-4 py-8 h-full">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-purple-light border border-[#DCDEE64D] px-4 py-3 rounded-tl-lg rounded-bl-lg font-medium">
                          Categories
                        </div>
                        <div className="bg-purple-light border border-[#DCDEE64D] px-4 py-3 rounded-tr-lg rounded-br-lg font-medium">
                          Total
                        </div>
                        {top10ExpenseVendor.map((expense, index) => (
                          <React.Fragment key={index}>
                            <div className="border border-[#DCDEE64D] px-4 py-3 rounded">
                              {expense.name}
                            </div>
                            <div className="border border-[#DCDEE64D] px-4 py-3 flex items-center rounded">
                              <span className="bg-purple-light text-purple-normal p-1 rounded mr-2">
                                NGN
                              </span>
                              {expense.total}
                            </div>
                          </React.Fragment>
                        ))}

                      </div>
                    </div>
                  ) : (
                    <EmptyState title="Top expense vendor" />
                  )}
                </>
              )}
            </div>

          </div>
          <div>
            <div className="bg-white py-2 px-4 flex justify-between">
              <h2 className="font-medium text-16 flex justify-center items-center">
                Top expense categories
              </h2>
              <div className="flex space-x-2">
                <div className="flex items-center gap-2">
                  <CustomSelectWithOverlay
                    options={monthOptions}
                    value={expenseCategorySelectedMonth}
                    onChange={(option: { text: string; value: string }) =>
                      setExpenseCategorySelectedMonth(option.value)
                    }
                    parentContainer="w-20"
                    className="text-sm border-neutral-gray text-neutral-gray"
                    showOverlay={true}
                  />
                  <ArrowDown2
                    size={16}
                    className="text-neutral-gray -ml-6 pointer-events-none"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <CustomSelectWithOverlay
                    options={yearOptions}
                    value={expenseCategorySelectedYear}
                    onChange={(option: { text: string; value: string }) =>
                      setExpenseCategorySelectedYear(option.value)
                    }
                    parentContainer="w-20"
                    className="text-sm border-neutral-gray text-neutral-gray"
                    showOverlay={true}
                  />
                  <ArrowDown2
                    size={16}
                    className="text-neutral-gray -ml-6 pointer-events-none"
                  />
                </div>
              </div>
            </div>
            <div className="mt-2">

              {tableLoadingState.expenseCategoryLoading ? (
                <div className="flex items-center justify-center h-full bg-white">
                  <div className="animate-pulse flex items-center justify-center text-gray-500 h-40">
                    Loading data...
                  </div>
                </div>
              ) : (
                <>
                  {top10ExpenseCategory?.length > 0 ? (
                    <div className="bg-white my-2 px-4 py-8 h-full">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-purple-light border border-[#DCDEE64D] px-4 py-3 rounded-tl-lg rounded-bl-lg font-medium">
                          Categories
                        </div>
                        <div className="bg-purple-light border border-[#DCDEE64D] px-4 py-3 rounded-tr-lg rounded-br-lg font-medium">
                          Total
                        </div>
                        {top10ExpenseCategory.map((expense, index) => (
                          <React.Fragment key={index}>
                            <div className="border border-[#DCDEE64D] px-4 py-3 rounded">
                              {expense.name}
                            </div>
                            <div className="border border-[#DCDEE64D] px-4 py-3 flex items-center rounded">
                              <span className="bg-purple-light text-purple-normal p-1 rounded mr-2">
                                NGN
                              </span>
                              {expense.total}
                            </div>
                          </React.Fragment>
                        ))}

                      </div>
                    </div>
                  ) : (
                    <EmptyState title="Top expense categories" />
                  )}
                </>
              )}
            </div>

          </div>

        </div>
      </div>

      {/* Lower Grid: Income */}
      <div className="mb-8">

        <div className="grid grid-cols-2 gap-6">
          {/* Top income customers */}
          <div>
            <div className="bg-white py-2 px-4 flex justify-between">
              <h2 className="font-medium text-16 flex justify-center items-center">
                Top income customers
              </h2>
              <div className="flex space-x-2">
                <div className="flex items-center gap-2">
                  <CustomSelectWithOverlay
                    options={monthOptions}
                    value={incomeCustomerSelectedMonth}
                    onChange={(option: { text: string; value: string }) =>
                      setIncomeCustomerSelectedMonth(option.value)
                    }
                    parentContainer="w-20"
                    className="text-sm border-neutral-gray text-neutral-gray"
                    showOverlay={true}
                  />
                  <ArrowDown2
                    size={16}
                    className="text-neutral-gray -ml-6 pointer-events-none"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <CustomSelectWithOverlay
                    options={yearOptions}
                    value={incomeCustomerSelectedYear}
                    onChange={(option: { text: string; value: string }) =>
                      setIncomeCustomerSelectedYear(option.value)
                    }
                    parentContainer="w-20"
                    className="text-sm border-neutral-gray text-neutral-gray"
                    showOverlay={true}
                  />
                  <ArrowDown2
                    size={16}
                    className="text-neutral-gray -ml-6 pointer-events-none"
                  />
                </div>
              </div>
            </div>

            {tableLoadingState.incomeCustomerLoading ? (
              <div className="flex items-center justify-center h-full bg-white mt-2">
                <div className="animate-pulse flex items-center justify-center text-gray-500 h-40">
                  Loading data...
                </div>
              </div>
            ) : (
              <>
                {top10IncomeCustomers?.length > 0 ? (
                  <div className=" bg-white my-2 px-4 py-8 h-full">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-purple-light border border-[#DCDEE64D] px-4 py-3 rounded-tl-lg rounded-bl-lg font-medium">
                        Categories
                      </div>
                      <div className="bg-purple-light border border-[#DCDEE64D] px-4 py-3 rounded-tr-lg rounded-br-lg font-medium">
                        Total
                      </div>
                      {top10IncomeCustomers.map((income, index) => (
                        <React.Fragment key={index}>
                          <div className="border border-[#DCDEE64D] px-4 py-3 rounded">
                            {income.name}
                          </div>
                          <div className="border border-[#DCDEE64D] px-4 py-3 flex items-center rounded">
                            <span className="bg-purple-light text-purple-normal p-1 rounded mr-2">
                              NGN
                            </span>
                            {income.total}
                          </div>
                        </React.Fragment>
                      ))}

                    </div>
                  </div>
                ) : (
                  <EmptyState title="Top income customers" />
                )}
              </>
            )}

          </div>

          {/* Top income source */}
          <div>
            <div className="bg-white py-2 px-4 flex justify-between">
              <h2 className="font-medium text-16 flex justify-center items-center">
                Top income source
              </h2>
              <div className="flex space-x-2">
                <div className="flex items-center gap-2">
                  <CustomSelectWithOverlay
                    options={monthOptions}
                    value={incomeSourceSelectedMonth}
                    onChange={(option: { text: string; value: string }) =>
                      setIncomeSourceSelectedMonth(option.value)
                    }
                    parentContainer="w-20"
                    className="text-sm border-neutral-gray text-neutral-gray"
                    showOverlay={true}
                  />
                  <ArrowDown2
                    size={16}
                    className="text-neutral-gray -ml-6 pointer-events-none"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <CustomSelectWithOverlay
                    options={yearOptions}
                    value={incomeSourceSelectedYear}
                    onChange={(option: { text: string; value: string }) =>
                      setIncomeSourceSelectedYear(option.value)
                    }
                    parentContainer="w-20"
                    className="text-sm border-neutral-gray text-neutral-gray"
                    showOverlay={true}
                  />
                  <ArrowDown2
                    size={16}
                    className="text-neutral-gray -ml-6 pointer-events-none"
                  />
                </div>
              </div>
            </div>

            {tableLoadingState.incomeSourceLoading ? (
              <div className="flex items-center justify-center h-full bg-white mt-2">
                <div className="animate-pulse flex items-center justify-center text-gray-500 h-40">
                  Loading data...
                </div>
              </div>
            ) : (
              <>
                {top10IncomeSources?.length > 0 ? (
                  <div className=" bg-white my-2 px-4 py-8 h-full">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-purple-light border border-[#DCDEE64D] px-4 py-3 rounded-tl-lg rounded-bl-lg font-medium">
                        Categories
                      </div>
                      <div className="bg-purple-light border border-[#DCDEE64D] px-4 py-3 rounded-tr-lg rounded-br-lg font-medium">
                        Total
                      </div>
                      {top10IncomeSources.map((income, index) => (
                        <React.Fragment key={index}>
                          <div className="border border-[#DCDEE64D] px-4 py-3 rounded">
                            {income.name}
                          </div>
                          <div className="border border-[#DCDEE64D] px-4 py-3 flex items-center rounded">
                            <span className="bg-purple-light text-purple-normal p-1 rounded mr-2">
                              NGN
                            </span>
                            {income.total}
                          </div>
                        </React.Fragment>
                      ))}

                    </div>
                  </div>
                ) : (
                  <EmptyState title="Top income source" />
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Report;

const EmptyState = ({ title }) => {
  return (
    <div className=" bg-white mt-2 px-4 py-8 h-">
      <div className="flex flex-col items-center justify-center mt-8">
        <span className="text-gray-400 text-2xl mb-2">
          <SearchCategoryIcon />

        </span>
        <span className="text-sm text-gray-500">Nothing to see yet</span>
        <span className="text-xs text-gray-400">
          No records for {title}
        </span>
      </div>
    </div>
  )
}
