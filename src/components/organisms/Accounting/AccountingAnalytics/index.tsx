import React, { useEffect, useState } from "react";
import StatisticsCard from "../../../atoms/Cards/StatisticsCard";
import DateRangePicker from "../../../atoms/DatePicker/DateRangePicker";
import { BiCaretDown } from "react-icons/bi";
import moment from "moment";
import { Doughnut } from "react-chartjs-2";
import { ChartOptions } from "chart.js";

import ExpenseData from "./ExpenseData";
import IncomeData from "./IncomeData";
import { MdTrendingDown, MdTrendingUp } from "react-icons/md";
import { GiTakeMyMoney } from "react-icons/gi";
import { defaults } from "chart.js";
import {
  getHighestExpenseBranch,
  getHighestIncomeBranch,
  getIncomeExpenseSummary,
} from "../../../../api/accountingAnalytics";
import { useRecoilState, useRecoilValue } from "recoil";
import {
  highestExpenseBranchAtom,
  highestIncomeBranchAtom,
  incomeExpenseSummaryAtom,
} from "../../../../recoil/atom/accountAnalytics";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import { loggedUserAtom } from "../../../../recoil/atom/authAtom";
import ExpenseChartTrend from "./ExpenseChartTrend";
import IncomeChartTrend from "./IncomeChartTrend";
import { AiOutlineStop } from "react-icons/ai";
import Loader from "../../../atoms/Loader";
defaults.font.family = "Cabin";


const AccountingAnalytics = () => {
  const [isFetching, setIsFetching] = useState(false);
  const [isPickerOpen, setIsPickerOpen] = useState({
    selectedRange: false,
    expenseTrend: false,
    expenseHighest: false,
    incomeTrend: false,
    incomeHighest: false,
  });

  const [expenseHighestSelectedRange, setExpenseHighestSelectedRange] =
    useState<{
      startDate: moment.Moment | null;
      endDate: moment.Moment | null;
    }>({ startDate: null, endDate: null });

  const [incomeHighestSelectedRange, setIncomeHighestSelectedRange] = useState<{
    startDate: moment.Moment | null;
    endDate: moment.Moment | null;
  }>({ startDate: null, endDate: null });

  const [, setIncomeExpenseSummary] = useRecoilState(incomeExpenseSummaryAtom);
  const [, setHighestBranchExpense] = useRecoilState(highestExpenseBranchAtom);
  const [, setHighestBranchIncome] = useRecoilState(highestIncomeBranchAtom);

  const getLoggedUser = useRecoilValue(loggedUserAtom);
  const defaultCurrency = getLoggedUser.businesses[0].default_currency;
  const highestExpenseBranchValue = useRecoilValue(highestExpenseBranchAtom);
  const highestIncomeBranchValue = useRecoilValue(highestIncomeBranchAtom);
  const incomeExpenseSummaryValue = useRecoilValue(incomeExpenseSummaryAtom);

  const fetchExpenseIncomeSummary = () => {
    setIsFetching(true);
    getIncomeExpenseSummary().then((res) => {
      if (res.success) {
        setIncomeExpenseSummary(res.data);
      };
      setIsFetching(false);
    });
  };

  const fetchHighestExpenseByBranch = () => {
    const startDate =
      expenseHighestSelectedRange.startDate &&
      moment(expenseHighestSelectedRange.startDate).format(
        "YYYY-MM-DD hh:mm:ss"
      );
    const endDate =
      expenseHighestSelectedRange.endDate &&
      moment(expenseHighestSelectedRange.endDate).format("YYYY-MM-DD hh:mm:ss");
    getHighestExpenseBranch({ from: startDate || "", to: endDate || "" }).then(
      (res) => {
        if (res.success) {
          setHighestBranchExpense(res.data);
        }
      }
    );
  };

  const fetchHighestIncomeByBranch = () => {
    const startDate =
      incomeHighestSelectedRange.startDate &&
      moment(incomeHighestSelectedRange.startDate).format(
        "YYYY-MM-DD hh:mm:ss"
      );
    const endDate =
      incomeHighestSelectedRange.endDate &&
      moment(incomeHighestSelectedRange.endDate).format("YYYY-MM-DD hh:mm:ss");
    getHighestIncomeBranch({ from: startDate || "", to: endDate || "" }).then(
      (res) => {
        if (res.success) {
          setHighestBranchIncome(res.data);
        }
      }
    );
  };

  const accountingStats = [
    {
      title: "Total income",
      value: incomeExpenseSummaryValue?.total_income?.toLocaleString() || 0,
      icon: <MdTrendingDown className="text-[#fff]" size={24} />,
      iconBackgroundColor: '#3730A399',
      currency: defaultCurrency,
      cardBackgroundColor: "#EEF2FFCC",
    },
    {
      title: "Total expense",
      value: incomeExpenseSummaryValue?.total_expense?.toLocaleString() || 0,
      icon: <MdTrendingUp className="text-[#fff]" size={24} />,
      iconBackgroundColor: '#B3AA0199',
      currency: defaultCurrency,
      cardBackgroundColor: "#FDFFE8CC",
    },
    {
      title: "Current balance",
      value: incomeExpenseSummaryValue?.balance?.toLocaleString() || 0,
      icon: <GiTakeMyMoney size={24} />,
      iconBackgroundColor: '#B3AA0199',
      currency: defaultCurrency,
      cardBackgroundColor: "#EBFDFFCC",
    },
  ];

  const expenseBranchChartLabel = highestExpenseBranchValue?.map(
    (value) => value.branch_name
  );
  const expenseBranchChartAmount = highestExpenseBranchValue?.map(
    (value) => value.total
  );
  const incomeBranchChartLabel = highestIncomeBranchValue?.map(
    (value) => value.branch_name
  );
  const incomeBranchChartAmount = highestIncomeBranchValue?.map(
    (value) => value.total
  );

  const expenseDoughnutData = {
    labels: expenseBranchChartLabel,
    datasets: [
      {
        label: "",
        data: expenseBranchChartAmount,
        backgroundColor: ["#4338CA", "#3730A3", "#331B43"],
        borderColor: ["#4338CA", "#3730A3", "#331B43"],
        borderWidth: 1,
      },
    ],
  };

  const incomeDoughnutData = {
    labels: incomeBranchChartLabel,
    datasets: [
      {
        label: "",
        data: incomeBranchChartAmount,
        backgroundColor: ["#4338CA", "#3730A3", "#331B43"],
        borderColor: ["#4338CA", "#3730A3", "#331B43"],
        borderWidth: 1,
      },
    ],
  };

  const options: ChartOptions<"doughnut"> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: "bottom",
      },
      tooltip: {
        enabled: true,
      },
    },
    // cutout: "70%",
    // offset: 14,
  };

  useEffect(() => {
    fetchExpenseIncomeSummary();
  }, []);

  useEffect(() => {
    fetchHighestExpenseByBranch();
  }, [expenseHighestSelectedRange]);

  useEffect(() => {
    fetchHighestIncomeByBranch();
  }, [incomeHighestSelectedRange]);


  if (isFetching) {
    return <div>
      <Loader />
    </div>
  };

  return (
    <div>
      <div className="flex justify-end"></div>
      <div className="grid grid-cols-3 gap-5 ">
        {accountingStats?.map((item, index) => (
          <div key={index}>
            <StatisticsCard
              backgroundColor={item?.cardBackgroundColor}
              key={index}
              title={item.title}
              value={item.value}
              icon={item.icon}
              iconBackgroundColor={item?.iconBackgroundColor}
              currency={item.currency}
              valueText=""
            />
          </div>
        ))}
      </div>

      <div className="mt-10">
        <div>
          <div className="grid grid-cols-5 gap-10">
            <div className="bg-white col-span-3 rounded-[18px] p-8 bg-opacity-55">
              <ExpenseChartTrend />
            </div>
            <div className="bg-white col-span-2 rounded-[18px] p-8 relative  bg-opacity-55">
              {highestExpenseBranchValue?.length > 0 || expenseHighestSelectedRange?.startDate ? (

                <div>
                  <div className="flex gap-4 justify-between">
                    <p className="text-14 font-poppins-medium text-purple-dark">
                      Highest expense by branch
                    </p>
                    <div>
                      <button
                        className="border-2 text-xs flex text-neutral-normal px-4 py-2 rounded-md"
                        onClick={() =>
                          setIsPickerOpen({ ...isPickerOpen, expenseHighest: true })
                        }
                      >
                        {expenseHighestSelectedRange.startDate
                          ? `${moment(
                            expenseHighestSelectedRange?.startDate
                          ).format("DD/MM/YYYY")} - ${moment(
                            expenseHighestSelectedRange?.endDate
                          ).format("DD/MM/YYYY")} `
                          : "Select date"}{" "}
                        <BiCaretDown className="mt-1 ml-2" />
                      </button>

                      <div>
                        <DateRangePicker
                          isOpen={isPickerOpen.expenseHighest}
                          onClose={() =>
                            setIsPickerOpen({
                              ...isPickerOpen,
                              expenseHighest: false,
                            })
                          }
                          onApply={(startDate, endDate) =>
                            setExpenseHighestSelectedRange({ startDate, endDate })
                          }
                          handleClear={() =>
                            setExpenseHighestSelectedRange({
                              startDate: null,
                              endDate: null,
                            })
                          }
                        />
                        <div />
                      </div>
                    </div>
                  </div>
                  <div>
                    <div
                      className="mt-14  item-center  bg-opacity-55"
                      style={{
                        position: "relative",
                        height: "40vh",
                        width: "100%",
                      }}
                    >
                      {highestExpenseBranchValue?.length > 0 ? (
                        <Doughnut
                          className=" mt-8"
                          data={expenseDoughnutData}
                          options={options}
                        />
                      ) : (
                        <div className=" mt-40 text-neutral-normal">
                          <div>
                            <div className="flex justify-center items-center">

                              <AiOutlineStop />
                            </div>
                            <p className="flex justify-center items-center">No record found</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex justify-center items-center">
                  <OrganizationEmptyState
                    className="!bg-transparent"
                    text="No highest expense branch chart record to show."
                  />
                </div>
              )}
            </div>
          </div>
          <div className="mt-8 ">
            <ExpenseData />
          </div>
        </div>

        <div className="mt-10">
          <div className="grid grid-cols-5  gap-10">
            <div className="bg-white col-span-3 rounded-[18px] p-8 relative  bg-opacity-55">
              <IncomeChartTrend />
            </div>
            <div className="bg-white col-span-2 rounded-[18px] p-8 relative  bg-opacity-55">
              {highestIncomeBranchValue?.length > 0 ||
                incomeHighestSelectedRange?.startDate ? (
                <div>
                  <div className="flex gap-4 justify-between">
                    <p className="text-14 font-poppins-medium text-purple-dark">
                      Highest revenue by branch
                    </p>
                    <div>
                      <button
                        className="border-2 text-xs flex text-neutral-normal px-4 py-2 rounded-md"
                        onClick={() =>
                          setIsPickerOpen({
                            ...isPickerOpen,
                            incomeHighest: true,
                          })
                        }
                      >
                        {incomeHighestSelectedRange.startDate
                          ? `${moment(
                            incomeHighestSelectedRange?.startDate
                          ).format("DD/MM/YYYY")} - ${moment(
                            incomeHighestSelectedRange?.endDate
                          ).format("DD/MM/YYYY")} `
                          : "Select date"}{" "}
                        <BiCaretDown className="mt-1 ml-2" />
                      </button>

                      <div>
                        <DateRangePicker
                          isOpen={isPickerOpen.incomeHighest}
                          onClose={() =>
                            setIsPickerOpen({
                              ...isPickerOpen,
                              incomeHighest: false,
                            })
                          }
                          onApply={(startDate, endDate) =>
                            setIncomeHighestSelectedRange({
                              startDate,
                              endDate,
                            })
                          }
                          handleClear={() =>
                            setIncomeHighestSelectedRange({
                              startDate: null,
                              endDate: null,
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    className=""
                    style={{
                      position: "relative",
                      height: "40vh",
                      width: "100%",
                    }}
                  >
                    {highestIncomeBranchValue?.length > 0 ? (
                      <Doughnut
                        className=" mt-8"
                        data={incomeDoughnutData}
                        options={options}
                      />
                    ) : (
                      <div className=" mt-40 text-neutral-normal">
                        <div>
                          <div className="flex justify-center items-center">

                            <AiOutlineStop />
                          </div>
                          <p className="flex justify-center items-center">No record found</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex justify-center items-center">
                  <OrganizationEmptyState
                    className="!bg-transparent"
                    text="No highest income branch chart record to show."
                  />
                </div>
              )}
            </div>
          </div>
          <div className="mt-8 ">
            <IncomeData />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountingAnalytics;
