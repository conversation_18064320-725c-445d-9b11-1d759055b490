import React, { useEffect, useState } from "react";
import { PiTrendUpDuotone } from "react-icons/pi";
import TrendAnalyticCard from "../../../atoms/Cards/TrendAnalyticCard";
import {
  FaList,
  FaUser,
  FaArrowTurnDown,
  FaDollarSign,
  FaBriefcase,
} from "react-icons/fa6";
import { BiCaretDown } from "react-icons/bi";
import moment from "moment";
import DateRangePicker from "../../../atoms/DatePicker/DateRangePicker";
import { useRecoilState, useRecoilValue } from "recoil";
import { revenueSummaryAtom, top10IncomeCustomersAtom, top10IncomeSourceAtom } from "../../../../recoil/atom/accountAnalytics";
import { getRevenueSUmmary, getTop10IncomeCustomers, getTop10IncomeSources } from "../../../../api/accountingAnalytics";
import { loggedUser<PERSON>tom } from "../../../../recoil/atom/authAtom";
import CurrencyTag from "../../../atoms/Ui/CurrencyTag";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";

const IncomeData = () => {
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const [isTablePickerOpen, setIsTablePickerOpen] = useState({
    incomeSource: false,
    incomeCustomer: false,
  });
  const [, setIncomeSummary] = useRecoilState(revenueSummaryAtom);
  const [, setTop10IncomeCustomers] = useRecoilState(top10IncomeCustomersAtom);
  const [, setTop10IncomeSources] = useRecoilState(top10IncomeSourceAtom);
    const incomeSummaryValue = useRecoilValue(revenueSummaryAtom);
    const top10IncomeCustomers = useRecoilValue(top10IncomeCustomersAtom);
    const top10IncomeSources = useRecoilValue(top10IncomeSourceAtom);
    const getLoggedUser = useRecoilValue(loggedUserAtom);
    const defaultCurrency = getLoggedUser.businesses[0].default_currency;

  const [selectedRange, setSelectedRange] = useState<{
    startDate: moment.Moment | null;
    endDate: moment.Moment | null;
  }>({ startDate: null, endDate: null });

  const [sourceSelectedRange, setSourceSelectedRange] = useState<{
    startDate: moment.Moment | null;
    endDate: moment.Moment | null;
  }>({ startDate: null, endDate: null });

  const [customerSelectedRange, setCustomerSelectedRange] = useState<{
    startDate: moment.Moment | null;
    endDate: moment.Moment | null;
  }>({ startDate: null, endDate: null });

  const fetchIncomeSummary = () => {
    const startDate = selectedRange.startDate && moment(selectedRange.startDate).format("YYYY-MM-DD hh:mm:ss");
    const endDate = selectedRange.endDate && moment(selectedRange.endDate).format("YYYY-MM-DD hh:mm:ss");
    getRevenueSUmmary({from: startDate || "", to: endDate || ""}).then((res) => {
      if(res.success){
        setIncomeSummary(res.data)
      }
    });
  };

  const fetchTop10IncomeCustomers = () => {
    const startDate = customerSelectedRange.startDate && moment(customerSelectedRange.startDate).format("YYYY-MM-DD hh:mm:ss");
    const endDate = customerSelectedRange.endDate && moment(customerSelectedRange.endDate).format("YYYY-MM-DD hh:mm:ss");
    getTop10IncomeCustomers({from: startDate || "", to: endDate || ""}).then((res) => {
      if(res.success){
        setTop10IncomeCustomers(res.data)
      }
    })
  };

  const fetchTop10IncomeSources = () => {
    const startDate = sourceSelectedRange.startDate && moment(sourceSelectedRange.startDate).format("YYYY-MM-DD hh:mm:ss");
    const endDate = sourceSelectedRange.endDate && moment(sourceSelectedRange.endDate).format("YYYY-MM-DD hh:mm:ss");
    getTop10IncomeSources({from: startDate || "", to: endDate || ""}).then((res) => {
      if(res.success){
        setTop10IncomeSources(res.data)
      }
    })
  };

  const incomeStats = [
    {
      title: "Highest earnings",
      value: incomeSummaryValue?.highestIncome ? parseInt(incomeSummaryValue?.highestIncome[0]?.total || 0)?.toLocaleString() : 0 ,
      currency: defaultCurrency,
      icon: (
        <PiTrendUpDuotone
          className="text-purple-dark bg-white font-bold p-2 rounded-full"
          size={30}
        />
      ),
      color: "bg-accent-blue-light",
    },

    {
      title: "Top performing vendor",
      value: incomeSummaryValue?.highestIncomeCustomer ?  parseInt(incomeSummaryValue?.highestIncomeCustomer[0]?.total || 0)?.toLocaleString() : 0,
      currency: defaultCurrency,
      icon: (
        <FaUser
          className="text-purple-dark bg-white font-bold p-2 rounded-full"
          size={30}
        />
      ),
      color: "bg-accent-green-light",
      name: incomeSummaryValue?.highestIncomeCustomer && incomeSummaryValue?.highestIncomeCustomer[0]?.name ? `(${incomeSummaryValue?.highestIncomeCustomer[0]?.name})` : "" 
    },
    {
      title: "Highest earning category",
      value: incomeSummaryValue?.highestIncomeSource ?  parseInt(incomeSummaryValue?.highestIncomeSource[0]?.total || 0)?.toLocaleString() : 0 ,
      currency: defaultCurrency,
      icon: (
        <FaDollarSign
          className="text-purple-dark bg-white font-bold p-2 rounded-full"
          size={30}
        />
      ),
      color: "bg-accent-orange-light",
      name: incomeSummaryValue?.highestIncomeSource && incomeSummaryValue?.highestIncomeSource[0]?.name ? `(${incomeSummaryValue?.highestIncomeSource[0]?.name})` : "" 

    },
    {
      title: "Lowest earnings",
      value: incomeSummaryValue?.lowestIncome ? parseInt(incomeSummaryValue?.lowestIncome[0]?.total || 0)?.toLocaleString() : 0,
      currency: defaultCurrency,
      icon: (
        <FaArrowTurnDown
          className="text-purple-dark bg-white font-bold p-2 rounded-full"
          size={30}
        />
      ),
      color: "bg-accent-purple-light",
    },
    {
      title: "Lowest earning category",
      value: incomeSummaryValue?.lowestIncomeSource ? parseInt(incomeSummaryValue?.lowestIncomeSource[0]?.total || 0)?.toLocaleString() : 0,
      currency: defaultCurrency,
      icon: (
        <FaList
          className="text-purple-dark bg-white font-bold p-2 rounded-full"
          size={30}
        />
      ),
      color: "bg-accent-blue-light",
      name: incomeSummaryValue?.lowestIncomeSource && incomeSummaryValue?.lowestIncomeSource[0]?.name ? `(${incomeSummaryValue?.lowestIncomeSource[0]?.name})` : "" 
    },
   
    {
      title: "Least profitable vendor",
      value: incomeSummaryValue?.lowestIncomeCustomer ? parseInt(incomeSummaryValue?.lowestIncomeCustomer[0]?.total || 0)?.toLocaleString() : 0,
      currency: defaultCurrency,
      icon: (
        <FaBriefcase
          className="text-purple-dark bg-white font-bold p-2 rounded-full"
          size={30}
        />
      ),
      color: "bg-accent-purple-light",
      name: incomeSummaryValue?.lowestIncomeCustomer && incomeSummaryValue?.lowestIncomeCustomer[0]?.name ? `(${incomeSummaryValue?.lowestIncomeCustomer[0]?.name})` : "" 

    },
  ];

  const customersColumns = [
    {
      Header: "Customer name",
      accessor: "name",
      Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
    },

    {
      Header: "Total",
      accessor: "total",
      Cell: (row: any) => (
        <p>
        {row.cell.row.original.currency ||
          (defaultCurrency && (
            <CurrencyTag
              currency={row.cell.row.original.currency || defaultCurrency}
            />
          ))}
          {`${parseInt(row.cell?.value).toLocaleString() || 0}` || "--"}
        </p>
      ),
    },
  ];

  const sourcesColumns = [
    {
      Header: "Income source",
      accessor: "name",
      Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
    },

    {
      Header: "Total",
      accessor: "total",
      Cell: (row: any) => (
        <p>
        {row.cell.row.original.currency ||
          (defaultCurrency && (
            <CurrencyTag
              currency={row.cell.row.original.currency || defaultCurrency}
            />
          ))}
          {`${parseInt(row.cell?.value).toLocaleString() || 0}` || "--"}
        </p>
      ),
    },
  ];

  useEffect(() => {
    fetchIncomeSummary();
  }, [selectedRange]);

  useEffect(() => {
    fetchTop10IncomeCustomers();
  },[customerSelectedRange]);
  
  useEffect(() => {fetchTop10IncomeSources()},[sourceSelectedRange])

  return (
    <div>
      <div className="mt-10 bg-white p-6 rounded-[18px] bg-opacity-55">
        <div className="flex justify-between">
          <p className="text-15 font-poppins-medium text-purple-dark mb-5">
            Revenue data
          </p>
          <div>
            <button
              className="border-2 text-xs flex text-neutral-normal px-4 py-2 rounded-md"
              onClick={() => setIsPickerOpen(true)}
            >
              {selectedRange.startDate
                ? `${moment(selectedRange?.startDate).format(
                    "DD/MM/YYYY"
                  )} - ${moment(selectedRange?.endDate).format("DD/MM/YYYY")}`
                : "Select date"}
              <BiCaretDown className="mt-1 ml-2" />
            </button>

            <div>
              <DateRangePicker
                isOpen={isPickerOpen}
                onClose={() => setIsPickerOpen(false)}
                onApply={(startDate, endDate) =>
                  setSelectedRange({ startDate, endDate })
                }
                handleClear={() =>
                  setSelectedRange({
                    startDate: null,
                    endDate: null,
                  })
                }
              />
            </div>
          </div>
        </div>
        <div className="grid grid-cols-3 gap-x-4 gap-y-8 mt-7">
          {incomeStats.map((item, index) => (
            <div key={index}>
              <TrendAnalyticCard
                key={index}
                title={item.title}
                value={item.value}
                currency={item.currency}
                icon={item.icon}
                color={item.color}
                name={item.name}
              />
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-8 mt-10 rounded-[18px]">
        <div className="h-[22rem] overflow-y-scroll show-scrollbar bg-white rounded-[18px]">
          <div className="relative">
            {(top10IncomeCustomers?.length > 0 || customerSelectedRange?.startDate) && (
              <div className="absolute right-2 top-3">
                <button
                  className="border-2 text-xs flex text-neutral-normal px-4 py-2 rounded-md"
                  onClick={() => setIsTablePickerOpen({...isTablePickerOpen, incomeCustomer: true})}
                >
                  {customerSelectedRange.startDate
                    ? `${moment(customerSelectedRange?.startDate).format(
                        "DD/MM/YYYY"
                      )} - ${moment(customerSelectedRange?.endDate).format(
                        "DD/MM/YYYY"
                      )} `
                    : "Select date"}
                  <BiCaretDown className="mt-1 ml-2" />
                </button>

                <div>
                  <DateRangePicker
                    isOpen={isTablePickerOpen.incomeCustomer}
                    onClose={() => setIsTablePickerOpen({...isTablePickerOpen, incomeCustomer: false})}
                    onApply={(startDate, endDate) =>
                      setCustomerSelectedRange({ startDate, endDate })
                    }
                    handleClear={() =>
                      setCustomerSelectedRange({
                        startDate: null,
                        endDate: null,
                      })
                    }
                  />
                </div>
              </div>
            )}
            {top10IncomeCustomers?.length > 0 || customerSelectedRange?.startDate ? (
              <CustomTable
                data={top10IncomeCustomers || []}
                columns={customersColumns}
                header={
                  <div>
                    <h1 className="font-poppins-medium text-purple-dark-active mb-4">
                      Top ten income customers
                    </h1>
                  </div>
                }
                hideSearch
              />
            ) : (
              <div className="flex justify-center items-center">
                <OrganizationEmptyState
                  className="!bg-transparent"
                  text="No record to show for top ten income customers"
                />
              </div>
            )}

          </div>
        </div>
        <div className="h-[22rem] overflow-y-scroll show-scrollbar bg-white rounded-[18px]">
          <div className="relative">
            {(top10IncomeSources?.length > 0 || sourceSelectedRange?.startDate) && (
              <div className="absolute right-2 top-3">
                <button
                  className="border-2 text-xs flex text-neutral-normal px-4 py-2 rounded-md"
                  onClick={() => setIsTablePickerOpen({...isTablePickerOpen, incomeSource: true})}
                >
                  {sourceSelectedRange.startDate
                    ? `${moment(sourceSelectedRange?.startDate).format(
                        "DD/MM/YYYY"
                      )} - ${moment(sourceSelectedRange?.endDate).format(
                        "DD/MM/YYYY"
                      )} `
                    : "Select date"}
                  <BiCaretDown className="mt-1 ml-2" />
                </button>

                <div>
                  <DateRangePicker
                    isOpen={isTablePickerOpen.incomeSource}
                    onClose={() => setIsTablePickerOpen({...isTablePickerOpen, incomeSource: false})}
                    onApply={(startDate, endDate) =>
                      setSourceSelectedRange({ startDate, endDate })
                    }
                    handleClear={() =>
                      setSourceSelectedRange({
                        startDate: null,
                        endDate: null,
                      })
                    }
                  />
                </div>
              </div>
            )}
            {top10IncomeSources?.length > 0 || sourceSelectedRange?.startDate ? (
              <CustomTable
                data={top10IncomeSources || []}
                columns={sourcesColumns}
                header={
                  <div>
                    <h1 className="font-poppins-medium text-purple-dark-active mb-4">
                      Top ten income sources
                    </h1>
                  </div>
                }
                hideSearch
              />
            ) : (
              <div className="flex justify-center items-center">
                <OrganizationEmptyState
                  className="!bg-transparent"
                  text="No record to show for top ten income sources"
                />
              </div>
            )}
          </div>
          
        </div>
      </div>
    </div>
  );
};

export default IncomeData;
