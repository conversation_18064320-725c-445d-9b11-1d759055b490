import React, { useEffect, useState } from "react";
import { PiTrendUpDuotone } from "react-icons/pi";
import TrendAnalyticCard from "../../../atoms/Cards/TrendAnalyticCard";
import {
  <PERSON>a<PERSON>ist,
  FaUser,
  FaArrowTurnDown,
  FaDollarSign,
  FaBriefcase,
} from "react-icons/fa6";
import moment from "moment";
import { BiCaretDown } from "react-icons/bi";
import DateRangePicker from "../../../atoms/DatePicker/DateRangePicker";
import {
  getDisbursementSummary,
  getTop10ExpenseCategory,
  getTop10ExpenseVendor,
} from "../../../../api/accountingAnalytics";
import { useRecoilState, useRecoilValue } from "recoil";
import {
  disbursementSummaryAtom,
  top10ExpenseCategoryAtom,
  top10ExpenseVendorAtom,
} from "../../../../recoil/atom/accountAnalytics";
import { loggedUserAtom } from "../../../../recoil/atom/authAtom";
import CurrencyTag from "../../../atoms/Ui/CurrencyTag";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";

const ExpenseData = () => {
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const [isTablePickerOpen, setIsTablePickerOpen] = useState({
    expenseCategory: false,
    expenseVendor: false,
  });
  const [, setExpenseSummary] = useRecoilState(disbursementSummaryAtom);
  const [, setTop10ExpenseCategory] = useRecoilState(top10ExpenseCategoryAtom);
  const [, setTop10ExpenseVendor] = useRecoilState(top10ExpenseVendorAtom);
  const top10ExpenseCategory = useRecoilValue(top10ExpenseCategoryAtom);
  const top10ExpenseVendor = useRecoilValue(top10ExpenseVendorAtom);
  const expenseSummaryValue = useRecoilValue(disbursementSummaryAtom);
  const getLoggedUser = useRecoilValue(loggedUserAtom);
  const defaultCurrency = getLoggedUser.businesses[0].default_currency;

  const [selectedRange, setSelectedRange] = useState<{
    startDate: moment.Moment | null;
    endDate: moment.Moment | null;
  }>({ startDate: null, endDate: null });

  const [categorySelectedRange, setCategorySelectedRange] = useState<{
    startDate: moment.Moment | null;
    endDate: moment.Moment | null;
  }>({ startDate: null, endDate: null });

  const [vendorSelectedRange, setVendorSelectedRange] = useState<{
    startDate: moment.Moment | null;
    endDate: moment.Moment | null;
  }>({ startDate: null, endDate: null });

  const fetchExpenseSummary = () => {
    const startDate = selectedRange.startDate && moment(selectedRange.startDate).format("YYYY-MM-DD hh:mm:ss");
    const endDate = selectedRange.endDate && moment(selectedRange.endDate).format("YYYY-MM-DD hh:mm:ss");
    getDisbursementSummary({ from: startDate || "", to: endDate || "" }).then(
      (res) => {
        if (res.success) {
          setExpenseSummary(res.data);
        }
      }
    );
  };

  const fetchTop10ExpenseVendor = () => {
    const startDate = vendorSelectedRange.startDate && moment(vendorSelectedRange.startDate).format("YYYY-MM-DD hh:mm:ss");
    const endDate = vendorSelectedRange.endDate && moment(vendorSelectedRange.endDate).format("YYYY-MM-DD hh:mm:ss");
    getTop10ExpenseVendor({ from: startDate || "", to: endDate || "" }).then((res) => {
      if (res.success) {
        setTop10ExpenseVendor(res.data);
      }
    });
  };

  const fetchTop10ExpenseCategory = () => {
    const startDate = categorySelectedRange.startDate && moment(categorySelectedRange.startDate).format("YYYY-MM-DD hh:mm:ss");
    const endDate = categorySelectedRange.endDate && moment(categorySelectedRange.endDate).format("YYYY-MM-DD hh:mm:ss");
    getTop10ExpenseCategory({ from: startDate || "", to: endDate || "" }).then((res) => {
      if (res.success) {
        setTop10ExpenseCategory(res.data);
      }
    });
  };

  const expenseStats = [
    {
      title: "Highest cost",
      value: expenseSummaryValue?.highestExpense
        ? parseInt(
            expenseSummaryValue?.highestExpense[0]?.total || 0
          )?.toLocaleString()
        : 0,
      currency: defaultCurrency,
      icon: (
        <PiTrendUpDuotone
          className="text-purple-dark bg-white font-bold p-2 rounded-full"
          size={30}
        />
      ),
      color: "bg-accent-purple-light",
    },

    {
      title: "Most expensive vendor",
      value: expenseSummaryValue?.highestVendorExpense
        ? parseInt(
            expenseSummaryValue?.highestVendorExpense[0]?.total || 0
          )?.toLocaleString()
        : 0,
      currency: defaultCurrency,
      icon: (
        <FaUser
          className="text-purple-dark bg-white font-bold p-2 rounded-full"
          size={30}
        />
      ),
      color: "bg-accent-orange-light",
      name:expenseSummaryValue?.highestVendorExpense && expenseSummaryValue?.highestVendorExpense[0]?.name 
      ? `(${expenseSummaryValue?.highestVendorExpense[0]?.name})` 
      : "",
    },
    {
      title: "Most expensive category",
      value: expenseSummaryValue?.highestExpenseCategory
        ? parseInt(
            expenseSummaryValue?.highestExpenseCategory[0]?.total || 0
          )?.toLocaleString()
        : 0,
      currency: defaultCurrency,
      icon: (
        <FaDollarSign
          className="text-purple-dark bg-white font-bold p-2 rounded-full"
          size={30}
        />
      ),
      color: "bg-accent-blue-light",
      name: expenseSummaryValue?.highestExpenseCategory && expenseSummaryValue?.highestExpenseCategory[0]?.name
        ? `(${expenseSummaryValue?.highestExpenseCategory[0]?.name})`
        : "",
    },
    {
      title: "Least expensive category",
      value: expenseSummaryValue?.lowestExpenseCategory
        ? parseInt(
            expenseSummaryValue?.lowestExpenseCategory[0]?.total || 0
          )?.toLocaleString()
        : 0,
      currency: defaultCurrency,
      icon: (
        <FaList
          className="text-purple-dark bg-white font-bold p-2 rounded-full"
          size={30}
        />
      ),
      color: "bg-accent-blue-light",
      name: expenseSummaryValue?.lowestExpenseCategory && expenseSummaryValue?.lowestExpenseCategory[0]?.name
        ? `(${expenseSummaryValue?.lowestExpenseCategory[0]?.name})`
        : "",
    },
    {
      title: "Lowest cost",
      value: expenseSummaryValue?.lowestExpense
        ? parseInt(
            expenseSummaryValue?.lowestExpense[0]?.total || 0
          )?.toLocaleString()
        : 0,
      currency: defaultCurrency,
      icon: (
        <FaArrowTurnDown
          className="text-purple-dark bg-white font-bold p-2 rounded-full"
          size={30}
        />
      ),
      color: "bg-accent-green-light",
    },
    {
      title: "Least expensive vendor",
      value: expenseSummaryValue?.lowestExpenseVendor
        ? parseInt(
            expenseSummaryValue?.lowestExpenseVendor[0]?.total || 0
          )?.toLocaleString()
        : 0,
      currency: defaultCurrency,
      icon: (
        <FaBriefcase
          className="text-purple-dark bg-white font-bold p-2 rounded-full"
          size={30}
        />
      ),
      color: "bg-accent-purple-light",
      name: expenseSummaryValue?.lowestExpenseVendor && expenseSummaryValue?.lowestExpenseVendor[0]?.name
        ? `(${expenseSummaryValue?.lowestExpenseVendor[0]?.name})`
        : "",
    },
  ];

  const vendorColumns = [
    {
      Header: "Vendor",
      accessor: "name",
      Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
    },

    {
      Header: "Total",
      accessor: "total",
      Cell: (row: any) => (
        <p>
          {row.cell.row.original.currency ||
            (defaultCurrency && (
              <CurrencyTag
                currency={row.cell.row.original.currency || defaultCurrency}
              />
            ))}
          {`${parseInt(row.cell?.value).toLocaleString() || 0}` || "--"}
        </p>
      ),
    },
  ];

  const categoryColumns = [
    {
      Header: "Expense Category",
      accessor: "name",
      Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
    },

    {
      Header: "Total",
      accessor: "total",
      Cell: (row: any) => (
        <p>
          {row.cell.row.original.currency ||
            (defaultCurrency && (
              <CurrencyTag
                currency={row.cell.row.original.currency || defaultCurrency}
              />
            ))}
          {`${parseInt(row.cell?.value).toLocaleString() || 0}` || "--"}
        </p>
      ),
    },
  ];


  useEffect(() => {
    fetchExpenseSummary();
  }, [selectedRange]);

  useEffect(() => {
    fetchTop10ExpenseVendor();
  }, [vendorSelectedRange]);

  useEffect(() => {fetchTop10ExpenseCategory();},[categorySelectedRange])

  return (
    <div >
      <div>
        <div className="flex justify-between">
          <p className="text-15 font-poppins-medium text-purple-dark">
            Disbursement data
          </p>
          <div>
            <button
              className="border-2 text-xs flex text-neutral-normal px-4 py-2 rounded-md"
              onClick={() => setIsPickerOpen(true)}
            >
              {selectedRange.startDate
                ? `${moment(selectedRange?.startDate).format(
                    "DD/MM/YYYY"
                  )} - ${moment(selectedRange?.endDate).format("DD/MM/YYYY")} `
                : "Select date"}
              <BiCaretDown className="mt-1 ml-2" />
            </button>

            <div>
              <DateRangePicker
                isOpen={isPickerOpen}
                onClose={() => setIsPickerOpen(false)}
                onApply={(startDate, endDate) =>
                  setSelectedRange({ startDate, endDate })
                }
                handleClear={() =>
                  setSelectedRange({
                    startDate: null,
                    endDate: null,
                  })
                }
              />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-x-4 gap-y-8 mt-7">
          {expenseStats.map((item, index) => (
            <div key={index}>
              <TrendAnalyticCard
                key={index}
                title={item.title}
                value={item.value}
                currency={item.currency}
                icon={item.icon}
                color={item.color}
                name={item.name}
              />
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-8 mt-10">
        <div className="h-[22rem] overflow-y-scroll show-scrollbar bg-white rounded-[18px] bg-opacity-45">
          <div className="relative">
            {(top10ExpenseVendor?.length > 0 || vendorSelectedRange?.startDate) && (
              <div className="absolute right-2 top-3">
                <button
                  className="border-2 text-xs flex text-neutral-normal px-4 py-2 rounded-md"
                  onClick={() => setIsTablePickerOpen({...isTablePickerOpen, expenseVendor: true})}
                >
                  {vendorSelectedRange.startDate
                    ? `${moment(vendorSelectedRange?.startDate).format(
                        "DD/MM/YYYY"
                      )} - ${moment(vendorSelectedRange?.endDate).format(
                        "DD/MM/YYYY"
                      )} `
                    : "Select date"}
                  <BiCaretDown className="mt-1 ml-2" />
                </button>

                <div>
                  <DateRangePicker
                    isOpen={isTablePickerOpen.expenseVendor}
                    onClose={() => setIsTablePickerOpen({...isTablePickerOpen, expenseVendor: false})}
                    onApply={(startDate, endDate) =>
                      setVendorSelectedRange({ startDate, endDate })
                    }
                    handleClear={() =>
                      setVendorSelectedRange({
                        startDate: null,
                        endDate: null,
                      })
                    }
                  />
                </div>
              </div>
            )}
            {top10ExpenseVendor?.length > 0 || vendorSelectedRange?.startDate ? (
              <div>
                <CustomTable
                  data={top10ExpenseVendor || []}
                  columns={vendorColumns}
                  header={
                    <div>
                      <h1 className="font-poppins-medium text-purple-dark-active mb-4">
                        Top ten expense vendors
                      </h1>
                    </div>
                  }
                  hideSearch
                />
              </div>
            ) : (
              <div className="flex justify-center items-center">
                <OrganizationEmptyState
                  className="!bg-transparent"
                  text="No record to show for top ten expense vendors"
                />
              </div>
            )}
          </div>
        </div>
        <div className="h-[22rem] overflow-y-scroll show-scrollbar bg-white rounded-[18px] bg-opacity-45">
          <div className="relative">
            {(top10ExpenseCategory?.length > 0 || categorySelectedRange?.startDate) && (
              <div className="absolute right-2 top-3">
                <button
                  className="border-2 text-xs flex text-neutral-normal px-4 py-2 rounded-md"
                  onClick={() => setIsTablePickerOpen({...isTablePickerOpen, expenseCategory: true})}
                >
                  {categorySelectedRange.startDate
                    ? `${moment(categorySelectedRange?.startDate).format(
                        "DD/MM/YYYY"
                      )} - ${moment(categorySelectedRange?.endDate).format(
                        "DD/MM/YYYY"
                      )} `
                    : "Select date"}
                  <BiCaretDown className="mt-1 ml-2" />
                </button>

                <div>
                  <DateRangePicker
                    isOpen={isTablePickerOpen.expenseCategory}
                    onClose={() => setIsTablePickerOpen({...isTablePickerOpen, expenseCategory: false})}
                    onApply={(startDate, endDate) =>
                      setCategorySelectedRange({ startDate, endDate })
                    }
                    handleClear={() =>
                      setCategorySelectedRange({
                        startDate: null,
                        endDate: null,
                      })
                    }
                  />
                </div>
              </div>
            )}
            {top10ExpenseCategory?.length > 0 || categorySelectedRange?.startDate ? (
              <CustomTable
                data={top10ExpenseCategory || []}
                columns={categoryColumns}
                header={
                  <div>
                    <h1 className="font-poppins-medium text-purple-dark-active mb-4">
                      Top ten expense categories
                    </h1>
                  </div>
                }
                hideSearch
              />
            ) : (
              <div className="flex justify-center items-center">
                <OrganizationEmptyState
                  className="!bg-transparent"
                  text="No record to show for top ten expense category"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpenseData;
