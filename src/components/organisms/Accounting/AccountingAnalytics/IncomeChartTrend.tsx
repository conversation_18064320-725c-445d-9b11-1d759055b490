import React, { useEffect, useState } from "react";
import { useRecoilState, useRecoilValue } from "recoil";
import { incomeChartTrendAtom } from "../../../../recoil/atom/accountAnalytics";
import moment from "moment";
import DateRangePicker from "../../../atoms/DatePicker/DateRangePicker";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import { ChartOptions } from "chart.js";
import { Bar } from "react-chartjs-2";
import { getIncomeChartTrend } from "../../../../api/accountingAnalytics";
import { BiCaretDown } from "react-icons/bi";
import { AiOutlineStop } from "react-icons/ai";

const IncomeChartTrend = () => {
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const [incomeTrendSelectedRange, setIncomeTrendSelectedRange] = useState<{
    startDate: moment.Moment | null;
    endDate: moment.Moment | null;
  }>({ startDate: null, endDate: null });
  const [, setIncomeChartTrend] = useRecoilState(incomeChartTrendAtom);
  const incomeChartTrendValue = useRecoilValue(incomeChartTrendAtom);

  const fetchIncomeTrendChart = () => {
    const startDate =
      incomeTrendSelectedRange.startDate &&
      moment(incomeTrendSelectedRange.startDate).format("YYYY-MM-DD hh:mm:ss");
    const endDate =
      incomeTrendSelectedRange.endDate &&
      moment(incomeTrendSelectedRange.endDate).format("YYYY-MM-DD hh:mm:ss");
    getIncomeChartTrend({ from: startDate || "", to: endDate || "" }).then(
      (res) => {
        if (res.success) {
          setIncomeChartTrend(res?.data?.income);
        }
      }
    );
  };

  const incomeChartLabel = incomeChartTrendValue?.map((value) =>
    moment(value.month).format("MMMM")
  );
  const incomeChartAmount = incomeChartTrendValue?.map((value) => value.total);
  const highestIncomeTrendTotal = Math.max(
    ...incomeChartTrendValue?.map((item) => Number(item.total))
  );

  const incomeTrendChartData = {
    labels: incomeChartLabel,
    datasets: [
      {
        label: "",
        data: incomeChartAmount,
        backgroundColor: ["#3730A3"],
      },
    ],
  };

  const incomeBarOptions: ChartOptions<"bar"> = {
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
      },
    },
    responsive: true,
    scales: {
      y: {
        beginAtZero: true,
        max: highestIncomeTrendTotal,
        stacked: true,
      },
      x: {
        stacked: true,
      },
    },
    maintainAspectRatio: false,
    animation: {
      duration: 0,
    },
  };

  useEffect(() => {
    fetchIncomeTrendChart();
  }, [incomeTrendSelectedRange]);

  return (
    <div>
      <div>
        {incomeChartTrendValue?.length > 0 || incomeTrendSelectedRange?.startDate ? (
          <>
            <div className="flex justify-between">
              <p className="text-15 font-poppins-medium text-purple-dark">
                Income trend
              </p>
              <div>
                <button
                  className="border-2 text-xs flex text-neutral-normal px-4 py-2 rounded-md"
                  onClick={() => setIsPickerOpen(true)}
                >
                  {incomeTrendSelectedRange.startDate
                    ? `${moment(incomeTrendSelectedRange?.startDate).format(
                        "DD/MM/YYYY"
                      )} - ${moment(incomeTrendSelectedRange?.endDate).format(
                        "DD/MM/YYYY"
                      )} `
                    : "Select date"}{" "}
                  <BiCaretDown className="mt-1 ml-2" />
                </button>

                <div>
                  <DateRangePicker
                    isOpen={isPickerOpen}
                    onClose={() => setIsPickerOpen(false)}
                    onApply={(startDate, endDate) =>
                      setIncomeTrendSelectedRange({ startDate, endDate })
                    }
                    handleClear={() =>
                      setIncomeTrendSelectedRange({
                        startDate: null,
                        endDate: null,
                      })
                    }
                  />
                </div>
              </div>
            </div>
            <div
              className="mt-14 col-span-2"
              style={{ position: "relative", height: "30vh", width: "100%" }}
            >
              {incomeChartTrendValue?.length > 0 ? (
                <Bar
                className=""
                data={incomeTrendChartData}
                options={incomeBarOptions}
              />
              ) : (
                <div className=" mt-40 text-neutral-normal">
                <div>
                  <div className="flex justify-center items-center">

                  <AiOutlineStop />
                  </div>
                  <p className="flex justify-center items-center">No record found</p>
                </div>
              </div>
              )}
              
            </div>
          </>
        ) : (
          <div className="flex justify-center items-center">
            <OrganizationEmptyState
              className="!bg-transparent"
              text="No income chart record to show."
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default IncomeChartTrend;
