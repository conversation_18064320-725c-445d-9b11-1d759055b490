import React, { useEffect, useState } from "react";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import { expenseChartTrendAtom } from "../../../../recoil/atom/accountAnalytics";
import { useRecoilState, useRecoilValue } from "recoil";
import moment from "moment";
import { getExpenseChartTrend } from "../../../../api/accountingAnalytics";
import { ChartOptions } from "chart.js";
import { Bar } from "react-chartjs-2";
import DateRangePicker from "../../../atoms/DatePicker/DateRangePicker";
import { BiCaretDown } from "react-icons/bi";
import { AiOutlineStop } from "react-icons/ai";

const ExpenseChartTrend = () => {
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const [expenseTrendSelectedRange, setExpenseTrendSelectedRange] = useState<{
    startDate: moment.Moment | null;
    endDate: moment.Moment | null;
  }>({ startDate: null, endDate: null });
  const [, setExpenseChartTrend] = useRecoilState(expenseChartTrendAtom);
  const expenseChartTrendValue = useRecoilValue(expenseChartTrendAtom);

  const fetchExpenseTrendChart = () => {
    const startDate = expenseTrendSelectedRange.startDate && moment(expenseTrendSelectedRange.startDate).format("YYYY-MM-DD hh:mm:ss");
    const endDate = expenseTrendSelectedRange.endDate && moment(expenseTrendSelectedRange.endDate).format("YYYY-MM-DD hh:mm:ss");
    getExpenseChartTrend({ from: startDate || "", to: endDate || "" }).then(
      (res) => {
        if (res.success) {
          setExpenseChartTrend(res.data);
        }
      }
    );
  };

  const expenseChartLabel = expenseChartTrendValue?.map((value) => moment(value.month).format("MMMM"));
  const expenseChartAmount = expenseChartTrendValue?.map((value) => value.total);
  const highestExpenseTrendTotal = Math.max( ...expenseChartTrendValue?.map((item) => Number(item.total)));

  const expenseTrendChartData = {
    labels: expenseChartLabel,
    datasets: [
      {
        label: "",
        data: expenseChartAmount,
        backgroundColor: ["#3730A3"],
      },
      // {
      //   label: "",
      //   data: [16, 10, 20, 25, 5, 10],
      //   backgroundColor: ["#753E99"],
      // },
      // {
      //   label: "",
      //   data: [8, 40, 20, 10],
      //   backgroundColor: ["#8345AC"],
      // },
      // {
      //   label: "",
      //   data: [40, 18, 20, 10, 9, 8],
      //   backgroundColor: ["#331B43"],
      // },
    ],
  };

  const expenseBarOptions: ChartOptions<"bar"> = {
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
      },
    },
    responsive: true,
    scales: {
      y: {
        beginAtZero: true,
        max: highestExpenseTrendTotal,
        stacked: true,
      },
      x: {
        stacked: true,
      },
    },
    maintainAspectRatio: false,
    animation: {
      duration: 0,
    },
  };

  useEffect(() => {
    fetchExpenseTrendChart();
  }, [expenseTrendSelectedRange]);

  return (
    <div>
      <div>
        {expenseChartTrendValue?.length > 0 || expenseTrendSelectedRange.startDate ? (
          <>
            <div className="flex justify-between">
              <p className="text-15 font-poppins-medium text-purple-dark">
                Expense trend
              </p>
              <div className="relative">
                <button
                  className="border-2 text-xs flex text-neutral-normal px-4 py-2 rounded-md"
                  onClick={() => setIsPickerOpen(true)}
                >
                  {expenseTrendSelectedRange.startDate
                    ? `${moment(expenseTrendSelectedRange?.startDate).format(
                        "DD/MM/YYYY"
                      )} - ${moment(expenseTrendSelectedRange?.endDate).format(
                        "DD/MM/YYYY"
                      )} `
                    : "Select date"}
                  <BiCaretDown className="mt-1 ml-2" />
                </button>

                <div>
                  <DateRangePicker
                    isOpen={isPickerOpen}
                    onClose={() => setIsPickerOpen(false)}
                    onApply={(startDate, endDate) =>
                      setExpenseTrendSelectedRange({ startDate, endDate })
                    }
                    handleClear={() =>
                      setExpenseTrendSelectedRange({
                        startDate: null,
                        endDate: null,
                      })
                    }
                  />
                </div>
              </div>
            </div>
            <div className="mt-14">
              <div>
                <div
                  className="mt-10"
                  style={{
                    position: "relative",
                    height: "30vh",
                    width: "100%",
                  }}
                >
                  {expenseChartTrendValue?.length > 0 ? (

                    <Bar
                      data={expenseTrendChartData}
                      options={{
                        ...expenseBarOptions,
                      }}
                    />
                  ) : (
                    <div className=" mt-40 text-neutral-normal">
                      <div>
                        <div className="flex justify-center items-center">

                        <AiOutlineStop />
                        </div>
                        <p className="flex justify-center items-center">No record found</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="flex justify-center items-center">
            <OrganizationEmptyState
              className="!bg-transparent"
              text="No expense chart record to show."
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ExpenseChartTrend;
