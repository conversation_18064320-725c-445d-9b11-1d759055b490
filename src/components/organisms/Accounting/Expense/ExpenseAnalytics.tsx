import React, { useEffect, useState } from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ChartOptions,
} from "chart.js";
import { ArrowDown2 } from "iconsax-react";
import CustomSelectWithOverlay from "../../../atoms/CustomInput/CustomSelectWithOverlay";
import StatsCard from "../../../atoms/Cards/StatsCard";
import { PiTrendDown } from "react-icons/pi";
import { useRecoilState, useRecoilValue } from "recoil";
import {
  disbursementSummaryAtom,
  expenseChartTrendAtom,
  highestExpenseBranchAtom,
  incomeExpenseSummaryAtom,
} from "../../../../recoil/atom/accountAnalytics";
import moment from "moment";
import {
  getDisbursementSummary,
  getExpenseChartTrend,
  getHighestExpenseBranch,
  getIncomeExpenseSummary,
} from "../../../../api/accountingAnalytics";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const ExpenseAnalytics = () => {
  const [selectedYear, setSelectedYear] = useState(moment().year().toString());
  const [selectedMonth, setSelectedMonth] = useState(
    (moment().month() + 1).toString()
  );
  const [selectedChartYear, setSelectedChartYear] = useState(
    moment().year().toString()
  );
  const [chartLoading, setChartLoading] = useState(false);
  const [, setExpenseSummary] = useRecoilState(disbursementSummaryAtom);
  const [, setExpenseChartTrend] = useRecoilState(expenseChartTrendAtom);
  const [, setHighestBranchExpense] = useRecoilState(highestExpenseBranchAtom);
  const [, setIncomeExpenseSummary] = useRecoilState(incomeExpenseSummaryAtom);
  const expenseChartTrendValue = useRecoilValue(expenseChartTrendAtom);
  const expenseSummaryValue = useRecoilValue(disbursementSummaryAtom);
  const highestExpenseBranchValue = useRecoilValue(highestExpenseBranchAtom);
  const incomeExpenseSummaryValue = useRecoilValue(incomeExpenseSummaryAtom);

  const fetchExpenseSummary = () => {
    const startDate =
      selectedMonth === "0"
        ? moment(`${selectedYear}-01`, "YYYY-MM")
            .startOf("month")
            .format("YYYY-MM-DD hh:mm:ss")
        : moment(`${selectedMonth} ${selectedYear}`, "MM YYYY")
            .startOf("month")
            .format("YYYY-MM-DD hh:mm:ss");
    const endDate =
      selectedMonth === "0"
        ? moment(`${selectedYear}-12`, "YYYY-MM")
            .startOf("month")
            .format("YYYY-MM-DD hh:mm:ss")
        : moment(`${selectedMonth} ${selectedYear}`, "MM YYYY")
            .endOf("month")
            .format("YYYY-MM-DD hh:mm:ss");
    getDisbursementSummary({ from: startDate || "", to: endDate || "" }).then(
      (res) => {
        if (res.success) {
          setExpenseSummary(res.data);
        }
      }
    );
  };

  const fetchExpenseIncomeSummary = () => {
    getIncomeExpenseSummary().then((res) => {
      if (res.success) {
        setIncomeExpenseSummary(res.data);
      }
    });
  };

  useEffect(() => {
    fetchExpenseSummary();
    fetchHighestExpenseByBranch();
    fetchExpenseIncomeSummary();
  }, [selectedMonth, selectedYear]);

  const fetchExpenseTrendChart = async () => {
    setChartLoading(true);
    try {
      const startDate = moment(`${selectedChartYear}-01`, "YYYY-MM")
        .startOf("month")
        .format("YYYY-MM-DD hh:mm:ss");
      const endDate = moment(`${selectedChartYear}-12`, "YYYY-MM")
        .endOf("month")
        .format("YYYY-MM-DD hh:mm:ss");
      const res = await getExpenseChartTrend({
        from: startDate || "",
        to: endDate || "",
      });

      if (res.success) {
        setExpenseChartTrend(res.data);
      } else {
        setExpenseChartTrend([]);
      }
    } catch (error) {
      setExpenseChartTrend([]);
    } finally {
      setChartLoading(false);
    }
  };

  useEffect(() => {
    fetchExpenseTrendChart();
  }, [selectedChartYear]);

  const fetchHighestExpenseByBranch = () => {
    const startDate =
      selectedMonth === "0"
        ? moment(`${selectedYear}-01`, "YYYY-MM")
            .startOf("month")
            .format("YYYY-MM-DD hh:mm:ss")
        : moment(`${selectedMonth} ${selectedYear}`, "MM YYYY")
            .startOf("month")
            .format("YYYY-MM-DD hh:mm:ss");
    const endDate =
      selectedMonth === "0"
        ? moment(`${selectedYear}-12`, "YYYY-MM")
            .startOf("month")
            .format("YYYY-MM-DD hh:mm:ss")
        : moment(`${selectedMonth} ${selectedYear}`, "MM YYYY")
            .endOf("month")
            .format("YYYY-MM-DD hh:mm:ss");
    getHighestExpenseBranch({ from: startDate || "", to: endDate || "" }).then(
      (res) => {
        if (res.success) {
          setHighestBranchExpense(res.data);
        }
      }
    );
  };

  const disbursementCards = [
    {
      title: "Total expenses",
      value: incomeExpenseSummaryValue?.total_expense?.toLocaleString() || 0,
      icon: <PiTrendDown size={16} />,
      iconBgColor: "bg-red-100",
      iconColor: "text-red-600",
      tooltipContent: "Total expenses",
      showCurrency: true,
      currencyCode: "NGN",
      showInfoDots: false,
      valueSize: "!text-24 text-[#DA1414]",
      cardBorder: "!border-[#CE0505A6]",
    },
    {
      title: "Highest expense branch",
      value: highestExpenseBranchValue[0]?.branch_name || "--",
      icon: <PiTrendDown size={16} />,
      iconBgColor: "bg-red-100",
      iconColor: "text-red-600",
      tooltipContent: "Highest expense branch",
      showCurrency: false,
      currencyCode: "NGN",
      showInfoDots: false,
      valueSize: "!text-24 text-[#DA1414]",
      cardBorder: "!border-[#CE0505A6]",
    },
    {
      title: "Highest spending",
      value: expenseSummaryValue?.highestExpense
        ? parseInt(
            expenseSummaryValue?.highestExpense[0]?.total || 0
          )?.toLocaleString()
        : 0,
      icon: <PiTrendDown size={16} />,
      iconBgColor: "bg-orange-100",
      iconColor: "text-orange-600",
      tooltipContent: expenseSummaryValue?.highestExpense[0]?.name || "Highest spending",
      showCurrency: true,
      currencyCode: "NGN",
      showInfoDots: false,
    },
    {
      title: "Lowest spending",
      value: expenseSummaryValue?.lowestExpense
        ? parseInt(
            expenseSummaryValue?.highestExpense[0]?.total || 0
          )?.toLocaleString()
        : 0,
      icon: <PiTrendDown size={16} />,
      iconBgColor: "bg-green-100",
      iconColor: "text-green-600",
      tooltipContent: expenseSummaryValue?.highestExpense[0]?.name || "Lowest spending",
      showCurrency: true,
      currencyCode: "NGN",
      showInfoDots: false,
    },
    {
      title: "Highest expense category",
      value: expenseSummaryValue?.lowestExpense
        ? parseInt(
            expenseSummaryValue?.highestExpenseCategory[0]?.total || 0
          )?.toLocaleString()
        : 0,
      icon: <PiTrendDown size={16} />,
      iconBgColor: "bg-purple-100",
      iconColor: "text-purple-600",
      tooltipContent: expenseSummaryValue?.highestExpenseCategory[0]?.name || "Highest expense category",
      showCurrency: true,
      currencyCode: "NGN",
      showInfoDots: true,
    },
    {
      title: "Lowest expense category",
      value: expenseSummaryValue?.lowestExpense
        ? parseInt(
            expenseSummaryValue?.lowestExpenseCategory[0]?.total || 0
          )?.toLocaleString()
        : 0,
      icon: <PiTrendDown size={16} />,
      iconBgColor: "bg-blue-100",
      iconColor: "text-blue-600",
      tooltipContent: expenseSummaryValue?.lowestExpenseCategory[0]?.name || "Lowest expense category",
      showCurrency: true,
      currencyCode: "NGN",
      showInfoDots: true,
    },
    {
      title: "Top spending vendor",
      value: expenseSummaryValue?.lowestExpense
        ? parseInt(
            expenseSummaryValue?.highestVendorExpense[0]?.total || 0
          )?.toLocaleString()
        : 0,
      icon: <PiTrendDown size={16} />,
      iconBgColor: "bg-yellow-100",
      iconColor: "text-yellow-600",
      tooltipContent: expenseSummaryValue?.highestVendorExpense[0]?.name || "Top spending vendor",
      showCurrency: true,
      currencyCode: "NGN",
      showInfoDots: true,
    },
    {
      title: "Least spending vendor",
      value: expenseSummaryValue?.lowestExpense
        ? parseInt(
            expenseSummaryValue?.lowestExpenseVendor[0]?.total || 0
          )?.toLocaleString()
        : 0,
      icon: <PiTrendDown size={16} />,
      iconBgColor: "bg-pink-100",
      iconColor: "text-pink-600",
      tooltipContent: expenseSummaryValue?.lowestExpenseVendor[0]?.name || "Least spending vendor",
      showCurrency: true,
      currencyCode: "NGN",
      showInfoDots: true,
    },
  ];

  const yearOptions = (() => {
    const currentYear = moment().year();
    const years: { text: string; value: string }[] = [];

    for (let year = currentYear - 2; year <= currentYear; year++) {
      years.push({
        text: year.toString(),
        value: year.toString(),
      });
    }

    return years.reverse();
  })();

  const monthOptions = [
    { text: "All", value: "0" },
    { text: "January", value: "1" },
    { text: "February", value: "2" },
    { text: "March", value: "3" },
    { text: "April", value: "4" },
    { text: "May", value: "5" },
    { text: "June", value: "6" },
    { text: "July", value: "7" },
    { text: "August", value: "8" },
    { text: "September", value: "9" },
    { text: "October", value: "10" },
    { text: "November", value: "11" },
    { text: "December", value: "12" },
  ];

  const processChartData = () => {
    const allMonths = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    const monthlyData = new Array(12).fill(0);

    if (expenseChartTrendValue && expenseChartTrendValue.length > 0) {
      expenseChartTrendValue.forEach(
        (item: { month: string; total: string }) => {
          const monthIndex = moment(item.month).month();
          monthlyData[monthIndex] = parseFloat(item.total);
        }
      );
    }

    const maxValue = Math.max(...monthlyData);
    const pointRadius = monthlyData.map((value: number) =>
      value > 0 && value === maxValue ? 6 : 0
    );

    return {
      labels: allMonths,
      datasets: [
        {
          data: monthlyData,
          fill: true,
          backgroundColor: "#FDE8E8", // Red gradient for expenses
          borderColor: "rgba(239, 68, 68, 0.8)",
          borderWidth: 2,
          tension: 0.4,
          pointRadius,
          pointHoverRadius: 6,
          pointBackgroundColor: "#1E40AF", // Blue color for the dot
          pointBorderColor: "#fff",
          pointBorderWidth: 2,
        },
      ],
    };
  };

  const chartData = processChartData();

  const getYAxisMax = () => {
    if (!expenseChartTrendValue || expenseChartTrendValue.length === 0) {
      return 4000;
    }
    const maxValue = Math.max(
      ...expenseChartTrendValue.map((item: { month: string; total: string }) =>
        Number(item.total)
      )
    );

    if (maxValue === 0) {
      return 4000;
    }

    const paddedMax = maxValue * 1.2;
    return Math.ceil(paddedMax / 1000) * 1000;
  };

  const yAxisMax = getYAxisMax();
  const stepSize = Math.max(Math.floor(yAxisMax / 5), 1000);

  const chartOptions: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: "#fff",
        titleColor: "#000",
        bodyColor: "#000",
        borderWidth: 1,
        displayColors: false,
        callbacks: {
          label: function (context) {
            return `₦${context.parsed.y.toLocaleString()}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: "#6B7280",
          font: {
            size: 12,
          },
        },
        border: {
          display: false,
        },
      },
      y: {
        beginAtZero: true,
        max: yAxisMax,
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
        ticks: {
          color: "#6B7280",
          font: {
            size: 12,
          },
          stepSize: stepSize,
        },
        border: {
          display: false,
        },
      },
    },
    elements: {
      point: {
        hoverRadius: 8,
      },
    },
  };

  return (
    <div className="p-6">
      <div className="grid grid-cols-5 gap-8 mb-8">
        <div className="col-span-3">
          <div className="flex justify-between items-center bg-white px-4 py-2 mb-4 rounded-lg">
            <h2 className="text-lg font-medium text-gray-900">Expense trend</h2>
            <div className="flex items-center gap-2">
              <CustomSelectWithOverlay
                options={yearOptions}
                value={selectedChartYear}
                onChange={(option: { text: string; value: string }) => {
                  setSelectedChartYear(option.value);
                }}
                parentContainer="w-20"
                className="text-sm border-neutral-gray text-neutral-gray"
                showOverlay={true}
              />
              <ArrowDown2
                size={16}
                className="text-neutral-gray -ml-6 pointer-events-none"
              />
            </div>
          </div>
          <div
            style={{ height: "465px", position: "relative" }}
            className="py-4 px-5 bg-white"
          >
            {chartLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-pulse text-gray-500">
                  Loading chart data...
                </div>
              </div>
            ) : (
              <Line data={chartData} options={chartOptions} />
            )}
          </div>
        </div>
        <div className="space-y-4 col-span-2">
          <div className="flex justify-between items-center bg-white px-4 py-2 rounded-lg">
            <h3 className="text-16 font-medium text-gray-900">
              Disbursement data
            </h3>
            <div className="flex items-center gap-2">
              <CustomSelectWithOverlay
                options={monthOptions}
                value={selectedMonth}
                onChange={(option: { text: string; value: string }) => {
                  setSelectedMonth(option.value);
                }}
                parentContainer="w-20"
                className="text-sm border-neutral-gray text-neutral-gray"
                showOverlay={true}
              />
              <ArrowDown2
                size={16}
                className="text-neutral-gray -ml-6 pointer-events-none"
              />
              <CustomSelectWithOverlay
                options={yearOptions}
                value={selectedYear}
                onChange={(option: { text: string; value: string }) =>
                  setSelectedYear(option.value)
                }
                parentContainer="w-20"
                className="text-sm border-neutral-gray text-neutral-gray ml-2"
                showOverlay={true}
              />
              <ArrowDown2
                size={16}
                className="text-neutral-gray -ml-6 pointer-events-none"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 py-4 px-5 bg-white">
            {disbursementCards.map((card, index) => (
              <StatsCard
                key={index}
                title={card.title}
                value={card.value}
                icon={card.icon}
                iconBgColor={card.iconBgColor}
                iconColor={card.iconColor}
                tooltipContent={card.tooltipContent}
                showCurrency={card.showCurrency}
                currencyCode={card.currencyCode}
                valueSize={card.valueSize}
                cardBorder={card.cardBorder}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpenseAnalytics;
