import React, { useRef } from "react";
import { FiDownload } from "react-icons/fi";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../shared/helpers";
import GoBack from "../../../atoms/Ui/GoBack";
import { Copy } from "iconsax-react";
import { useLocation } from "react-router-dom";
import moment from "moment";
import {
  handleCopyPdfLink,
  handleDownloadPdf,
} from "../../../shared/hooks/handlePdf";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { useRecoilValue } from "recoil";
import { loggedUserAtom } from "../../../../recoil/atom/authAtom";
import { IoCheckmarkDoneCircle } from "react-icons/io5";
import logo from "../../../../assets/images/logo.png";

const ExpenseReceipt = () => {
  const location = useLocation();
  const { data } = location.state || {};
  const getLoggedUser = useRecoilValue(loggedUserAtom);

  const defaultCurrency = getLoggedUser.businesses[0].default_currency;

  const printRef = useRef<HTMLDivElement>(null);

  const copyLink = async () => {
    const link = await handleCopyPdfLink(printRef);
    if (link) {
      clustarkToast(NotificationTypes.SUCCESS, "Link copied to clipboard!");
    }
  };

  return (
    <div>
      <GoBack />
      <div className="mt-10 flex justify-between px-4 py-3 text-purple-dark bg-purple-light">
        <h1 className="text-18 font-poppins-medium  ">Expense receipt</h1>
        <div className="flex justify-center gap-4">
          <CustomButton
            className="!w-[150px] !h-6"
            leftIcon={<Copy size={18} />}
            title="Copy Link"
            handleClick={copyLink}
            variant={ButtonProperties.VARIANT.primary.name}
          />
          <CustomButton
            className="!w-[150px] !h-6"
            leftIcon={<FiDownload size={18} />}
            title="Download receipt"
            handleClick={() => handleDownloadPdf(printRef, "Receipt")}
            variant={ButtonProperties.VARIANT.primary.name}
          />
        </div>
      </div>
      <div className="bg-white py-10 h-full">
        <div className="flex justify-center">
          <div
            className="smallLaptop:w-1/3 border rounded-lg p-5 text-neutral-normal"
            ref={printRef}
          >
            <div>
              <div className="flex justify-center text-alert-text-success">
                <IoCheckmarkDoneCircle size={40} />
              </div>
              <h1 className="text-center text-20 font-semibold text-neutral-normal">
                Expense receipt
              </h1>
              <p className="py-1 text-center">
                Your expense receipt has been successfully generated.
              </p>
              <div className="pt-5">
                <h1 className="text-center text-16">Total Amount</h1>
                <p className="text-center text-18 font-semibold">
                  {defaultCurrency} {data?.amount?.toLocaleString()}
                </p>
                <div className="mt-8">
                  <div className="flex justify-between mt-4">
                    <p>Name</p>
                    <p>{data?.name}</p>
                  </div>
                  {data?.income_type && (
                    <div className="flex justify-between mt-4">
                      <p>Type</p>
                      <p>{data?.income_type}</p>
                    </div>
                  )}
                  {data?.branch?.name && (
                    <div className="flex justify-between mt-4 capitalize">
                      <p>Branch</p>
                      <p>{data?.branch?.name}</p>
                    </div>
                  )}
                  {data?.department?.name && (
                    <div className="flex justify-between mt-4 capitalize">
                      <p>Department</p>
                      <p>{data?.department?.name}</p>
                    </div>
                  )}
                  {data?.customer?.name && (
                    <div className="flex justify-between mt-4 capitalize">
                      <p>Customer</p>
                      <p>{data?.customer?.name}</p>
                    </div>
                  )}
                  <div className="flex justify-between mt-4">
                    <p>Date</p>
                    <p>{moment(data?.date).format("DD/MM/YYYY hh:mm:ss")}</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-center items-center mt-10 border-t">
              <div>
                <div className="flex justify-center items-center">
                  <h1 className="mt-[16px] font-poppins-medium text-20 text-purple-normal">
                    <img src={logo} alt="logo" />
                  </h1>
                </div>
                <div>
                  <p>ArkHR, Ipent 4 extension, Lokogoma, Ap Abuja</p>
                  <p className="text-center">+234 903 901 1682</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpenseReceipt;
