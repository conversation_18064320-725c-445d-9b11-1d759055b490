import React, { useEffect, useRef, useState } from "react";
import { Eye, Receipt, UserEdit } from "iconsax-react";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import { ButtonProperties, debounce } from "../../../shared/helpers";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../../shared/hooks";
import { FiPlus } from "react-icons/fi";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import { PiTrash } from "react-icons/pi";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { Form, Formik } from "formik";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { getAllBranchesAtom, getDepartmentsAtom } from "../../../../recoil/atom/organizationAtom";
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";
import { deleteExpense, getExpenseCategories, getExpenses } from "../../../../api/expense";
import { getExpenseCategoryAtom, getExpensesAtom } from "../../../../recoil/atom/expense";
import { getVendorsAtom } from "../../../../recoil/atom/vendors";
import { getVendors } from "../../../../api/vendor";
import moment from "moment";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import ExpenseAnalytics from "./ExpenseAnalytics";
import AccountingAnalyticsSkeleton from "../../../atoms/Skeleton/AccountingAnalyticsSkeleton";

const Expense = () => {
  const navigate = useNavigate();
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [branchName, setBranchName] = useState<string>("");
  const [branchId, setBranchId] = useState<string>("");
  const [departmentName, setDepartmentName] = useState<string>("");
  const [departmentId, setDepartmentId] = useState<string>("");
  const [vendorName, setVendorName] = useState<string>("");
  const [vendorId, setVendorId] = useState<string>("");
  const [categoryName, setCategoryName] = useState<string>("");
  const [categoryId, setCategoryId] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");

  const [pageNumber, setPageNumber] = useState<number>(1);
  const [, setExpensesAtom] = useRecoilState(getExpensesAtom);
  const [rowId, setRowId] = useState(0);
  const getEntireBranchesValue = useRecoilValue(getAllBranchesAtom);
  const expenses = useRecoilValue(getExpensesAtom);
  const [, setCategoriesAtom] = useRecoilState(getExpenseCategoryAtom);
  const [, setVendorsAtom] = useRecoilState(getVendorsAtom);
  const categoriesValue = useRecoilValue(getExpenseCategoryAtom);
  const vendorValue = useRecoilValue(getVendorsAtom);
  const getDepartments = useRecoilValue(getDepartmentsAtom);
  const { fetchEntireBranches, fetchDepartments } = useUpdateRecoilAtom();

  const debounceSearch = useRef(
    debounce((q) => fetchExpenses(q), 2000)
  ).current;

  const branches =
    getEntireBranchesValue?.data?.map((branch) => ({
      text: branch.name,
      value: branch.id,
    })) || [];

  const fetchExpenses = (q?) => {
    setIsFetching(true);
    getExpenses({ page: pageNumber, search: q }, { branch_id: branchId, department_id: departmentId, vendor_id: vendorId, expense_category_id: categoryId, date: selectedDate }).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setExpensesAtom(res.data);
      }
    });
  };

  const fetchExpenseCategories = () => {
    getExpenseCategories().then((res) => {
      if (res.success) {
        setCategoriesAtom(res.data);
      }
    });
  };

  const fetchVendors = () => {
    getVendors().then((res) => {
      if (res.success) {
        setVendorsAtom(res.data);
      }
    });
  };

  const categories = categoriesValue.map((item) => ({
    text: item.name,
    value: item.id,
  }));

  const vendors = vendorValue?.data?.map((item) => ({
    text: item.name,
    value: item.id,
  }));

  const departments = getDepartments?.data?.map((item) => ({
    text: item.name,
    value: item.id
  }));

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const handleDeleteExpense = (id) => {
    setIsLoading(true);
    deleteExpense(id).then((res) => {
      setIsLoading(false);
      if (res.success) {
        setShowDropdown(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchExpenses();
      }
    });
  };

  useEffect(() => {
    fetchExpenses(searchQuery);
  }, [pageNumber, categoryId, departmentId, vendorId, selectedDate, branchId]);

  useEffect(() => {
    fetchEntireBranches();
    fetchExpenseCategories();
    fetchVendors();
  }, []);

  useEffect(() => { fetchDepartments(); }, [])

  if (isFetching && !searchQuery) {
    return (
      <div>
        <AccountingAnalyticsSkeleton />
      </div>
    );
  }

  const columns = [
    {
      Header: "Expense name",
      accessor: "name",
      Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
    },
    {
      Header: "Expense category",
      accessor: "expenseCategory.name",
      Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
    },
    {
      Header: "Cost",
      accessor: "amount",
      Cell: (row: any) => (
        <p className="whitespace-nowrap flex gap-2">
          {row.cell.row.original.currency && (
            <span className="text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1">
              {row.cell.row.original.currency}
            </span>
          )}
          {`${row.cell?.value?.toLocaleString()}` || "--"}
        </p>
      ),
    },

    {
      Header: "Branch",
      accessor: "branch.name",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Department",
      accessor: "department.name",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Vendor",
      accessor: "vendor.name",
      Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
    },
    {
      Header: "Date",
      accessor: "date",
      Cell: (row: any) => <p>{moment(row.cell.value).format("DD-MM-YYYY") || "--"}</p>,
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() =>
                    navigate(`/accounting/add-expense`, {
                      state: { data: row.cell.row.original },
                    })
                  }
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>
                <li
                  onClick={() => {
                    navigate(`/accounting/add-expense`, {
                      state: { isEdit: true, data: row.cell.row.original },
                    });
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <UserEdit size={18} />
                  Edit
                </li>
                <li
                  onClick={() => {
                    navigate(`/accounting/expense-receipt`, {
                      state: { data: row.cell.row.original },
                    });
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Receipt size={18} />
                  View receipt
                </li>
                {showDeleteWarn ? (
                  <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                    <div className="flex gap-3">
                      <PiTrash size={18} />
                      Are you sure?
                    </div>
                    <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                      <CustomButton
                        isLoading={isLoading}
                        title="Yes"
                        handleClick={() =>
                          handleDeleteExpense(row.cell.row.original.id)
                        }
                        className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer"
                      />
                      <span
                        onClick={() => {
                          setShowDeleteWarn(false);
                        }}
                        className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                      >
                        No
                      </span>
                    </div>
                  </li>
                ) : (
                  <li
                    onClick={() => setShowDeleteWarn(true)}
                    className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer"
                  >
                    <PiTrash size={18} />
                    Delete
                  </li>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  return (
    <>
      <div>
        <ExpenseAnalytics />

        <div>

        </div>
        <div className=" my-9 px-4 py-[23px]">
          {expenses?.data?.length > 0 || searchQuery || branchId || vendorId || departmentId || categoryId || selectedDate ? (
            <CustomTable
              data={expenses?.data || []}
              meta={expenses?.meta || {}}
              columns={columns}
              customFilter={
                <div className="pb-4 absolute w-[50rem] bg-white right-0">
                  <div>
                    <h1 className="border-b border-neutral-light pl-4 py-3">
                      Modify this view
                    </h1>
                    <Formik
                      initialValues={{
                        branch: branchName || "",
                        department: departmentName || "",
                        vendor: vendorName || "",
                        category: categoryName || "",
                        date: selectedDate || "",
                      }}
                      onSubmit={() => { }}
                    >
                      {({ setFieldValue, values }) => (
                        <Form>
                          <div className="flex px-4 gap-5 py-[17.5px] border-b">
                            <p className="whitespace-nowrap">Filter by:</p>
                            <div className="flex gap-2">
                              <div>
                                <FormikCustomDate
                                  value={moment(values.date)}
                                  inputClassName="border bg-transparent"
                                  name="date"
                                  onChange={(date) => {
                                    setFieldValue("date", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null);
                                    setSearchQuery("");
                                    setSelectedDate(moment(date).format("YYYY-MM-DD"));
                                  }}
                                  height="25px"
                                  textSize="12px"
                                />
                              </div>
                              <div>
                                <FormikCustomSelect
                                  parentContainer="!h-7"
                                  placeholder="Expense Category"
                                  optionsParentClassName="!capitalize"
                                  options={categories}
                                  name="category"
                                  onChange={(item: {
                                    value: string;
                                    text: string;
                                  }) => {
                                    setSearchQuery("");
                                    setCategoryId(item.value);
                                    setCategoryName(item.text);
                                    setFieldValue("category", item.text);
                                  }}
                                  value={values.category}
                                />
                              </div>
                              <div className="">
                                <FormikCustomSelect
                                  parentContainer="!h-7"
                                  placeholder="Select Branch"
                                  optionsParentClassName="!capitalize"
                                  options={branches}
                                  name="branch"
                                  onChange={(item: {
                                    value: string;
                                    text: string;
                                  }) => {
                                    setSearchQuery("");
                                    setBranchId(item.value);
                                    setBranchName(item.text);
                                    setFieldValue("branch", item.text);
                                  }}
                                  value={values.branch}
                                />
                              </div>
                              <div>
                                <FormikCustomSelect
                                  parentContainer="!h-7"
                                  placeholder="Select Department"
                                  optionsParentClassName="!capitalize"
                                  options={departments}
                                  name="department"
                                  onChange={(item: {
                                    value: string;
                                    text: string;
                                  }) => {
                                    setSearchQuery("");
                                    setDepartmentId(item.value);
                                    setDepartmentName(item.text);
                                    setFieldValue("department", item.text);
                                  }}
                                  value={values.department}
                                // disabled={!branchId}
                                />
                              </div>
                              <div>
                                <FormikCustomSelect
                                  parentContainer="!h-7"
                                  placeholder="Select Vendor"
                                  optionsParentClassName="!capitalize"
                                  options={vendors}
                                  name="vendor"
                                  onChange={(item: {
                                    value: string;
                                    text: string;
                                  }) => {
                                    setSearchQuery("");
                                    setVendorId(item.value);
                                    setVendorName(item.text);
                                    setFieldValue("vendor", item.text);
                                  }}
                                  value={values.vendor}
                                />
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-end items-end place-content-end">
                            <p
                              className=" pt-3 pr-3 cursor-pointer"
                              onClick={() => {
                                setBranchId("");
                                setBranchName("");
                                setDepartmentId("");
                                setDepartmentName("");
                                setCategoryId("");
                                setCategoryName("");
                                setVendorId("");
                                setVendorName("");
                                setSearchQuery("");
                                setSelectedDate("");
                              }}
                            >
                              Clear Filter
                            </p>
                          </div>
                        </Form>
                      )}
                    </Formik>
                  </div>
                </div>
              }
              handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
              handleSearch={(search) => {
                setSearchQuery(search);
                debounceSearch(search);
              }}
              header={
                <div className="flex justify-between items-center h-[45px] px-2">
                  <h1>
                    Expenses
                  </h1>
                  <div className="bg-black rounded">
                    <CustomButton
                      className="!w-[250px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md !h-10"
                      isTransparent={true}
                      handleClick={() => {
                        navigate("/accounting/add-expense");
                      }}
                      leftIcon={<FiPlus className="ml-3" size={20} />}
                      title="Add expense"
                    />
                  </div>
                </div>
              }
            />
          ) : (
            <div className="flex justify-center items-center py-[120px]">
              <OrganizationEmptyState
                buttonTitle="Add new expense"
                handleClick={() => navigate("/accounting/add-expense")}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default Expense;
