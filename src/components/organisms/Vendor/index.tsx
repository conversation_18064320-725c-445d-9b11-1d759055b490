import React, { useEffect, useRef, useState } from "react";
import { Eye, UserEdit } from "iconsax-react";
import OrganizationEmptyState from "../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../atoms/CustomTable/CustomTable";
import { debounce } from "../../shared/helpers";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../shared/hooks";
import { FiPlus } from "react-icons/fi";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import Loader from "../../atoms/Loader";
import { PiTrash } from "react-icons/pi";
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";
import { getVendorsAtom } from "../../../recoil/atom/vendors";
import { getVendors } from "../../../api/vendor";
import { deleteCustomer } from "../../../api/customer";

const Vendors = () => {
  const navigate = useNavigate();
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [, setVendorsAtom] = useRecoilState(getVendorsAtom);
  const [rowId, setRowId] = useState(0);
  const vendors = useRecoilValue(getVendorsAtom);


  const debounceSearch = useRef(debounce((q) => fetchVendors(q), 2000)).current;

  const fetchVendors = (q?) => {
    setIsFetching(true);
    getVendors({ page: pageNumber, search: q }).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setVendorsAtom(res.data)
      }
    });
  };

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const handleDeleteVendor = (id) => {
    setIsLoading(true);
    deleteCustomer(id).then((res) => {
      setIsLoading(false);
      if (res.success) {
        setShowDropdown(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message)
        fetchVendors();
      }
    });
  };

  useEffect(() => {
    fetchVendors(searchQuery);
  }, [pageNumber]);


  if (isFetching && !searchQuery) {
    return <div>
      <Loader />
    </div>
  };

  const columns = [
    {
      Header: "Vendor name",
      accessor: "name",
      Cell: (row: any) => (<p> {row.cell.value || "--"}</p>)
    },

    {
      Header: "Email",
      accessor: "email",
      Cell: (row: any) => (<p className="lowercase"> {row.cell.value || "--"}</p>),
    },
    {
      Header: "Phone",
      accessor: "phone",
      Cell: (row: any) => (<p>{row.cell.value || "--"} </p>),
    },
    {
      Header: "Address",
      accessor: "address",
      Cell: (row: any) => (<p>{row.cell.value || "--"}</p>),
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative" >
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown >
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li onClick={() => navigate(`/crm/add-vendor`, { state: { data: row.cell.row.original } })} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <Eye size={18} />
                  View
                </li>
                <li onClick={() => { navigate(`/crm/add-vendor`, { state: { isEdit: true, data: row.cell.row.original } }) }} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <UserEdit size={18} />
                  Edit
                </li>
                {showDeleteWarn ? (
                  <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                    <div className="flex gap-3">
                      <PiTrash size={18} />
                      Are you sure?
                    </div>
                    <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                      <CustomButton isLoading={isLoading} title="Yes" handleClick={() => handleDeleteVendor(row.cell.row.original.id)} className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer" />
                      <span onClick={() => { setShowDeleteWarn(false) }} className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center">No</span>
                    </div>
                  </li>
                ) : (
                  <li onClick={() => setShowDeleteWarn(true)} className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer">
                    <PiTrash size={18} />
                    Delete
                  </li>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  return (
    <>
      <div>

        {/* <h1 className="font-poppins font-semibold text-24">Vendors</h1> */}

        {/* <div className="flex justify-end">
        <CustomButton
          className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
          isTransparent={true}
          handleClick={() => {navigate("/crm/add-vendor")}}
          leftIcon={<FiPlus className="ml-3" size={20} />}
          title="Add new vendor"
        />
      </div> */}

        <div className=" my-10 px-4 py-[23px]">

          {(vendors?.data?.length > 0 || searchQuery) ? (

            <CustomTable
              data={vendors?.data || []}
              meta={vendors?.meta || {}}
              columns={columns}
              handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
              handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
              header={
                <div className="flex justify-between items-center h-[40px] px-2">
                  <h1>
                    Vendors
                  </h1>
                  <div className="bg-black rounded">
                    <CustomButton
                      className="!w-[250px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md !h-10"
                      isTransparent={true}
                      handleClick={() => { navigate("/crm/add-vendor") }}
                      leftIcon={<FiPlus className="ml-3" size={20} />}
                      title="Add vendor"
                    />
                  </div>
                </div>
              }
            />
          ) : (
            <div className="flex justify-center items-center py-[120px]">
              <OrganizationEmptyState buttonTitle="Add new vendor" handleClick={() => navigate("/crm/add-vendor")} />
            </div>
          )}
        </div>


      </div>
    </>
  );
};

export default Vendors;
