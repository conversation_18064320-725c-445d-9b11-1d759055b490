import { Formik, Form } from "formik";
import { ArrowLeft2 } from "iconsax-react";
import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages } from "../../shared/helpers";
import SuccessModal from "../../atoms/CustomModal/SuccessModal";
import * as yup from "yup"
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";
import { BiSave } from "react-icons/bi";
import { AiOutlineEdit } from "react-icons/ai";
import { addVendor, updateVendor } from "../../../api/vendor";
import FormikCustomPhoneInput from "../../atoms/CustomInput/FormikCustomPhoneInput";

interface AddVendorProps {
  name: string;
  address: string;
  email: string;
  phone: string;
};

const addVendorSchema = yup.object().shape({
    name: yup.string().required(errorMessages.required),
    phone: yup.string().min(13),
});

const AddVendor = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const {isEdit, data} = location.state || {}
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [edit, setEdit] = useState<boolean>(false);

  let initialValues = {
    name: data?.name || "",
    address: data?.address || "",
    email: data?.email || "",
    phone: data?.phone || "",
  };

  const handleSubmit = (values: AddVendorProps) => {
    setIsLoading(true);
    const payload = {
      name: values.name,
      address: values.address,
      email: values.email,
      phone: values.phone,
    };
    if(isEdit || edit) {
      const editPayload = {
        name: values.name,
        address: values.address,
        email: values.email,
        phone: values.phone,
        vendorId: data.id,
      };
      updateVendor(editPayload).then((response) => {
        setIsLoading(false);
        if(response?.success) {
          clustarkToast(NotificationTypes.SUCCESS, response.message);
          navigate("/crm/vendors")
        }
    })
    } else {
      addVendor(payload).then((response) => {
          setIsLoading(false);
          if(response?.success) {
            setShowSuccessModal(true);
          }
      })
    }
  };

  return (
    <div>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 cursor-pointer w-fit"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6">
        <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
          <h1 className="font-poppins-medium text-18 text-purple-dark">
            {isEdit || data ? "Vendor information" :"Add new vendor"}
          </h1>
        </div>
        <div className="bg-[#F5F5F5] pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
          
          <Formik<AddVendorProps>
            initialValues={initialValues}
            onSubmit={handleSubmit}
            validationSchema={addVendorSchema}
          >
            {({ values, setFieldValue }) => (
              <Form>
                <div className="flex justify-end">
                  {data && (
                    <>
                    {isEdit || edit ? (
                      <CustomButton leftIcon={<BiSave size={20} />}  type="submit" isLoading={isLoading} title="Save" handleClick={() => {}} className="!w-[108px]"  variant={ButtonProperties.VARIANT.primary.name}/>
                    ) : (
                      <div onClick={() => {setEdit(true)}} className="!w-[108px] bg-purple-normal text-white text-center py-3 rounded flex justify-center gap-2"><AiOutlineEdit size={18} /> Edit</div>
                    )}
                    </>
                  )}
                </div>
                <div>
                  <FormikCustomInput
                    label="Vendor/Company name *"
                    id="name"
                    name="name"
                    placeholder="enter vendor/company name"
                    type="text"
                    inputClassName="!bg-transparent"
                    disabled={!edit && !isEdit && data}
                  />
                </div>
                <div className="grid grid-cols-2 gap-8 mt-8">
                  <div>
                    <FormikCustomInput
                      label="Email"
                      id="email"
                      name="email"
                      placeholder="enter email address"
                      type="email"
                      inputClassName="!bg-transparent"
                      disabled={!edit && !isEdit && data}

                    />
                  </div>
                  <div>
                    <FormikCustomPhoneInput
                      label="Phone number"
                      id="phone"
                      name="phone"
                      placeholder="enter phone number"
                      className="!bg-transparent"
                      onChange={(value: string) => {
                        setFieldValue("phone", value);
                      }}
                      disabled={!edit && !isEdit && data}
                      value={values.phone}
                    />
                  </div>
                </div>
                <div className="mt-8">
                  <div>
                    <FormikCustomInput
                      label="Address"
                      id="address"
                      name="address"
                      placeholder="enter address"
                      type="text"
                      inputClassName="!bg-transparent"
                      disabled={!edit && !isEdit && data}

                    />
                  </div>
                </div>
                {!data && (
                  <div className="mt-[120px] flex justify-end">
                    <CustomButton
                      type="submit"
                      title="Add vendor"
                      handleClick={() => {}}
                      isLoading={isLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                    />
                  </div>
                )}
                
              </Form>
            )}
          </Formik>
        </div>
      </div>
      <SuccessModal
        visibility={showSuccessModal}
          text="Vendor added successfully."
        route="/crm/vendors"
      />
    </div>
  );
};

export default AddVendor;
