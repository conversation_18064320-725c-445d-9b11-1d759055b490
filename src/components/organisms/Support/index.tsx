import React, { useEffect, useState } from 'react'
import CustomTable from '../../atoms/CustomTable/CustomTable';
import OrganizationEmptyState from '../../atoms/Cards/OrganizationEmptyState';
import FilterDropdown from '../../atoms/Cards/FilterDropdown';
import { Eye } from 'iconsax-react';
import { FaEllipsisV } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import useClickOutside from '../../shared/hooks';
import CustomButton from '../../atoms/CustomButton/CustomButton';
import { FiPlus } from 'react-icons/fi';
import StatusTag from '../../atoms/StatusTag';
import { useRecoilState, useRecoilValue } from 'recoil';
import { getTicketsAtom } from '../../../recoil/atom/ticket';
import { getTickets } from '../../../api/ticket';
import { truncateText } from '../../shared/helpers';
import StatisticsCard from '../../atoms/Cards/StatisticsCard';
import { GoCommentDiscussion, GoDiscussionClosed, GoDiscussionOutdated } from 'react-icons/go';

const Support = () => {
  const navigate = useNavigate();
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [rowId, setRowId] = useState(0);
  const [, setGetTickets] = useRecoilState(getTicketsAtom);
  const getTicketsValue = useRecoilValue(getTicketsAtom);


  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const fetchTickets = () => {
    getTickets().then((res) => {
      if (res.success) {
        setGetTickets(res.data);
      }
    })
  };


  const columns = [
    {
      Header: "Subject",
      accessor: "subject",
      Cell: (row: any) => (
        <p>{truncateText(row.cell.value, 30)}</p>
      )
    },

    {
      Header: "Ticket iD",
      accessor: "ticket_uid",
      Cell: (row: any) => (
        <p className="rounded-2xl bg-[#F5F6F7] w-fit px-2 py-1 font-poppins-medium">
          {row.cell.value || "--"}{" "}
        </p>
      ),
    },
    {
      Header: "Priority",
      accessor: "priority",
      Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
    },
    {
      Header: "Type",
      accessor: "type",
    },

    {
      Header: "Status",
      accessor: "status",
      Cell: (row: any) => (
        <StatusTag status={row.cell.value} />
      ),
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() =>
                    navigate(`/support/ticket-information`, {
                      state: { id: row.cell.row.original.id, data: row.cell.row.original },
                    })
                  }
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>

              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  const ticketStats = [
    {
      title: "Total tickets",
      value: getTicketsValue?.data?.length,
      icon: <GoDiscussionOutdated className="rotate-90" size={24} />,
      iconBackgroundColor: '#3730A399',
      valueText: "Tickets",
      cardBackgroundColor: "#EEF2FFCC",
    },
    {
      title: "Open tickets",
      value: getTicketsValue?.data?.filter(tickets => tickets.status === "OPEN").length,
      icon: <GoCommentDiscussion className="rotate-90" size={24} />,
      iconBackgroundColor: '#3730A399',
      valueText: "Open",
      cardBackgroundColor: "#FDFFE8CC",
    },
    {
      title: "Closed tickets",
      value: getTicketsValue?.data?.filter(tickets => tickets.status === "CLOSED").length,
      icon: <GoDiscussionClosed size={24} />,
      iconBackgroundColor: '#3730A399',
      valueText: "Closed",
      cardBackgroundColor: "#EBFDFFCC",
    },
  ];


  useEffect(() => {
    fetchTickets();
  }, [])

  return (
    <div>
      {/* <h1 className="font-poppins font-semibold text-24">Support</h1> */}
      <div className=" grid grid-cols-3 gap-5 ">
        {ticketStats?.map((item, index) => (
          <div key={index}>
            <StatisticsCard
              backgroundColor={item?.cardBackgroundColor}
              key={index}
              title={item.title}
              value={item.value}
              icon={item.icon}
              iconBackgroundColor={item?.iconBackgroundColor}
              valueText={item.valueText}
            />
          </div>
        ))}
      </div>

      {/* <div className="pt-10 flex justify-end">
        <CustomButton
          className="!w-[180px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
          isTransparent={true}
          handleClick={() => {
            navigate("/support/create-ticket");
          }}
          leftIcon={<FiPlus className="ml-3" size={20} />}
          title="Create new ticket"
        />
      </div> */}

      <div className=" my-10  py-[23px]">
        {getTicketsValue?.data?.length > 0 ? (
          <CustomTable
            data={getTicketsValue?.data || []}
            meta={getTicketsValue?.meta || {}}
            hideSearch
            columns={columns}
            header={
              <div className="flex justify-between items-center h-[40px] px-2">
                <h1>
                  Tickets
                </h1>
                <div className="bg-black">
                  <CustomButton
                    className="!w-[250px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md !h-10"
                    isTransparent={true}
                    handleClick={() => {
                      navigate("/support/create-ticket");
                    }}
                    leftIcon={<FiPlus className="ml-3" size={20} />}
                    title="Create new ticket"
                  />
                </div>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState
              text="There are no tickets, when you there is, it will appear here"
              buttonTitle="Create new ticket"
              handleClick={() => navigate("/support/create-ticket")}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default Support