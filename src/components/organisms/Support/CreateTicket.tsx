import { Form, Formik } from "formik";
import { ArrowLeft2 } from "iconsax-react";
import React, { useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages } from "../../shared/helpers";
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";
import * as yup from "yup";
import { createTicket } from "../../../api/ticket";
import { FaImage } from "react-icons/fa";
import { FaXmark } from "react-icons/fa6";

const createTicketSchema = yup.object().shape({
  subject: yup.string().required(errorMessages.required),
  priority: yup.string().required(errorMessages.required),
  type: yup.string().required(errorMessages.required),
});

const CreateTicket = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [ticketMessage, setTicketMessage] = useState<string>("");
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files ? event.target.files[0] : null;

    if (file) {
      const validTypes = ["image/jpg", "image/png"];
      if (validTypes.includes(file.type)) {
        const reader = new FileReader();
        reader.onload = () => {
          setImagePreview(reader.result as string);
        };
        reader.readAsDataURL(file);
        setSelectedImage(file)
      } else {
        const maxSize = 900 * 1024; // 1 MB in bytes
        if (file.size > maxSize) {
          clustarkToast(NotificationTypes.ERROR, 'File size must be less than 900kb.');
          return;
        }
          clustarkToast(
            NotificationTypes.ERROR,
            "Please select a valid image file (JPEG, JPG, or PNG)."
          );
        }
    };
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleSubmit = (values) => {
    if (!ticketMessage) {
      clustarkToast(NotificationTypes.ERROR, "Please enter a message");
    } else {
      setIsLoading(true);
      const data = new FormData();

      data.append("subject", values.subject);
      data.append("priority", values.priority);
      data.append("type", values.type);
      data.append("message", ticketMessage);
      data.append("image", selectedImage ? selectedImage : "");
      createTicket(data).then((res) => {
        setIsLoading(false);
        if (res.success) {
          clustarkToast(NotificationTypes.SUCCESS, res.message);
          navigate("/support");
        }
      });
    }
  };

  return (
    <div>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 cursor-pointer"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6">
        <h1 className="font-poppins-medium text-18 rounded-tl-[10px] rounded-tr-[10px] text-purple-dark bg-purple-light pl-10 py-[33px]">
          Create new ticket
        </h1>
      </div>
      <div>
        <Formik
          initialValues={{
            subject: "",
            priority: "",
            type: "",
          }}
          onSubmit={handleSubmit}
          validationSchema={createTicketSchema}
        >
          {({ setFieldValue }) => (
            <Form>
              <div className="grid grid-cols-5 gap-10 mt-10">
                <div className="bg-white p-5 h-fit">
                  <div>
                    <FormikCustomInput
                      inputClassName="!h-10"
                      label="Subject"
                      name="subject"
                    />
                  </div>
                  <div className="mt-4">
                    <FormikCustomSelect
                      parentContainer="!h-10"
                      name="priority"
                      label="Priority"
                      options={[
                        { text: "HIGH", value: "HIGH" },
                        { text: "MEDIUM", value: "MEDIUM" },
                        { text: "LOW", value: "LOW" },
                      ]}
                      onChange={(item) => setFieldValue("priority", item.value)}
                    />
                  </div>
                  <div className="mt-4">
                    <FormikCustomSelect
                      parentContainer="!h-10"
                      name="type"
                      label="Type"
                      options={[
                        { text: "SALES", value: "SALES" },
                        { text: "ENGINEERING", value: "ENGINEERING" },
                        { text: "QUESTIONS", value: "QUESTIONS" },
                        { text: "ISSUES", value: "ISSUES" },
                        { text: "FEEDBACK", value: "FEEDBACK" },
                      ]}
                      onChange={(item) => setFieldValue("type", item.value)}
                    />
                  </div>
                </div>
                <div className="bg-white h-[40rem] col-span-4 p-5">
                  <h1 className="text-16 pb-5">Message</h1>
                  <div className="  w-full ">
                    <div className="flex justify-end">
                      <p
                        className="flex gap-1 mb-1 cursor-pointer w-fit"
                        onClick={handleClick}
                      >
                        <FaImage color="#546881" size={16} />
                        Attach image
                      </p>
                      <input
                        type="file"
                        ref={fileInputRef}
                        accept="image/jpeg, image/png"
                        style={{ display: "none" }}
                        onChange={handleImageUpload}
                      />
                    </div>
                    <div className="relative h-[300px] border-2 py-4 rounded-lg ">
                      <textarea
                        className="w-full show-scrollbar outline-none py-1 px-4"
                        name="message"
                        id="message"
                        value={ticketMessage}
                        rows={10}
                        placeholder="Write a message"
                        onChange={(e) => setTicketMessage(e.target.value)}
                      ></textarea>
                      {/* <div className="absolute right-4 top-[40%] cursor-pointer">
                  <FiSend onClick={sendMessage} color="#546881" size={20} />
                </div> */}
                      {imagePreview && (
                        <div className="mt-2 absolute bottom-0 m-4">
                          <img
                            width={150}
                            height={150}
                            src={imagePreview}
                            alt="Selected"
                            className="bg-gray-50 w-[150px] h-[150px] border rounded-lg relative object-contain"
                          />
                          <div
                            onClick={() => setImagePreview(null)}
                            className="cursor-pointer absolute -top-1 right-0 text-alert-text-error bg-alert-bg-error p-1 border border-alert-text-error rounded-full"
                          >
                            <FaXmark size={10} />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex justify-end mt-20">
                    <CustomButton
                      type="submit"
                      isLoading={isLoading}
                      title="Create ticket"
                      handleClick={() => {}}
                      variant={ButtonProperties.VARIANT.primary.name}
                      size={ButtonProperties.SIZES.small}
                    />
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default CreateTicket;
