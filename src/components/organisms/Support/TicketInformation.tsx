import { ArrowLeft2 } from "iconsax-react";
import React, { useEffect, useRef, useState } from "react";
import { FiSend } from "react-icons/fi";
import { useLocation, useNavigate } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import { getChatId<PERSON>tom, getTicketChatAtom, getTicketChatHeaderAtom, getTicketsAtom } from "../../../recoil/atom/ticket";
import { getTicketChats, getTickets, sendTicketChat } from "../../../api/ticket";
import moment from "moment";
import { loggedUserAtom } from "../../../recoil/atom/authAtom";
import { MdAttachment, } from "react-icons/md";
import { FaUser, FaXmark } from "react-icons/fa6";
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";
// import { removeHtmlTags } from "../../shared/helpers";
import { FaSearch, FaSpinner } from "react-icons/fa";
// import CustomModal from "../../atoms/CustomModal/CustomModal";
// import CustomButton from "../../atoms/CustomButton/CustomButton";
import Loader from "../../atoms/Loader";
import StatusTag from "../../atoms/StatusTag";
import CustomModal from "../../atoms/CustomModal/CustomModal";

const TicketInformation = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const searchTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
  const lastMessageRef = useRef<HTMLDivElement | null>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { id, data } = location?.state || "";
  const [ticketMessage, setTicketMessage] = useState<string>("");
  const [, setGetChats] = useRecoilState(getTicketChatAtom);
  const getTicketChatValue = useRecoilValue(getTicketChatAtom);
  const getLoggedUserValue = useRecoilValue(loggedUserAtom);

  const [, setGetChatHeader] = useRecoilState(getTicketChatHeaderAtom);
  const getTicketChatHeaderValue = useRecoilValue(getTicketChatHeaderAtom);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState(false);

  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const [, setGetTickets] = useRecoilState(getTicketsAtom);
  const getTicketsValue = useRecoilValue(getTicketsAtom);

  const [, setChatId] = useRecoilState(getChatIdAtom);
  const getChatIdValue = useRecoilValue(getChatIdAtom);

  const [searchQuery, setSearchQuery] = useState("");
  const [modalImage, setModalImage] = useState(null);
  const [open, setOpen] = useState(false);



  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files ? event.target.files[0] : null;

    if (file) {
      const validTypes = ["image/jpeg", "image/png"];
      const maxSize = 900 * 1024;

      if (file.size > maxSize) {
        clustarkToast(
          NotificationTypes.ERROR,
          "File size must be less than 900kb."
        );
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      if (!validTypes.includes(file.type)) {
        clustarkToast(
          NotificationTypes.ERROR,
          "Please select a valid image file (JPEG or PNG)."
        );
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      setSelectedImage(file);

      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [getTicketChatValue]);

  const fetchTicketChats = () => {
    getTicketChats(id).then((res) => {
      if (res.success) {
        setGetChats(res.data);
        setGetChatHeader(res.data[0].ticket)
        setChatId(id)
      }
    });
  };


  const fetchChat = (newId: any) => {
    setIsLoading(true)
    getTicketChats(newId).then((res) => {
      if (res.success) {
        setGetChats(res.data);
        setGetChatHeader(res.data[0].ticket)
        setChatId(newId)
        setIsLoading(false)
      }
    });
  };


  const sendMessage = () => {
    if (ticketMessage.trim() || selectedImage) {
      setIsLoading(true);
      const newMessage = {
        message: ticketMessage,
        ticketId: getChatIdValue,
        user_id: getLoggedUserValue?.id,
        attachment: imagePreview
      };
      const data = new FormData();
      data.append("ticketId", getChatIdValue);
      data.append("message", ticketMessage);
      data.append("image", selectedImage ? selectedImage : "");
      sendTicketChat(data).then((res) => {
        setIsLoading(false);
        if (res.success) {
          setGetChats((prevChats) => [...prevChats, newMessage]);
          setTicketMessage("");
          setImagePreview("");
          setSelectedImage(null);
          fetchTicketChats();
        }
      });
    }
  };


  const fetchTickets = (q: any) => {
    setIsFetching(true)
    getTickets({ search: q }).then((res) => {
      if (res.success) {
        setGetTickets(res.data);
        setIsFetching(false)
      } else {
        setIsFetching(false)
      }
    })
  };


  const handleSearch = (query: string) => {

    setSearchQuery(query);

    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    searchTimeout.current = setTimeout(() => {
      fetchTickets(query);
    }, 2000);

  };

  
  const HandleModal = (img) => {
    setModalImage(img)
    setOpen(true)
  }


  // const scrollToLast = () => {
  //   if (lastMessageRef.current) {
  //     lastMessageRef.current.scrollIntoView({ behavior: "smooth" });
  //   }
  // }

  // useEffect(() => {
  //   if (getTicketChatValue) {
  //     scrollToLast();
  //   }
  // }, [getTicketChatValue]);


  useEffect(() => {

    const interval = setInterval(() => {
      if (getChatIdValue) {
        getTicketChats(getChatIdValue).then((res) => {
          if (res.success) {
            setGetChats(res.data);
          }
        });
      }
    }, 5000);

    return () => clearInterval(interval);

  }, [getChatIdValue]);


  useEffect(() => {
    if (!id) {
      navigate("/support");
    }
    fetchTicketChats();
  }, []);


  return (

    <div className="h-[70vh] bg-white rounded-md p-5">

      <div className="mt-4 mb-4">
        <h1 className="font-poppins-medium text-18 rounded-tl-[10px] rounded-tr-[10px] text-purple-dark bg-purple-light pl-10 py-[33px]">
          Ticket information
        </h1>
      </div>
      <div className="flex ">
        <div className="w-[20%] h-[70vh] flex flex-col gap-5 bg-white ">
          <div className="">
            <h2 className="text-15 font-semibold text-purple-dark">Tickets</h2>
          </div>
          <div className="relative">
            <FaSearch size={14} className="absolute top-[0.9rem] left-3 text-gray-400 " />
            <input
              type="text"
              placeholder="search tickets"
              className="w-[250px] px-8 py-2 rounded-md focus:outline-none focus:border-purple-700 shadow-md"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>

          {
            isFetching ?
              <Loader /> :
              <div className="flex flex-col gap-4">
                {
                  getTicketsValue ? getTicketsValue?.data?.map(({ id: chatId, subject, ticket_uid, created_at }) => (
                    <div
                      key={chatId}
                      className={`flex gap-2 cursor-pointer p-2 rounded-md mr-4 ${getChatIdValue === chatId ? "bg-purple-light-hover" : ""
                        }`}
                      onClick={() => {
                        fetchChat(chatId);
                      }}
                    >
                      <div className="w-[50px] h-[50px] bg-purple-300 rounded-full flex justify-center items-center">
                        <div className="w-[30px] h-[30px] bg-purple-500 rounded-full flex justify-center items-center ">
                          <FaUser className="text-white" />
                        </div>
                      </div>
                      <div className="w-full">
                        <div className="flex items-center justify-between ">
                          <p className="text-[1rem] font-bold text-purple-darker" >{ticket_uid}</p>
                          <p className="text-[0.8rem] mr-5">
                            {moment(created_at).calendar(null, {
                              sameDay: "h:mm A",
                              lastDay: "[Yesterday] h:mm A",
                              lastWeek: "ddd h:mm A",
                              sameElse: "D MMM YYYY",
                            })}
                          </p>
                        </div>
                        <p className="text-[0.9rem] text-purple-dark">{subject}</p>
                      </div>
                    </div>
                  )) :
                    '__'
                }
              </div>
          }

        </div>

        <div className="w-[80%] bg-accent-purple-light h-[70vh] rounded-md relative flex flex-col">
          <div className="flex justify-between bg-white px-4 py-4 rounded-md border border-purple-200 h-[60px] sticky top-0 z-10">
            <div className="flex items-center gap-2 font-bold">
              <div className="w-[30px] h-[30px] bg-purple-500 rounded-full flex justify-center items-center">
                <FaUser className="text-white" />
              </div>
              <p className="text-purple-dark"><span>{getTicketChatHeaderValue.ticket_uid || '__'}</span> </p>
            </div>
            <div className="text15 font-semibold text-purple-dark ">
              <p>- {getTicketChatHeaderValue.subject || ' __'} -</p>
            </div>
            <div className="flex gap-5 items-center text-[0.9rem]">
              <p className="lowercase"> <StatusTag status={getTicketChatHeaderValue?.status || '__'} /> </p>
              <p>Priority : <span className="text-purple-dark lowercase">{getTicketChatHeaderValue.priority || '__'}</span></p>
              <p>Type : <span  className="text-purple-dark lowercase">{getTicketChatHeaderValue.type || '__'}</span></p>
            </div>
          </div>

          {
            isLoading ?
              <Loader /> :
              <div className="flex-grow p-4 space-y-6 overflow-y-scroll show-scrollbar" >
                {
                  getTicketChatValue ? getTicketChatValue.map(({ id, message, attachment, created_at, is_business_message, }, index) => (
                    <div className="flex flex-col gap-2 "
                      key={id}
                      ref={index === getTicketChatValue.length - 1 ? lastMessageRef : null}
                    >
                      {
                        attachment &&
                        <div className={` w-[250px] h-[250px] flex flex-col justify-center items-center rounded-md bg-white gap-3 ${!is_business_message ? "bg-purple-light-hover" : "ml-auto"}`}>
                          <div className={`overflow-hidden w-[200px] h-[200px] bg-white `}>
                            <img
                              src={attachment}
                              alt='img'
                              onClick={() => HandleModal(attachment)}
                              className="w-full h-full object-cover cursor-pointer"
                            />
                          </div>
                          <p className="text-[0.7rem] whitespace-nowrap ml-auto mr-5 text-purple-dark">
                            {moment(created_at).calendar(null, {
                              sameDay: "h:mm A",
                              lastDay: "[Yesterday] h:mm A",
                              lastWeek: "ddd h:mm A",
                              sameElse: "D MMM YYYY",
                            })}
                          </p>
                          <CustomModal
                            toggleVisibility={setOpen}
                            visibility={open}
                          >
                            <div className="">
                              {attachment && modalImage &&
                                <img
                                  src={modalImage}
                                  alt='img'
                                  className="l "
                                />}
                            </div>
                          </CustomModal>
                        </div>
                      }
                      <div
                        className={`w-[600px] bg-white p-3 rounded-lg shadow-sm border border-gray-200 text-purple-dark ${!is_business_message ? "bg-purple-light-hover" : "ml-auto"
                          }`}
                      >
                        <div className="flex flex-col gap-3">
                          <p className="flex-1 break-words">{message}</p>
                          <p className="text-[0.7rem] whitespace-nowrap ml-auto">
                            {moment(created_at).calendar(null, {
                              sameDay: "h:mm A",
                              lastDay: "[Yesterday] h:mm A",
                              lastWeek: "ddd h:mm A",
                              sameElse: "D MMM YYYY",
                            })}
                          </p>
                        </div>
                      </div>
                    </div>
                  )) :
                    <div className="flex justify-center items-center w-full h-full text-purple-dark-hover font-semibold">
                      <p>__No Active Chat__</p>
                    </div>
                }
              </div>
          }

          {
            getTicketChatHeaderValue.status !== 'CLOSED' &&
            <div className=" bg-white border-t border-purple-200 sticky bottom-0 p-2">
              <textarea
                name='message'
                id="message"
                value={ticketMessage}
                onChange={(e) => setTicketMessage(e.target.value)}
                placeholder="type a message"
                className="h-[60px] w-full rounded-md focus:outline-none p-2 border border-purple-200 "
              >
              </textarea>
              {imagePreview && (
                <div className=" absolute bottom-20">
                  <img
                    width={100}
                    height={100}
                    src={imagePreview}
                    alt="Selected"
                    className="bg-gray-50 h-[150px] w-[150px] object-contain border rounded-lg relative"
                  />
                  <div
                    onClick={() => setImagePreview(null)}
                    className="cursor-pointer absolute -top-1 right-0 text-alert-text-error bg-alert-bg-error p-1 border border-alert-text-error rounded-full"
                  >
                    <FaXmark size={10} />
                  </div>
                </div>
              )}

              <div className="absolute flex gap-2 bottom-8 right-4 text-purple-normal cursor-pointer">
                <div>
                  <p
                    className="flex gap-1 mb-1 cursor-pointer w-fit"
                    onClick={handleClick}
                  >
                    <MdAttachment color="#546881" size={20} />
                  </p>
                  <input
                    type="file"
                    ref={fileInputRef}
                    accept="image/jpg, image/png, image/jpeg"
                    style={{ display: "none" }}
                    onChange={handleImageUpload}
                  />
                </div>
                {isLoading ? (
                  <FaSpinner className="animate-spin" size={18} color="#546881" />
                ) : (
                  <FiSend onClick={sendMessage} color="#546881" size={20} />
                )}
              </div>
            </div>
          }
        </div>
      </div>
    </div >




    // <div>
    //   <div
    //     onClick={() => navigate(-1)}
    //     className="flex text-16 gap-1 cursor-pointer"
    //   >
    //     <ArrowLeft2 size={20} /> <p>Back</p>
    //   </div>
    //   <div className="mt-6">
    //     <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-[33px]">
    //       Ticket information
    //     </h1>
    //   </div>
    //   <div>
    //     <div className="flex gap-10 mt-10">
    //       <div>
    //         <div className="bg-accent-orange-light px-5 h-fit w-[25rem] rounded-lg py-10 shadow-lg">
    //           <div className="flex justify-between">
    //             <h1>Ticket id</h1>
    //             <p className="rounded-2xl bg-[#F5F6F7] w-fit px-2 py-1 font-poppins-medium">
    //               {data?.ticket_uid || "--"}
    //             </p>
    //           </div>
    //           <div className="flex justify-between mt-6">
    //             <h1>Ticket priority</h1>
    //             <p className="text-alert-text-error">{data?.priority}</p>
    //           </div>
    //           <div className="flex justify-between mt-6">
    //             <h1>Ticket type</h1>
    //             <p>{data?.type}</p>
    //           </div>
    //         </div>
    //         <div className="bg-accent-blue-light rounded-lg px-5 h-fit w-[25rem] mt-8 py-10 shadow-lg">
    //           <div className="flex justify-between">
    //             <h1>Ticket status</h1>
    //             <p className="text-alert-text-success font-bold">
    //               {data?.status}
    //             </p>
    //           </div>
    //           <div className="flex justify-between mt-6">
    //             <h1>Date created</h1>
    //             <p>{moment(data?.created_at).format("DD-MM-YYYY")}</p>
    //           </div>
    //           <div className="flex justify-between mt-6">
    //             <h1>Date closed</h1>
    //             <p>
    //               {data?.date_closed_at
    //                 ? moment(data?.created_at).format("DD-MM-YYYY")
    //                 : "--"}
    //             </p>
    //           </div>
    //         </div>
    //       </div>

    //       <div className={`bg-white relative ${imagePreview ? "h-[600px]" : "h-[500px]"} rounded-lg w-full`}>
    //         <h1 className="text-purple-normal-hover font-semibold bg-gray-100 p-5 text-16 rounded-lg">
    //           Ticket response - {data?.subject}
    //         </h1>
    //         <div className="flex-grow overflow-y-auto m-4 h-[300px] show-scrollbar">
    //           {getTicketChatValue.map((item, index) => (
    //             <div
    //               key={index}
    //               className={`flex ${item?.user_id === getLoggedUserValue?.id
    //                   ? "justify-end"
    //                   : "justify-start"
    //                 } mb-2`}
    //             >
    //               <p
    //                 // dangerouslySetInnerHTML={{ __html: item?.message?.replace(/\n/g, "<br>") }}

    //                 className={`max-w-lg px-4 py-2 text-neutral-normal rounded-lg shadow-md ${item?.user_id === getLoggedUserValue?.id
    //                     ? "bg-gray-50 text-left"
    //                     : "bg-accent-blue-light text-left"
    //                   }`}
    //               >
    //                 <span>{removeHtmlTags(item?.message)}</span>
    //                 {item?.attachment && (
    //                   <img
    //                     className="object-contain h-[200px] w-[200px] border bg-white rounded-lg mt-2"
    //                     src={item?.attachment}
    //                     width={200}
    //                     height={200}
    //                     alt="image"
    //                   />
    //                 )}
    //               </p>
    //             </div>
    //           ))}
    //           <div ref={messagesEndRef} />
    //         </div>

    //         <div className=" bottom-0 absolute w-full">
    //           <div className={`${imagePreview ? "h-[200px]" : "h-[120px]"} border-2 rounded-lg relative`}>
    //             <textarea
    //               className="w-full show-scrollbar outline-none py-4 pl-4 pr-20"
    //               name="message"
    //               id="message"
    //               value={ticketMessage}
    //               rows={4}
    //               placeholder="Write a message"
    //               onChange={(e) => setTicketMessage(e.target.value)}
    //             ></textarea>
    //             {imagePreview && (
    //               <div className="mt-2 absolute bottom-0 m-4">
    //                 <img
    //                   width={100}
    //                   height={100}
    //                   src={imagePreview}
    //                   alt="Selected"
    //                   className="bg-gray-50 h-[100px] w-[100px] object-contain border rounded-lg relative"
    //                 />
    //                 <div
    //                   onClick={() => setImagePreview(null)}
    //                   className="cursor-pointer absolute -top-1 right-0 text-alert-text-error bg-alert-bg-error p-1 border border-alert-text-error rounded-full"
    //                 >
    //                   <FaXmark size={10} />
    //                 </div>
    //               </div>
    //             )}
    //           </div>
    //           <div className="absolute flex gap-3 right-4 top-[40%] cursor-pointer">
    //             <div>
    //               <p
    //                 className="flex gap-1 mb-1 cursor-pointer w-fit"
    //                 onClick={handleClick}
    //               >
    //                 <MdAttachment color="#546881" size={20} />
    //               </p>
    //               <input
    //                 type="file"
    //                 ref={fileInputRef}
    //                 accept="image/jpg, image/png, image/jpeg"
    //                 style={{ display: "none" }}
    //                 onChange={handleImageUpload}
    //               />
    //             </div>
    //             {/* <MdAttachment onClick={handleImageUpload} color="#546881" size={20} /> */}
    //             {isLoading ? (
    //               <FaSpinner className="animate-spin" size={18} color="#546881" />
    //             ) : (

    //               <FiSend onClick={sendMessage} color="#546881" size={20} />
    //             )}
    //           </div>
    //         </div>
    //       </div>
    //     </div>
    //   </div>
    // </div>


  );
};

export default TicketInformation;
