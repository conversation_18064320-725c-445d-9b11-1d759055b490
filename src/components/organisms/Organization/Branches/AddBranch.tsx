import { Formik, Form } from "formik";
import { ArrowLeft2 } from "iconsax-react";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import { Country, State } from "country-state-city";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import { addBranch } from "../../../../api/organization";
import * as yup from "yup"
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";

interface AddBranchProps {
  branchName: string;
  branchAddress: string;
  country: string;
  state: string;
}

let initialValues = {
  branchName: "",
  branchAddress: "",
  country: "",
  state: "",
};

const addBranchSchema = yup.object().shape({
    branchName: yup.string().required(errorMessages.required),
    branchAddress: yup.string().required(errorMessages.required),
    country: yup.string().required(errorMessages.required),
    state: yup.string().required(errorMessages.required),
});

const AddBranch = () => {
  const navigate = useNavigate();
  let countries = Country.getAllCountries().map((country) => ({
    text: country.name,
    value: country.isoCode,
  }));

  const [selectedCountry, setSelectedCountry] = useState<string>("");
  const [, setSelectedState] = useState<string>("");
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const { fetchOrganization, fetchEntireBranches } = useUpdateRecoilAtom();

  let states = State.getStatesOfCountry(selectedCountry).map((state) => ({
    text: state.name,
    value: state.isoCode,
  }));

  const handleSubmit = (values: AddBranchProps, {resetForm}) => {
    setIsLoading(true);
    const payload = {
      name: values.branchName,
      address: values.branchAddress,
      country: values.country,
      state: values.state,
    };
    addBranch(payload).then((response) => {
        setIsLoading(false);
        if(response?.success) {
          setShowSuccessModal(true);
          resetForm();
          fetchOrganization();
          fetchEntireBranches();
          navigate("/organization/branches")
        }
      })
  };

  return (
    <div>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 cursor-pointer w-fit"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6">
        <h1 className="font-poppins-medium text-18 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-[33px]">
          Add new branch
        </h1>
        <div className="bg-[#F5F5F5] pt-[95px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
          <Formik<AddBranchProps>
            initialValues={initialValues}
            onSubmit={handleSubmit}
            validationSchema={addBranchSchema}
          >
            {({ values, setFieldValue }) => (
              <Form>
                <div>
                  <FormikCustomInput
                    label="Branch name *"
                    id="branchName"
                    name="branchName"
                    placeholder="enter branch name"
                    type="text"
                    inputClassName="!bg-transparent"
                  />
                </div>
                <div className="mt-8">
                  <FormikCustomInput
                    label="Branch address *"
                    id="branchAddress"
                    name="branchAddress"
                    placeholder="enter branch address"
                    type="text"
                    inputClassName="!bg-transparent"
                  />
                </div>
                <div className="mt-8 grid tablet:grid-cols-2 gap-[33px]">
                  <div>
                    <FormikCustomSelect
                      label="Country *"
                      options={countries}
                      name="country"
                      value={selectedCountry || values.country}
                      onChange={(item: { value: string; text: string }) => {
                        setSelectedCountry(item.value);
                        setFieldValue("country", item.text);
                      }}
                    />
                  </div>
                  <div>
                    <FormikCustomSelect
                      label="State *"
                      options={states}
                      name="state"
                      onChange={(item: { value: string; text: string }) => {
                        setSelectedState(item.value);
                        setFieldValue("state", item.text);
                      }}
                      value={values.state}
                    />
                  </div>
                </div>
                <div className="mt-[120px] flex justify-end">
                  <CustomButton
                    type="submit"
                    title="Add branch"
                    handleClick={() => {}}
                    isLoading={isLoading}
                    size={ButtonProperties.SIZES.small}
                    variant={ButtonProperties.VARIANT.primary.name}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
      <SuccessModal
        visibility={showSuccessModal}
        toggleVisibility={setShowSuccessModal}
        text="Your new branch has been added successfully! You can view branch details in dashboard."
      />
    </div>
  );
};

export default AddBranch;
