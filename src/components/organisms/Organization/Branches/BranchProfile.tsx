import { Formik, Form } from "formik";
import { ArrowLeft2 } from "iconsax-react";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import { Country, State } from "country-state-city";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, getNameInitials, truncateText } from "../../../shared/helpers";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import { changeBranchHQ, getBranchById, updateBranch } from "../../../../api/organization";
import CustomToggle from "../../../atoms/CustomToggle/CustomToggle";
import { useRecoilState, useRecoilValue } from "recoil";
import { getBranchByIdAtom } from "../../../../recoil/atom/organizationAtom";
import { BiSave } from "react-icons/bi";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import AddBranchManager from "./AddBranchManager";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import FormikCustomPhoneInput from "../../../atoms/CustomInput/FormikCustomPhoneInput";
import { AiOutlineEdit } from "react-icons/ai";
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";
import { FaSpinner } from "react-icons/fa6";
import Loader from "../../../atoms/Loader";


const BranchProfile = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { isEdit, id } = location?.state || ""
  let countries = Country.getAllCountries().map((country) => ({
    text: country.name,
    value: country.isoCode,
  }));

  const [selectedCountry, setSelectedCountry] = useState<string>("");
  const [, setSelectedState] = useState<string>("");
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [showManagerModal, setShowManagerModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isToggle, setIsToggle] = useState<boolean>(false);
  const [isEditActive, setIsEditActive] = useState<boolean>(false);
  const [, setBranchByIdAtom] = useRecoilState(getBranchByIdAtom);
  const getBranchByIdValue = useRecoilValue(getBranchByIdAtom);
  const { fetchOrganization, fetchEntireBranches } = useUpdateRecoilAtom();


  const initialCountry = countries.filter((item) => item.text === getBranchByIdValue?.country)[0]?.value 

  let states = State.getStatesOfCountry(initialCountry).map((state) => ({
    text: state.name,
    value: state.isoCode,
  }));

  
  const fetchBranchById = () => {
    setIsFetching(true);
    getBranchById(id).then((response) => {
        if(response?.success){
            setIsFetching(false);
            setBranchByIdAtom(response.data)
        }
    })
  }

  useEffect(() => {
    fetchBranchById();
    if(!id) {
      navigate("/organization/branches")
    }
  }, [id, isToggle]);

  if(isFetching) {
    return <div>
     <Loader/>
    </div>
  }
    
    
  let initialValues = {
    branchName: getBranchByIdValue?.name || "",
    branchAddress: getBranchByIdValue?.address || "",
    country: getBranchByIdValue?.country || "",
    branchContact: getBranchByIdValue?.business?.business_phone_contact || "",
    state: getBranchByIdValue?.state || "",
    noOfDepartment: getBranchByIdValue?.meta?.total_departments || "",
    noOfStaff: getBranchByIdValue?.meta?.total_staffs || "",
    companyEmail: getBranchByIdValue?.business?.business_email || "",
    companyWebsite: getBranchByIdValue?.business?.business_website || "",
  };

  const handleSubmit = (values) => {
    setIsLoading(true);
    const payload = {
      branchId: getBranchByIdValue?.id,
      name: values.branchName,
      address: values.branchAddress,
      country: values.country,
      state: values.state,
      branchPhoneNumber: values.branchContact 
    };
    updateBranch(payload).then((response) => {
      setIsLoading(false);
      if (response?.success) {
        setIsEditActive(false)
        fetchBranchById();
        setShowSuccessModal(true);
        fetchOrganization();
        fetchEntireBranches();
      }
    });
  };


  const handleChangeHQ = (isToggle) => {
    setIsToggle(isToggle);

    if (isToggle) {
      changeBranchHQ({branchId: getBranchByIdValue.id}).then((response) => {
        if(response.success) {
          clustarkToast(NotificationTypes.SUCCESS, response.message);
        } 
      })
    } 
  }

  return (
    <div>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 cursor-pointer"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6">
        <div className="bg-[#F5F5F5] pb-[60px] pt-6 px-7 rounded-bl-[10px] rounded-br-[10px]">
          <div>
            <Formik
              initialValues={initialValues}
              onSubmit={handleSubmit}
              enableReinitialize
            >
              {({ values, setFieldValue }) => (
                <Form>
                  <div className="font-poppins-medium rounded-tl-[10px] rounded-tr-[10px] bg-purple-light pl-10 pr-7 py-[30px]">
                    <div className="flex justify-between">
                      <h1 className="text-18 text-purple-dark">Branch profile</h1>
                      {(isEditActive || isEdit) ? (
                        <div>
                          <CustomButton
                            leftIcon={<BiSave size={20}/>}
                            className="!w-[102px] !h-10"
                            title="Save"
                            type="submit"
                            handleClick={() => {}}
                            isLoading={isLoading}
                            variant={ButtonProperties.VARIANT.primary.name}
                          />
                        </div>
                      ) : (

                        <div className="!w-[87px] !h-10 gap-2 bg-purple-dark rounded flex justify-center items-center text-white" onClick={() => {setIsEditActive(true)}}>
                          <AiOutlineEdit size={18}/> Edit
                        </div>
                      )}
                    </div>
                  </div>
                  <div className=" bg-white py-7 px-8 rounded-xl">
                    <div className="grid grid-cols-4 gap-11 border-b border-neutral-[#B2BBC6] pb-10">
                      <div className="border-[0.5px] py-7 flex flex-col justify-center items-center border-[#E8E8E8] rounded-[10px]">
                        <div>
                          <div className="w-[120px] h-[120px] p-2 bg-purple-light-hover rounded-full">
                            <div className=" w-full h-full flex justify-center items-center bg-purple-light-active rounded-full">
                              <p className="text-[80px] text-purple-normal font-poppins-medium">
                                {getNameInitials(getBranchByIdValue?.name || "?")}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="mt-5">
                          <p className="text-neutral-dark font-medium font-poppins text-20 text-center">
                            {truncateText(getBranchByIdValue?.name, 20)}
                          </p>
                          <p className="pt-3.5 px-1 text-center text-neutral-normal">
                            {getBranchByIdValue?.address || ""}
                          </p>
                        </div>
                      </div>
                      <div className="col-span-3">
                        {/* <h1 className="text-17 text-purple-dark font-poppins-medium pb-11">
                          Branch details
                        </h1> */}
                        <div className="pl-3.5">
                          <div className="grid grid-cols-3 ">
                            <label
                              htmlFor="branchName"
                              className="flex items-center text-neutral-normal"
                            >
                              Branch Name
                            </label>
                            <div className="col-span-2">
                              <FormikCustomInput
                                id="branchName"
                                name="branchName"
                                type="text"
                                disabled={!isEdit && !isEditActive}
                              />
                            </div>
                          </div>
                          <div className="grid grid-cols-3 mt-4">
                            <label
                              htmlFor="branchAddress"
                              className="flex items-center text-neutral-normal"
                            >
                              Branch Address
                            </label>
                            <div className="col-span-2">
                              <FormikCustomInput
                                id="branchAddress"
                                name="branchAddress"
                                type="text"
                                disabled={!isEdit && !isEditActive }
                              />
                            </div>
                          </div>
                          <div className="grid grid-cols-3 mt-4">
                            <label
                              htmlFor="companyEmail"
                              className="flex items-center text-neutral-normal"
                            >
                              Company Email
                            </label>
                            <div className="col-span-2">
                              <FormikCustomInput
                                id="companyEmail"
                                name="companyEmail"
                                type="text"
                                disabled
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-[30px] grid grid-cols-2 gap-[55px]">
                      <div>
                        <div className="grid grid-cols-2">
                          <label
                            htmlFor="country"
                            className="flex items-center text-neutral-normal"
                          >
                            Country
                          </label>
                          <div>
                            <FormikCustomSelect
                              options={countries}
                              name="country"
                              value={selectedCountry || values.country}
                              onChange={(item: {
                                value: string;
                                text: string;
                              }) => {
                                setSelectedCountry(item.value);
                                setFieldValue("country", item.text);
                              }}
                              disabled={!isEdit && !isEditActive}
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-2 mt-4">
                            <label
                              htmlFor="state"
                              className="flex items-center text-neutral-normal"
                            >
                              State
                            </label>
                            <div >
                              <FormikCustomSelect
                                options={states}
                                name="state"
                                onChange={(item: {
                                  value: string;
                                  text: string;
                                }) => {
                                  setSelectedState(item.value);
                                  setFieldValue("state", item.text);
                                }}
                                value={values.state}
                                disabled={!isEdit && !isEditActive}
                              />
                            </div>
                          </div>
                        <div className="grid grid-cols-2 mt-4">
                          <label
                            htmlFor="companyWebsite"
                            className="flex items-center text-neutral-normal"
                          >
                            Company website
                          </label>
                          <FormikCustomInput
                            id="companyWebsite"
                            name="companyWebsite"
                            type="text"
                            disabled
                          />
                        </div>
                        <div className="grid grid-cols-2 mt-4">
                          <label
                            htmlFor="companySize"
                            className="flex items-center text-neutral-normal"
                          >
                            Company size
                          </label>
                          <FormikCustomInput
                            id="companySize"
                            name="companySize"
                            type="text"
                            disabled
                          />
                        </div>
                      </div>
                      <div className="">
                        <div className="grid grid-cols-2">
                          <label
                            htmlFor="branchContact"
                            className="flex items-center text-neutral-normal"
                          >
                            Branch contact
                          </label>
                          <div className="w-full">

                            <FormikCustomPhoneInput
                              name="branchContact"
                              id="branchContact"
                              value={values.branchContact}
                              onChange={(value: string) => {
                                setFieldValue("branchContact", value);
                              }}
                              disabled={!isEdit && !isEditActive }
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-2 mt-4">
                          <label
                            htmlFor="noOfDepartment"
                            className="flex items-center text-neutral-normal"
                          >
                            Total departments
                          </label>
                          <FormikCustomInput
                            id="noOfDepartment"
                            name="noOfDepartment"
                            type="text"
                            disabled
                          />
                        </div>
                        <div className="grid grid-cols-2 mt-4">
                          <label
                            htmlFor="noOfStaff"
                            className="flex items-center text-neutral-normal"
                          >
                            Total staff
                          </label>
                          <FormikCustomInput
                            id="noOfStaff"
                            name="noOfStaff"
                            type="text"
                            disabled
                          />
                        </div>
                        <div className="grid grid-cols-2 mt-4 py-3">
                          <label
                            htmlFor="country"
                            className="flex items-center text-neutral-normal"
                          >
                            Assign branch as HQ:
                          </label>
                          <div className="flex justify-end">
                            <CustomToggle
                              isOn={getBranchByIdValue?.is_hq ? getBranchByIdValue?.is_hq : isToggle}
                              onToggle={(toggle) => {handleChangeHQ(toggle) }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white py-7 px-8 rounded-xl mt-5">
                    <div className="flex justify-between py-11">
                      <h1 className="text-17 text-purple-dark font-poppins-medium">
                        Manager Information
                      </h1>
                      {getBranchByIdValue?.branchManager && (
                        <div onClick={() => setShowManagerModal(true)} className="flex gap-2.5 text-neutral-normal cursor-pointer">
                          <AiOutlineEdit size={18}/>
                          <p>Edit</p>
                        </div>
                      )}

                    </div>
                    <div className="grid grid-cols-4 gap-5 text-neutral-normal px-7 py-10 bg-[#BEC1C429] bg-opacity-[16%]">
                      <div>
                        <h1 className="text-15">Name</h1>
                        <p className="mt-7">{getBranchByIdValue?.branchManager?.staff?.staffPersonalInformations?.first_name} {getBranchByIdValue?.branchManager?.staff?.staffPersonalInformations?.last_name || "--"}</p>
                      </div>
                      <div>
                        <h1 className="text-15">Staff ID</h1>
                        <p className="mt-7">{getBranchByIdValue?.branchManager?.staff?.staff_identification_tag || "--"}</p>
                      </div>
                      <div>
                        <h1 className="text-15">Email</h1>
                        <p className="mt-7 underline"><a href={`tel:${getBranchByIdValue?.branchManager?.staff?.staffBasicInformations?.email || "--"}`}>{getBranchByIdValue?.branchManager?.staffBasicInformations?.user?.email || "--"}</a></p>
                      </div>
                      <div>
                        <h1 className="text-15">Phone</h1>
                        <p className="mt-7 underline"><a href={`tel:${getBranchByIdValue?.branchManager?.staff?.staffBasicInformations?.phone || "--"}`}>{getBranchByIdValue?.branchManager?.staff?.staffBasicInformations?.phone || "--"}</a></p>
                      </div>
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
      <SuccessModal
        visibility={showSuccessModal}
        toggleVisibility={setShowSuccessModal}
        text="Your changes has been saved"
      />
      <CustomModal visibility={showManagerModal} toggleVisibility={setShowManagerModal}>
          <AddBranchManager isEdit={showManagerModal} branchId={id} showManagerModal={setShowManagerModal} />
      </CustomModal>
    </div>
  );
};

export default BranchProfile;
