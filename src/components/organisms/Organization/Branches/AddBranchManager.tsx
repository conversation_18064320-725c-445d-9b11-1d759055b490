import React, { useEffect, useState } from "react";
import { Formik, Form } from "formik";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import { useRecoilState, useRecoilValue } from "recoil";
import {
  getBranchByIdAtom,
  getBranchManagerPermissionsAtom,
} from "../../../../recoil/atom/organizationAtom";
import { getBranchManagerPermissions, updateBranchManager } from "../../../../api/organization";
import { getStaff } from "../../../../api/staff";
import { getStaffAtom } from "../../../../recoil/atom/staff";
import * as yup from "yup";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import useUpdateR<PERSON>oil<PERSON>tom from "../../../shared/hooks/updateRecoilAtom";

const addManagerSchema = yup.object().shape({
  branchManager: yup.string().required(errorMessages.required),
});


const AddBranchManager = ({ isEdit, branchId, toogleVisibity }: any) => {
  const [activePermissions, setActivePermissions] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [StaffId, setStaffId] = useState('');
  const [permissionAtom, setPermissionAtom] = useRecoilState(getBranchManagerPermissionsAtom);
  const [staffAtom, setStaffAtom] = useRecoilState(getStaffAtom);
  const getBranchByIdValue = useRecoilValue(getBranchByIdAtom);
  const [selectedPermissions, setSelectedPermissions] = useState(getBranchByIdValue?.branchManager?.all_permissions.length > 0 ? getBranchByIdValue?.branchManager?.all_permissions : []);

  const { fetchBranches } = useUpdateRecoilAtom();


  const fetchPermissions = () => {
    getBranchManagerPermissions().then((response) => {
      if (response.success) {
        setPermissionAtom(response.data);
      }
    });
  };

  const fetchStaffs = () => {
    getStaff({}, { branch_id: branchId }).then((response) => {
      if (response.success) {
        setStaffAtom(response.data);
      }
    });
  };

  const handlePermissions = (permission) => {
    setActivePermissions(!activePermissions);
    setSelectedPermissions((prevState: any) => {
      const isAlreadySelected = prevState.some((item) => item === permission);
      if (isAlreadySelected) {
        return prevState.filter((item) => item !== permission);
      } else {
        return [...prevState, permission];
      }
    });
  };

  useEffect(() => {
    fetchPermissions();
    fetchStaffs();
  }, []);

  const handleSubmit = (values) => {
    setIsLoading(true);
    // const staffName = staffs?.filter((staff) => staff?.user?.first_name + " " + staff?.user?.last_name)[0]?.text
    const payload = {
      // staffId: staffs?.filter(() => (staffName === values.branchManager))[0]?.value || getBranchByIdValue?.branchManager?.staff_id,
      staffId: StaffId || getBranchByIdValue?.branchManager?.staff_id,
      permissions: selectedPermissions,
      branchId: getBranchByIdValue?.id || branchId
    }


    updateBranchManager(payload).then((response) => {
      setIsLoading(false);
      if (response.success) {
        setShowSuccessModal(true);
        fetchBranches();
        toogleVisibity(false);
      }
    })
  };

  const staffs = staffAtom?.data?.map((staff) => ({
    text: staff?.staffPersonalInformations?.first_name + " " + staff?.staffPersonalInformations?.last_name,
    value: staff.id,
  }));


  return (
    <div className="mb-10">
      <h1 className="font-poppins-medium text-18 rounded-tl-[10px] rounded-tr-[10px] text-purple-dark-hover bg-purple-light pl-10 py-6">
        Branch Manager
      </h1>
      <div className="px-7">
        <h1 className="mt-5 text-20 font-poppins-medium text-neutral-dark">
          {getBranchByIdValue?.business?.name}
        </h1>
        <div className="mt-10">
          <Formik
            initialValues={{
              branchManager: (isEdit && getBranchByIdValue?.branchManager?.staff?.user?.first_name + " " + getBranchByIdValue?.branchManager?.staff?.user?.last_name) || "",
            }}
            onSubmit={handleSubmit}
            enableReinitialize
            validationSchema={addManagerSchema}
          >
            {({ values, setFieldValue }) => (
              <Form>
                <div>
                  <label
                    htmlFor="branchManager"
                    className="font-poppins-medium text-neutral-dark"
                  >
                    Branch Manager
                  </label>
                  <FormikCustomSelect
                    value={values.branchManager}
                    name="branchManager"
                    options={staffs}
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("branchManager", item.text);
                      setStaffId(item.value);
                    }}
                    required
                  />
                </div>
                <div className="mt-8">
                  <h1 className="font-poppins-medium text-neutral-dark">
                    Permission
                  </h1>
                  <div className="border-[0.5px] border-[#B2BBC699] rounded-lg mt-4 px-3 py-4">
                    <div className="grid grid-cols-3 gap-x-5 gap-y-3">
                      {permissionAtom?.map((permission, index) => (
                        <div key={index}>
                          <p
                            className={`${selectedPermissions?.some(
                              (item: any) => item === permission?.code
                            ) && "bg-purple-light"
                              } text-center px-1 py-1 cursor-pointer text-12`}
                            onClick={() => handlePermissions(permission.code)}
                          >
                            {permission?.code}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex justify-end mt-20">
                  <CustomButton
                    type="submit"
                    className="!w-[128px] !h-10"
                    title={isEdit ? "Update" : "Add"}
                    isLoading={isLoading}
                    handleClick={() => { }}
                    variant={ButtonProperties.VARIANT.primary.name}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>

      <SuccessModal
        visibility={showSuccessModal}
        toggleVisibility={setShowSuccessModal}
        text="Manager information has been updated."
      />
    </div>
  );
};

export default AddBranchManager;
