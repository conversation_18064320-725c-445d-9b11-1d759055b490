import React, { useEffect, useRef, useState } from "react";
import StatisticsCard from "../../../atoms/Cards/StatisticsCard";
import { Data, Eye, Profile2User, UserEdit } from "iconsax-react";
import { TbBrandStackshare } from "react-icons/tb";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import { getNameInitials, truncateText } from "../../../shared/helpers";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../../shared/hooks";
import { FiPlus } from "react-icons/fi";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import { getBranches<PERSON>tom, getOrganizationAtom } from "../../../../recoil/atom/organizationAtom";
import { getBranches } from "../../../../api/organization";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import AddBranchManager from "./AddBranchManager";
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";
import Loader from "../../../atoms/Loader";

const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

const Branches = () => {
  const navigate = useNavigate();
  // const [groupCheck, setGroupCheck] = useState<boolean>(false);
  // const [checkedItems, setCheckedItems] = useState<any[]>([]);
  // const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showManagerModal, setShowManagerModal] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [, setBranchesAtom] = useRecoilState(getBranchesAtom);
  const [rowId, setRowId] = useState(0);
  const [branchId, setBranchId] = useState("");
  const { fetchOrganization } = useUpdateRecoilAtom();

  const getAllBranchesAtom = useRecoilValue(getBranchesAtom);
  const getOrganizationValue = useRecoilValue(getOrganizationAtom);

  const debounceSearch = useRef(debounce((q) => fetchBranches(q), 2000)).current;

  // const handleCheckboxChange = (event, row) => {
  //   const isChecked = event.target.checked;
  //   setCheckedItems((prevState: any) => {
  //     if (isChecked) {
  //       return [...prevState, row];
  //     } else {
  //       setGroupCheck(false);
  //       return prevState.filter((item) => item.id !== row.id);
  //     }
  //   });
  // };

  // const handleSelectAllChange = (event) => {
  //   const isChecked = event.target.checked;
  //   setGroupCheck(isChecked);

  //   if (isChecked) {
  //     setCheckedItems(getAllBranchesAtom.data);
  //   } else {
  //     setCheckedItems([]);
  //   }
  // };

  const fetchBranches = (q) => {
    setIsFetching(true);
    getBranches({ page: pageNumber, search: q }).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setBranchesAtom(res.data)
      }
    });
  };


  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  useEffect(() => {
    fetchBranches(searchQuery);
  }, [pageNumber]);

  useEffect(() => {
    fetchOrganization();
  }, []);

  if (isFetching && !searchQuery) {
    return <div>
      <Loader />
    </div>
  }



  const branchesStats = [
    {
      title: "Total branches",
      value: getOrganizationValue?.businesses?.meta?.total_branches,
      icon: <TbBrandStackshare className="rotate-90" size={24} />,
      iconBackgroundColor: '#3730A399',
      valueText: "Branches",
      cardBackgroundColor: "#EEF2FFCC",
    },
    {
      title: "Total departments",
      value: getOrganizationValue?.businesses?.meta?.total_departments,
      icon: <Data className="rotate-90" size={24} />,
      iconBackgroundColor: '#B3AA0199',
      valueText: "Departments",
      cardBackgroundColor: "#FDFFE8CC",
    },
    {
      title: "Total employees",
      value: getOrganizationValue?.businesses?.meta?.total_staffs,
      icon: <Profile2User size={24} />,
      iconBackgroundColor: '#09778399',
      valueText: "Employees",
      cardBackgroundColor: "#EBFDFFCC",
    },
  ];

  const columns = [
    {
      Header: (
        <div className="flex">
          {/* <CustomCheckBox
          checked={groupCheck}
            onChange={(e: any) => handleSelectAllChange(e)}
            customClass="!mr-3"
          />{" "} */}
          <p className="mt-1">Branch name</p>
        </div>
      ),
      accessor: "name",
      Cell: (row: any) => (
        <div className="flex  items-center">
          {/* <CustomCheckBox checked={checkedItems.some((item:any) => item.id === row.cell.row.original.id)}  onChange={(e:any) => { handleCheckboxChange(e, row.cell.row.original)}} customClass="!mr-3" /> */}
          <div className="font-poppins-medium flex justify-center items-center gap-3">
            <p className="w-10 h-10 text-20 rounded-full bg-accent-blue-light text-accent-blue-normal font-poppins-medium font-bold flex justify-center items-center">
              {getNameInitials(row.cell.row.original.name)}
            </p>
            <div>
              <div className="flex capitalize">
                {truncateText(row.cell.value, 20)}
                {row.cell.row.original.is_hq && (
                  <p className=" w-3.5 h-3.5 -translate-y-2 text-[5px] rounded-full bg-purple-light-active text-purple-dark font-poppins-medium font-bold flex justify-center items-center">
                    HQ
                  </p>
                )}
              </div>
              <p className="font-poppins text-[#6B788E]">
                {truncateText(row.cell.row.original.address, 30)}
              </p>
            </div>
          </div>
        </div>
      ),
    },

    {
      Header: "Branch manager",
      accessor: "branchManager.staff.user.first_name",
      Cell: (row: any) => (<p> {row.cell.row.original.branchManager ? `${row.cell.value} ${row.cell.row.original.branchManager?.staff.user.last_name}` : "--"}</p>
      ),
    },
    {
      Header: "Staff ID",
      accessor: "branchManager.staff.staff_identification_tag",
      Cell: (row: any) => (<p className="rounded-2xl bg-[#F5F6F7] w-fit px-2 py-1 font-poppins-medium">{row.cell.value || "--"} </p>),
    },
    {
      Header: "Total departments",
      accessor: "meta.total_departments",
      Cell: (row: any) => (<p>{row.cell.value || "--"}</p>),
    },

    {
      Header: "Total employees",
      accessor: "meta.total_staffs",
      Cell: (row: any) => (<p>{row.cell.value || "--"} </p>),
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative" >
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown >
              <ul className="text-14 text-neutral-dark" ref={node}>
                {!row.cell.row.original.branchManager && (
                  <li onClick={() => { setShowManagerModal(true); setShowDropdown(false); setBranchId(row.cell.row.original.id) }} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                    <FiPlus size={18} />
                    Add Branch Manager
                  </li>
                )}
                <li onClick={() => navigate(`/organization/branch-profile`, { state: { id: row.cell.row.original.id } })} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <Eye size={18} />
                  View
                </li>
                <li onClick={() => { navigate(`/organization/branch-profile`, { state: { isEdit: "true", id: row.cell.row.original.id } }) }} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <UserEdit size={18} />
                  Edit
                </li>
                {/* <li className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] !text-alert-text-error cursor-pointer">
                  <PiTrash size={18} />
                  Delete
                </li> */}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  return (
    <>
      <div>
        {/* <h1 className="font-poppins font-semibold text-24">Branches<//h1> */}
        <div className=" grid grid-cols-3 gap-5 ">
          {branchesStats?.map((item, index) => (
            <div key={index}>
              <StatisticsCard
                backgroundColor={item.cardBackgroundColor}
                key={index}
                title={item.title}
                value={item.value}
                icon={item.icon}
                iconBackgroundColor={item?.iconBackgroundColor}
                valueText={item.valueText}
              />
            </div>
          ))}
        </div>
        {/* <div className="pt-10 flex justify-end">
        <CustomButton
          className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
          isTransparent={true}
          handleClick={() => {navigate("/organization/add-branch")}}
          leftIcon={<FiPlus className="ml-3" size={20} />}
          title="Add branch"
        />
      </div> */}
        <div className=" my-10 py-[23px]">

          {(getAllBranchesAtom?.data?.length > 0 || searchQuery) ? (

            <CustomTable
              data={getAllBranchesAtom?.data || []}
              meta={getAllBranchesAtom?.meta}
              columns={columns}
              handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
              handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
              header={
                <div className="flex items-center h-[40px] px-2">
                  <h1 className=" text-center">
                    Branches
                  </h1>
                </div>
              }
            />
          ) : (
            <div className="flex justify-center items-center py-[120px]">
              <OrganizationEmptyState buttonTitle="Add New Branch" handleClick={() => navigate("/organization/add-branch")} />
            </div>
          )}
        </div>

        <CustomModal visibility={showManagerModal} toggleVisibility={setShowManagerModal}>
          <AddBranchManager toogleVisibity={setShowManagerModal} branchId={branchId} />
        </CustomModal>
      </div>
    </>
  );
};

export default Branches;
