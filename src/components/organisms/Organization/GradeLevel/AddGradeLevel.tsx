import { Formik, Form } from "formik";
import { ArrowLeft2 } from "iconsax-react";
import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import { addGradeLevel, updateGradeLevel } from "../../../../api/organization";
import * as yup from "yup";
import FormikSelectCurrency from "../../../atoms/CustomInput/FormikSelectCurrency";
import { Country } from "country-state-city";
import { useRecoilValue } from "recoil";
import { loggedUserAtom } from "../../../../recoil/atom/authAtom";
import { defaultBusinessAtom } from "../../../../recoil/atom/organizationAtom";

interface AddGradeLevelProps {
  levelName: string;
  description: string;
  gradeLevel: string;
  currency: string;
  amount: string;
}

const addBranchSchema = yup.object().shape({
  levelName: yup.string().required(errorMessages.required),
  // description: yup.string().required(errorMessages.required),
  gradeLevel: yup.string().required(errorMessages.required),
  // currency: yup.string().required(errorMessages.required),
  // amount: yup.string().required(errorMessages.required),
});

const AddGradeLevel = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isEdit, data, id } = location.state || ""
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const getUser = useRecoilValue(loggedUserAtom);
  const defaultBusiness = useRecoilValue(defaultBusinessAtom);

  const defaultCountry = Country.getAllCountries()
    .filter((value) => value.name === getUser?.businesses[0]?.country)
    .map((country) => ({
      text: country?.currency,
      value: country?.isoCode,
    }));

    let initialValues = {
    levelName: data?.name || "",
    description: data?.description || "",
    gradeLevel: data?.code || "",
    currency: data?.currency ? data?.currency : defaultCountry[0]?.text || defaultBusiness?.currency,
    amount: data?.salary || "",
  };

  const handleSubmit = (values: AddGradeLevelProps, { resetForm }) => {
    setIsLoading(true);
    const payload = {
      name: values.levelName,
      description: values.description,
      code: values.gradeLevel,
      estimated_salary: values.amount,
      currency: values.currency
    };
    if(isEdit){
      updateGradeLevel({...payload, gradeLevelId: id}).then((response) => {
        setIsLoading(false);
        if(response?.success) {
          setShowSuccessModal(true);
        }
      })

    } else  {
      addGradeLevel(payload).then((response) => {
        setIsLoading(false);
        if(response?.success) {
          setShowSuccessModal(true);
          resetForm();
          navigate("/organization/grade-level")
        }
      })
    }
  };



  return (
    <div>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 cursor-pointer"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6">
        <h1 className="font-poppins-medium text-18 rounded-tl-[10px] rounded-tr-[10px] text-purple-dark bg-purple-light pl-10 py-[33px]">
          {isEdit ? "Edit" : "Add"} grade level
        </h1>
        <div className="bg-[#F5F5F5] pt-[95px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
          <Formik<AddGradeLevelProps>
            initialValues={initialValues}
            onSubmit={handleSubmit}
            validationSchema={addBranchSchema}
            enableReinitialize
          >
            {({ values, setFieldValue }) => (
              <Form>
                <div>
                  <FormikCustomInput
                    label="Level name *"
                    id="levelName"
                    name="levelName"
                    placeholder="e.g entry level"
                    type="text"
                    maxLength={40}
                    inputClassName="!bg-transparent"
                  />
                </div>
                <div className="mt-8">
                  <FormikCustomInput
                    label="Description"
                    id="description"
                    name="description"
                    placeholder="write a short description"
                    maxLength={50}
                    type="text"
                    inputClassName="!bg-transparent"
                  />
                </div>
                <div className="mt-8 grid tablet:grid-cols-1 gap-[33px]">
                  <div>
                    <FormikCustomInput
                      label="Grade level *"
                      id="gradeLevel"
                      name="gradeLevel"
                      placeholder="e.g EL-02"
                      maxLength={20}
                      type="text"
                      inputClassName="!bg-transparent"
                    />
                  </div>
                  {/* <div>
                    <FormikSelectCurrency
                      label="Expected salary"
                      // options={countries}
                      name="currency"
                      value={values.currency}
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("currency", item.text);
                      }}
                      handleAmount={(amount) => setFieldValue("amount", amount)}
                      amountValue={data?.salary}
                    />
                  </div> */}
                </div>
                <div className="mt-[120px] flex justify-end">
                  <CustomButton
                    type="submit"
                    title={`${isEdit ? "Save" : "Add"}`}
                    handleClick={() => {}}
                    isLoading={isLoading}
                    size={ButtonProperties.SIZES.small}
                    variant={ButtonProperties.VARIANT.primary.name}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
      <SuccessModal
        visibility={showSuccessModal}
        toggleVisibility={setShowSuccessModal}
        text="Your new grade level has been added successfully! You can view grade level details in dashboard."
      />
    </div>
  );
};

export default AddGradeLevel;
