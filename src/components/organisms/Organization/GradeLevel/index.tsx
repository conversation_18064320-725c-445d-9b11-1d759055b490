import React, { useEffect, useRef, useState } from "react";
import { UserEdit } from "iconsax-react";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import { truncateText } from "../../../shared/helpers";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../../shared/hooks";
import { FiPlus } from "react-icons/fi";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import { getGradeLevelsAtom } from "../../../../recoil/atom/organizationAtom";
import { deleteGradeLevel, getGradeLevels } from "../../../../api/organization";
import { PiTrash } from "react-icons/pi";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { FaSpinner } from "react-icons/fa6";
import Loader from "../../../atoms/Loader";

const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

const GradeLevel = () => {
  const navigate = useNavigate();
  // const [groupCheck, setGroupCheck] = useState<boolean>(false);
  // const [checkedItems, setCheckedItems] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [, setGradeLevelAtom] = useRecoilState(getGradeLevelsAtom);
  const [rowId, setRowId] = useState(0);

  const getGradeLevelValue = useRecoilValue(getGradeLevelsAtom);

  // const handleCheckboxChange = (event, row) => {
  //   const isChecked = event.target.checked;
  //   setCheckedItems((prevState: any) => {
  //     if (isChecked) {
  //       return [...prevState, row];
  //     } else {
  //       setGroupCheck(false);
  //       return prevState.filter((item) => item.id !== row.id);
  //     }
  //   });
  // };

  // const handleSelectAllChange = (event) => {
  //   const isChecked = event.target.checked;
  //   setGroupCheck(isChecked);

  //   if (isChecked) {
  //     setCheckedItems(getAllBranchesAtom.data);
  //   } else {
  //     setCheckedItems([]);
  //   }
  // };

  const fetchGradeLevels = (q) => {
    setIsFetching(true);
    getGradeLevels({ page: pageNumber, search: q }).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setGradeLevelAtom(res.data);
      }
    });
  };

  const handleDeleteGradeLevel = (id) => {
    setIsLoading(true);
    deleteGradeLevel(id).then((res) => {
      setIsLoading(false);
      if (res.success) {
        setShowDropdown(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message)
        fetchGradeLevels(searchQuery);
      }
    });
  }

  // const handleBranchSearch = (query) => {
  //   searchBranch({page:pageNumber, search: query}).then((res) => {
  //     if(res.success) {
  //       setBranchesAtom(res.data)
  //     }
  //   });
  // }

  const debounceSearch = useRef(debounce((q) => fetchGradeLevels(q), 2000)).current;

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  useEffect(() => {
    fetchGradeLevels(searchQuery);
  }, [pageNumber]);

  if (isFetching && !searchQuery) {
    return <div>
      <Loader />
    </div>
  };

  const columns = [
    {
      Header: (
        <div className="flex">
          {/* <CustomCheckBox
          checked={groupCheck}
            onChange={(e: any) => handleSelectAllChange(e)}
            customClass="!mr-3"
          />{" "} */}
          <p className="mt-1">Grade level name</p>
        </div>
      ),
      accessor: "name",
      Cell: (row: any) => (
        <p className="text-[#101828]">
          {truncateText(row.cell.value, 40) || "--"}{" "}
        </p>
      ),
    },

    {
      Header: "Description",
      accessor: "description",
      Cell: (row: any) => <p>{truncateText(row.cell.value, 40) || "--"} </p>,
    },
    {
      Header: "GL",
      accessor: "code",
      Cell: (row: any) => <p className="uppercase">{truncateText(row.cell.value, 40) || "--"} </p>,
    },
    // {
    //   Header: "Salary",
    //   accessor: "estimated_salary",
    //   Cell: (row: any) => <p className="whitespace-nowrap">{`${row.cell.value.toLocaleString()}` || "--"} {row.cell.row.original.currency && <span className="text-[8px] bg-accent-blue-normal text-white px-1 rounded mb-1">{row.cell.row.original.currency}</span>}  </p>,
    // },
    {
      Header: "Total employees",
      accessor: "meta.total_staffs",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() => {
                    navigate(`/organization/add-grade-level`, {
                      state: {
                        isEdit: "true",
                        id: row.cell.row.original.id,
                        data: {
                          name: row.cell.row.original.name,
                          description: row.cell.row.original.description,
                          code: row.cell.row.original.code,
                          salary: row.cell.row.original.estimated_salary,
                          currency: row.cell.row.original.currency,
                        },
                      },
                    });
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <UserEdit size={18} />
                  Edit
                </li>

                {showDeleteWarn ? (
                  <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                    <div className="flex gap-3">
                      <PiTrash size={18} />
                      Are you sure?
                    </div>
                    <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                      <CustomButton isLoading={isLoading} title="Yes" handleClick={() => handleDeleteGradeLevel(row.cell.row.original.id)} className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer" />
                      <span onClick={() => { setShowDeleteWarn(false) }} className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center">No</span>
                    </div>
                  </li>
                ) : (
                  <li onClick={() => setShowDeleteWarn(true)} className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer">
                    <PiTrash size={18} />
                    Delete
                  </li>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  return (
    <>
      <div ref={node}>
        {/* <h1 className="font-poppins font-semibold text-24">Grade Level</h1> */}

        <div className=" my-10  py-[23px]">
          {getGradeLevelValue?.data?.length > 0 || searchQuery ? (
            <CustomTable
              data={getGradeLevelValue?.data || []}
              meta={getGradeLevelValue?.meta}
              columns={columns}
              // filterListOptions={["State", "Country"]}
              handleFilter={(e) => console.log(e)}
              handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
              // checkedItems={checkedItems}
              handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
              header={
                <div className="flex justify-between px-2 items-center h-[40px]">
                  <h1>
                    Grade levels
                  </h1>
                  <div className="bg-black rounded">
                    <CustomButton
                      className="!w-[150px] !h-10 !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md "
                      isTransparent={true}
                      handleClick={() => {
                        navigate("/organization/add-grade-level");
                      }}
                      leftIcon={<FiPlus className="ml-3" size={20} />}
                      title="Add grade level"
                    />
                  </div>
                </div>
              }
            />
          ) : (
            <div className="flex justify-center items-center py-[120px]">
              <OrganizationEmptyState
                buttonTitle="Add new grade level"
                handleClick={() => navigate("/organization/add-grade-level")}
              />
            </div>
          )}
        </div>


      </div>
    </>
  );
};

export default GradeLevel;
