import { Formik, Form } from "formik";
import { ArrowLeft2 } from "iconsax-react";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import {
  ButtonProperties,
  getNameInitials,
  truncateText,
} from "../../../shared/helpers";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import {
  getDepartmentById,
  updateDepartment,
} from "../../../../api/organization";
import { useRecoilState, useRecoilValue } from "recoil";
import { getDepartmentByIdAtom } from "../../../../recoil/atom/organizationAtom";
import { BiSave } from "react-icons/bi";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import AddDepartmentHead from "./AddDepartmentHead";
import { AiOutlineEdit } from "react-icons/ai";
import moment from "moment";
import Loader from "../../../atoms/Loader";

const DepartmentInformation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isEdit, id } = location?.state || "";

  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [showManagerModal, setShowManagerModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isEditActive, setIsEditActive] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [, setDepartmentByIdAtom] = useRecoilState(getDepartmentByIdAtom);
  const getDepartmentByIdValue = useRecoilValue(getDepartmentByIdAtom);


  const fetchDepartmentById = () => {
    setIsFetching(true);
    getDepartmentById(id).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setDepartmentByIdAtom(res.data);
      }
    });
  };

  useEffect(() => {
    fetchDepartmentById();
    if(!id) {
      navigate("/organization/department")
    }
  }, [id]);


  let initialValues = {
    branchName: getDepartmentByIdValue?.department?.branch?.name || "",
    departmentName: getDepartmentByIdValue?.department?.name || "",
    noOfStaffs: getDepartmentByIdValue?.staffs?.meta?.total,
    createdAt: moment(getDepartmentByIdValue?.department?.created_at).format("DD/MM/YYYY") || "",
  };

  const handleSubmit = (values) => {
    setIsLoading(true);
    const payload = {
      departmentId: id,
      name: values.departmentName,
    };
    updateDepartment(payload).then((response) => {
      setIsLoading(false);
      if (response?.success) {
        setIsEditActive(false);
        fetchDepartmentById();
        setShowSuccessModal(true);
      }
    });
  };

  if(isFetching) {
    return <div>
      <Loader/>
    </div>
  };

  return (
    <div>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 cursor-pointer"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6">
        <div className="bg-[#F5F5F5] pb-[60px] pt-6 px-7 rounded-bl-[10px] rounded-br-[10px]">
          <div>
            <Formik
              initialValues={initialValues}
              onSubmit={handleSubmit}
              enableReinitialize
            >
              {({values}) => (
                <Form>
                  <div className="font-poppins-medium rounded-tl-[10px] rounded-tr-[10px] bg-purple-light pl-10 pr-7 py-[30px]">
                    <div className="flex justify-between">
                      <h1 className="text-18 text-purple-dark">
                        Department details
                      </h1>
                    </div>
                  </div>
                  <div className=" bg-white pt-7 pb-16 px-8 rounded-xl">
                    <div className="flex justify-between border-b py-7 border-[#E8E8E8] rounded-[10px]">
                      <div className="flex gap-6">
                        <div>
                          <div className="w-[100px] h-[100px] p-2 bg-purple-light-hover rounded-full">
                            <div className=" w-full h-full flex justify-center items-center bg-purple-light-active rounded-full">
                              <p className="text-[70px] text-purple-dark font-poppins-medium">
                                {getNameInitials(
                                  getDepartmentByIdValue?.department?.name || "?"
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="flex justify-center items-center">
                          <div>
                            <p className="text-neutral-dark font-medium font-poppins-medium text-20">
                              {truncateText(getDepartmentByIdValue?.department?.name, 20) || ""}
                            </p>
                            <p className="pt-2 text-neutral-normal">
                              {getDepartmentByIdValue?.department?.branch?.name || ""}
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-center items-center">
                          {isEditActive || isEdit ? (
                            <div>
                              <CustomButton
                                leftIcon={<BiSave size={20} />}
                                className="!w-[102px] !h-10"
                                title="Save"
                                type="submit"
                                handleClick={() => {}}
                                isLoading={isLoading}
                                variant={ButtonProperties.VARIANT.primary.name}
                              />
                            </div>
                          ) : (
                            <div
                              className="!w-[87px] !h-10 gap-2 cursor-pointer bg-purple-dark rounded flex justify-center items-center text-white"
                              onClick={() => {
                                setIsEditActive(true);
                              }}
                            >
                             <AiOutlineEdit size={18}/> Edit
                            </div>
                          )}
                      </div>
                    </div>
                    <div className="border-neutral-[#B2BBC6] mt-8">
                      <div className="">
                        {/* <h1 className="text-20 text-neutral-dark font-poppins-medium pb-11">
                          Department information
                        </h1> */}
                        <div className="pl-3.5">
                          <div className="grid grid-cols-4 ">
                            <label
                              htmlFor="branchName"
                              className="flex items-center text-neutral-normal"
                            > 
                              Branch Name
                            </label>
                            <div className="col-span-3">
                              <FormikCustomInput
                                id="branchName"
                                name="branchName"
                                type="text"
                                disabled
                              />
                            </div>
                          </div>
                          <div className="grid grid-cols-4 mt-4">
                            <label
                              htmlFor="departmentName"
                              className="flex items-center text-neutral-normal"
                            >
                              Department name
                            </label>
                            <div className="col-span-3">
                              <FormikCustomInput
                                id="departmentName"
                                name="departmentName"
                                type="text"
                                disabled={!isEdit && !isEditActive}
                              />
                            </div>
                          </div>
                          <div className="grid grid-cols-4 mt-4">
                            <label
                              htmlFor="noOfStaffs"
                              className="flex items-center text-neutral-normal"
                            >
                              No. Of employees
                            </label>
                            <div className="col-span-3">
                              <FormikCustomInput
                                id="noOfStaffs"
                                name="noOfStaffs"
                                type="text"
                                value={values.noOfStaffs}
                                disabled
                              />
                            </div>
                          </div>
                          <div className="grid grid-cols-4 mt-4">
                            <label
                              htmlFor="createdAt"
                              className="flex items-center text-neutral-normal"
                            >
                              Date created
                            </label>
                            <div className="col-span-3">
                              <FormikCustomInput
                                id="createdAt"
                                name="createdAt"
                                type="text"
                                disabled
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                   
                  </div>
                  <div className="bg-white py-7 px-8 rounded-xl mt-10">
                    <div className="flex justify-between py-11">
                      <h1 className="text-18 text-purple-dark font-poppins-medium">
                        Department head 
                      </h1>
                      {getDepartmentByIdValue?.department?.departmentHead && (
                        <div
                          onClick={() => setShowManagerModal(true)}
                          className="flex gap-2.5 text-neutral-normal cursor-pointer"
                        >
                          <AiOutlineEdit size={18} />
                          <p>Edit</p>
                        </div>
                      )}
                    </div>
                    <div className="grid grid-cols-4 gap-5 text-neutral-normal px-7 py-10 bg-[#BEC1C429] bg-opacity-[16%]">
                      <div>
                        <h1 className="text-15">Name</h1>
                        <p className="mt-7">
                          {
                            getDepartmentByIdValue?.department?.departmentHead?.staff?.staffPersonalInformations
                              ?.first_name
                          }{" "}
                          {getDepartmentByIdValue?.department?.departmentHead?.staff?.staffPersonalInformations
                            ?.last_name || "--"}
                        </p>
                      </div>
                      <div>
                        <h1 className="text-15">Staff ID</h1>
                        <p className="mt-7">
                          {getDepartmentByIdValue?.department?.departmentHead?.staff
                            ?.staff_identification_tag || "--"}
                        </p>
                      </div>
                      <div>
                        <h1 className="text-15">Email</h1>
                        <p className="mt-7 underline">
                          <a
                            href={`tel:${
                              getDepartmentByIdValue?.department?.departmentHead?.staff?.staffBasicInformations
                                ?.email || "--"
                            }`}
                          >
                            {getDepartmentByIdValue?.department?.departmentHead?.staff?.staffBasicInformations
                              ?.email || "--"}
                          </a>
                        </p>
                      </div>
                      <div>
                        <h1 className="text-15">Phone</h1>
                        <p className="mt-7 underline">
                          <a
                            href={`tel:${
                              getDepartmentByIdValue?.department?.departmentHead?.staff?.staffBasicInformations
                                ?.phone || "--"
                            }`}
                          >
                            {getDepartmentByIdValue?.department?.departmentHead?.staff?.staffBasicInformations
                              ?.phone || "--"}
                          </a>
                        </p>
                      </div>
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
      <SuccessModal
        visibility={showSuccessModal}
        toggleVisibility={setShowSuccessModal}
        text="Your changes has been saved"
      />
      <CustomModal
        visibility={showManagerModal}
        toggleVisibility={setShowManagerModal}
      >
        <AddDepartmentHead
          isEdit={showManagerModal}
          showManagerModal={setShowManagerModal}
          departmentId={getDepartmentByIdValue?.department?.id}
          departmentName={getDepartmentByIdValue?.department?.name}
        />
      </CustomModal>
    </div>
  );
};

export default DepartmentInformation;
