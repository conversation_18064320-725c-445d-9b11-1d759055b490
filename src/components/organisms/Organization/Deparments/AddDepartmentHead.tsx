import React, { useEffect, useState } from "react";
import { Formik, Form } from "formik";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import { useRecoilState, useRecoilValue } from "recoil";
import { getDepartmentByIdAtom, getDepartmentHeadPermissionsAtom } from "../../../../recoil/atom/organizationAtom";
import { getDepartmentHeadPermissions, updateDepartmentHead } from "../../../../api/organization";
import * as yup from "yup";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";
import { getStaff } from "../../../../api/staff";
import { getStaffAtom } from "../../../../recoil/atom/staff";

const addDepartmentSchema = yup.object().shape({
  headOfDepartment: yup.string().required(errorMessages.required),
});

const AddDepartmentHead = ({ isEdit, departmentId, departmentName }: any) => {
  const [activePermissions, setActivePermissions] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [, setPermissionAtom] = useRecoilState(getDepartmentHeadPermissionsAtom);
  const [staffAtom, setStaffAtom] = useRecoilState(getStaffAtom);
  const getDepartmentByIdValue = useRecoilValue(getDepartmentByIdAtom);
  const getPermissionsValue = useRecoilValue(getDepartmentHeadPermissionsAtom);
  const [selectedPermissions, setSelectedPermissions] = useState(getDepartmentByIdValue?.department?.departmentHead?.all_permissions.length > 0 ? getDepartmentByIdValue?.department?.departmentHead?.all_permissions : []);
  const [staffId, setStaffId] = useState('')
  const { fetchDepartments } = useUpdateRecoilAtom();

  const fetchPermissions = () => {
    getDepartmentHeadPermissions().then((response) => {
      if (response.success) {
        setPermissionAtom(response.data);
      }
    });
  };

  const fetchStaffs = () => {
    getStaff({}, { department_id: departmentId }).then((response) => {
      if (response.success) {
        setStaffAtom(response.data);
      }
    });
  };

  const handlePermissions = (permission) => {
    setActivePermissions(!activePermissions);
    setSelectedPermissions((prevState: any) => {
      const isAlreadySelected = prevState.some((item) => item === permission);
      if (isAlreadySelected) {
        return prevState.filter((item) => item !== permission);
      } else {
        return [...prevState, permission];
      }
    });
  };

  useEffect(() => {
    fetchPermissions();
    fetchStaffs();
  }, []);


  const handleSubmit = (values) => {

    setIsLoading(true);
    // const staffName = staffs?.filter((staff) => staff?.user?.first_name + " " + staff?.user?.last_name)[0]?.text

    const payload = {

      // staffId: staffs?.filter(() => (staffName === values.headOfDepartment))[0]?.value,

      staffId: staffId,
      permissions: selectedPermissions,
      departmentId: departmentId
    }
    updateDepartmentHead(payload).then((response) => {
      setIsLoading(false);
      if (response.success) {
        setShowSuccessModal(true);
        fetchDepartments();
      }
    })
  };

  const staffs = staffAtom?.data?.map((staff) => ({
    text: staff?.user?.first_name + " " + staff?.user?.last_name,
    value: staff.id,
  }));

  return (
    <div className="mb-10">
      <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-6">
        Head of Department
      </h1>
      <div className="px-7">
        <h1 className="mt-5 text-20 font-poppins-medium text-neutral-dark">
          {departmentName}
        </h1>
        <div className="mt-10">
          <Formik
            initialValues={{
              headOfDepartment: (isEdit && getDepartmentByIdValue?.department?.departmentHead?.staff?.user?.first_name + " " + getDepartmentByIdValue?.department?.departmentHead?.staff?.user?.last_name) || "",
            }}
            onSubmit={handleSubmit}
            enableReinitialize
            validationSchema={addDepartmentSchema}
          >
            {({ values, setFieldValue }) => (
              <Form>
                <div>
                  <label
                    htmlFor="headOfDepartment"
                    className="font-poppins-medium text-neutral-dark"
                  >
                    Head of Department
                  </label>
                  <FormikCustomSelect
                    value={values.headOfDepartment}
                    name="headOfDepartment"
                    options={staffs}
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("headOfDepartment", item.text);
                      setStaffId(item.value)
                    }}
                    required
                  />
                </div>
                <div className="mt-8">
                  <h1 className="font-poppins-medium text-neutral-dark">
                    Permission
                  </h1>
                  <div className="border-[0.5px] border-[#B2BBC699] rounded-lg mt-4 px-3 py-4">
                    <div className="grid grid-cols-3 gap-x-5 gap-y-3">
                      {getPermissionsValue?.map((permission, index) => (
                        <div key={index}>
                          <p
                            className={`${selectedPermissions?.some(
                              (item: any) => item === permission?.code
                            ) && "bg-purple-light"
                              } text-center px-1 py-1 cursor-pointer text-12`}
                            onClick={() => handlePermissions(permission.code)}
                          >
                            {permission?.code}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex justify-end mt-20">
                  <CustomButton
                    type="submit"
                    className="!w-[128px] !h-10"
                    title={isEdit ? "Update" : "Add"}
                    isLoading={isLoading}
                    handleClick={() => { }}
                    variant={ButtonProperties.VARIANT.primary.name}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>

      <SuccessModal
        visibility={showSuccessModal}
        toggleVisibility={setShowSuccessModal}
        text="Manager information has been updated."
        route="/organization/department"
      />
    </div>
  );
};

export default AddDepartmentHead;
