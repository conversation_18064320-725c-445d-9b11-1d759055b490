import React, { useEffect, useRef, useState } from "react";
import StatisticsCard from "../../../atoms/Cards/StatisticsCard";
import { Data, Eye, Profile2User, UserEdit } from "iconsax-react";
import { TbBrandStackshare } from "react-icons/tb";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import { truncateText } from "../../../shared/helpers";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../../shared/hooks";
import { FiPlus } from "react-icons/fi";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import { getDepartmentsAtom, getOrganizationAtom } from "../../../../recoil/atom/organizationAtom";
import { getDepartments } from "../../../../api/organization";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import AddDepartmentHead from "./AddDepartmentHead";
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";
import Loader from "../../../atoms/Loader";

const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

const Departments = () => {
  const navigate = useNavigate();
  const [groupCheck, setGroupCheck] = useState<boolean>(false);
  const [checkedItems, setCheckedItems] = useState<any[]>([]);
  const [departmentId, setDepartmentId] = useState<string>("");
  const [departmentName, setDepartmentName] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showManagerModal, setShowManagerModal] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [rowId, setRowId] = useState(0);
  const [, setDepartmentAtom] = useRecoilState(getDepartmentsAtom);
  const { fetchOrganization } = useUpdateRecoilAtom();

  const debounceSearch = useRef(debounce((q) => fetchDepartments(q), 2000)).current;

  const getAllDepartmentsAtom = useRecoilValue(getDepartmentsAtom);
  const getOrganizationValue = useRecoilValue(getOrganizationAtom);

  const handleCheckboxChange = (event, row) => {
    const isChecked = event.target.checked;
    setCheckedItems((prevState: any) => {
      if (isChecked) {
        return [...prevState, row];
      } else {
        setGroupCheck(false);
        return prevState.filter((item) => item.id !== row.id);
      }
    });
  };

  const handleSelectAllChange = (event) => {
    const isChecked = event.target.checked;
    setGroupCheck(isChecked);

    if (isChecked) {
      setCheckedItems(getAllDepartmentsAtom.data);
    } else {
      setCheckedItems([]);
    }
  };

  const fetchDepartments = (q?: string) => {
    setIsFetching(true);
    getDepartments({ page: pageNumber, search: q }).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setDepartmentAtom(res.data)
      }
    });
  }

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  useEffect(() => {
    fetchDepartments(searchQuery)
  }, [pageNumber]);

  useEffect(() => {
    fetchOrganization();
  }, []);

  if (isFetching && !searchQuery) {
    return <div>
      <Loader />
    </div>
  };

  const departmentStats = [
    {
      title: "Total departments",
      value: getOrganizationValue?.businesses?.meta?.total_departments,
      icon: <TbBrandStackshare className="rotate-90" size={24} />,
      iconBackgroundColor: '#3730A399',
      valueText: "Departments",
      cardBackgroundColor: "#EEF2FFCC",
    },
    {
      title: "Total employees",
      value: getOrganizationValue?.businesses?.meta?.total_staffs,
      icon: <Profile2User size={24} />,
      iconBackgroundColor: '#B3AA0199',
      valueText: "Employees",
      cardBackgroundColor: "#FDFFE8CC",
    },
    {
      title: "Active employees",
      value: getOrganizationValue?.businesses?.meta?.total_staffs,
      icon: <Data className="rotate-90" size={24} />,
      iconBackgroundColor: '#09778399',
      valueText: "Active Employees",
      cardBackgroundColor: "#EBFDFFCC",
    },
  ];

  const columns = [
    {
      Header: (
        <div className="flex">
          {/* <CustomCheckBox
            checked={groupCheck}
            onChange={(e: any) => handleSelectAllChange(e)}
            customClass="!mr-3"
          /> */}
          <p className="mt-1">Department</p>
        </div>
      ),
      accessor: "name",
      Cell: (row: any) => (
        <div className="flex  items-center">
          {/* <CustomCheckBox checked={checkedItems.some((item:any) => item.id === row.cell.row.original.id)}  onChange={(e:any) => { handleCheckboxChange(e, row.cell.row.original)}} customClass="!mr-3" /> */}
          <p className="font-poppins-medium text-neutral-dark capitalize">
            {truncateText(row.cell.value, 20)}
          </p>
        </div>
      ),
    },

    {
      Header: "Branch",
      accessor: "branch.name",
      Cell: (row) => (<p> {row.cell.value || "--"}</p>
      ),
    },
    {
      Header: "Head of department",
      accessor: "departmentHead.staff.user.first_name",
      Cell: (row: any) => (<p>{row.cell.row.original.departmentHead ? `${row.cell.value} ${row.cell.row.original.departmentHead?.staff.user?.last_name}` : "--"}</p>),
    },

    {
      Header: "Total employees",
      accessor: "meta.total_staffs",
      Cell: (row: any) => (<p>{row.cell.value || "--"} </p>),
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative" >
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14  text-neutral-dark" ref={node}>
                {!row.cell.row.original.departmentHead && (
                  <li onClick={() => { setShowManagerModal(true); setDepartmentId(row.cell.row.original.id); setDepartmentName(row.cell.row.original.name); setShowDropdown(false) }} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                    <FiPlus size={18} />
                    Add Department Head
                  </li>
                )}
                <li onClick={() => navigate(`/organization/department-information`, { state: { id: row.cell.row.original.id } })} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <Eye size={18} />
                  View
                </li>
                <li onClick={() => navigate(`/organization/department-information`, { state: { isEdit: "true", id: row.cell.row.original.id } })} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <UserEdit size={18} />
                  Edit
                </li>
                {/* <li className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] !text-alert-text-error cursor-pointer">
                  <PiTrash size={18} />
                  Delete
                </li> */}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];


  return (
    <div ref={node}>
      {/* <h1 className="font-poppins font-semibold text-24">Department</h1> */}
      <div className="grid grid-cols-3 gap-5">
        {departmentStats.map((item, index) => (
          <div key={index}>
            <StatisticsCard
              backgroundColor={item?.cardBackgroundColor}
              key={index}
              title={item.title}
              value={item.value}
              icon={item.icon}
              iconBackgroundColor={item?.iconBackgroundColor}
              valueText={item.valueText}
            />
          </div>
        ))}
      </div>

      <div className=" my-10 py-[23px]">
        {getAllDepartmentsAtom?.data?.length > 0 || searchQuery ? (
          <CustomTable
            // checkedItems={checkedItems} 
            data={getAllDepartmentsAtom?.data || []}
            meta={getAllDepartmentsAtom?.meta}
            columns={columns}
            // filterListOptions={["Branch", "Department"]  z}
            // handleFilter={(e) => console.log(e)}
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
            header={
              <div className="flex justify-between px-2 items-center h-[40px]">
                <h1>
                  Departments
                </h1>
                <div className="bg-black rounded">
                  <CustomButton
                    className="!w-[150px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md !h-10 "
                    isTransparent={true}
                    handleClick={() => { navigate("/organization/add-department") }}
                    leftIcon={<FiPlus className="ml-3" size={20} />}
                    title="Add department"
                  />
                </div>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState buttonTitle="Add new department" handleClick={() => navigate("/organization/add-department")} />
          </div>
        )}
      </div>

      <CustomModal visibility={showManagerModal} toggleVisibility={setShowManagerModal}>
        <AddDepartmentHead departmentId={departmentId} departmentName={departmentName} toogleVisibity={setShowManagerModal} />
      </CustomModal>
    </div>
  );
};

export default Departments;
