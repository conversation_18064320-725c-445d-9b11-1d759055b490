import { Formik, Form } from "formik";
import { ArrowLeft2 } from "iconsax-react";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import { addDepartment } from "../../../../api/organization";
import * as yup from "yup";
import { useRecoilValue } from "recoil";
import { getAllBranchesAtom } from "../../../../recoil/atom/organizationAtom";
import CustomCheckBox from "../../../atoms/CustomCheckBox/CustomCheckBox";

interface AddDepartmentProps {
  branchName: string;
  departmentName: string;
  isChecked: boolean;
}

let initialValues = {
  branchName: "",
  departmentName: "",
  isChecked: false
};

const addDepartmentSchema = yup.object().shape({
  // branchName: yup
  // .string()
  // .test(
  //   "is-required-based-on-isChecked",
  //   errorMessages.required,
  //   function (value) {
  //     const { isChecked } = this.parent;
  //     return isChecked || (!!value && value.trim().length > 0); 
  //   }
  // ),
  departmentName: yup.string().required(errorMessages.required),
  // isChecked: yup.boolean()
});

const AddDepartment = () => {
  const navigate = useNavigate();
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const getEntireBranchesValue = useRecoilValue(getAllBranchesAtom);

  const branches = getEntireBranchesValue?.data?.map((branch) => ({
    text: branch.name,
    value: branch.id,
  }));

  const handleSubmit = (values: AddDepartmentProps, { resetForm }) => {
    setIsLoading(true);
    const payload = {
      branchId: values.branchName,
      name: values.departmentName,
      add_for_all: values.isChecked
    };
    addDepartment(payload).then((response) => {
      setIsLoading(false);
      if (response?.success) {
        setShowSuccessModal(true);
        resetForm();
        navigate("/organization/department")
        // fetchOrganization();
        // fetchEntireBranches();
      }
    });
  };

  return (
    <div>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 cursor-pointer w-fit"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6">
        <h1 className="font-poppins-medium text-18 rounded-tl-[10px] rounded-tr-[10px] text-purple-dark bg-purple-light pl-10 py-[33px]">
          Add department
        </h1>
        <div className="bg-[#F5F5F5] pt-[15px] pb-[20px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
          <Formik<AddDepartmentProps>
            initialValues={initialValues}
            onSubmit={handleSubmit}
            validationSchema={addDepartmentSchema}
          >
            {({ }) => (
              <Form>
                {/* <div>
                  <FormikCustomSelect
                    label="Branch name *"
                    options={branches}
                    name="branchName"
                    placeholder="Select branch"
                    value={values.branchName}
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("branchName", item.value);
                    }}
                    disabled={values.isChecked}
                  />
                </div>
                <div className="flex gap-3 mt-9">
                  <CustomCheckBox name="isChecked" onChange={(e: any) => {setFieldValue("isChecked", e.target.checked); }}/>
                  <p className="text-14 mt-0.5 text-neutral-dark">Add Department for all Branches</p>
                </div> */}
                <div className="mt-8">
                  <FormikCustomInput
                    label="Department name *" 
                    id="departmentName"
                    name="departmentName"
                    placeholder="enter department name"
                    type="text"
                    inputClassName="!bg-transparent"
                  />
                </div>
                
                <div className="mt-[50px] flex justify-end">
                  <CustomButton
                    type="submit"
                    title="Add department"
                    handleClick={() => {}}
                    isLoading={isLoading}
                    className="!w-[263px]"
                    variant={ButtonProperties.VARIANT.primary.name}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
      <SuccessModal
        visibility={showSuccessModal}
        toggleVisibility={setShowSuccessModal}
        text="Your new department has been added successfully! You can view all departments in dashboard."
      />
    </div>
  );
};

export default AddDepartment;
