import React, { useEffect, useState } from "react";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import StatusTag from "../../../atoms/StatusTag";
import { getSubscriptionTransactionHistory } from "../../../../api/subscription";
import { useRecoilState, useRecoilValue } from "recoil";
import { getSubscriptionTransactionHistoryAtom } from "../../../../recoil/atom/subscription";
import Loader from "../../../atoms/Loader";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import moment from "moment";

const TransactionHistory = () => {
  const [isFetching, setIsFetching] = useState(false);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [, setSubTranHistory] = useRecoilState(
    getSubscriptionTransactionHistoryAtom
  );
  const subTranHistoryValue = useRecoilValue(
    getSubscriptionTransactionHistoryAtom
  );


  const fetchTransactionHistory = () => {
    setIsFetching(true);
    getSubscriptionTransactionHistory({ page: pageNumber }).then((res) => {
      if (res.success) {
        setSubTranHistory(res.data);
      }
      setIsFetching(false);
    });
  };



  const columns = [
    {
      Header: "Plan name",
      accessor: "planPrice.plan.name",
      Cell: (row: any) => <div>{row.cell.value || "--"}</div>,
    },
    {
      Header: "Cost",
      accessor: "amount",
      Cell: (row: any) => <p className="whitespace-nowrap flex gap-2">
        {row.cell.row.original.currency && (
          <span className="text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1"> {row.cell.row.original.currency} </span>)} {parseInt(row.cell?.value / 100)?.toLocaleString() || 0}
      </p>,
    },
    {
      Header: "Payment type",
      accessor: "payment_type",
      Cell: (row: any) => <div>{row.cell.value || "--"} </div>,
    },
    {
      Header: "Transaction reference",
      accessor: "transaction_reference",
      Cell: (row: any) => <div>{row.cell.value || "--"} </div>,
    },
    {
      Header: "Billing date",
      accessor: "created_at",
      Cell: (row: any) => <p className="whitespace-nowrap">{moment(row.cell.value).format("ll")}</p>,
    },
    {
      Header: "Transaction status",
      accessor: "status",
      Cell: (row: any) => <StatusTag status={row.cell.value} />,
    },


    // {
    //   Header: "",
    //   accessor: "action",
    //   Cell: (row: any) => (
    //     <div className="relative">
    //       <CustomButton
    //         className="!w-[120px] !h-8 !text-12"
    //         title="Remove Card"
    //         isTransparent
    //         handleClick={() => {}}
    //         leftIcon={<MdRemoveCircle size={16} />}
    //         variant={ButtonProperties.VARIANT.primary.name}
    //       />
    //     </div>
    //   ),
    // },

  ];


  useEffect(() => {
    fetchTransactionHistory();
  }, [pageNumber]);

  if (isFetching) {
    return <Loader />;
  }


  return (
    <div>
      <div className="mt-6">
        {subTranHistoryValue?.data?.length > 0 ? (
          <CustomTable
            data={subTranHistoryValue?.data || []}
            meta={subTranHistoryValue?.meta || {}}
            columns={columns}
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            hideSearch
            header={
              <div>
                <h1 className="font-poppins-medium text-purple-dark mb-4">
                  Transaction History
                </h1>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState text="No Transaction History Available" />
          </div>
        )}
      </div>
    </div>
  );
};

export default TransactionHistory;
