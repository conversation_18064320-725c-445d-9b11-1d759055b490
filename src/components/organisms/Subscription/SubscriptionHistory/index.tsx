import React, { useEffect, useState } from "react";
import SubscriptionPlanHistory from "./SubscriptionPlanHistory";
import TransactionHistory from "./TransactionHistory";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ArrowLeft2 } from "iconsax-react";
import Card<PERSON>istory from "./CardHistory";

const SubscriptionHistory = () => {
  const navigate = useNavigate();
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("activeTab");
  const [activeTab, setActiveTab] = useState<number>(0);

  const SubHistoryTabs = [
    {
      title: "Subscription history",
      component: <SubscriptionPlanHistory />,
      name: "subscription-history",
    },
    {
      title: "Transaction history",
      component: <TransactionHistory />,
      name: "transaction-history",
    },
    {
      title: "Card history",
      component: <CardHistory />,
      name: "card-history",
    },
  ];

  useEffect(() => {
    setActiveTab(queryParam || 0)
  }, [queryParam, activeTab]);
  return (
    <div>
      <div onClick={() => navigate(-1)} className="flex text-16 gap-1 cursor-pointer">
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-10 px-4 bg-white flex justify-between">
        <div className="flex gap-10 py-6 justify-center items-center">
          {SubHistoryTabs.map((item, index) => (
            <div
              key={index}
              onClick={() => {
                setActiveTab(index);
                navigate(
                  `/subscription-history?activeTab=${index}&tab=${item.name}`
                );
              }}
              className="cursor-pointer"
            >
              <p
                className={`text-neutral-normal font-poppins-medium pb-2 px-5 text-center text-16 ${
                  activeTab == index
                    ? "text-purple-normal"
                    : "text-neutral-normal"
                }`}
              >
                {" "}
                {item.title}
              </p>
              {activeTab == index && (
                <hr
                  className={`border-2  rounded-full ${
                    activeTab == index
                      ? "border-purple-normal"
                      : "border-[#E5E5E5]"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
        <div></div>
      </div>
      {SubHistoryTabs[activeTab].component}
    </div>
  );
};

export default SubscriptionHistory;
