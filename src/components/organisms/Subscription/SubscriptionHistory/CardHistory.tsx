import React, { useEffect, useState } from "react";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import masterCard from "../../../../assets/images/mastercard.svg";
import visaCard from "../../../../assets/images/visa.svg";
import { getCardHistory } from "../../../../api/subscription";
import { useRecoilState, useRecoilValue } from "recoil";
import { getCardHistoryAtom } from "../../../../recoil/atom/subscription";
import Loader from "../../../atoms/Loader";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import moment from "moment";

const CardHistory = () => {
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [isFetching, setIsFetching] = useState(false);
  const [, setCardHistory] = useRecoilState(
    getCardHistoryAtom
  );
  const cardHistoryValue = useRecoilValue(
    getCardHistoryAtom
  );

  const fetchCardHistory = () => {
    setIsFetching(true);
    getCardHistory({page: pageNumber}).then((res) => {
      if (res.success) {
        setCardHistory(res.data)
      }
      setIsFetching(false);
    })
  };

  const columns = [
   
    {
      Header: "Card type",
      accessor: "card_type",
      Cell: (row: any) => <div className="flex">
        <img width={14} src={row.cell.value.trim() === "visa" ? visaCard : masterCard }/>
        {row.cell.value || "--"} 
        </div>,
    },
    {
      Header: "Card number",
      accessor: "card4",
        Cell: (row: any) => <div>****{row.cell.value || "--"}</div>,
    },
    {
      Header: "Card country",
      accessor: "card_country_iso",
      Cell: (row: any) => <div>{row.cell.value || "--"} </div>,
    },
    {
      Header: "Billing date",
      accessor: "created_at",
      Cell: (row: any) => <p className="whitespace-nowrap">{moment(row.cell.value).format("ll")}</p>,
    },
    // {
    //   Header: "",
    //   accessor: "action",
    //   Cell: (row: any) => (
    //     <div className="relative">
    //       <CustomButton
    //         className="!w-[120px] !h-8 !text-12"
    //         title="Remove Card"
    //         isTransparent
    //         handleClick={() => {}}
    //         leftIcon={<MdRemoveCircle size={16} />}
    //         variant={ButtonProperties.VARIANT.primary.name}
    //       />
    //     </div>
    //   ),
    // },
  ];

  useEffect(() => {
    fetchCardHistory();
  }, [pageNumber]);

  if (isFetching) {
    return <Loader />;
  }

  return (
    <div>
      <div className="mt-6">
        {cardHistoryValue?.data?.length > 0 ? (
          <CustomTable
            data={cardHistoryValue?.data || []}
            meta={cardHistoryValue?.meta || {}}
            columns={columns}
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            hideSearch
            header={
              <div>
                <h1 className="font-poppins-medium text-purple-dark mb-4">
                  Card History
                </h1>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState text="No Card History Available" />
          </div>
        )}
      </div>
    </div>
  );
};

export default CardHistory;
