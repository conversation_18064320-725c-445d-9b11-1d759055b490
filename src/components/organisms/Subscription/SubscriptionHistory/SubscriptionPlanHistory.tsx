import React, { useEffect, useState } from 'react'
import CustomTable from '../../../atoms/CustomTable/CustomTable';
import StatusTag from '../../../atoms/StatusTag';
import { FaEllipsisV, FaRegArrowAltCircleUp } from 'react-icons/fa';
import FilterDropdown from '../../../atoms/Cards/FilterDropdown';
import useClickOutside from '../../../shared/hooks';
import { MdOutlineCancel } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import { PiReceiptLight, PiTrash } from 'react-icons/pi';
import BillingForm from '../BillingForm';
import CustomModal from '../../../atoms/CustomModal/CustomModal';
import CustomButton from '../../../atoms/CustomButton/CustomButton';
import { cancelSubscription, getCardHistory, getSubscriptionHistory } from '../../../../api/subscription';
import { useRecoilState, useRecoilValue } from 'recoil';
import { getSubscriptionHistoryAtom } from '../../../../recoil/atom/subscription';
import Loader from '../../../atoms/Loader';
import moment from 'moment';
import OrganizationEmptyState from '../../../atoms/Cards/OrganizationEmptyState';
import { NotificationTypes, subscriptionPlans } from '../../../shared/helpers/enums';
import { clustarkToast } from '../../../atoms/Toast';

const SubscriptionPlanHistory = () => {
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [rowId, setRowId] = useState(0);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const [, setSubHistoryAtom] = useRecoilState(getSubscriptionHistoryAtom);
  const historyValue = useRecoilValue(getSubscriptionHistoryAtom);

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const handleCancelSubscription = (id) => {
    // setShowDeleteWarn(false);
    setIsLoading(true)
    cancelSubscription(id).then((res) => {
      if (res.success) {
        clustarkToast(NotificationTypes.SUCCESS, res.message)
      }
      setShowDeleteWarn(false);
      setIsLoading(false);
    })
  };

  const fetchSubscriptionHistory = () => {
    setIsFetching(true);
    getSubscriptionHistory({ page: pageNumber }).then((res) => {
      if (res.success) {
        setSubHistoryAtom(res.data);
      }
      setIsFetching(false);
    })
  };



  const columns = [
    {
      Header: "Plan name",
      accessor: "plan_price.plan.name",
      Cell: (row: any) => (
        <p className="text-[#101828]">{row.cell.value || "--"} </p>),
    },

    {
      Header: "Start date",
      accessor: "start_date",
      Cell: (row: any) => <p>{moment(row.cell.value).format("ll")}</p>,
    },
    {
      Header: "End date",
      accessor: "expiration_date",
      Cell: (row: any) => <p>{moment(row.cell.value).format("ll")}</p>,
    },
    {
      Header: "Cost",
      accessor: "plan_price.price",
      Cell: (row: any) => <p className='flex gap-2'>
        {row.cell.row.original.plan_price.currency && (
          <span className="text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1">
            {row.cell.row.original.plan_price.currency}
          </span>
        )}
        {row.cell.value.toLocaleString() || 0}
      </p>,
    },
    {
      Header: "Frequency",
      accessor: "plan_price.frequency",
      Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: (row: any) => <StatusTag status={row.cell.value} />,
    },
    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {row.cell.row.original.status === "ACTIVE" && showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>

                {/* {row.cell.row.original.plan_price.plan.name === subscriptionPlans.FREE_TRIAL && row.cell.row.original.status === "ACTIVE" && (
                   <li
                   onClick={() => {setShowModal(true); setShowDropdown(false); }}
                   className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                 >
                   <FaRegArrowAltCircleUp size={18}/>
                   Upgrade Plan
                 </li>
                )}
                {row.cell.row.original.plan_price.plan.name !== subscriptionPlans.FREE_TRIAL && row.cell.row.original.status === "ACTIVE" && (
                   <li
                   onClick={() => {setShowModal(true); setShowDropdown(false); }}
                   className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                 >
                   <FaRegArrowAltCircleUp size={18}/>
                   Renew Plan
                 </li>
                )} */}

                {row.cell.row.original.plan_price.plan.name !== subscriptionPlans.FREE_TRIAL && row.cell.row.original.status === "ACTIVE" && (
                  <li
                    onClick={() => { navigate("/subscription-invoice", { state: { planData: row.cell.row.original } }) }}
                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                  >
                    <PiReceiptLight size={18} />
                    View Invoice
                  </li>
                )}

                {row.cell.row.original.plan_price.plan.name !== subscriptionPlans.FREE_TRIAL && row.cell.row.original.status === "ACTIVE" && (
                  <>
                    {showDeleteWarn ? (
                      <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                        <div className="flex gap-3">
                          <PiTrash size={18} />
                          Are you sure?
                        </div>
                        <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                          <CustomButton isLoading={isLoading} title="Yes" handleClick={() => { handleCancelSubscription(row.cell.row.original.business_id) }} className="border !bg-transparent text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer" />
                          <span onClick={() => { setShowDeleteWarn(false) }} className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center">No</span>
                        </div>
                      </li>
                    ) : (
                      <li onClick={() => { setShowDeleteWarn(true) }} className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer">
                        <MdOutlineCancel size={18} />
                        Cancel Subscription
                      </li>
                    )}
                  </>
                )}

              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  useEffect(() => {
    fetchSubscriptionHistory();
  }, [pageNumber]);

  if (isFetching) {
    return <div>
      <Loader />
    </div>
  };

  return (
    <div>

      <div className='mt-6'>
        {historyValue?.data?.length > 0 ? (
          <CustomTable
            data={historyValue?.data || []}
            meta={historyValue?.meta || {}}
            columns={columns}
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            hideSearch
            header={
              <div>
                <h1 className="font-poppins-medium text-purple-dark mb-4">
                  Subscription History
                </h1>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState text="No Transaction History Available" />
          </div>
        )}
      </div>

      <CustomModal visibility={showModal} toggleVisibility={setShowModal}>
        <BillingForm />
      </CustomModal>
    </div>
  )
};

export default SubscriptionPlanHistory;