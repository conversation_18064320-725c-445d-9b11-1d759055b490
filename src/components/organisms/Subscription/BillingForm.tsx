import React from "react";
import { Form, Formik } from "formik";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties, currencySymbol, paymentCurrency } from "../../shared/helpers";
import { useNavigate } from "react-router-dom";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";


const BillingForm = ({planData} : any) => {


  const navigate = useNavigate();


  const handleSubmit = (values) => {
    navigate("/subscription-invoice", {state: {planData: planData, selectedSubscription: values}});
  };

  return (
    <div className="mb-10">
      <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-6">
        Billing Information
      </h1>
      <div className="mt-10 px-7">
        <Formik
          initialValues={{
            period: "month",
            noOfMonth: 1,
            cost: planData[0]?.price || 0,
          }}
          onSubmit={handleSubmit}
        >
          {({ values, setFieldValue }) => (
            <Form>
                <h1 className="text-20 font-poppins-medium mb-6">Premium Plan</h1>
              <div>
              <div>
                    <FormikCustomSelect
                      label="Period"
                      value={values.period}
                      name="period"
                      placeholder="Select option"
                      options={[
                        { text: "Month", value: "month" },
                        { text: "Year", value: "year" },
                       
                      ]}
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("period", item.value);
                        setFieldValue("cost", item.value === "year" ? planData[1].price : planData[0].price);
                      }}
                      required
                    />
                  </div>
                  
                
              </div>
              <div className="mt-8">
                
                <FormikCustomInput
                  label="Amount"
                  value={`${planData[0].currency === paymentCurrency.USD ? currencySymbol.USD : currencySymbol.NGN} ${values.period === "year" ? planData[1]?.price?.toLocaleString() : planData[0].price?.toLocaleString()}`}
                  name="cost"
                  disabled
                />
              </div>
              <p className="mt-2">You will be charged {planData[0].currency === paymentCurrency.USD ? currencySymbol.USD : currencySymbol.NGN}{values.period === "year" ? planData[1]?.price?.toLocaleString() : planData[0]?.price?.toLocaleString()} for this transaction</p>

              <div className="flex justify-end mt-10">
                  <CustomButton
                    type="submit"
                    title="Generate Invoice"
                    handleClick={() => {}}
                    variant={ButtonProperties.VARIANT.primary.name}
                  />
                </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default BillingForm;
