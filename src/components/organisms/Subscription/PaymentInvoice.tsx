import { ArrowLeft2 } from 'iconsax-react'
import React, { useRef } from 'react'
import { useLocation, useNavigate } from 'react-router-dom';
import CustomButton from '../../atoms/CustomButton/CustomButton';
import { ButtonProperties, currencySymbol, paymentCurrency } from '../../shared/helpers';
import { triggerPayment } from '../../shared/hooks/triggerPayment';
import { FiDownload } from 'react-icons/fi';
import { getCurrentPlanAtom } from '../../../recoil/atom/subscription';
import { useRecoilValue } from 'recoil';
import moment from 'moment';
import { loggedUserAtom } from '../../../recoil/atom/authAtom';
import { usePaystackPayment } from 'react-paystack';
import { defaultBusinessAtom } from '../../../recoil/atom/organizationAtom';
import { clustarkToast } from '../../atoms/Toast';
import { NotificationTypes } from '../../shared/helpers/enums';
import { handleDownloadPdf } from '../../shared/hooks/handlePdf';

const PaymentInvoice = () => {
    const navigate = useNavigate();
    const printRef = useRef<HTMLDivElement>(null);
    const location = useLocation();
    const { planData, selectedSubscription } = location?.state || "";
  const currentPlanValue = useRecoilValue(getCurrentPlanAtom);
  const loggedUser = useRecoilValue(loggedUserAtom);
  const defaultBusiness = useRecoilValue(defaultBusinessAtom);

  const config = {
    reference: (new Date()).getTime().toString(),
    email: loggedUser?.email,
    amount: (selectedSubscription?.cost * 100) || (planData?.plan_price?.price * 100), 
    publicKey: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY,
    metadata: {
        user_id: loggedUser?.id,
        plan_price_id: planData[0]?.plan_id, 
        business_id: defaultBusiness?.id,
      },
    
};

  const initializePayment = usePaystackPayment(config);

  const onSuccess = (reference) => {
    clustarkToast(NotificationTypes.SUCCESS, "Subscription Payment was successful")
    navigate("/subscription-history")
  };

  const onClose = () => {
    navigate("/subscription-history")
  }

    const handlePayment = (response) => {
        clustarkToast(NotificationTypes.SUCCESS, response.message)
        navigate("/subscription-history")
       
      };

    const handleSubscriptionPayment = () => {
        triggerPayment({
          amount: selectedSubscription?.cost?.toLocaleString() || planData?.plan_price?.price?.toLocaleString(),
          currency: 'USD',
          customerData: {
            email: loggedUser?.email,
            phone_number: loggedUser?.phone_number,
            name: loggedUser?.first_name + " " + loggedUser?.last_name
          },
          meta: {
            user_id: loggedUser?.id,
            plan_price_id: planData?.plan_id, 
            business_id: defaultBusiness?.id,
          },
          customizeData: {
            title: 'my Payment Title',
            description: 'Payment for items in cart',
            logo: 'https://st2.depositphotos.com/4403291/7418/v/450/depositphotos_74189661-stock-illustration-online-shop-log.jpg',
          },
        }, handlePayment)
      };

  return (
    <div>
        <div onClick={() => navigate(-1)} className="flex text-16 gap-1 cursor-pointer">
            <ArrowLeft2 size={20} /> <p>Back</p>
        </div>
        <div className='mt-10 flex justify-between px-4 py-3 text-purple-normal-hover bg-purple-light'>
            <h1 className='text-18 text-purple-dark font-poppins-medium '>Payment invoice</h1>
            <div className='flex justify-center'>
                <CustomButton
                className='!w-[150px] !h-6'
                leftIcon={<FiDownload size={18}/>}
                title="Download Invoice"
                handleClick={() => handleDownloadPdf(printRef, "Invoice")}
                variant={ButtonProperties.VARIANT.primary.name}
                />

            </div>
        </div>
        <div className='bg-white p-10' ref={printRef}>
            <div>
                <div className='flex justify-between pb-3'>
                    <div>
                        <p className='text-24 text-purple-normal font-poppins-medium'>ArkHR</p>
                    </div>
                    <p className={`text-18 ${planData?.status === "ACTIVE" ? "bg-alert-text-success" : "bg-amber-500"}   text-white px-4 py-1 rounded font-poppins-medium`}> {planData?.status || "PENDING"}</p>
                </div>
                <hr />
                {/* <div className='pt-3'>
                    <div>
                        <p>Date: <span>{moment().format('ll')}</span></p>
                    </div>
                </div> */}
                <div className='grid grid-cols-3 gap-10 py-8 border-b capitalize'>
                    <div>
                        <p className='font-bold'>Bill From</p>
                        <p>ArkHR</p>
                        <p>Ipent 4 extension, Lokogoma, Ap Abuja</p>
                        <p>+234 903 901 1682</p>
                    </div>
                    <div>
                        <p className='font-bold'>Bill To</p>
                        <p>{currentPlanValue?.business?.name || "--"}</p>
                        <p>{currentPlanValue?.business?.address || "--"}</p>
                        <p>{currentPlanValue?.business?.country || "--"}</p>
                    </div>
                    <div>
                        <p>Amount: <span className='font-bold'>{planData?.plan_price?.currency === paymentCurrency?.USD ? currencySymbol.USD : currencySymbol.NGN}{selectedSubscription?.cost?.toLocaleString() || planData?.plan_price?.price?.toLocaleString()}</span> </p>
                        <p>Date Issued: <span>{planData?.start_date ? moment(planData?.start_date).format("DD-MM-YYYY") : moment().format("DD-MM-YYYY")}</span> </p>
                        <p>Date Due: <span>{planData?.expiration_date ? moment(planData?.expiration_date).format("DD-MM-YYYY") : "--"}</span> </p>
                    </div>
                </div>
                <div className='py-8 border-b'>
                    <h1>Summary</h1>
                    <p>Your renewal plan will be activated from next month 01/02/2024.</p>
                </div>
            </div>

            <div className='py-8'>
                <table className='w-full text-left'>
                    <thead>
                        <tr className='border-b'>
                            <th className='py-2'>Description</th>
                            <th>Quantity</th>
                            <th>Unit Price</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr className='border-b'>
                            <td className='py-2'>Premium Plan</td>
                            <td>1</td>
                            <td>{planData[0]?.currency === paymentCurrency.USD ? currencySymbol.USD : currencySymbol.NGN}{selectedSubscription?.cost?.toLocaleString() || planData?.plan_price?.price?.toLocaleString()}</td>
                            <td>{planData[0]?.currency === paymentCurrency.USD ? currencySymbol.USD : currencySymbol.NGN}{selectedSubscription?.cost?.toLocaleString() || planData?.plan_price?.price?.toLocaleString()}</td>
                        </tr>
                       
                    </tbody>
                </table>
            </div>
            <div className='text-right'>
                <div>
                    <div className='pr-4'>
                        <h1 className='mt-1'>Subtotal : <span>{planData[0]?.currency === paymentCurrency.USD ? currencySymbol.USD : currencySymbol.NGN}{selectedSubscription?.cost?.toLocaleString() || planData?.plan_price?.price?.toLocaleString()}</span></h1>
                        <h1 className='mt-1'>Discount : <span>{planData[0]?.currency === paymentCurrency.USD ? currencySymbol.USD : currencySymbol.NGN}0.00</span></h1>
                        <h1 className='mt-1'>VAT : <span>{planData[0]?.currency === paymentCurrency.USD ? currencySymbol.USD : currencySymbol.NGN}0.00</span></h1>
                    </div>
                    <hr />
                    <div className='mt-2'>
                        <h1 className='bg-purple-light text-purple-normal font-poppins-medium text-20 py-3 pr-4'>Total : <span>{planData[0]?.currency === paymentCurrency.USD ? currencySymbol.USD : currencySymbol.NGN}{selectedSubscription?.cost?.toLocaleString() || planData?.plan_price?.price?.toLocaleString()}</span></h1>
                    </div>
                </div>
            </div>

        </div>
        {selectedSubscription?.cost && (

            <div className='mt-8'>
                <CustomButton
                    type="submit"
                    title="Proceed to Payment"
                    handleClick={() => {planData[0]?.currency === paymentCurrency.USD ? handleSubscriptionPayment: initializePayment({onSuccess, onClose})} }
                    variant={ButtonProperties.VARIANT.primary.name}
                />
            </div>
        )}

    </div>
  )
}

export default PaymentInvoice 