import React, { useEffect, useState } from "react";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import {
  ButtonProperties,
  calculateDaysLeft,
  currencySymbol,
  paymentCurrency,
} from "../../shared/helpers";
import { <PERSON><PERSON><PERSON>heck } from "react-icons/bi";
import { MdOutlineSubscriptions } from "react-icons/md";
import { useNavigate } from "react-router-dom";
import CustomModal from "../../atoms/CustomModal/CustomModal";
import BillingForm from "./BillingForm";
import { useRecoilState, useRecoilValue } from "recoil";
import {
  getAllPlansAtom,
  getCurrentPlanAtom,
} from "../../../recoil/atom/subscription";
import {
  NotificationTypes,
  subscriptionPlans,
} from "../../shared/helpers/enums";
import moment from "moment";
import { cancelSubscription, getPlans, startFreeTrial } from "../../../api/subscription";
import Loader from "../../atoms/Loader";
import birddance from "../../../assets/images/bluebird-dance.gif";
import { clustarkToast } from "../../atoms/Toast";
import { Form, Formik } from "formik";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";
import { getBusinessesAtom } from "../../../recoil/atom/organizationAtom";
import useUpdateRecoilAtom from "../../shared/hooks/updateRecoilAtom";
import { loggedUserAtom } from "../../../recoil/atom/authAtom";

const Subscription = () => {
  const navigate = useNavigate();
  const [isFetching, setIsFetching] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelLoading, setCancelLoading] = useState(false);
  const [freeTrialModal, setFreeTrialModal] = useState(false);
  const [, setPlanAtom] = useRecoilState(getAllPlansAtom);
  const currentPlanValue = useRecoilValue(getCurrentPlanAtom);
  const subscriptionPlansValue = useRecoilValue(getAllPlansAtom);
  const getBusinessesValue = useRecoilValue(getBusinessesAtom);
  const { fetchCurrentPlan } = useUpdateRecoilAtom();
  const loggedUser = useRecoilValue(loggedUserAtom);


  const days = calculateDaysLeft(
    currentPlanValue?.start_date,
    currentPlanValue?.expiration_date
  );
  const plan = currentPlanValue?.plan?.name;

  const progressWidth = Math.min(((30 - days) / 30) * 100, 100);

  const fetchAllPlans = () => {
    setIsFetching(true);
    getPlans().then((res) => {
      setIsFetching(false);
      if (res.success) {
        setPlanAtom(res.data);
      }
    });
  };

  useEffect(() => {
    fetchAllPlans();
  }, []);

  const handleCancelSubscription = () => {
    setCancelLoading(true);
    cancelSubscription(currentPlanValue?.business?.id).then((res) => {
      if (res.success) {
        clustarkToast(NotificationTypes.SUCCESS, res.message);
      }
      setCancelLoading(false);
      setShowCancelModal(false);
    });
  };

  const businesses =
  getBusinessesValue?.map((item) => ({
    text: item.name + " " + `(${item.role})`,
    value: item.id,
  })) || [];

  const handleSubmit = (values) => {
    setIsLoading(true);
    const payload = {
      businessId: values.organization || getBusinessesValue[0]?.id || loggedUser?.businesses[0]?.id,
    }
    startFreeTrial(payload).then((res) => {
      setIsLoading(false);
      clustarkToast(NotificationTypes.SUCCESS, res.message)
      fetchCurrentPlan();
      setFreeTrialModal(false);
    })
  };

  if (isFetching) {
    return (
      <div>
        <Loader />
      </div>
    );
  }

  return (
    <div>
      <h1 className="font-poppins font-semibold text-24">Subscription</h1>
      <div className="pt-10 flex justify-between">
        <div className="bg-accent-blue-light rounded-lg w-[24rem] py-4 px-4">
          <div>
            <p>
              Subscripton plan:{" "}
              <span className="bg-white px-3 py-1 ml-2 text-purple-dark font-poppins-medium rounded-lg text-12">
                {plan === subscriptionPlans.FREE_TRIAL
                  ? "Free Trial"
                  : plan || "--"}
              </span>
            </p>
            <p className="mt-4">
              Start date:{" "}
              <span className=" font-poppins-medium">
                {currentPlanValue?.start_date
                  ? moment(currentPlanValue?.start_date).format("DD-MM-YYYY")
                  : "--"}
              </span>
            </p>
            <p className="mt-1">
              Expires on:{" "}
              <span className=" font-poppins-medium">
                {currentPlanValue?.expiration_date
                  ? moment(currentPlanValue?.expiration_date).format(
                      "DD-MM-YYYY"
                    )
                  : "--"}
              </span>
            </p>
            {currentPlanValue && (

              <div className="mt-3">
                <div className="w-full bg-gray-100 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progressWidth}%` }}
                  />
                </div>
                <p className="mt-1 text-[10px] text-right font-poppins-medium">
                  {days <= 0 ? 0 : days} days left
                </p>
              </div>
            )}
            <div className="grid grid-cols-2 gap-4">
              {plan === subscriptionPlans.FREE_TRIAL ? (
                <CustomButton
                  className=" !bg-white mt-4 !h-[20px] !border-none !font-normal !font-poppins-medium"
                  isTransparent={true}
                  handleClick={() => {
                    setShowModal(true);
                  }}
                  title="Upgrade plan"
                />
              ) : (
                <>
                  {currentPlanValue && days <= 3 && (
                    <CustomButton
                      className=" !bg-white mt-4 !h-[20px] !border-none !font-normal !font-poppins-medium"
                      isTransparent={true}
                      handleClick={() => {
                        setShowModal(true);
                      }}
                      title="Renew plan"
                    />
                  )}
                </>
              )}
              {currentPlanValue && plan !== subscriptionPlans.FREE_TRIAL && (
                <CustomButton
                  className=" !bg-white mt-4 !h-[20px] !border-none !font-normal !font-poppins-medium"
                  isTransparent={true}
                  handleClick={() => setShowCancelModal(true)}
                  title="Cancel subscription"
                />
              )}
            </div>
          </div>
        </div>
        <div>
          <CustomButton
            className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium"
            isTransparent={true}
            handleClick={() => {
              navigate("/subscription-history");
            }}
            title="Subscription history"
            leftIcon={<MdOutlineSubscriptions size={18} />}
          />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-10 mt-8">
        {/* <div className="bg-white rounded-lg px-6  py-10">
          <div>
            <h1 className="text-center text-24 pb-4 font-poppins-medium">Basic</h1>
            <p className="text-center">The basic plan for individuals and organization</p>
            <div className="py-6">
              <p className="text-center">
                <span className="font-poppins-medium text-24">$10 / monthly - ($100 / Year)</span>{" "}
              </p>
            </div>
            <div className="mb-10">
              <h1 className="py-2 font-poppins-medium text-18">Benefits</h1>
              <ul>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/> Up to 100 employees</li>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/>Up to 10 branches</li>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/>Up to 35 departments</li>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/>Up to 2 team mwmbers</li>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/>Access to 12 invoices/month</li>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/>Access to payroll (E-wallet, automated, manual)</li>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/>Access to scheduled payments</li>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/>Access to KPI Management</li>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/>Access to leave management</li>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/>Access to expense tracker</li>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/>Access to performance management and tracking</li>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/>2GB of file storage</li>
                <li className="flex gap-3 pt-2"> <BiCheck className="mt-0.5"/>24/7 support</li>
                
              </ul>
            </div>
            <div>
              <CustomButton
                title="Start Free Trail ($0 / 5days)"
                handleClick={() => {}}
                isTransparent
                variant={ButtonProperties.VARIANT.primary.name}
              />
              <CustomButton
              className="mt-4"
                title="Subscribe Now"
                handleClick={() => {setShowModal(true)}}
                variant={ButtonProperties.VARIANT.primary.name}
              />
            </div>
          </div>
        </div> */}
        <div className="bg-purple-light rounded-lg px-6 py-10">
          <div>
            <div className="flex justify-end items-end  text-right">
              {currentPlanValue &&
                (plan === subscriptionPlans.FREE_TRIAL ||
                  plan === subscriptionPlans.PREMIUM) && (
                  <p className=" px-2 py-1 bg-green-400 w-fit rounded-full text-white font-poppins-medium">
                    ACTIVE
                  </p>
                )}
            </div>
            <h1 className="text-center text-24 pb-4 font-poppins-medium">
              Premium
            </h1>
            <p className="text-center">
              Advanced collaboration for individuals and organizations
            </p>
            <div className="py-6">
              <p className="text-center">
                <span className="font-poppins-medium text-24">
                  {subscriptionPlansValue[0]?.currency === paymentCurrency?.USD
                    ? currencySymbol.USD
                    : currencySymbol.NGN}
                  {subscriptionPlansValue[0]?.price?.toLocaleString()} / monthly
                  - (
                  {subscriptionPlansValue[0]?.currency === paymentCurrency.USD
                    ? currencySymbol.USD
                    : currencySymbol.NGN}
                  {subscriptionPlansValue[1]?.price?.toLocaleString()} / Yearly)
                </span>{" "}
              </p>
            </div>
            <div className="mb-10">
              <h1 className="py-2 font-poppins-medium text-18">Benefits</h1>
              <ul>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" /> Unlimited employees
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" /> Unlimited branches
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Up to 5 admin team member invitations
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Up 10 automated Email Campaigns/month
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Up to 10GB of file storage
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Access to payroll (E-wallet, automated, Manual)
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Access to expense tracker
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Access to scheduled payments
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Access to custom invoices
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Access to Learning & Development
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Access to Attendance tracking
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Access to Performance Management and tracking
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Access to chats/announcements
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Access to leave management
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Access to ATS
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  KPI Management
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Meetings
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  Detailed reports
                </li>
                <li className="flex gap-3 pt-2">
                  <BiCheck className="mt-0.5" />
                  24/7 support
                </li>
              </ul>
            </div>

            <div>
              {!currentPlanValue && (
                <CustomButton
                  title={`Start Free Trail (${subscriptionPlansValue[0]?.currency === paymentCurrency?.USD
                    ? currencySymbol.USD
                    : currencySymbol.NGN}0 / 30days)`}
                  handleClick={() => setFreeTrialModal(true)}
                  isTransparent
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              )}
              {currentPlanValue && plan === subscriptionPlans.FREE_TRIAL ? (
                <CustomButton
                  className="mt-4"
                  title="Upgrade now"
                  handleClick={() => {
                    setShowModal(true);
                  }}
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              ) : (
                <>
                  {currentPlanValue && days <= 3 && (
                    <CustomButton
                      className="mt-4"
                      title="Renew now"
                      handleClick={() => {
                        setShowModal(true);
                      }}
                      variant={ButtonProperties.VARIANT.primary.name}
                    />
                  )}
                </>
              )}
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg px-6 py-10">
          <div>
            <h1 className="text-center text-24 pb-4 font-poppins-medium">
              Enterprise
            </h1>
            <p className="text-center">
              Security, compliance, and flexible deployment
            </p>
            <div className="py-6">
              <p className="text-center">
                <span className="font-poppins-medium text-24">Contact us</span>{" "}
              </p>
            </div>
            <div className="mb-10">
              <h1 className="py-2 font-poppins-medium text-18">Benefits</h1>
              <ul>
                <li className="flex gap-3 pt-2">
                  {" "}
                  <BiCheck className="mt-0.5" />
                  Your own dedicated server
                </li>
                <li className="flex gap-3 pt-2">
                  {" "}
                  <BiCheck className="mt-0.5" />
                  Custom domain of your choice
                </li>
                <li className="flex gap-3 pt-2">
                  {" "}
                  <BiCheck className="mt-0.5" />
                  Flexible custom features
                </li>
                <li className="flex gap-3 pt-2">
                  {" "}
                  <BiCheck className="mt-0.5" />
                  up to 250GB of file storage
                </li>
                <li className="flex gap-3 pt-2">
                  {" "}
                  <BiCheck className="mt-0.5" />
                  24/7 support
                </li>
              </ul>
            </div>
            <CustomButton
              title="Contact us"
              handleClick={() => {}}
              variant={ButtonProperties.VARIANT.primary.name}
            />
          </div>
        </div>
      </div>
      <CustomModal visibility={showModal} toggleVisibility={setShowModal}>
        <BillingForm planData={subscriptionPlansValue} />
      </CustomModal>

      <CustomModal
        visibility={showCancelModal}
        toggleVisibility={setShowCancelModal}
      >
        <div>
          <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-6">
            Cancel subscription
          </h1>
          <p className="text-20 mt-10 text-center">
            Are you sure you want to cancel this subscription
          </p>

          <div className="flex gap-6 px-6 my-10">
            <CustomButton
              title="No"
              handleClick={() => {
                setShowCancelModal(false);
              }}
              variant={ButtonProperties.VARIANT.primary.name}
              isTransparent
            />
            <CustomButton
              title="Yes, cancel"
              isLoading={cancelLoading}
              handleClick={handleCancelSubscription}
              variant={ButtonProperties.VARIANT.primary.name}
            />
          </div>
        </div>
      </CustomModal>

      <CustomModal visibility={freeTrialModal} toggleVisibility={setFreeTrialModal}>
      <div className="w-full py-10">
        <div className="flex items-center justify-center">
          <img width={200} height={200} src={birddance} alt="GIF" />
        </div>
        <div className="px-10">
          <h1 className="text-center text-24 mt-10 font-semibold"> Welcome onboard.</h1>
          <p className="text-center mt-5 text-16">
          We are glad to have you with us. You will receive a free trial plan for 30 days. Start free trial to gain access to your dashboard and all features.
          </p>

            <Formik initialValues={{organization: ""}} onSubmit={handleSubmit}>
              {({values, setFieldValue}) => (
                  <Form>
                      <div>
                      {businesses.length > 1  && (
                      <div className="mt-10">
                        <FormikCustomSelect
                          label="Select Organization"
                          placeholder="Select option"
                          optionsParentClassName="text-12"
                          value={values.organization}
                          name="organization"
                          options={businesses}
                          onChange={(item: { value: string; text: string }) => {
                            setFieldValue("organization", item.value);
                          }}
                        />
                      </div>
                    )}
                      <div className="mt-10">
                          <CustomButton
                          type="submit"
                          className="!text-16"
                          title="Start free trial"
                          isLoading={isLoading}
                          handleClick={() => {}}
                          variant={ButtonProperties.VARIANT.primary.name}
                          />
                      </div>
                      </div>
                  </Form>
              )}
            </Formik>
        </div>
      </div>
    </CustomModal>
    </div>
  );
};

export default Subscription;
