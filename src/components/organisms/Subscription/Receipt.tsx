import { ArrowLeft2 } from "iconsax-react";
import React, { useRef } from "react";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { FiDownload } from "react-icons/fi";
import { ButtonProperties } from "../../shared/helpers";
import { useNavigate } from "react-router-dom";
import { IoCheckmarkDoneCircle } from "react-icons/io5";
import { handleDownloadPdf } from "../../shared/hooks/handlePdf";

const Receipt = () => {
    const navigate = useNavigate();

    const printRef = useRef<HTMLDivElement>(null);

  return (
    <div>
      <div onClick={() => navigate(-1)} className="flex text-16 gap-1 cursor-pointer">
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-10 flex justify-between px-4 py-3 text-purple-normal-hover bg-purple-light">
        <h1 className="text-24 font-poppins-medium ">Transaction Receipt</h1>
        <div className="flex justify-center">
          <CustomButton
            className="!w-[150px] !h-6"
            leftIcon={<FiDownload size={18} />}
            title="Download Receipt"
            handleClick={() => handleDownloadPdf(printRef, "Receipt")}
            variant={ButtonProperties.VARIANT.primary.name}
          />
        </div>
      </div>
      <div className="bg-white py-10 h-full" ref={printRef}>
        <div className="flex justify-center">
            <div className="smallLaptop:w-1/3 border rounded-lg p-5 text-neutral-normal">
                <div>
                    <div className="flex justify-center text-alert-text-success">
                        <IoCheckmarkDoneCircle size={40}/>
                    </div>
                    <h1 className="text-center text-20 font-semibold text-neutral-normal">Payment Successful</h1>
                    <p className="py-1 text-center">Your payment has been successfully completed.</p>
                    <div className="pt-5">
                        <h1 className="text-center text-16">Total Payment</h1>
                        <p className="text-center text-18 font-semibold">$100.56</p>
                        <div className="mt-8">
                            <div className="flex justify-between mt-4">
                                <p>Organization Name</p>
                                <p>Hooli</p>
                            </div>
                            <div className="flex justify-between mt-4">
                                <p>Sender Name</p>
                                <p>John Doe</p>
                            </div>
                            <div className="flex justify-between mt-4">
                                <p>Payment Method</p>
                                <p>Bank Transfer</p>
                            </div>
                            <div className="flex justify-between mt-4">
                                <p>Subscription Plan</p>
                                <p>Basic</p>
                            </div>
                            <div className="flex justify-between mt-4">
                                <p>Payment Date</p>
                                <p>12 Dec 2023 21:03:56</p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
      </div>
    </div>
  );
};

export default Receipt;
