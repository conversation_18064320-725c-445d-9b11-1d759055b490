import React, { useEffect, useRef, useState } from 'react'
import CustomButton from '../../atoms/CustomButton/CustomButton';
import { useNavigate } from 'react-router-dom';
import CustomTable from '../../atoms/CustomTable/CustomTable';
import OrganizationEmptyState from '../../atoms/Cards/OrganizationEmptyState';
import { PiTrash } from 'react-icons/pi';
import FilterDropdown from '../../atoms/Cards/FilterDropdown';
import { FaEllipsisV } from 'react-icons/fa';
import useClickOutside from '../../shared/hooks';
import { useRecoilState, useRecoilValue } from 'recoil';
import { getPendingTeamMembersAtom } from '../../../recoil/atom/teams';
import { cancelTeamInvitation, getPendingInvitation } from '../../../api/teams';
import StatusTag from '../../atoms/StatusTag';
import moment from 'moment';
import { clustarkToast } from '../../atoms/Toast';
import { NotificationTypes } from '../../shared/helpers/enums';
import Loader from '../../atoms/Loader';
import { FiPlus } from 'react-icons/fi';

const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

const PendingInvites = () => {
  const navigate = useNavigate();

  const [isFetching, setIsFetching] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState(false);
  const [rowId, setRowId] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [, setPendingTeamMembersAtom] = useRecoilState(getPendingTeamMembersAtom);
  const getPendingInvitationValue = useRecoilValue(getPendingTeamMembersAtom);

  const debounceSearch = useRef(debounce((q) => fetchPendingTeamMembers(q), 2000)).current;

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const fetchPendingTeamMembers = (q) => {
    setIsFetching(true);
    getPendingInvitation({ search: q }).then((res) => {
      if (res.success) {
        setPendingTeamMembersAtom(res.data);
        setIsFetching(false);
      }
    })
  };

  const handleCancelInvitation = (id) => {
    setIsLoading(true);
    cancelTeamInvitation(id).then((res) => {
      setIsLoading(false);
      if (res.success) {
        setShowDeleteWarn(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchPendingTeamMembers(searchQuery);
      }
    }).catch(() => setIsLoading(false));

  };


  const columns = [
    {
      Header: "Email",
      accessor: "email",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },

    {
      Header: "Role",
      accessor: "role",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Invitation date",
      accessor: "created_at",
      Cell: (row: any) => (moment(row.cell.value).format("DD/MM/YYYY")),
    },
    {
      Header: "Status",
      accessor: "invitation_status",
      Cell: (row: any) => (<StatusTag status={row.cell.value} />),
    },


    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>

                {showDeleteWarn ? (
                  <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                    <div className="flex gap-3">
                      <PiTrash size={18} />
                      Are you sure?
                    </div>
                    <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                      <CustomButton isLoading={isLoading} isTransparent title="Yes" handleClick={() => handleCancelInvitation(row.cell.row.original.id)} className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer" />
                      <span onClick={() => { setShowDeleteWarn(false) }} className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center">No</span>
                    </div>
                  </li>
                ) : (
                  <li onClick={() => setShowDeleteWarn(true)} className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer">
                    <PiTrash size={18} />
                    Cancel Invitation
                  </li>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  useEffect(() => {
    fetchPendingTeamMembers(searchQuery);
  }, [pageNumber]);

  if (isFetching && !searchQuery) {
    return <div>
      <Loader />
    </div>
  };

  return (
    <div>
      <div className=" my-10 py-[23px]">
        {getPendingInvitationValue?.data?.length > 0 || searchQuery ? (
          <CustomTable
            data={getPendingInvitationValue?.data || []}
            meta={getPendingInvitationValue?.meta || {}}
            columns={columns}
            // filterListOptions={["State", "Country"]}
            handleFilter={(e) => console.log(e)}
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            // checkedItems={checkedItems}
            handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
            header={
              <div className="flex justify-between items-center h-[60px] px-2">
                <h1>
                  Pending invitations
                </h1>
                <div className="flex justify-center items-center">
                  <CustomButton
                    className="!w-[180px] !bg-purple-normal !text-white !border-none !font-normal !font-poppins-medium shadow-md"
                    isTransparent={true}
                    handleClick={() => {
                      navigate("/add-team-member")
                    }}
                    leftIcon={<FiPlus className="ml-3" size={20} />}
                    title="Add team member"
                  />
                </div>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState
              buttonTitle="Invite team member"
              handleClick={() => navigate("/add-team-member")}
            />
          </div>
        )}
      </div>

    </div>
  )
}

export default PendingInvites