import React, { useState } from 'react'
import SuccessModal from '../../atoms/CustomModal/SuccessModal'
import CustomButton from '../../atoms/CustomButton/CustomButton'
import { ButtonProperties } from '../../shared/helpers'
import { ArrowLeft2 } from 'iconsax-react'
import { useNavigate } from 'react-router-dom'
import { Form, Formik } from 'formik'
import FormikCustomInput from '../../atoms/CustomInput/FormikCustomInput'
import FormikCustomSelect from '../../atoms/CustomInput/FormikCustomSelect'
import { inviteTeamMember } from '../../../api/teams'
import { clustarkToast } from '../../atoms/Toast'
import { NotificationTypes } from '../../shared/helpers/enums'

const AddTeamMember = () => {
  const navigate = useNavigate();
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const roles = [
    {text: "Owner", value: "OWNER"}, 
    {text: "Admin", value:"ADMIN"}, 
    {text: "Operations", value:"OPERATIONS"}, 
    {text: "Finance", value:"FINANCE"}, 
    // {text: "Customer Support", value: "CUSTOMER_SUPPORT"}
  ]
  const initialValues = {
    email: '',
    role: '',
  };


  const handleSubmit = (values) => {
    if (!values.role) {
      clustarkToast(NotificationTypes.ERROR, "Please select a role")
    } else {
      setIsLoading(true);
      inviteTeamMember(values).then((res) => {
        if (res.success) {
          setShowSuccessModal(true);
        }
        setIsLoading(false);
      }).catch(() => {
        setIsLoading(false);
      })

    }
  };

  return (
    <div>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 cursor-pointer"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6">
        <h1 className="font-poppins-medium text-18 rounded-tl-[10px] rounded-tr-[10px] text-purple-dark bg-purple-light pl-10 py-[33px]">
          Add team member
        </h1>
        <div className="bg-[#F5F5F5] pt-[95px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
          <div className='grid grid-cols-4'>
            <div className='col-span-2'>
              {/* <h1 className='text-20 mb-5'>Send an invite to a new member</h1> */}

              <Formik
                initialValues={initialValues}
                onSubmit={handleSubmit}
                enableReinitialize
              >
                {({ setFieldValue }) => (
                  <Form>
                    <div>
                      <FormikCustomInput
                        label="Email"
                        id="email"
                        name="email"
                        type="text"
                        maxLength={40}
                        inputClassName="!bg-transparent"
                        required
                      />
                    </div>
                    <div className="mt-8">
                      <FormikCustomSelect
                        label="Role"
                        id="role"
                        name="role"
                        placeholder="Select role"
                        type="text"
                        inputClassName="!bg-transparent"
                        options={roles}
                        onChange={(item: { value: string; text: string }) => {
                          setFieldValue("role", item.value);
                        }}
                      />
                    </div>

                    <div className="mt-[120px] flex justify-end">
                      <CustomButton
                        type="submit"
                        title="Invite member"
                        handleClick={() => { }}
                        isLoading={isLoading}
                        size={ButtonProperties.SIZES.small}
                        variant={ButtonProperties.VARIANT.primary.name}
                      />
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
            <div className=' bg-white h-full col-span-2 p-4 ml-[10rem] rounded-lg'>
              <h1 className='p-4 border-b text-18 text-purple-dark'>Role description</h1>
              <ul className='list-item  px-6 pb-6'>
                <li className='border-b mt-8 list-disc pb-2'>
                  <p className='text-16 mb-4 text-purple-normal'>Owner</p>
                  {/* <p className=''>Manage team, access all features, and receive notifications.</p> */}
                  <ul>
                    <li>- Manage team: Add, remove or update team members' roles.</li>
                    <li>- Access all features: View and edit all settings, data and configurations.</li>
                    <li>- Receive notifications: Get alerts on key events, updates and system issues.</li>
                  </ul>
                </li>
                <li className='border-b mt-8 list-disc pb-2'>
                  <p className='text-16 mb-4 text-purple-normal'>Admin</p>
                  {/* <p className=''>Manage team, access all features, and receive notifications.</p> */}
                  <ol>
                    <li>- User management: Create, edit and delete user accounts.</li>
                    <li>- Permission management: Assign roles, access levels and feature permissions.</li>
                    <li>- Configuration: Adjust system settings, workflows and integrations.</li>
                    <li>- Monitoring: Track user activity, system performance and security.</li>
                    <li>- Support: Handle user queries, issues and feedback.</li>
                  </ol>
                </li>
                <li className='border-b mt-8 list-disc pb-2'>
                  <p className='text-16 mb-4 text-purple-normal'>Operations</p>
                  <p className=''>Manage team, access all features, and receive notifications.</p>
                  <ul>
                    <li>- Day-to-day management: Oversee daily tasks, workflows and processes.</li>
                    <li>- Resource allocation: Manage resource assignment and utilization.</li>
                    <li>- Performance tracking: Monitor key performance indicators (KPIs) and metrics.</li>
                    <li>- Process optimization: Analyze, improve and automate workflows.</li>
                  </ul>
                </li>
                {/* <li className='border-b mt-8 list-disc pb-2'>
                  <p className='text-16 mb-4 text-purple-normal'>Customer Support</p>
                  <p className=''>Manage team, access all features, and receive notifications. </p>
                </li> */}
              </ul>
            </div>

          </div>
        </div>
      </div>
      <SuccessModal
        visibility={showSuccessModal}
        toggleVisibility={setShowSuccessModal}
        text="Your invitation has been sent successfully! You can view team members details in dashboard."
        route='/team-members'
      />
    </div>
  )
}

export default AddTeamMember