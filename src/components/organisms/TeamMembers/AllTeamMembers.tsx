import React, { useEffect, useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom';
import CustomTable from '../../atoms/CustomTable/CustomTable';
import OrganizationEmptyState from '../../atoms/Cards/OrganizationEmptyState';
import { useRecoilState, useRecoilValue } from 'recoil';
import { getTeamMembersAtom } from '../../../recoil/atom/teams';
import { getTeamMembers, revokeTeamAccess } from '../../../api/teams';
import StatusTag from '../../atoms/StatusTag';
import { FaEllipsisV } from 'react-icons/fa';
import FilterDropdown from '../../atoms/Cards/FilterDropdown';
import { PiTrash } from 'react-icons/pi';
import CustomButton from '../../atoms/CustomButton/CustomButton';
import useClickOutside from '../../shared/hooks';
import { clustarkToast } from '../../atoms/Toast';
import { NotificationTypes } from '../../shared/helpers/enums';
import Loader from '../../atoms/Loader';
import { loggedTeamInfoAtom } from '../../../recoil/atom/authAtom';
import TeamMembers from '.';
import { FiPlus } from 'react-icons/fi';

const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

const AllTeamMembers = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState(false);
  const [rowId, setRowId] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [, setTeamMembersAtom] = useRecoilState(getTeamMembersAtom);
  const getTeamMembersValue = useRecoilValue(getTeamMembersAtom);
  const getLoggedTeam = useRecoilValue(loggedTeamInfoAtom);

  const debounceSearch = useRef(debounce((q) => fetchTeamMembers(q), 2000)).current;

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const fetchTeamMembers = (q) => {
    setIsFetching(true);
    getTeamMembers({ search: q }).then((res) => {
      if (res.success) {
        setTeamMembersAtom(res.data);
        setIsFetching(false);
      }
    })
  };

  const handleRevokeInvitation = (id) => {
    setIsLoading(true);
    revokeTeamAccess({ teamId: id.toString() }).then((res) => {
      setIsLoading(false);
      if (res.success) {
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        setShowDeleteWarn(false);
        fetchTeamMembers(searchQuery);
      }
    }).catch(() => setIsLoading(false));
  };



  const columns = [
    {
      Header: "Full name",
      accessor: "user.first_name",
      Cell: (row: any) => (
        <p className="text-[#101828]">
          {row.cell.value + " " + row?.cell?.row?.original?.user?.last_name || "--"}
        </p>
      ),
    },

    {
      Header: "Email address",
      accessor: "user.email",
      Cell: (row: any) => <p className='lowercase'>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Phone number",
      accessor: "user.phone_number",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Gender",
      accessor: "user.gender",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Role",
      accessor: "role",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Status",
      accessor: "user.user_account_status",
      Cell: (row: any) => (<StatusTag status={row.cell.value || ''} />),
    },
    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <>
          {getLoggedTeam.role !== "ADMIN" && (

            <div className="relative">
              <FaEllipsisV
                onClick={() => {
                  setShowDropdown(!showDropdown);
                  setRowId(row.cell.row.id);
                }}
                className="text-[#98A2B3] cursor-pointer"
              />
              {showDropdown && row.cell.row.id === rowId && (
                <FilterDropdown>
                  <ul className="text-14 text-neutral-dark" ref={node}>

                    {showDeleteWarn ? (
                      <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                        <div className="flex gap-3">
                          <PiTrash size={18} />
                          Are you sure?
                        </div>
                        <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                          <CustomButton isLoading={isLoading} isTransparent title="Yes" handleClick={() => handleRevokeInvitation(row.cell.row.original.id)} className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer" />
                          <span onClick={() => { setShowDeleteWarn(false) }} className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center">No</span>
                        </div>
                      </li>
                    ) : (
                      <li onClick={() => setShowDeleteWarn(true)} className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer">
                        <PiTrash size={18} />
                        Revoke Access
                      </li>
                    )}
                  </ul>
                </FilterDropdown>
              )}
            </div>
          )}
        </>
      ),
    },
  ];

  useEffect(() => {
    fetchTeamMembers(searchQuery);
  }, []);

  if (isFetching && !searchQuery) {
    return <div>
      <Loader />
    </div>
  };

  return (
    <div>
      <div className=" my-10 py-[23px]">
        {getTeamMembersValue?.data?.length > 0 || searchQuery ? (
          <CustomTable
            data={getTeamMembersValue.data}
            meta={getTeamMembersValue?.meta || {}}
            columns={columns}
            // filterListOptions={["State", "Country"]}
            handleFilter={(e) => console.log(e)}
            // handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            // checkedItems={checkedItems}
            handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
            header={
              <div className="flex justify-between items-center h-[45px] px-2">
                <h1>
                  Teams
                </h1>
                <div className="flex justify-center items-center">
                  <CustomButton
                    className="!w-[180px] !bg-purple-normal !text-white !border-none !font-normal !font-poppins-medium shadow-md !h-10"
                    isTransparent={true}
                    handleClick={() => {
                      navigate("/add-team-member")
                    }}
                    leftIcon={<FiPlus className="ml-3" size={20} />}
                    title="Add team member"
                  />
                </div>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState
              buttonTitle="Add New Team Member"
              handleClick={() => navigate("/add-team-member")}
            />
          </div>
        )}
      </div>

    </div>
  )
}

export default AllTeamMembers