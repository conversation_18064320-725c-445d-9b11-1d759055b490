import React, { useEffect, useState } from "react";
import { FiPlus } from "react-icons/fi";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { useNavigate, useSearchParams } from "react-router-dom";
import PendingInvites from "./PendingInvites";
import AllTeamMembers from "./AllTeamMembers";


const TeamMembers = () => {
  const navigate = useNavigate();
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("activeTab");
  const [activeTab, setActiveTab] = useState<number>(0)

  const teamMembersTab = [
    {
      title: "All team members",
      component: <AllTeamMembers />,
      name: "teamMembers"
    },
    {
      title: "Pending Invitation",
      component: <PendingInvites />,
      name: "pendingInvite"
    },
  ];



  useEffect(() => {
    setActiveTab(queryParam || 0)

  }, [queryParam, activeTab]);


  return (
    <>
      <div>
        
        <div className=" px-1 bg-[#F4F4F6] flex justify-between">

          <div className=" bg-[#F4F4F6] flex justify-between">
            <div className="flex gap-10 py-1 px-1 justify-center items-center  bg-[#F4F4F6] rounded-md">
              {teamMembersTab.map((item, index) => (
                <div
                  key={index}
                  onClick={() => { setActiveTab(index); navigate(`/team-members?activeTab=${index}&tab=${item.name}`) }}
                  className={`cursor-pointer px-5 py-2 rounded  ${activeTab == index ? "bg-white" : "bg-none"}`}>
                  <p className={`text-neutral-normal font-poppins-medium  px-5 text-center text-16 ${activeTab == index ? "text-purple-normal" : "text-gray-950"} `}> {item.title}</p>
                </div>
              ))}
            </div>
          </div>

          
        </div>

        <div>
          {teamMembersTab[activeTab].component}
        </div>

      </div>
    </>
  );
};

export default TeamMembers;
