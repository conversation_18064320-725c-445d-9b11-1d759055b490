import React, { useState } from "react";
import CustomButton from "../../../../components/atoms/CustomButton/CustomButton";
import {
  ButtonProperties,
  errorMessages,
} from "../../../../components/shared/helpers";
import FormikCustomInput from "../../../../components/atoms/CustomInput/FormikCustomInput";
import { Formik, Form } from "formik";
import { ArrowRight } from "iconsax-react";
import { useNavigate } from "react-router-dom";
import * as yup from "yup";
import { forgetPassword } from "../../../../api/auth";
import { emailAtom } from "../../../../recoil/atom/authAtom";
import { useRecoilState } from "recoil";

interface Values {
  email: string;
}
const initialState = {
  email: "",
};
const ForgotPassword = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [, setEmailAtom] = useRecoilState(emailAtom);

  const step1Schema = yup.object().shape({
    email: yup
      .string()
      .email(errorMessages.email)
      .required(errorMessages.required),
  });

  const handleSubmit = (value: Values) => {
    setIsLoading(true)
    forgetPassword({ email: value.email }).then((response) => {
      setIsLoading(false)
      if (response?.success) {
        setEmailAtom({email: value.email});
        navigate(`/auth-otp`, { state: { email: value.email } });
      }
    });
  };

  return (
    <>
      <div className="bg-white w-[90%] smallLaptop:w-4/5 desktop:min-w-[636px] mx-auto relative  h-[90vh] overflow-y-scroll hide-scrollbar pt-28 pb-10">
        <div>
          <h1 className="text-purple-darker text-[32px] font-poppins-medium font-semibold text-center">
            Forgot Password?
          </h1>
          <div className="text-center text-neutral-normal text-[18px] font-mulish w-[4/5] m-auto px-10 mt-2.5">
            Enter your company’s email address linked to <br /> this account.
          </div>
          <div className="flex flex-col px-7 pb-4 mt-[72px]">
            <Formik<Values>
              initialValues={initialState}
              onSubmit={handleSubmit}
              validationSchema={step1Schema}
            >
              {() => (
                <Form>
                  <div>
                    <FormikCustomInput
                      label="Email"
                      id="email"
                      name="email"
                      placeholder="enter email address"
                      type="email"
                    />
                  </div>
                  <CustomButton
                    className="mt-[56px]"
                    icon={<ArrowRight className="ml-3" size={20} />}
                    type="submit"
                    title="Send"
                    handleClick={() => {}}
                    isLoading={isLoading}
                    variant={ButtonProperties.VARIANT.primary.name}
                  />
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </>
  );
};

export default ForgotPassword;
