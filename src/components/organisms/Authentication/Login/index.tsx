import React, { useEffect, useState } from 'react';
import CustomButton from '../../../../components/atoms/CustomButton/CustomButton';
import { ButtonProperties, errorMessages } from '../../../../components/shared/helpers';
import FormikCustomInput from '../../../../components/atoms/CustomInput/FormikCustomInput';
import { Formik, Form } from 'formik';
import { ArrowRight } from 'iconsax-react';
import { useNavigate } from 'react-router-dom';
import * as yup from 'yup';
import CustomLink from '../../../atoms/CustomLink/CustomLink';
import { login } from '../../../../api/auth';
import { loginProps } from '../../../schemas/auth';
import { useRecoilState } from 'recoil';
import {
  createAccountAtom,
  emailAtom,
  loggedTeamInfoAtom,
  loggedUserAtom,
  otpAtom,
} from '../../../../recoil/atom/authAtom';

interface IValues {
  email: string;
  password: string;
}
const initialState = {
  email: '',
  password: '',
};
const Login = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [, setLoggedUserAtom] = useRecoilState(loggedUserAtom);
  const [, setLoggedTeamInfoAtom] = useRecoilState(loggedTeamInfoAtom);
  const [, setAccountAtom] = useRecoilState(createAccountAtom);
  const [, setEmailAtom] = useRecoilState(emailAtom);
  const [, setOtpAtom] = useRecoilState(otpAtom);
  const token = localStorage.getItem("token")

  useEffect(() => {
    setAccountAtom({});
    setEmailAtom({ email: '' });
    setOtpAtom({ email: '', otp: '' });
  }, []);

  useEffect(() => {
    if(token) {
      navigate("/dashboard")
    }
  }, [token]);



  const loginSchema = yup.object().shape({
    email: yup.string().email(errorMessages.email).required(errorMessages.required),
    password: yup.string().required(errorMessages.required),
  });

  const handleSubmit = (values: IValues) => {
    setIsLoading(true);
    const payload: loginProps = {
      email: values.email,
      password: values.password,
    };
    login(payload).then((response) => {
      setIsLoading(false);
      if (response?.success) {
        setLoggedUserAtom(response?.data?.user);
        setLoggedTeamInfoAtom(response?.data?.team);
        navigate('/dashboard');
      }
    });
  };

  return (
    <div className='bg-white w-[90%] smallLaptop:w-4/5 desktop:min-w-[636px] mx-auto relative  h-[90vh] overflow-y-scroll hide-scrollbar py-20'>
      <div>
        <h1 className='text-purple-dark text-18 font-poppins-medium font-semibold text-center'>
          Login to your account
        </h1>
        <div className='text-center text-neutral-normal text-[15px] font-poppins-medium w-[4/5] m-auto px-10 mt-2.5'>
          Welcome back! <br />
          Sign into your account to start the productivity of the day!.
        </div>
        <div className='flex flex-col px-7 pb-4 mt-[72px]'>
          <Formik<IValues>
            initialValues={initialState}
            onSubmit={handleSubmit}
            validationSchema={loginSchema}
          >
            {() => (
              <Form>
                <div>
                  <FormikCustomInput
                    label='Company email'
                    id='email'
                    name='email'
                    placeholder='enter email address'
                    type='email'
                  />
                </div>
                <div className='mt-8'>
                  <FormikCustomInput
                    label='Password'
                    id='password'
                    name='password'
                    placeholder='**********'
                    type='password'
                  />
                </div>
                <div className='flex justify-end mt-4'>
                  <CustomLink
                    destination='/forgot-password'
                    className='font-semibold !font-poppins'
                    text='Forgot password?'
                  />
                </div>
                <CustomButton
                  className='mt-[47px]'
                  icon={<ArrowRight className='ml-3' size={20} />}
                  type='submit'
                  title='Sign in'
                  handleClick={() => {}}
                  isLoading={isLoading}
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </Form>
            )}
          </Formik>
          <p className='text-poppins text-neutral-normal text-center mt-10'>
            Don’t have an account yet?{' '}
            <CustomLink destination='/create-account' text='Create Account' />
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
