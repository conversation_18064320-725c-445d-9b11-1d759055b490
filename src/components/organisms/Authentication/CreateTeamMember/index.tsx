import React, { useState } from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { Form, Formik } from "formik";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import { ButtonProperties } from "../../../shared/helpers";
import FormikCustomPhoneInput from "../../../atoms/CustomInput/FormikCustomPhoneInput";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { createTeamMemberSchema } from "../../../shared/helpers/schemas";
import { acceptTeamInvite, declineTeamInvitation } from "../../../../api/teams";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { clustarkToast } from "../../../atoms/Toast";
import { login } from "../../../../api/auth";
import { useNavigate } from "react-router-dom";
import { useRecoilState } from "recoil";
import { loggedUserAtom } from "../../../../recoil/atom/authAtom";

interface Values {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  gender: string;
  password: string;
  confirmPassword: string;
}

const CreateTeamMember = ({token, data}) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [, setLoggedUserAtom] = useRecoilState(loggedUserAtom);

  const initialState = {
    firstName: data?.user ? data?.user?.first_name : "",
    lastName: data?.user ? data?.user?.last_name : "",
    phoneNumber: data?.user ? data?.user?.phone_number : "",
    gender: data?.user ? data?.user?.gender : "",
    password: "",
    confirmPassword: "",
  };

  const loginUser = (email: string, password: string) => {
    setIsLoading(true);
    const payload = {
      email: email,
      password:  password,
    }
    login(payload).then((response) => {
      setIsLoading(false);
      if(response?.success) {
        setLoggedUserAtom(response?.data?.user);
        navigate("/dashboard")
      }
    })
  }

  const handleSubmit = (values) => {
    const payload = {
      firstName: values.firstName,
      lastName: values.lastName,
      gender: values.gender,
      password: values.password,
      password_confirmation: values.confirmPassword,
      phoneNumber: values.phoneNumber,
      email: data?.email,
      role: data?.role,
      token: token,
    };

    if(token) {
      setIsLoading(true);
      acceptTeamInvite(payload).then((res) => {
        if(res.success) {
          setIsLoading(false);
          if(data?.user) {
            navigate("/")
          } else {
            clustarkToast(NotificationTypes.SUCCESS, res.message)
            loginUser(data?.email, values.password)
          }
        }
      }).catch(() => {
        setIsLoading(false);
      })
    }
  };

  
   const declineInvitation = () => {
    setIsLoading(true);
    if(token) {
      declineTeamInvitation({token: token}).then((res) => {
        if(res.success) {
          clustarkToast(NotificationTypes.SUCCESS, res.message)
          window.location.href = "/"
        }
      }).catch(()=> setIsLoading(false));
    }
  }


  return (
    <>
      <div className="bg-white w-[90%] smallLaptop:w-4/5 desktop:min-w-[636px] mx-auto relative  h-[90vh] overflow-y-scroll hide-scrollbar py-20">
        <h1 className="text-purple-darker text-[32px] font-poppins-medium font-semibold text-center">
          Accept Team Member Invite
        </h1>
        <div className="text-center text-neutral-normal text-[18px] font-mulish w-[4/5] m-auto px-10 mt-2.5">
          <strong>{data?.business?.name}</strong> will love to have you come onboard. <br />
          Please fill in your details to set up your team membership account. <br /> <br />
           <strong>Do you want to decline this invitation?. <span className="font-poppins-medium text-purple-normal cursor-pointer" onClick={declineInvitation}>Click here</span></strong>   
        </div>
        <div className="flex flex-col px-7 mt-[48px]">
          <p className="text-neutral-dark text-20 font-poppins-medium">
            Personal Information
          </p>
          <Formik<Values>
            initialValues={initialState}
            onSubmit={handleSubmit}
            validationSchema={data?.user ? null : createTeamMemberSchema}
            enableReinitialize
          >
            {({ setFieldValue, values }) => (
              <Form>
                <div className="grid tablet:grid-cols-2 gap-4 mt-8">
                  <div>
                    <FormikCustomInput
                      label="First Name"
                      id="firstName"
                      name="firstName"
                      placeholder="enter your first name"
                      type="text"
                      value={values.firstName}
                      disabled={data?.user}
                    />
                  </div>
                  <div>
                    <FormikCustomInput
                      label="Last Name"
                      id="lastName"
                      name="lastName"
                      placeholder="enter your last name"
                      type="text"
                      disabled={data?.user}
                    />
                  </div>
                </div>
                <div className="grid tablet:grid-cols-2 gap-4 mt-8">
                  <div>
                    <FormikCustomPhoneInput
                      label="Phone Number"
                      name="phoneNumber"
                      id="phoneNumber"
                      value={values.phoneNumber}
                      onChange={(value: string) => {
                        setFieldValue("phoneNumber", value);
                      }}
                      disabled={data?.user}
                    />
                  </div>
                  <div>
                    <FormikCustomSelect
                      label="Gender"
                      options={[
                        { text: "Male", value: "Male" },
                        { text: "Female", value: "Female" },
                      ]}
                      name="gender"
                      value={values.gender}
                      placeholder="select gender"
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("gender", item.value);
                      }}
                      disabled={data?.user}
                    />
                  </div>
                </div>

                <div>
                  <div className="mt-8">
                    <FormikCustomInput
                      label="Email"
                      id="email"
                      name="email"
                      type="email"
                      value={data?.invitation?.email || data?.email}
                      disabled
                    />
                  </div>
                  <div className="mt-8">
                    <FormikCustomInput
                      label="Role"
                      id="role"
                      name="role"
                      type="text"
                      value={data?.invitation?.role || data?.role}
                      disabled
                    />
                  </div>
                  {!data.user && (
                    <>
                    <div className="mt-8">
                      <FormikCustomInput
                        label="Create Password"
                        id="password"
                        name="password"
                        placeholder="*************"
                        type="password"
                        value={values.password}
                      />
                    </div>
                    <div className="mt-8">
                      <FormikCustomInput
                        label="Confirm Password"
                        id="confirmPassword"
                        name="confirmPassword"
                        placeholder="*************"
                        type="password"
                        value={values.confirmPassword}
                      />
                    </div>
                    </>
                  )}
                </div>
                <CustomButton
                  className="mt-[86px]"
                  type="submit"
                  title="Accept Invite"
                  isLoading={isLoading}
                  handleClick={() => {}}
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </>
  );
};

export default CreateTeamMember;
