import React, { useEffect, useState } from "react";
import CustomButton from "../../../../components/atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../../components/shared/helpers";
import FormikCustomInput from "../../../../components/atoms/CustomInput/FormikCustomInput";
import { Formik, Form } from "formik";
import { ArrowRight } from "iconsax-react";
import { passwordSchema } from "../../../../components/shared/helpers/schemas";
import AuthSuccessModal from "../../../atoms/CustomModal/AuthSuccessModal";
import { resetPassword } from "../../../../api/auth";
import { resetPasswordProps } from "../../../schemas/auth";
import { useRecoilValue } from "recoil";
import { otpAtom } from "../../../../recoil/atom/authAtom";
import { useNavigate, useSearchParams } from "react-router-dom";

interface Values {
  password: string;
  confirmPassword: string;
}

const CreateNewPassword = () => {
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("status");
 
  const getOtpAtom = useRecoilValue(otpAtom);

  const initialState = {
    password: "",
    confirmPassword: "",
  };

  const handleSubmit = (values: Values) => {
    setIsLoading(true);
  const payload: resetPasswordProps = {
    email: getOtpAtom.email,
    otp:  getOtpAtom.otp,
    password_confirmation: values.password,
    password:  values.password,
  }
  resetPassword(payload).then((response) => {
    setIsLoading(false);
    if(response?.success) {
      setShowSuccessModal(true);
      navigate(`/create-password?status=success`)
    }
  })   
  
  }

  useEffect(() => {
    if(queryParam === "success") {
      setShowSuccessModal(true)
    }
  }, [])

  return (
    <>
    <div className="bg-white w-[90%] smallLaptop:w-4/5 desktop:min-w-[636px] mx-auto relative  h-[90vh] overflow-y-scroll hide-scrollbar py-20">

      <div>
        <h1 className="text-purple-darker text-[32px] font-poppins-medium font-semibold text-center">
          Create New Password
        </h1>
        <div className="text-center text-neutral-normal text-[18px] font-mulish w-[4/5] m-auto px-10 mt-2.5">
        Your new password must be different from previously used password
        </div>
        <div className="flex flex-col px-7 mt-[72px]">
          <Formik<Values>
            initialValues={initialState}
            onSubmit={handleSubmit}
            validationSchema={passwordSchema}
            enableReinitialize
          >
            {({values}) => (
              <Form>
                <div>
                  <FormikCustomInput
                    label="New Password"
                    id="password"
                    name="password"
                    placeholder="*************"
                    type="password"
                    value={values.password}
                  />
                </div>
                <div className="mt-8">
                  <FormikCustomInput
                    label="Confirm Password"
                    id="confirmPassword"
                    name="confirmPassword"
                    placeholder="*************"
                    type="password"
                    value={values.confirmPassword}
                  />
                </div>
                <CustomButton
                  className="mt-[56px]"
                  icon={<ArrowRight className="ml-3" size={20} />}
                  type="submit"
                  title="Continue"
                  handleClick={() => {}}
                  isLoading={isLoading}
                  variant={ButtonProperties.VARIANT.primary.name}
                />
                
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
    {showSuccessModal && <AuthSuccessModal/>}
    </>
  );
};

export default CreateNewPassword;
