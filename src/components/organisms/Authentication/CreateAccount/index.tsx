import StepperComponent from "../../../../components/atoms/StepperComponent/StepperComponent";
import AuthLayout from "../../../../components/layouts/auth/AuthLayout";
import React from "react";
import Step1 from "./Step1";
import OtpStep2 from "./OtpStep2";
import Step3 from "./Step3";
import Step4 from "./Step4";
import Step5 from "./Step5";


interface StepProps {
  goNextStep: () => void;
  goPreviousStep: () => void;
  currentStep: number;
  isLast: boolean;
  isFirst: boolean;
  step: number;
}

const CreateAccount = () => {

  return (
    <AuthLayout>
      <StepperComponent steps={createAccountStep} />
    </AuthLayout>
  );
};

export default CreateAccount;
const createAccountStep = [
  { element: (stepProps: StepProps) => <Step1 step={stepProps} /> },
  { element: (stepProps: StepProps) => <OtpStep2 step={stepProps} /> },
  { element: (stepProps: StepProps) => <Step3 step={stepProps} /> },
  { element: (stepProps: StepProps) => <Step4 step={stepProps} /> },
  { element: (stepProps: StepProps) => <Step5 step={stepProps}  /> },
];
