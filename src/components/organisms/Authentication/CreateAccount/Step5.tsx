import React, { useState } from "react";
import CustomButton from "../../../../components/atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../../components/shared/helpers";
import FormikCustomInput from "../../../../components/atoms/CustomInput/FormikCustomInput";
import { Formik, Form } from "formik";
import { ArrowLeft, ArrowRight } from "iconsax-react";
import { useNavigate } from "react-router-dom";
import { passwordSchema } from "../../../../components/shared/helpers/schemas";
import { useRecoilState, useRecoilValue } from "recoil";
import { createAccountAtom, loggedTeamInfoAtom, loggedUserAtom } from "../../../../recoil/atom/authAtom";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { login, registerCompany } from "../../../../api/auth";
import { loginProps, registerCompanyProps } from "../../../schemas/auth";

interface Values {
  password: string;
  confirmPassword: string;
}

const Step5 = ({step}: any) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [, setLoggedUserAtom] = useRecoilState(loggedUserAtom);
  const [, setAccountAtom] = useRecoilState(createAccountAtom);
  const getAuthDataAtom = useRecoilValue(createAccountAtom);
  const [, setLoggedTeamInfoAtom] = useRecoilState(loggedTeamInfoAtom);

  const initialState = {
    password:  "",
    confirmPassword:  "",
  };

  const loginUser = (email: string, password: string) => {
    setIsLoading(true);
    const payload: loginProps = {
      email: email,
      password:  password,
    }
    login(payload).then((response) => {
      setIsLoading(false);
      if(response?.success) {
        setAccountAtom({})
        setLoggedUserAtom(response?.data?.user);
        setLoggedTeamInfoAtom(response?.data?.team);
        navigate("/dashboard")
      }
    })
  }

  const handleSubmit = (values: Values) => {
    setAccountAtom(prevState => ({
      ...prevState,
      password: values.password,
      confirmPassword: values.confirmPassword,
    }));

    setIsLoading(true);
    const payload: registerCompanyProps = {
      email: getAuthDataAtom?.companyEmail || "",
      first_name: getAuthDataAtom?.firstName || "",
      last_name: getAuthDataAtom?.lastName || "",
      phone_number: getAuthDataAtom?.phoneNumber || "",
      gender: getAuthDataAtom?.gender || "",
      password: values.password || "",
      password_confirmation: values.confirmPassword || "",
      business_name: getAuthDataAtom?.businessName || "",
      business_address: getAuthDataAtom?.businessAddress || "",
      business_country: getAuthDataAtom?.country || "",
      business_state: getAuthDataAtom?.state || "",
    };
    registerCompany(payload).then((response) => {
      setIsLoading(false);
      if (response?.success) {
        clustarkToast(NotificationTypes.SUCCESS, response.message)
        loginUser(payload.email, payload.password)
      }
    });
  };

  const handleGoBack = (values: Values) => {step.goPreviousStep();  setAccountAtom(prevState => ({
    ...prevState,
    password: values.password,
    confirmPassword: values.confirmPassword,
  })); navigate("/create-account?step=4");}

  return (
    <>
      <div>
        <h1 className="text-purple-darker text-[32px] font-poppins-medium font-semibold text-center">
          Create Account
        </h1>
        <div className="text-center text-neutral-normal text-[18px] font-mulish w-[4/5] m-auto px-10 mt-2.5">
          We’ll love to have you come onboard. <br />
          Complete sign up to continue.
        </div>
        <div className="flex flex-col px-7 mt-[72px]">
          <Formik<Values>
            initialValues={initialState}
            onSubmit={handleSubmit}
            validationSchema={passwordSchema}
            enableReinitialize
          >
            {({ values }) => (
              <Form>
                <div>
                  <FormikCustomInput
                    label="Create Password"
                    id="password"
                    name="password"
                    placeholder="*************"
                    type="password"
                    value={values.password}
                  />
                </div>
                <div className="mt-8">
                  <FormikCustomInput
                    label="Confirm Password"
                    id="confirmPassword"
                    name="confirmPassword"
                    placeholder="*************"
                    type="password"
                    value={values.confirmPassword}
                  />
                </div>

                <div className="mt-[86px] flex justify-between">
                  <CustomButton
                    leftIcon={<ArrowLeft className="mr-3" size={20} />}
                    type="button"
                    isTransparent
                    title="Back"
                    handleClick={() => handleGoBack(values)}
                    size={ButtonProperties.SIZES.small}
                    variant={ButtonProperties.VARIANT.secondary.name}
                  />
                  <CustomButton
                    icon={<ArrowRight className="ml-3" size={20} />}
                    type="submit"
                    title="Sign Up"
                    isLoading={isLoading}
                    handleClick={() => {}}
                    size={ButtonProperties.SIZES.small}
                    variant={ButtonProperties.VARIANT.primary.name}
                  />
                </div>
                {/* <CustomButton
                  className="mt-[56px]"
                  icon={<ArrowRight className="ml-3" size={20} />}
                  type="submit"
                  title="Sign Up"
                  handleClick={() => {}}
                  isLoading={isLoading}
                  variant={ButtonProperties.VARIANT.primary.name}
                /> */}
               
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </>
  );
};

export default Step5;
