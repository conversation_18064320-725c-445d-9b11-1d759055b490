import React, { useState } from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import { Formik, Form } from "formik";
import { ArrowLeft, ArrowRight } from "iconsax-react";
import { useNavigate } from "react-router-dom";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { Country, State } from "country-state-city";
import * as yup from "yup";
import { createAccountAtom } from "../../../../recoil/atom/authAtom";
import { useRecoilState, useRecoilValue } from "recoil";

interface Values {
  businessName: string;
  businessAddress: string;
  country: string;
  state: string;
}

const Step4 = ({ step }: any) => {
  const navigate = useNavigate();

  const countries = Country.getAllCountries().map((country) => ({
    text: country.name,
    value: country.isoCode,
  }));

  const [selectedCountry, setSelectedCountry] = useState<string>("");
  const [, setSelectedState] = useState<string>("");
  const [, setAccountAtom] = useRecoilState(createAccountAtom);
  const getAuthDataAtom = useRecoilValue(createAccountAtom)

  const states = State.getStatesOfCountry(selectedCountry).map((state) => ({
    text: state.name,
    value: state.isoCode,
  }));


  const initialState = {
    businessName: getAuthDataAtom?.businessName ? getAuthDataAtom?.businessName : "",
    businessAddress: getAuthDataAtom?.businessAddress ? getAuthDataAtom?.businessAddress : "",
    country: getAuthDataAtom?.country ? getAuthDataAtom?.country : "",
    state: getAuthDataAtom?.state ? getAuthDataAtom?.state : "",
  };

  const step4Schema = yup.object().shape({
    businessName: yup.string().required(errorMessages.required),
    businessAddress: yup.string().required(errorMessages.required),
    country: yup.string().required(errorMessages.required),
    state: yup.string().required(errorMessages.required),
  });

  const handleSubmit = (value: Values) => {
    setAccountAtom(prevState => ({
      ...prevState,
      businessName: value.businessName,
      businessAddress: value.businessAddress,
      country: value.country,
      state: value.state,
    }))
    step.goNextStep();
    navigate("/create-account?step=5");
  }

  const handleGoBack = (values: Values) => {step.goPreviousStep();  setAccountAtom(prevState => ({
    ...prevState,
    businessName: values.businessName,
    businessAddress: values.businessAddress,
    country: values.country,
    state: values.state,
  })); navigate("/create-account?step=3");}

  return (
    <>
      <div>
        <h1 className="text-purple-darker text-[32px] font-poppins-medium font-semibold text-center">
          Create Account
        </h1>
        <div className="text-center text-neutral-normal text-[18px] font-mulish w-[4/5] m-auto px-10 mt-2.5">
          We will love to have you come onboard. <br />
          Complete sign up to continue.
        </div>
        <div className="flex flex-col px-7 mt-[48px]">
          <p className="text-neutral-dark text-20 font-poppins-medium">
            Business Information
          </p>
          <Formik<Values>
            initialValues={initialState}
            onSubmit={handleSubmit}
            validationSchema={step4Schema}
            enableReinitialize
          >
            {({ setFieldValue, values }) => (
              <Form>
                <div className=" mt-8">
                  <FormikCustomInput
                    label="Business Name"
                    id="businessName"
                    name="businessName"
                    placeholder="enter your business name"
                    type="text"
                  />
                </div>
                <div className="mt-8">
                  <FormikCustomInput
                    label="Business Address"
                    id="businessAddress"
                    name="businessAddress"
                    placeholder="enter your business address"
                    type="text"
                  />
                </div>

                <div className="mt-8">
                  <FormikCustomSelect
                    label="Country"
                    options={countries}
                    name="country"
                    value={selectedCountry || values.country}
                    onChange={(item: {value: string, text: string}) => {
                     
                      setSelectedCountry(item.value);
                      setFieldValue("country", item.text);
                    }}
                  />
                </div>
                <div className="mt-8">
                  <FormikCustomSelect
                    label="State"
                    options={states}
                    name="state"
                    onChange={(item: {value: string, text: string}) => {
                      setSelectedState(item.value);
                      setFieldValue("state", item.text);
                    }}
                    value={values.state}
                  />
                </div>
                <div className="mt-[86px] flex justify-between">
                  <CustomButton
                    leftIcon={<ArrowLeft className="mr-3" size={20} />}
                    type="button"
                    isTransparent
                    title="Back"
                    handleClick={() => handleGoBack(values)}
                    size={ButtonProperties.SIZES.small}
                    variant={ButtonProperties.VARIANT.secondary.name}
                  />
                  <CustomButton
                    icon={<ArrowRight className="ml-3" size={20} />}
                    type="submit"
                    title="Next"
                    handleClick={() => {}}
                    size={ButtonProperties.SIZES.small}
                    variant={ButtonProperties.VARIANT.primary.name}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </>
  );
};

export default Step4;
