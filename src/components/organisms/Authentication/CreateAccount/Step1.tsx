import React, { useState } from "react";
import CustomButton from "../../../../components/atoms/CustomButton/CustomButton";
import {
  ButtonProperties,
  errorMessages,
} from "../../../../components/shared/helpers";
import FormikCustomInput from "../../../../components/atoms/CustomInput/FormikCustomInput";
import { Formik, Form } from "formik";
import { ArrowRight } from "iconsax-react";
import { useNavigate } from "react-router-dom";
import * as yup from "yup";
import CustomLink from "../../../atoms/CustomLink/CustomLink";
import { validateEmail } from "../../../../api/auth";
import { useRecoilState } from "recoil";
import { createAccountAtom } from "../../../../recoil/atom/authAtom";
import CustomCheckBox from "../../../atoms/CustomCheckBox/CustomCheckBox";

interface Values {
  email: string;
  terms: boolean;
}

const initialState = {
  email: "",
  terms: false,
};

const Step1 = ({ step }: any) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [, setAccountAtom] = useRecoilState(createAccountAtom);

  const step1Schema = yup.object().shape({
    email: yup
      .string()
      .email(errorMessages.email)
      .required(errorMessages.required),
    terms: yup
      .boolean()
      .oneOf([true], errorMessages.required)
      .required(errorMessages.required),
  });

  const handleSubmit = (value: Values) => {
    setIsLoading(true);
    validateEmail(value).then((response) => {
      setIsLoading(false);
      if (response?.success) {
        step.goNextStep();
        setAccountAtom({ companyEmail: value.email });
        navigate("/create-account?step=2", { state: { email: value.email } });
      }
    });
  };

  return (
    <>
      <div>
        <h1 className="text-purple-darker text-[18px] font-poppins-medium font-semibold text-center">
          Create Account
        </h1>
        <div className="text-center text-neutral-normal text-[12px] font-poppins-medium w-[4/5] m-auto px-10 mt-2.5">
          We will love to have you come onboard. <br />
          Join over 500+ organization around the globe and enhance your
          productivity.
        </div>
        <div className="flex flex-col px-7 pb-4 mt-[72px]">
          <Formik<Values>
            initialValues={initialState}
            onSubmit={handleSubmit}
            validationSchema={step1Schema}
          >
            {({ values, setFieldValue }) => (
              <Form>
                <div>
                  <FormikCustomInput
                    label="Email"
                    id="email"
                    name="email"
                    placeholder="enter email address"
                    type="email"
                  />
                </div>

                <CustomButton
                  className="mt-[50px]"
                  icon={<ArrowRight className="ml-3" size={20} />}
                  type="submit"
                  title="Next"
                  handleClick={() => {}}
                  isLoading={isLoading}
                  variant={ButtonProperties.VARIANT.primary.name}
                  isDisabled={!values.terms}
                />

                <div className="flex gap-2 mt-3">
                  <CustomCheckBox
                    name="terms"
                    id="terms"
                    onChange={() => setFieldValue("terms", !values.terms)}
                    required
                  />
                  <a
                    href="https://www.clustark.io/terms-and-conditions"
                    className=" "
                  >
                    I accept the{" "}
                    <span className="text-purple-normal font-bold text-[1rem]">
                      terms & conditions
                    </span>{" "}
                  </a>
                </div>
              </Form>
            )}
          </Formik>
          <p className="text-poppins text-neutral-normal text-center mt-10">
            Already had an account?{" "}
            <CustomLink
              destination="/"
              className="text-purple-normal-active"
              text="Sign In"
            />
          </p>
        </div>
      </div>
    </>
  );
};

export default Step1;
