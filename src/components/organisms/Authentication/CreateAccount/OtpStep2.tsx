import React, { useEffect, useRef, useState } from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../shared/helpers";
import { useLocation, useNavigate } from "react-router-dom";
import OTPInput from "react-otp-input";
import moment from "moment";
import ErrorMessage from "../../../atoms/ErrorMessage";
import { sendOtp, validateOtp } from "../../../../api/auth";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { useRecoilState } from "recoil";
import { otpAtom } from "../../../../recoil/atom/authAtom";

const OtpStep2 = ({ step, destination, emailState, handleAction }: any) => {
  const isMounted = useRef(false);
  const [otp, setOtp] = useState<string>("");
  const [timer, setTimer] = useState<number>(120);
  const [error, setError] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [, setOtpAtom] = useRecoilState(otpAtom);

  const navigate = useNavigate();
  const location = useLocation();
  const { email } = location.state || {};

  const sendOtpRequest = () => {
    sendOtp({ email: email || emailState }).then((response) => {
      if (response?.success) {
        clustarkToast(NotificationTypes.SUCCESS, response.message);
      }
    });
  };

  const validateOtpRequest = () => {
    const payload = {
      email: email || emailState,
      otp: otp,
    };
    validateOtp(payload).then((response) => {
      setIsLoading(false);
      if (response?.success) {
        setOtpAtom({ email: email, otp: otp });
        if (destination) {
          navigate(destination);
          handleAction()
        } else {
          step.goNextStep();
          navigate("/create-account?step=3");
        }
      }
    });
  };

  useEffect(() => {
    if (isMounted.current) return;
    isMounted.current = true;
    sendOtpRequest();
  }, []);

  useEffect(() => {
    const timerInterval = setInterval(() => {
      setTimer((prevTime) => (prevTime > 0 ? prevTime - 1 : 0));
    }, 1000);


    return () => clearInterval(timerInterval);
  }, [timer]);


  useEffect(() => {
    if (otp) {
      setError("");
    }
  }, [otp]);

  const handleSubmit = () => {
    if (!otp) {
      setError("Enter OTP");
    } else {
      setError("");
      setIsLoading(true);
      validateOtpRequest();
    }
  };

  const resendOtp = () => {
    if(timer === 0) {
      sendOtpRequest()
      setTimer(120)
    }
  }

  return (
    <>
      <div>
        {/* <h1 className="text-purple-dark text-[18px] font-poppins-medium font-semibold text-center">
          OTP
        </h1> */}
        <div className="text-center font-poppins text-[13px] font-mulish w-[4/5] m-auto px-10 mt-2.5">
          Please enter the 6 digit code sent to
          <br />
          {email || emailState}
        </div>
        <div className="flex flex-col justify-center font-poppins items-center px-7 mt-[36px]">
          <div>
            <h1 className="font-poppins font-semibold text-neutral-normal-active mb-4">
              Enter OTP
            </h1>
            <OTPInput
              value={otp}
              inputStyle="!w-[46px] !h-[46px] smallLaptop:!w-[56px] smallLaptop:!h-[56px]  !rounded-md border-[0.5px] border-purple-dark-active outline-none mr-4"
              onChange={setOtp}
              numInputs={6}
              inputType="number"
              renderInput={(props) => <input {...props} />}
            />
            <ErrorMessage error={error} />
          </div>
          <p className="text-[#484E62] text-14 mt-6">
            Didn’t get the code?{" "}
            <span
              className={`text-purple-dark cursor-pointer  ${timer === 0 ? "cursor-pointer font-semibold" : ""} `}
              onClick={resendOtp}
            >
              Resend
            </span>
          </p>
          {timer !== 0 && (
            <p className="mt-1.5 font-medium font-poppins-medium text-[#484E62] text-14">
              Expires in{" "}
              <span className="text-[#C03535]">
                {moment.utc(timer * 1000).format("mm:ss")}
              </span>
            </p>
          ) }
          <CustomButton
            className="mt-10"
            type="submit"
            title="Confirm"
            handleClick={handleSubmit}
            isLoading={isLoading}
            variant={ButtonProperties.VARIANT.primary.name}
          />
          {!destination && (

          <p className="mt-10 font-poppins-medium text-neutral-normal text-14 mb-6">
            Want to change your email address?{" "}
            <span onClick={() => navigate(-1)} className="text-purple-dark font-semibold cursor-pointer"> Click Here</span>
          </p>
          )}
        </div>
      </div>
    </>
  );
};

export default OtpStep2;
