import React from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import { Formik, Form } from "formik";
import { ArrowRight } from "iconsax-react";
import { useNavigate } from "react-router-dom";
import FormikCustomPhoneInput from "../../../atoms/CustomInput/FormikCustomPhoneInput";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import * as yup from "yup";
import { useRecoilState, useRecoilValue } from "recoil";
import { createAccountAtom } from "../../../../recoil/atom/authAtom";


interface Values {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  gender: string;
  email: string;
};

const Step3 = ({ step }: any) => {
  const navigate = useNavigate();
  const [, setAccountAtom] = useRecoilState(createAccountAtom);
  const getAuthDataAtom = useRecoilValue(createAccountAtom);

  const initialState = {
    firstName: getAuthDataAtom?.firstName ? getAuthDataAtom?.firstName : "",
    lastName: getAuthDataAtom?.lastName ? getAuthDataAtom?.lastName : "",
    phoneNumber: getAuthDataAtom?.phoneNumber ? getAuthDataAtom?.phoneNumber : "",
    gender: getAuthDataAtom?.gender ? getAuthDataAtom?.gender : "",
    email: getAuthDataAtom?.companyEmail ? getAuthDataAtom?.companyEmail : "",
  };

  const step3Schema = yup.object().shape({
    firstName: yup.string().required(errorMessages.required),
    lastName: yup.string().required(errorMessages.required),
    phoneNumber: yup.string().required(errorMessages.required).min(13),
    gender: yup.string().required(errorMessages.required),
    email: yup
      .string()
      .email(errorMessages.email)
      .required(errorMessages.required),
  });

  const handleSubmit = (values) => {
    setAccountAtom(prevState => ({
      ...prevState,
      firstName: values.firstName,
      lastName: values.lastName,
      phoneNumber: values.phoneNumber,
      gender: values.gender,
      email: values.email,
    }));
    step.goNextStep();
    navigate("/create-account?step=4");
  };

  return (
    <>
      <div>
        <h1 className="text-purple-darker text-[32px] font-poppins-medium font-semibold text-center">
          Create Account
        </h1>
        <div className="text-center text-neutral-normal text-[18px] font-mulish w-[4/5] m-auto px-10 mt-2.5">
          We will love to have you come onboard. <br />
          Complete sign up to continue.
        </div>
        <div className="flex flex-col px-7 mt-[48px]">
          <p className="text-neutral-dark text-20 font-poppins-medium">
            Personal Information
          </p>
          <Formik<Values>
            initialValues={initialState}
            onSubmit={handleSubmit}
            validationSchema={step3Schema}
            enableReinitialize
          >
            {({ setFieldValue, values }) => (
              <Form>
                <div className="grid tablet:grid-cols-2 gap-4 mt-8">
                  <div>
                    <FormikCustomInput
                      label="First Name"
                      id="firstName"
                      name="firstName"
                      placeholder="enter your first name"
                      type="text"
                      value={values.firstName}
                    />
                  </div>
                  <div>
                    <FormikCustomInput
                      label="Last Name"
                      id="lastName"
                      name="lastName"
                      placeholder="enter your last name"
                      type="text"
                    />
                  </div>
                </div>
                <div className="mt-8">
                  <FormikCustomInput
                    label="Email"
                    id="email"
                    name="email"
                    placeholder="enter your email address"
                    type="email"
                    readOnly
                    disabled
                  />
                </div>
                <div>
                  <div className="mt-8">
                    <FormikCustomPhoneInput
                      label="Phone Number"
                      name="phoneNumber"
                      id="phoneNumber"
                      value={values.phoneNumber}
                      onChange={(value: string) => {
                        setFieldValue("phoneNumber", value);
                      }}
                    />
                  </div>
                  <div className="mt-8">
                    <FormikCustomSelect
                      label="Gender"
                      options={[
                        { text: "Male", value: "Male" },
                        { text: "Female", value: "Female" },
                      ]}
                      name="gender"
                      value={values.gender}
                      placeholder="select gender"
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("gender", item.value);
                      }}
                    />
                  </div>
                </div>
                <CustomButton
                  className="mt-[86px]"
                  icon={<ArrowRight className="ml-3" size={20} />}
                  type="submit"
                  title="Next"
                  handleClick={() => {}}
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </>
  );
};

export default Step3;
