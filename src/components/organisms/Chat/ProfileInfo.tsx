import { getNameInitials } from "../../shared/helpers";
import { FaRegPenToSquare } from "react-icons/fa6";
import { Trash } from "iconsax-react";
import { GoPin } from "react-icons/go";
import imageEvent from "../../../assets/images/event-email.png";
import profilePic from "../../../assets/images/randomImage.jpg";

const ProfileInfo = ({chat}) => {
  return (
    <div className=" pt-5 px-3 h-[80vh] overflow-y-scroll hide-scrollbar shadow-md rounded-md">
    <div className="flex justify-center">

        <div>
            <div className="flex justify-center mt-4">

                {/* <p className="w-[80px] h-[80px] bg-purple-normal flex justify-center items-center text-white text-3xl rounded-full">
                {getNameInitials(
                    chat.sender.split(" ")[0],
                    chat.sender.split(" ")[1]
                )}
                </p> */}
                <img src={profilePic} alt="dp" className="w-[80px] h-[80px] rounded-full flex justify-center items-center" />
            </div>
            <div className="text-center">
                <p>{chat.sender}</p>
                <p >Product Designer  </p>
                <p>Information Technology</p>
            </div>
            <div className="flex mt-3 gap-3 justify-center">
                <FaRegPenToSquare size={18}/>
                <GoPin size={18}/>
                <Trash size={18}/>
            </div>

        </div>
    </div>
            <div className="bg-gray-100  rounded-md p-3 mt-5">
                <div>
                    <p>Last seen: 10 hours ago.</p>
                    <p>Work hours: 9am -5pm. (Mon-Fri)</p>
                </div>
            </div>

            <div className="mt-5 border-b pb-4">
                <h1 className="text-purple-normal font-poppins-medium">Contact information</h1>
                <div className=" gap-3 mt-2 bg-gray-100 rounded-md px-2 py-2">
                    <div className="flex justify-between">
                        <p>Email</p>
                        <p className="font-poppins-medium"><EMAIL></p>
                    </div>
                    <div className="flex justify-between mt-1 border-b">
                        <p > Phone</p>
                        <p className="font-poppins-medium"> +****************</p>
                    </div>
                    <div className="flex justify-between mt-1 border-b">
                        <p> Department</p>
                        <p className="font-poppins-medium"> Information Technology</p>
                    </div>
                    <div className="flex justify-between mt-1 border-b">
                        <p>Job title</p>
                        <p className="font-poppins-medium">Product Designer</p>
                    </div>
                    <div className="flex justify-between mt-1 border-b">
                        <p>Staff ID</p>
                        <p className="font-poppins-medium"> #126778jj</p>
                    </div>
                   
                </div>
            </div>
            <div className="mt-5">
                <h1 className="text-purple-normal font-poppins-medium">Shared Files</h1>
                <div className=" grid grid-cols-3 gap-2 mt-4">
                    <img className="object-cover w-[100px] h-[100px] border" src={imageEvent} width={100} height={100} alt="" /> 
                    <img className="object-cover w-[100px] h-[100px] border" src={imageEvent} width={100} height={100} alt="" /> 
                    <img className="object-cover w-[100px] h-[100px] border" src={imageEvent} width={100} height={100} alt="" /> 
                    <img className="object-cover w-[100px] h-[100px] border" src={imageEvent} width={100} height={100} alt="" /> 
                    <img className="object-cover w-[100px] h-[100px] border" src={imageEvent} width={100} height={100} alt="" /> 
                </div>
            </div>
    </div>
  );
};

export default ProfileInfo;
