import { useEffect, useRef, useState } from "react";
import { ChatProps } from "./MessageList";
import { AttachCircle, AttachSquare, Send } from "iconsax-react";
import { MdAttachment, MdOutlineAttachment } from "react-icons/md";
import { RiAttachmentLine } from "react-icons/ri";
import { getNameInitials } from "../../shared/helpers";

interface Attachment {
  id: string;
  type: "file" | "image";
  name: string;
  url?: string;
  size?: number;
}

interface Message {
  id: string;
  text: string;
  sender: "user" | "other";
  timestamp: Date;
  attachments?: Attachment[];
}

interface ChatAreaProps {
  chat?: ChatProps;
  messages: Message[];
  onSendMessage: (text: string, files: File[]) => void;
  showContactInfo: any;
}
const ChatArea: React.FC<ChatAreaProps> = ({
  chat,
  messages,
  onSendMessage,
  showContactInfo,
}) => {
  const [newMessage, setNewMessage] = useState("");
  const [imageUrl, setImageUrl] = useState([]);
  const [attachments, setAttachments] = useState<File[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = () => {
    if (!newMessage.trim() && attachments.length === 0) return;
    onSendMessage(newMessage, attachments);
    setNewMessage("");
    setAttachments([]);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleAttachmentClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const filesArray = Array.from(e.target.files);
      setAttachments([...attachments, ...filesArray]);
      const file = e.target.files?.[0];
      const reader = new FileReader();
        reader.onloadend = () => {
          let urlArray = reader.result as string
          let imageArray = [];
          imageArray.push(urlArray);
          setImageUrl([...imageUrl, ...imageArray])
        };
        reader.readAsDataURL(file);
    }
  };


  const removeAttachment = (index: number) => {
    const newAttachments = [...attachments];
    newAttachments.splice(index, 1);
    setAttachments(newAttachments);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  if (!chat) {
    return (
        <div className="flex-1 flex items-center justify-center bg-gray-50 border h-[80vh]">
          <div className="text-center text-gray-500">
            <div className="text-xl mb-2">Select a chat to start messaging</div>
            <div>Click on a conversation from the sidebar</div>
          </div>
        </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-[80vh]">
      <div className="p-4 border-b border-gray-200 bg-white flex items-center gap-3 cursor-pointer" onClick={() => showContactInfo(true)}>
        {chat.avatar ? (
          <img
            src={chat.avatar}
            alt={chat.sender}
            className="w-[40px] h-[40px] rounded-full"
          />
        ) : (
          <p className="w-[40px] h-[40px] bg-purple-normal flex justify-center items-center text-white text-xl rounded-full">
            {getNameInitials(
              chat.sender.split(" ")[0],
              chat.sender.split(" ")[1]
            )}
          </p>
        )}
        <h3 className="text-lg font-semibold">{chat.sender}</h3>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${
              message.sender === "user" ? "justify-end" : "justify-start"
            }`}
          >
            <div
              className={`max-w-xs md:max-w-md rounded-lg p-3 ${
                message.sender === "user"
                  ? "bg-purple-normal text-white rounded-br-none"
                  : "bg-gray-200 text-gray-800 rounded-bl-none"
              }`}
            >
              <div>{message.text}</div>

              {message.attachments && message.attachments.length > 0 && (
                <div className="mt-2 space-y-2">
                  {message.attachments.map((attachment) => (
                    <div
                      key={attachment.id}
                      className={`p-2 rounded ${
                        message.sender === "user"
                          ? "bg-purple-normal"
                          : "bg-gray-300"
                      } flex items-center`}
                    >
                      {/* {attachment.type === "image" ? (
                        <div className="w-6 h-6 mr-2">📷</div>
                      ) : (
                        <div className="w-6 h-6 mr-2">📎</div>
                      )} */}
                      <span className="text-sm truncate">
                        {attachment.name}
                        {message.sender === "user" ? (
                          <>
                            {attachments.length > 0 && imageUrl?.map((data, index) => (

                            <img key={index} src={data} alt="image"/>
                            ))}
                          </>
                        ): (
                          <div>
                              {message?.attachments?.map((data, index) => (

                                <img key={index} src={data.url} alt="image"/>
                              ))}
                          </div>
                        )}
                      </span>
                    </div>
                  ))}
                </div>
              )}

              <div
                className={`text-xs mt-1 ${
                  message.sender === "user"
                    ? "text-purple-light"
                    : "text-gray-500"
                }`}
              >
                {formatTime(message.timestamp)}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {attachments.length > 0 && (
        <div className="bg-gray-50 p-2 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {attachments.map((file, index) => (
              <div
                key={index}
                className="bg-white rounded p-2 flex items-center"
              >
                {file.type.startsWith("image/") ? (
                  <div className="w-6 h-6 mr-2">📷</div>
                ) : (
                  <div className="w-6 h-6 mr-2">📎</div>
                )}
                <span className="text-sm truncate max-w-xs">{file.name}</span>
                <button
                  onClick={() => removeAttachment(index)}
                  className="ml-2 text-gray-500 hover:text-gray-700"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Message Input */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex items-end">
          <button
            className="p-2 rounded-full hover:bg-gray-100 mr-2"
            onClick={handleAttachmentClick}
          >
            <RiAttachmentLine size={16} />
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              onChange={handleFileChange}
              multiple
            />
          </button>
          <div className="flex-1 border rounded-lg overflow-hidden focus-within:ring-2 focus-within:ring-purple-normal">
            <textarea
              className="w-full p-2 focus:outline-none resize-none"
              placeholder="Type a message..."
              rows={1}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyDown={handleKeyPress}
            />
          </div>
          <button
            className="ml-2 p-2 bg-purple-normal text-white rounded-full hover:bg-purple-normal"
            onClick={handleSendMessage}
          >
            <Send size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatArea;
