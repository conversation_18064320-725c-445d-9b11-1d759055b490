import { FaEllipsisH } from "react-icons/fa";
import { FaCaretDown, FaRegPenToSquare } from "react-icons/fa6";
import { MdFilterList } from "react-icons/md";
import { getNameInitials, truncateText } from "../../shared/helpers";
import { useState } from "react";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import { PiNeedle } from "react-icons/pi";
import { Trash, User } from "iconsax-react";
import useClickOutside from "../../shared/hooks";
import { GoPin } from "react-icons/go";

export interface ChatProps {
    id: string;
    lastMessage: string;
    sender: string;
    timestamp: string;
    attachments?: Attachment[];
    unreadCount?: number;
    avatar?: string;

  }
  
  interface Attachment {
    id: string;
    type: 'file' | 'image';
    name: string;
    url?: string;
    size?: number;
  }

  interface ChatListProps {
    chats: ChatProps[];
    activeChatId?: string;
    onChatSelect: (chatId: string) => void;
  }

const MessageList : React.FC<ChatListProps> = ({ chats, activeChatId, onChatSelect }) => {
    const [messageId, setMessageId] = useState<string>("");
    const [showDropdown, setShowDropdown] = useState<string>("");

    const node = useClickOutside(() => {
        setShowDropdown("");
    });
  
    return (
        <div className="h-[70vh] w-[360px]">
        <div className="border-b p-6 flex justify-between">
            <h1 className="text-semibold text-20">Chat</h1>
            <div className="flex gap-3 text-neutral-normal">
                <FaEllipsisH size={18}/>
                <MdFilterList size={18}/>
                <FaRegPenToSquare size={18}/>
            </div>
        </div>
        <div className="h-full overflow-y-scroll hide-scrollbar">

            <div className="p-3">
                <div className="my-4">
                    <div className="flex gap-3 text-purple-normal font-poppins-medium">
                        <FaCaretDown className="mt-1"/>
                        <p>Pinned</p>
                    </div>
                </div>
                {chats.map((message) => (
                    <div key={message.id} 
                    className={`p-3 whitespace-nowrap hover:bg-purple-light rounded-md border-b border-gray-100 cursor-pointer flex justify-between gap-3 py-3 px-4  ${
                      message.id === activeChatId ? 'bg-purple-light hover:bg-purple-light rounded-md' : ''
                    }`}
                    onMouseOver={() =>{setMessageId(message.id)}}
                    onMouseLeave={() => {showDropdown !== message.id && setMessageId("");}}
                    onClick={() => {onChatSelect(message.id);}}>
                        <div className="flex gap-2">

                            {message.avatar ? (

                                <img src={message.avatar} alt={message.sender} className="w-[40px] h-[40px] rounded-full"/>
                            ) : (
                                <p className="w-[40px] h-[40px] bg-purple-normal flex justify-center items-center text-white text-xl rounded-full">{getNameInitials(message.sender.split(" ")[0], message.sender.split(" ")[1])}</p>
                            )}
                            <div className="flex">
                                <div>
                                    <h3 className="font-poppins-medium text-neutral-normal">{message.sender}</h3>
                                    <p className="text-neutral-light-active">{truncateText(message.lastMessage, 30) }</p>
                                </div>
                            </div>
                        </div>
                        <div className="flex gap-3">
                            <span className="text-12 text-neutral-normal">{message.timestamp}</span>
                            <FaEllipsisH onClick={() => {setShowDropdown(message.id);}} className={`cursor-pointer mt-1 text-neutral-normal ${message.id === messageId ? "visible" : "invisible"}`} size={10}/>

                            <div className="relative" ref={node}>
                                <div>
                                    <div className="relative justify-end my-5">
                                        <div>
                                            { message.id === showDropdown && (
                                                <FilterDropdown className="right-1 !top-0 w-[8rem] !min-h-3">
                                                    <ul className="py-2 px-2">
                                                        <li className="flex gap-2 text-14 cursor-pointer pb-2"><GoPin className="mt-0.5" size={14}/>Pin Chat</li>
                                                        <li className="flex gap-2 text-14 cursor-pointer pb-2 pt-2 border-t"><User className="mt-0.5" size={14}/>Contact Info</li>
                                                        <li className="flex gap-2 text-14 cursor-pointer pt-2 border-t"><Trash className="mt-0.5" size={14}/>Delete Chat</li>
                                                    </ul>
                                                </FilterDropdown>
                                            )}

                                            </div>
                                            
                                        </div>

                                    </div>
                                </div>

                            </div>
                    </div>
                ))}
            </div>

            <div className="px-6">
                <div className="my-4">
                    <div className="flex gap-3 text-purple-normal font-poppins-medium">
                        <FaCaretDown className="mt-1"/>
                        <p>Recent</p>
                    </div>
                </div>
                {chats.map(message => (
                    <div key={message.id} className="flex justify-between gap-3 py-3 px-2 hover:bg-purple-light rounded-md">
                        <div className="flex gap-2">

                            {message.avatar ? (

                                <img src={message.avatar} alt={message.sender} className="w-[40px] h-[40px] rounded-full"/>
                            ) : (
                                <p className="w-[40px] h-[40px] bg-purple-normal flex justify-center items-center text-white text-xl rounded-full">{getNameInitials(message.sender.split(" ")[0], message.sender.split(" ")[1])}</p>
                            )}
                            <div className="flex">
                                <div>

                                    <h3 className="font-poppins-medium text-neutral-normal">{message.sender}</h3>
                                    <p className="text-neutral-light-active">{truncateText(message.lastMessage, 30) }</p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <span className="text-12 text-neutral-normal">{message.timestamp}</span>

                        </div>
                    </div>
                ))}
            </div>
        </div>
        </div>
    )
};

export default MessageList