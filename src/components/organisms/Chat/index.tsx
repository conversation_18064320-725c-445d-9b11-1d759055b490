import React, { useState, useEffect, useRef } from "react";
import MessageList, { ChatProps } from "./MessageList";
import Chat<PERSON>rea from "./ChatArea";
import ProfileInfo from "./ProfileInfo";

interface Attachment {
  id: string;
  type: "file" | "image";
  name: string;
  url?: string;
  size?: number;
}

interface Message {
  id: string;
  text: string;
  sender: "user" | "other";
  timestamp: Date;
  attachments?: Attachment[];
}

const ChatContainer: React.FC = () => {
  const [showContactInfo, setShowContactInfo] = useState(false);
  const [chats, setChats] = useState<ChatProps[]>([
    {
      id: "1",
      sender: "<PERSON>",
      lastMessage: "Hello, how are you?",
      avatar: "",
      timestamp: "10:00 AM",
      unreadCount: 2,
    },
    {
      id: "2",
      sender: "<PERSON>",
      lastMessage: "Hi, I'm doing well. How about you?",
      avatar: "",
      timestamp: "11:00 AM",
    },
    {
      id: "3",
      sender: "<PERSON> Josie",
      lastMessage: "I'm good too. How about you?",
      avatar: "",
      timestamp: "12:00 PM",
    },
    {
      id: "4",
      sender: "Ife Oluwatemi",
      lastMessage: "Mr <PERSON> needs your attention",
      avatar: "",
      timestamp: "1:00 PM",
    },
    {
      id: "5",
      sender: "Nkechi Okoro",
      lastMessage: "I'm feeling great. Thanks for asking.",
      avatar: "",
      timestamp: "2:00 PM",
    },
  ]);
  const [activeChatId, setActiveChatId] = useState<string | undefined>(
    undefined
  );
  const [messages, setMessages] = useState<Message[]>([]);

  // Load chat messages when active chat changes
  useEffect(() => {
    if (activeChatId) {
      loadChatMessages(activeChatId);
    }
  }, [activeChatId]);

  const loadChatMessages = (chatId: string) => {
    // This would typically be an API call
    // For demo purposes, we'll generate some messages
    const demoMessages: Message[] = [
      {
        id: "1",
        text: "Hello there!",
        sender: "other",
        timestamp: new Date(Date.now() - 3600000),
      },
      {
        id: "2",
        text: "Hi! How are you?",
        sender: "user",
        timestamp: new Date(Date.now() - 3500000),
      },
      {
        id: "3",
        text: "I'm good, thanks for asking.",
        sender: "other",
        timestamp: new Date(Date.now() - 3400000),
        attachments: [{ id: "a1", type: "image", name: "photo.jpg", url: "" }],
      },
    ];

    setMessages(demoMessages);

    // Clear unread count for selected chat
    setChats((prevChats) =>
      prevChats.map((chat) =>
        chat.id === chatId ? { ...chat, unreadCount: 0 } : chat
      )
    );
  };

  const handleChatSelect = (chatId: string) => {
    setActiveChatId(chatId);
  };

  const handleSendMessage = (text: string, files: File[]) => {
    const newMsg: Message = {
      id: Date.now().toString(),
      text: text,
      sender: "user",
      timestamp: new Date(),
      attachments: files.map((file) => ({
        id: Date.now().toString() + file.name,
        type: file.type.startsWith("image/") ? "image" : "file",
        name: file.name,
        size: file.size,
      })),
    };

    setMessages([...messages, newMsg]);

    // Update last message in chat list
    if (activeChatId) {
      setChats((prevChats) =>
        prevChats.map((chat) =>
          chat.id === activeChatId
            ? { ...chat, lastMessage: text || "Attachment sent" }
            : chat
        )
      );
    }
  };

  const activeChat = chats.find((chat) => chat.id === activeChatId);

  return (
    <div>
      <div className="flex">
        <div className="bg-white col-span-2 rounded-md">
          <MessageList
            chats={chats}
            activeChatId={activeChatId}
            onChatSelect={handleChatSelect}
          />
        </div>
        <div className={`w-full   ${showContactInfo && "grid grid-cols-3"} `}>

          <div className="col-span-2">
            <ChatArea
              chat={activeChat}
              messages={messages}
              onSendMessage={handleSendMessage}
              showContactInfo={setShowContactInfo}
            />
          </div>
          {showContactInfo && (
            <div className="bg-white col-span-1 max-w-[300px]">
              <ProfileInfo 
              chat={activeChat}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatContainer;
