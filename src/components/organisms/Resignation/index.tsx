import { FaEllipsisV } from "react-icons/fa";
import StatusTag from "../../atoms/StatusTag";
import moment from "moment";
import { useEffect, useRef, useState } from "react";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { PiTrash } from "react-icons/pi";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import { Eye } from "iconsax-react";
import useClickOutside from "../../shared/hooks";
import CustomTable from "../../atoms/CustomTable/CustomTable";
import { FiPlus } from "react-icons/fi";
import { useNavigate } from "react-router-dom";
import OrganizationEmptyState from "../../atoms/Cards/OrganizationEmptyState";
import { FaDownload } from "react-icons/fa6";
import { MdClose, MdOutlineTipsAndUpdates } from "react-icons/md";
import CustomModal from "../../atoms/CustomModal/CustomModal";
import { getResignationAtom, getResignationDetailAtom } from "../../../recoil/atom/resignation";
import { useRecoilState, useRecoilValue } from "recoil";
import { getResignationById, getResignations, UpdateResination } from "../../../api/resignation";
import { debounce } from "../../shared/helpers";
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";
import { LuImage } from "react-icons/lu";
import Loader from "../../atoms/Loader";



const Resignation = () => {

    const navigate = useNavigate();

    const [showDetails, setShowDetails] = useState<boolean>(false);
    const [rowId, setRowId] = useState(0);
    const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
    const [showDropdown, setShowDropdown] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const debounceSearch = useRef(debounce((q) => fetchResignation(q), 2000)).current;
    const [searchQuery, setSearchQuery] = useState<string>("");
    const [pageNumber, setPageNumber] = useState<number>(1);

    const [, setResignationAtom] = useRecoilState(getResignationAtom);
    const ResignationValue = useRecoilValue(getResignationAtom);

    const [, setResignationDetailAtom] = useRecoilState(getResignationDetailAtom);
    const ResignationDetailValue = useRecoilValue(getResignationDetailAtom);




    const node = useClickOutside(() => {
        setShowDropdown(false);
        setRowId(0);
    });

    const fetchResignation = (q) => {
        setIsLoading(true);
        getResignations({ search: q, page: pageNumber }).then((res) => {
            if (res.success) {
                setResignationAtom(res.data);
                setIsLoading(false);
            }
        })
    };

    const viewResignationDetails = (id: any) => {

        setIsLoading(true);
        getResignationById(id).then((res) => {
            setIsLoading(false);
            if (res.success) {
                setResignationDetailAtom(res.data);
                setShowDetails(true);
                setIsLoading(false);
            }
        });

    }

    const updateResignationStatus = ({ resignation_id, status }: any) => {

        UpdateResination({ resignation_id, status }).then((res) => {
            setIsLoading(false);
            if (res.success) {
                setShowDropdown(false);
                clustarkToast(NotificationTypes.SUCCESS, res.message);
                fetchResignation('');
            }
        });

    };


    useEffect(() => {
        fetchResignation(searchQuery);
    }, []);


    const columns = [
        {
            Header: "Staff",
            accessor: `staff.staffPersonalInformations`,
            Cell: (row: any) => {
                return (
                    <>
                        <div className="flex  items-center">
                            <span className="text-[12px] capitalize">{`${row?.cell?.value?.first_name} ${row?.cell?.value?.last_name}` || "--"}</span>
                        </div>
                        {/* <span className="text-[9px] text-[#6B788E]">{email}</span> */}
                    </>
                )
            }
        },

        {
            Header: "Reason",
            accessor: "reason",
            Cell: (row: any) => (
                <div className="flex  items-center">
                    {row.cell.value || "--"}
                </div>
            ),
        },

        {
            Header: "Updatedby",
            accessor: "reviewed_by.first_name",
            Cell: (row: any) => (
                <div className="flex  items-center">
                    {row.cell.value || "--"}
                </div>
            ),
        },

        {
            Header: "Status",
            accessor: "status",
            Cell: (row: any) => <StatusTag status={row.cell.value} />,
        },

        {
            Header: "Attatchment",
            accessor: "attatchment",
            Cell: (row: any) => (
                <div className="flex  items-center">
                    {row.cell.value || "--"}
                </div>
            ),
        },

        {
            Header: "Date applied",
            accessor: "created_at",
            Cell: (row: any) => (<p>{moment(row.cell.value).format("lll") || "--"} </p>),
        },

        {
            Header: "",
            accessor: "action",
            Cell: (row: any) => (
                <div className="relative">
                    <FaEllipsisV
                        onClick={() => {
                            setShowDropdown(!showDropdown);
                            setRowId(row.cell.row.id);
                        }}
                        className="text-[#98A2B3] cursor-pointer"
                    />
                    {showDropdown && row.cell.row.id === rowId && (
                        <FilterDropdown>
                            <ul className="text-14 text-neutral-dark" ref={node}>
                                <li onClick={() => viewResignationDetails(row.cell.row.original.id)} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                                    <Eye size={18} />
                                    View
                                </li>
                                {showDeleteWarn ? (
                                    <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                                        <div className="flex gap-3">
                                            <MdOutlineTipsAndUpdates size={18} />
                                            Approve or decline resignation?
                                        </div>
                                        <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                                            <CustomButton
                                                isLoading={isLoading}
                                                title="Approve"
                                                handleClick={() =>
                                                    updateResignationStatus({ resignation_id: row.cell.row.original.id, status: 'approved' })
                                                }
                                                className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer"
                                            />
                                            <span
                                                onClick={() => {
                                                    updateResignationStatus({ resignation_id: row.cell.row.original.id, status: 'declined' })
                                                }}
                                                className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                                            >
                                                Decline
                                            </span>
                                        </div>
                                    </li>
                                ) : (row.cell.row.original.status === "pending") && (
                                    <li
                                        onClick={() => setShowDeleteWarn(true)}
                                        className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-warning cursor-pointer"
                                    >
                                        <MdOutlineTipsAndUpdates size={18} />
                                        Update
                                    </li>
                                )}
                            </ul>
                        </FilterDropdown>
                    )}
                </div>
            ),
        },

    ];


    return (
        <>
            {
                isLoading ? <Loader /> :
                    <div className="">
                        <div className="">
                            <div className="">
                                {
                                    (ResignationValue?.data?.length > 0 || searchQuery) ? (
                                        <div className="">
                                            <CustomTable
                                                data={ResignationValue?.data || []}
                                                meta={ResignationValue?.meta || {}}
                                                columns={columns}
                                                handlePageChange={(pageNumber: number) => setPageNumber(pageNumber)}
                                                handleSearch={(search: string) => {
                                                    setSearchQuery(search);
                                                    debounceSearch(search)
                                                }}
                                                header={
                                                    <div className="flex items-center h-[40px] px-2">
                                                        <h1 className="text-center">
                                                            Resignations
                                                        </h1>
                                                    </div>
                                                }
                                            />
                                        </div>
                                    ) :
                                        (
                                            <div className="flex justify-center items-center py-[120px]">
                                                <OrganizationEmptyState text="No record found. When there is a record, they will appear here." />
                                            </div>
                                        )
                                }


                            </div>
                        </div>

                        <CustomModal
                            visibility={showDetails}
                        >
                            <div className='mb-5'>
                                <div className='flex justify-between bg-purple-light py-[33px] px-10 '>
                                    <h1 className=" font-poppins-medium text-18 text-purple-dark">
                                        Resignation information
                                    </h1>
                                    <MdClose
                                        size={18}
                                        color='#0C0123'
                                        className="cursor-pointer"
                                        onClick={() => setShowDetails(false)}
                                    />

                                </div>
                                <div className="flex gap-8 mt-9 px-10">
                                    <h1 className="text-18 text-neutral-normal font-poppins-medium">
                                        Status
                                    </h1>
                                    <p className='capitalize mt-1' ><StatusTag status={ResignationDetailValue?.status ? ResignationDetailValue.status : null} /></p>
                                </div>
                                <div className="mt-8 text-neutral-normal px-10">

                                    {/* <p>
                        Your request is being reviewed by HR. You will be notified once it's reviewed.
                    </p> */}

                                    <div className="grid grid-cols-2 items-center">
                                        <div className="mt-8">
                                            <h1 className="text-neutral-dark">Resignation filed by </h1>
                                            <p className="mt-2.5">{ResignationDetailValue?.staff?.staffPersonalInformations?.first_name ? ResignationDetailValue.staff?.staffPersonalInformations?.first_name : '--'}</p>
                                        </div>

                                        <div>
                                            <h1 className="text-neutral-dark">Reason</h1>
                                            <p className="mt-2.5">{ResignationDetailValue?.reason ? ResignationDetailValue.reason : null}</p>
                                        </div>
                                    </div>

                                    <div className="mt-12 grid grid-cols-2 items-center">

                                        <div>
                                            <h1 className="text-neutral-dark">Reviewed by</h1>
                                            <p className="mt-2.5">{ResignationDetailValue?.reviewed_by?.first_name ? ResignationDetailValue?.reviewed_by?.first_name : '---'}</p>
                                        </div>

                                        <div>
                                            <h1 className="text-neutral-dark">Staff Id</h1>
                                            <p className="mt-2.5 rounded-2xl bg-[#F5F6F7] w-fit px-2 py-1 font-poppins-medium" >{ResignationDetailValue?.staff?.staff_identification_tag ? ResignationDetailValue?.staff?.staff_identification_tag : null}</p>
                                        </div>

                                    </div>

                                    <div className="mt-12 grid grid-cols-2 items-center">

                                        <div>
                                            <h1 className="text-neutral-dark">Description</h1>
                                            <p className="mt-2.5">{ResignationDetailValue?.description ? ResignationDetailValue?.description : null}</p>
                                        </div>

                                        <div>
                                            <h1 className="text-neutral-dark">Attachment Upload</h1>
                                            {ResignationDetailValue?.attachment ? (
                                                <div className="mt-2.5 ">
                                                    <div className="flex gap-4">
                                                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <rect width="32" height="32" rx="16" fill="#B2BBC6" fill-opacity="0.18" />
                                                            <path d="M23.4405 14.325C17.9321 13.5683 13.1821 17.6583 13.5013 23.0833M11.418 12.6667C11.418 13.1087 11.5936 13.5326 11.9061 13.8452C12.2187 14.1577 12.6426 14.3333 13.0846 14.3333C13.5267 14.3333 13.9506 14.1577 14.2631 13.8452C14.5757 13.5326 14.7513 13.1087 14.7513 12.6667C14.7513 12.2246 14.5757 11.8007 14.2631 11.4882C13.9506 11.1756 13.5267 11 13.0846 11C12.6426 11 12.2187 11.1756 11.9061 11.4882C11.5936 11.8007 11.418 12.2246 11.418 12.6667Z" stroke="#1D242D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                            <path d="M8.5 16.8868C10.8167 16.5659 12.8958 17.6851 14.02 19.4701" stroke="#1D242D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                            <path d="M8.5 13.8333C8.5 11.9667 8.5 11.0333 8.86333 10.32C9.18291 9.69282 9.69282 9.18291 10.32 8.86333C11.0333 8.5 11.9667 8.5 13.8333 8.5H18.1667C20.0333 8.5 20.9667 8.5 21.68 8.86333C22.3072 9.18291 22.8171 9.69282 23.1367 10.32C23.5 11.0333 23.5 11.9667 23.5 13.8333V18.1667C23.5 20.0333 23.5 20.9667 23.1367 21.68C22.8171 22.3072 22.3072 22.8171 21.68 23.1367C20.9667 23.5 20.0333 23.5 18.1667 23.5H13.8333C11.9667 23.5 11.0333 23.5 10.32 23.1367C9.69282 22.8171 9.18291 22.3072 8.86333 21.68C8.5 20.9667 8.5 20.0333 8.5 18.1667V13.8333Z" stroke="#1D242D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                        </svg>

                                                        <a className="mt-2 underline" href={ResignationDetailValue?.attachment} target="_blank">click to view file attached</a>

                                                    </div>
                                                </div>

                                            ) : (
                                                <p>Nil</p>
                                            )}
                                        </div>

                                    </div>


                                </div>

                            </div>
                        </CustomModal>

                    </div>
            }
        </>
    )
}

export default Resignation;