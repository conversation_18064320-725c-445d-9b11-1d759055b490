import React, { useEffect, useRef, useState } from 'react';
import CustomTable from '../../atoms/CustomTable/CustomTable';
import OrganizationEmptyState from '../../atoms/Cards/OrganizationEmptyState';
import { useRecoilState, useRecoilValue } from 'recoil';
import useClickOutside from '../../shared/hooks';
import { getAssetRequestAtom, getUnassignedAssetsAtom } from '../../../recoil/atom/assets';
import StatusTag from '../../atoms/StatusTag';
import { FaEllipsisV } from 'react-icons/fa';
import FilterDropdown from '../../atoms/Cards/FilterDropdown';
import { Eye } from 'iconsax-react';
import { ButtonProperties, debounce, errorMessages } from '../../shared/helpers';
import moment from 'moment';
import CustomModal from '../../atoms/CustomModal/CustomModal';
import CustomButton from '../../atoms/CustomButton/CustomButton';
import { MdClose } from 'react-icons/md';
import { approveAssetRequest, getAssetRequests, getUnassignedAssets, rejectAssetRequest } from '../../../api/assetManagement';
import { FiPlus, FiUserCheck } from 'react-icons/fi';
import { AiOutlineStop } from 'react-icons/ai';
import { clustarkToast } from '../../atoms/Toast';
import { NotificationTypes } from '../../shared/helpers/enums';
import { Form, Formik } from 'formik';
import FormikCustomSelect from '../../atoms/CustomInput/FormikCustomSelect';
import * as yup from "yup";
import { useNavigate } from 'react-router-dom';

const requestSchema = yup.object().shape({
  asset: yup.string().required(errorMessages.required),
});

const RequestList = () => {

  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [assetModal, setAssetModal] = useState<boolean>(false);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [requestAction, setRequestAction] = useState<string>("");
  const [requestActionModal, setRequestActionModal] = useState<boolean>(false);
  const [rowId, setRowId] = useState("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [singleAsset, setSingleAsset] = useState<any>({});
  const [, setAssetRequestAtom] = useRecoilState(getAssetRequestAtom);
  const [, setUnassignedAssetAtom] = useRecoilState(getUnassignedAssetsAtom);
  const assetRequestsValue = useRecoilValue(getAssetRequestAtom);
  const unassignedAssetValue = useRecoilValue(getUnassignedAssetsAtom);
  const debounceSearch = useRef(debounce((q) => fetchAssetRequests(q), 2000)).current;

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId("");
  });

  const fetchUnassignedAssets = () => {
    getUnassignedAssets().then((res) => {
      if (res.success) {
        setUnassignedAssetAtom(res.data);
      }
    })
  };

  const assets = unassignedAssetValue?.data?.map((item) => ({
    text: item?.name,
    value: item.id
  }));

  const fetchAssetRequests = (q?) => {
    getAssetRequests({ search: q, page: pageNumber }).then((res) => {
      if (res.success) {
        setAssetRequestAtom(res.data);
      }
    })
  };



  useEffect(() => {
    fetchAssetRequests(searchQuery);
  }, [pageNumber, searchQuery]);

  useEffect(() => {
    fetchUnassignedAssets();
  }, []);


  const columns = [
    {
      Header: "Asset Type",
      accessor: "assetType.name",
      Cell: (row: any) => (
        <p>{row.cell.value || "--"}</p>
      ),
    },

    {
      Header: "Asset Name",
      accessor: "name",
      Cell: (row: any) => (
        <p>
          {row.cell.value || "--"}{" "}
        </p>
      ),
    },
    {
      Header: "Quantity",
      accessor: "quantity",
      Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
    },
    {
      Header: "Priority",
      accessor: "priority",
      Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: (row: any) => (<StatusTag status={row.cell.value} />),
    },
    {
      Header: "Date Requested",
      accessor: "created_at",
      Cell: (row: any) => <p>{moment(row.cell.value).format("DD-MM-YYYY") || "--"} </p>,
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.original.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.original.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() => {
                    setAssetModal(true);
                    setSingleAsset(row.cell.row.original);
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>
                {row.cell.row.original.status === "PENDING" && (
                  <>
                    <li
                      onClick={() => {
                        setRequestAction("approve");
                        setRequestActionModal(true);
                        setSingleAsset(row.cell.row.original);
                      }}
                      className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                    >
                      <FiUserCheck size={18} />
                      Approve Request
                    </li>

                    <li
                      onClick={() => {
                        setRequestAction("reject");
                        setRequestActionModal(true);
                        setSingleAsset(row.cell.row.original);
                      }}
                      className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                    >
                      <AiOutlineStop size={18} />
                      Reject Request
                    </li>
                  </>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  const handleRequestApproval = (values) => {
    if (requestAction === "approve" && requestActionModal) {
      setIsLoading(true);
      approveAssetRequest({ assetRequestId: singleAsset.id, assetId: values.asset }).then((res) => {
        if (res.success) {
          clustarkToast(NotificationTypes.SUCCESS, res.message);
          fetchAssetRequests();
          setRequestActionModal(false);
        };
        setIsLoading(false);
      }).catch(() => setIsLoading(false));
    };

    if (requestAction === "reject" && requestActionModal) {
      setIsLoading(true);
      rejectAssetRequest({ assetRequestId: singleAsset.id }).then((res) => {
        if (res.success) {
          clustarkToast(NotificationTypes.SUCCESS, res.message);
          fetchAssetRequests();
          setRequestActionModal(false);
        };
        setIsLoading(false);
      }).catch(() => setIsLoading(false));
    }
  }


  return (
    <div>
      <div className=" my-10 py-[23px]">
        {assetRequestsValue?.data?.length > 0 || searchQuery ? (
          <CustomTable
            data={assetRequestsValue?.data || []}
            meta={assetRequestsValue?.meta || {}}
            columns={columns}
            handlePageChange={(pageNumber: number) => setPageNumber(pageNumber)}
            handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
            header={
              <div className="flex justify-between items-center h-[40px] px-2">
                <h1>
                  Asset requests
                </h1>
                
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState
              text="No asset request yet."
            />
          </div>
        )}
      </div>

      <CustomModal
        visibility={assetModal}
        toggleVisibility={setAssetModal}
      >
        <div>
          <div className='flex justify-between bg-purple-light py-[33px] px-10 '>
            <h1 className=" font-poppins-medium text-18 text-purple-dark">
              Request asset form
            </h1>
            <MdClose
              size={18}
              color='#0C0123'
              className="cursor-pointer"
              onClick={() => setAssetModal(false)}
            />

          </div>
          <div className="flex gap-8 mt-9 px-10">
            <h1 className="text-18 text-neutral-normal font-poppins-medium">
              Status
            </h1>
            <p className='capitalize mt-1' ><StatusTag status={singleAsset?.status || "--"} /></p>
          </div>
          <div className="mt-8 text-neutral-normal px-10">
            <p>
              Your request is being reviewed by HR. You will be notified once it's reviewed.
            </p>
            <div className="mt-12">
              <div>
                <h1 className="text-neutral-dark">Asset type</h1>
                <p className="mt-2.5">{singleAsset?.assetType?.name}</p>
              </div>
              <div className="mt-8 grid grid-cols-2">
                <div>
                  <h1 className="text-neutral-dark">Asset name</h1>
                  <p className="mt-2.5">{singleAsset?.name}</p>
                </div>
                <div>
                  <h1 className="text-neutral-dark">Quantity</h1>
                  <p className="mt-2.5">{singleAsset?.quantity}</p>
                </div>
              </div>
              <div className="mt-8">
                <h1 className="text-neutral-dark">Reason for request</h1>
                <p className="mt-2.5">{singleAsset?.reason || "Nil"}</p>
              </div>
              <div className="mt-8">
                <h1 className="text-neutral-dark">Preferred specification</h1>
                <p className="mt-2.5">{singleAsset?.specifications || "Nil"}</p>
              </div>
              <div className="mt-8 grid grid-cols-2">
                <div>
                  <h1 className="text-neutral-dark">Priority level</h1>
                  <p className="mt-2.5">{singleAsset?.priority}</p>
                </div>
                <div>
                  <h1 className="text-neutral-dark">Attachment upload</h1>
                  {singleAsset?.image ? (
                    <div className="mt-2.5 flex justify-between">
                      <div className="flex gap-10">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <rect width="32" height="32" rx="16" fill="#B2BBC6" fill-opacity="0.18" />
                          <path d="M23.4405 14.325C17.9321 13.5683 13.1821 17.6583 13.5013 23.0833M11.418 12.6667C11.418 13.1087 11.5936 13.5326 11.9061 13.8452C12.2187 14.1577 12.6426 14.3333 13.0846 14.3333C13.5267 14.3333 13.9506 14.1577 14.2631 13.8452C14.5757 13.5326 14.7513 13.1087 14.7513 12.6667C14.7513 12.2246 14.5757 11.8007 14.2631 11.4882C13.9506 11.1756 13.5267 11 13.0846 11C12.6426 11 12.2187 11.1756 11.9061 11.4882C11.5936 11.8007 11.418 12.2246 11.418 12.6667Z" stroke="#1D242D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                          <path d="M8.5 16.8868C10.8167 16.5659 12.8958 17.6851 14.02 19.4701" stroke="#1D242D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                          <path d="M8.5 13.8333C8.5 11.9667 8.5 11.0333 8.86333 10.32C9.18291 9.69282 9.69282 9.18291 10.32 8.86333C11.0333 8.5 11.9667 8.5 13.8333 8.5H18.1667C20.0333 8.5 20.9667 8.5 21.68 8.86333C22.3072 9.18291 22.8171 9.69282 23.1367 10.32C23.5 11.0333 23.5 11.9667 23.5 13.8333V18.1667C23.5 20.0333 23.5 20.9667 23.1367 21.68C22.8171 22.3072 22.3072 22.8171 21.68 23.1367C20.9667 23.5 20.0333 23.5 18.1667 23.5H13.8333C11.9667 23.5 11.0333 23.5 10.32 23.1367C9.69282 22.8171 9.18291 22.3072 8.86333 21.68C8.5 20.9667 8.5 20.0333 8.5 18.1667V13.8333Z" stroke="#1D242D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>

                        <a href={singleAsset?.image}>file</a>

                      </div>
                      <MdClose className="mt-2" />
                    </div>

                  ) : (
                    <p>Nil</p>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-end mt-16 pr-10 pb-10">
            {singleAsset?.status === "PENDING" && (

              <div className="flex gap-6">
                <CustomButton className="!w-[173px]" title="Approve request" variant={ButtonProperties.VARIANT.primary.name} handleClick={() => { setRequestAction("approve"); setRequestActionModal(true); setAssetModal(false) }} />

                <CustomButton className="!w-[173px] !border-alert-text-error  !text-alert-text-error" title="Reject request" handleClick={() => { setRequestAction("reject"); setRequestActionModal(true); setAssetModal(false) }} />
              </div>
            )}
          </div>
        </div>
      </CustomModal>

      <CustomModal visibility={requestActionModal} toggleVisibility={setRequestActionModal}>
        <div className="flex flex-col gap-4">
          <h2 className="text-18 bg-purple-light py-[33px] px-10  font-poppins-medium text-purple-dark">
            {requestAction === "approve" ? "Approve" : "Reject"} request
          </h2>
          <p className="text-16 px-10 font-poppins-medium text-[#4A504C]">
            {/* Are you sure you want to {requestAction === "approve" ? "approve" : "reject"} this asset request? */}
          </p>
          <div className='mt-8 px-10'>
            <Formik initialValues={{ asset: "" }} onSubmit={handleRequestApproval} validationSchema={requestAction === "approve" && requestSchema}>
              {({ values, setFieldValue }) => (
                <Form>
                  {requestAction === "approve" && (
                    <>
                      <p className='text-16 text-[#4A504C]  mb-3'>Select an asset to assign- to approve this request.</p>

                      <div>
                        <FormikCustomSelect
                          name="asset"
                          options={assets}
                          value={values.asset}
                          onChange={(item: { value: string; text: string }) => {
                            setFieldValue("asset", item.value);
                          }}
                        />

                      </div>
                    </>
                  )}

                  <div className=' mb-8'>
                    <div className="flex gap-10 mt-10">

                      <CustomButton
                        title="Cancel"
                        handleClick={() => { setRequestActionModal(false) }}
                        className="!w-[154px]"
                        isTransparent
                        variant={ButtonProperties.VARIANT.primary.name}
                      />
                      <CustomButton
                        type="submit"
                        title="Proceed"
                        handleClick={() => { }}
                        className={`!w-[154px] !border-none ${requestAction === "reject" ? "!bg-[#DA1414]" : ""}  text-white"`}
                        isLoading={isLoading}
                        variant={ButtonProperties.VARIANT.primary.name}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </CustomModal>

    </div>
  )
}

export default RequestList