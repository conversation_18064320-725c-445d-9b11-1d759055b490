import React, { useEffect, useRef, useState } from 'react';
import CustomTable from '../../atoms/CustomTable/CustomTable';
import OrganizationEmptyState from '../../atoms/Cards/OrganizationEmptyState';
import { useNavigate } from 'react-router-dom';
import { useRecoilState, useRecoilValue } from 'recoil';
import useClickOutside from '../../shared/hooks';
import { getAssignedRequestAtom } from '../../../recoil/atom/assets';
import { getAssignedRequests, markAsReturned, reassignAsset } from '../../../api/assetManagement';
import StatusTag from '../../atoms/StatusTag';
import { FaEllipsisV } from 'react-icons/fa';
import FilterDropdown from '../../atoms/Cards/FilterDropdown';
import { Eye } from 'iconsax-react';
import { ButtonProperties, debounce } from '../../shared/helpers';
import moment from 'moment';
import { RiCheckboxMultipleLine } from 'react-icons/ri';
import { PiShareFatLight } from 'react-icons/pi';
import CustomModal from '../../atoms/CustomModal/CustomModal';
import { MdClose } from 'react-icons/md';
import { Formik, Form } from 'formik';
import FormikCustomSelect from '../../atoms/CustomInput/FormikCustomSelect';
import CustomButton from '../../atoms/CustomButton/CustomButton';
import { getStaffAtom } from '../../../recoil/atom/staff';
import { getStaff } from '../../../api/staff';
import { clustarkToast } from '../../atoms/Toast';
import { NotificationTypes } from '../../shared/helpers/enums';
import Loader from '../../atoms/Loader';
import { FiPlus } from 'react-icons/fi';

const AssignedAsset = () => {
  const navigate = useNavigate();
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [rowId, setRowId] = useState("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [assignModal, setAssignModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [returnModal, setReturnModal] = useState(false);
  const [staffId, setStaffId] = useState("");
  const [, setStaffAtom] = useRecoilState(getStaffAtom);
  const getStaffValue = useRecoilValue(getStaffAtom);
  const [, setAssetAtom] = useRecoilState(getAssignedRequestAtom);
  const assetRequestsValue = useRecoilValue(getAssignedRequestAtom);
  const debounceSearch = useRef(debounce((q) => fetchAssignedAssets(q), 2000)).current;


  const node = useClickOutside(() => {
    setShowDropdown(false);
  });

  const fetchAssignedAssets = (q) => {
    setIsFetching(true);
    getAssignedRequests({ search: q, page: pageNumber }).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setAssetAtom(res.data);
      }
    })
  };


  useEffect(() => {
    fetchAssignedAssets(searchQuery);
  }, [searchQuery, pageNumber]);

  const fetchStaff = async () => {
    getStaff().then((res) => {
      if (res.success) {
        setStaffAtom(res.data);
      }
    })
  };

  useEffect(() => {
    fetchStaff();
  }, []);

  const staffs = getStaffValue?.data?.map((item) => ({
    text: item?.staffPersonalInformations?.first_name + " " + item?.staffPersonalInformations?.last_name,
    value: item.id
  }));

  const handleAssign = () => {
    setIsLoading(true);
    reassignAsset({
      assetId: rowId,
      staffId: staffId
    }).then((res) => {
      if (res.success) {
        setIsLoading(false);
        setAssignModal(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchAssignedAssets(searchQuery);
      }
    }).catch(() => setIsLoading(false))
  };

  const handleReturn = () => {
    setIsLoading(true);
    markAsReturned({
      assignedAssetId: rowId,
    }).then((res) => {
      if (res.success) {
        setIsLoading(false);
        setReturnModal(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchAssignedAssets(searchQuery);
      }
    }).catch(() => setIsLoading(false))
  };


  const columns = [
    {
      Header: "Asset ID",
      accessor: "asset.serial_number",
      Cell: (row: any) => (
        <p>{row.cell.value || "--"}</p>
      ),
    },
    {
      Header: "Asset type",
      accessor: "asset.assetType.name",
      Cell: (row: any) => (
        <p>{row.cell.value || "--"}</p>
      ),
    },

    {
      Header: "Asset name",
      accessor: "asset.name",
      Cell: (row: any) => (
        <p>
          {row.cell.value || "--"}
        </p>
      ),
    },
    {
      Header: "Assigned to",
      accessor: "staff.user.first_name",
      Cell: (row: any) => <p> {row.cell.value || "-"} {row.cell.row.original?.staff?.user?.last_name || "-"}</p>,
    },
    {
      Header: "Date assigned",
      accessor: "issued_at",
      Cell: (row: any) => <p>{moment(row.cell.value).format("DD-MM-YYYY") || "--"}</p>,
    },

    {
      Header: "Condition",
      accessor: "asset.condition",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: (row: any) => (<StatusTag status={row.cell.value} />),
    },
    {
      Header: "Return date",
      accessor: "returned_at",
      Cell: (row: any) => <p>{row.cell.value ? moment(row.cell.value).format("DD-MM-YYYY") : "--"} </p>,
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.original.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.original.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() => {
                    navigate(`/asset/asset-information`, {
                      state: { id: row.cell.row.original.id },
                    });
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>
                <li
                  onClick={() => {
                    setAssignModal(true)
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <PiShareFatLight size={18} />
                  Re-Assign Asset
                </li>
                <li
                  onClick={() => {
                    setReturnModal(true);
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <RiCheckboxMultipleLine size={18} />
                  Mark as Returned
                </li>
                {/* <li
                   onClick={() => {
                    // setReturnAsset(true)
                   }}
                   className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                 >
                   <RiDeleteBin6Line size={18} />
                   Delete
                 </li> */}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  if (isFetching && !searchQuery) {
    return <div>
      <Loader />
    </div>
  };
  return (
    <div>
      <div className=" my-10 py-[23px]">
        {assetRequestsValue?.data?.length > 0 || searchQuery ? (
          <CustomTable
            data={assetRequestsValue?.data || []}
            meta={assetRequestsValue?.meta || {}}
            columns={columns}
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
            header={
              <div className="flex justify-between items-center h-[40px] px-2">
                <h1>
                  Assigned assets
                </h1>
                
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState
              text="You have no assigned assets."
            />
          </div>
        )}
      </div>


      <CustomModal
        visibility={assignModal}
        toggleVisibility={setAssignModal}
      >
        <div>
          <div className='flex justify-between bg-purple-light py-[33px] px-10 '>
            <h1 className=" font-poppins-medium text-18 text-purple-dark">
              Re-Assign asset
            </h1>
            <MdClose
              size={18}
              color='#0C0123'
              className="cursor-pointer"
              onClick={() => setAssignModal(false)}
            />
          </div>
          <div className='mt-8 px-10'>
            <Formik initialValues={{ staff: "" }} onSubmit={handleAssign}>
              {({ values, setFieldValue }) => (
                <Form>
                  <div>
                    <FormikCustomSelect
                      label="Select Employee"
                      name="staff"
                      options={staffs}
                      value={values.staff}
                      onChange={(item: { value: string; text: string }) => {
                        setStaffId(item.value);
                        setFieldValue("staff", item.value);
                      }}
                    />

                  </div>

                  <div className='flex justify-end mt-14 mb-8'>
                    <CustomButton isLoading={isLoading} type='submit' className='w-[170px]' title='Assign' variant={ButtonProperties.VARIANT.primary.name} handleClick={() => { setAssignModal(false) }} />
                  </div>
                </Form>
              )}
            </Formik>
          </div>


        </div>
      </CustomModal>

      <CustomModal
        visibility={returnModal}
        toggleVisibility={setReturnModal}
      >
        <div>
          <div className='flex justify-between bg-purple-light py-[33px] px-10 '>
            <h1 className=" font-poppins-medium text-18 text-purple-dark">
              Mark as "Returned" ?
            </h1>
            <MdClose
              size={18}
              color='#3730A3'
              className="cursor-pointer"
              onClick={() => setReturnModal(false)}
            />
          </div>
          <div className='mt-8 px-10'>
            <p className='text-16 text-neutral-normal font-poppins-medium mb-12 text-center'>Are you sure you want to mark this asset as "returned"?</p>
            <div>
              <div className='flex justify-center gap-6 mt-14 mb-8'>
                <CustomButton isLoading={isLoading} className='w-[170px]' title='Mark as "Returned" ' variant={ButtonProperties.VARIANT.primary.name} handleClick={handleReturn} />
                <CustomButton className='w-[170px] text-neutral-dark' isTransparent title='Cancel' handleClick={() => setReturnModal(false)} />
              </div>
            </div>

          </div>


        </div>
      </CustomModal>
    </div>
  )
}

export default AssignedAsset;