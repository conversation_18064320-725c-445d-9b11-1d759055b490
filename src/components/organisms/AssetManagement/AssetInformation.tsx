import React, { useEffect, useState } from 'react'
import GoBack from '../../atoms/Ui/GoBack'
import { Formik, Form } from 'formik'
import FormikCustomInput from '../../atoms/CustomInput/FormikCustomInput';
import CustomButton from '../../atoms/CustomButton/CustomButton';
import { ButtonProperties } from '../../shared/helpers';
import { useLocation, useNavigate } from 'react-router-dom';
import { getAssignedAssetbyId, markAsReturned, reassignAsset } from '../../../api/assetManagement';
import moment from 'moment';
import CustomModal from '../../atoms/CustomModal/CustomModal';
import { MdClose } from 'react-icons/md';
import FormikCustomSelect from '../../atoms/CustomInput/FormikCustomSelect';
import { getStaff } from '../../../api/staff';
import { useRecoilState, useRecoilValue } from 'recoil';
import { getStaffAtom } from '../../../recoil/atom/staff';
import { clustarkToast } from '../../atoms/Toast';
import { NotificationTypes } from '../../shared/helpers/enums';
import Loader from '../../atoms/Loader';

const AssetInfromation = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const {id} = location?.state || {};
    const [assetData, setAssetData] = useState<any>({});
    const [isFetching, setIsFetching] = useState(false);
    const [assignModal, setAssignModal] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [returnModal, setReturnModal] = useState(false);
    const [staffId, setStaffId] = useState("");
    const [, setStaffAtom] = useRecoilState(getStaffAtom);
    const getStaffValue = useRecoilValue(getStaffAtom);


    const initialValues = {
        assetType: assetData?.asset?.assetType?.name,
        assetId: assetData?.asset?.serial_number,
        assetModel: assetData?.asset?.model,
        purchaseDate: assetData?.asset?.purchase_date ? moment(assetData?.asset?.purchase_date).format("DD-MM-YYYY") : "",
        supplierContact: assetData?.asset?.supplier_contact,
        description: assetData?.asset?.assetType?.description,
        assetName: assetData?.asset?.name,
        assignedTo: assetData?.staff?.staffPersonalInformations?.first_name + ' ' + assetData?.staff?.staffPersonalInformations?.last_name,
        jobTitle: assetData?.staff?.job_title,
        assignedDate: assetData?.assignedByUser?.created_at ? moment(assetData?.assignedByUser?.created_at).format("DD-MM-YYYY") : "",
        returnDate: assetData?.returned_at ? moment(assetData?.returned_at).format("DD-MM-YYYY") : "",
        approvedBy: assetData?.assignedByUser?.first_name + " " + assetData?.assignedByUser?.last_name,
        condition: assetData?.asset?.condition
    };

    const fetchAsset = async () => {
        setIsFetching(true);
        getAssignedAssetbyId(id).then((res) => {
            setIsFetching(false);
            if(res.success) {
                setAssetData(res.data);
            }
        })
    };

    
    const fetchStaff= async () => {
        getStaff().then((res) => {
            if(res.success) {
                setStaffAtom(res.data);
            }
        })
    };

    const staffs = getStaffValue?.data?.map((item) => ({
        text: item?.user?.first_name + " " + item?.user?.last_name,
        value: item.id
    }));

    const handleAssign = () => {
        setIsLoading(true);
        reassignAsset({
            assetId: id,
            staffId: staffId
        }).then((res) => {
            if(res.success) {
                setIsLoading(false);
                setAssignModal(false);
                clustarkToast(NotificationTypes.SUCCESS, res.message);
                fetchAsset();
            }
        }).catch(() => setIsLoading(false))
    };

    const handleReturn = () => {
        setIsLoading(true);
        markAsReturned({
            assignedAssetId: id,
        }).then((res) => {
            if(res.success) {
                setIsLoading(false);
                setReturnModal(false);
                clustarkToast(NotificationTypes.SUCCESS, res.message);
                fetchAsset();
            }
        }).catch(() => setIsLoading(false))
    };

  


    useEffect(() => {
        if(id) {
          fetchAsset();
          fetchStaff();
        } else {
          navigate("/asset-management")
        }
      }, [id]);

      

    if(isFetching) {
        return <div>
          <Loader/>
        </div>
      };

  return (
    <div>
      <GoBack/>
        <div className='mt-6'>
        <h1 className="font-poppins-medium text-18 rounded-tl-[10px] rounded-tr-[10px] text-purple-dark bg-purple-light pl-10 py-[33px]">
          Asset information
        </h1>
        <div className='mt-12'>
            <Formik initialValues={initialValues} onSubmit={() => {}} enableReinitialize>
                {() => (
                    <Form>
                        <div className='bg-white py-5 px-9 '>
                            {/* <h1 className='text-purple-dark font-poppins-medium text-18'>Asset information</h1> */}
                            <div className='mt-16 grid grid-cols-2 gap-[42px]'>
                                <div>
                                    <div className='grid grid-cols-2 gap-3'>
                                        <label className='place-content-center text-neutral-normal text-18' htmlFor="assetType">Asset type</label>
                                        <div>
                                            <FormikCustomInput
                                                name="assetType"
                                                type="text"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                    <div className='grid grid-cols-2 gap-3 mt-4'>
                                        <label className='place-content-center text-neutral-normal text-18' htmlFor="assetId">Asset ID</label>
                                        <div>
                                            <FormikCustomInput
                                                name="assetId"
                                                type="text"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                    <div className='grid grid-cols-2 gap-3 mt-4'>
                                        <label className='place-content-center text-neutral-normal text-18' htmlFor="returnedDate">Purchased date</label>
                                        <div>
                                            <FormikCustomInput
                                                name="purchaseDate"
                                                type="text"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div className='grid grid-cols-2 gap-3'>
                                        <label className='place-content-center text-neutral-normal text-18' htmlFor="assetName">Asset name</label>
                                        <div>
                                            <FormikCustomInput
                                                name="assetName"
                                                type="text"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                    <div className='grid grid-cols-2 gap-3 mt-4'>
                                        <label className='place-content-center text-neutral-normal text-18' htmlFor="assetModel">Asset model</label>
                                        <div>
                                            <FormikCustomInput
                                                name="assetModel"
                                                type="text"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                    <div className='grid grid-cols-2 gap-3 mt-4'>
                                        <label className='place-content-center text-neutral-normal text-18' htmlFor="supplierContact">Supplier contact</label>
                                        <div>
                                            <FormikCustomInput
                                                name="supplierContact"
                                                type="text"
                                                disabled
                                            />
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div className='grid grid-cols-4 mt-4'>
                                <label className='place-content-center text-neutral-normal text-18' htmlFor="description">Description</label>
                                <div className='col-span-3'>
                                    <textarea rows={4} className='disabled: border-none border w-full rounded disabled:bg-[#F4F4F4]' name="description" disabled />
                                </div>
                            </div>
                        </div>

                        <div className='mt-8'>
                        <div className='bg-white py-11 px-9 '>
                            {/* <h1 className='text-purple-dark font-poppins-medium text-18'>Assignee information</h1> */}
                            <div className='mt-16 grid grid-cols-2 gap-[42px]'>
                                <div>
                                    <div className='grid grid-cols-2 gap-3'>
                                        <label className='place-content-center text-neutral-normal text-18' htmlFor="assignedTo">Assigned to</label>
                                        <div>
                                            <FormikCustomInput
                                                name="assignedTo"
                                                type="text"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                    <div className='grid grid-cols-2 gap-3 mt-4'>
                                        <label className='place-content-center text-neutral-normal text-18' htmlFor="assignedDate">Assigned date</label>
                                        <div>
                                            <FormikCustomInput
                                                name="assignedDate"
                                                type="text"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                    <div className='grid grid-cols-2 gap-3 mt-4'>
                                        <label className='place-content-center text-neutral-normal text-18' htmlFor="returnedDate">Returned date</label>
                                        <div>
                                            <FormikCustomInput
                                                name="returnedDate"
                                                type="text"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div className='grid grid-cols-2 gap-3'>
                                        <label className='place-content-center text-neutral-normal text-18' htmlFor="jobTitle">Role</label>
                                        <div>
                                            <FormikCustomInput
                                                name="jobTitle"
                                                type="text"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                    <div className='grid grid-cols-2 gap-3 mt-4'>
                                        <label className='place-content-center text-neutral-normal text-18' htmlFor="approvedBy">Approved by</label>
                                        <div>
                                            <FormikCustomInput
                                                name="approvedBy"
                                                type="text"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                    <div className='grid grid-cols-2 gap-3 mt-4'>
                                        <label className='place-content-center text-neutral-normal text-18' htmlFor="condition">Condition</label>
                                        <div>
                                            <FormikCustomInput
                                                name="condition"
                                                type="text"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                    

                                </div>
                            </div>
                        </div>
                        </div>
                        <div className='flex justify-end mt-16'>
                            <div className='flex gap-3'>
                                <CustomButton className='!w-[170px]' title='Re-Assign Asset' variant={ButtonProperties.VARIANT.primary.name} handleClick={() => {setAssignModal(true)}}/>
                                    {assetData?.status !== "RETURNED" && (
                                        <CustomButton className='!w-[221px] !bg-[#027A48] text-white' title='Mark as Returned' handleClick={() => {setReturnModal(true)}}/>
                                    )}
                            </div>
                        </div>
                    </Form>
                )}
            </Formik>
        </div>
        </div>

        <CustomModal
        visibility={assignModal}
        toggleVisibility={setAssignModal}
      >
        <div>
          <div className='flex justify-between bg-purple-light py-[33px] px-10 '>
            <h1 className=" font-poppins-medium text-24 text-purple-normal">
              Re-Assign Asset
            </h1>
            <MdClose
            size={18}
            color='#0C0123'
              className="cursor-pointer"
              onClick={() => setAssignModal(false)}
            />
          </div>
          <div className='mt-8 px-10'>
                <h1 className='text-20 text-neutral-dark font-poppins-medium mb-12'>Select Employee</h1>
                <Formik initialValues={{staff: (assetData?.staff?.user?.first_name + " " + assetData?.staff?.user?.last_name) || "" }} onSubmit={handleAssign}>
                    {({values, setFieldValue}) => (
                        <Form>
                            <div>
                                <FormikCustomSelect
                                label="Employee"
                                name="staff"
                                options={staffs}
                                value={values.staff}
                                onChange={(item: { value: string; text: string }) => {
                                    setStaffId(item.value);
                                    setFieldValue("staff", item.value);
                                  }}
                                />

                            </div>

                            <div className='flex justify-end mt-14 mb-8'>
                                <CustomButton isLoading={isLoading} type='submit' className='w-[170px]' title='Assign' variant={ButtonProperties.VARIANT.primary.name} handleClick={() => {setAssignModal(false)}}/>
                            </div>
                        </Form>
                    )}
                </Formik>
          </div>
         
          
        </div>
      </CustomModal>

      <CustomModal
        visibility={returnModal}
        toggleVisibility={setReturnModal}
      >
        <div>
          <div className='flex justify-between bg-purple-light py-[33px] px-10 '>
            <h1 className=" font-poppins-medium text-24 text-purple-normal">
              Mark as Returned
            </h1>
            <MdClose
            size={18}
            color='#0C0123'
              className="cursor-pointer"
              onClick={() => setReturnModal(false)}
            />
          </div>
          <div className='mt-8 px-10'>
                <p className='text-20 text-neutral-normal font-poppins-medium mb-12 text-center'>Are you sure you want to mark this asset as returned?</p>
                <div>
                    <div className='flex justify-center gap-6 mt-14 mb-8'>
                        <CustomButton isLoading={isLoading} className='w-[170px]' title='Mark as Returned' variant={ButtonProperties.VARIANT.primary.name} handleClick={handleReturn}/>
                        <CustomButton  className='w-[170px] text-neutral-dark' isTransparent title='Cancel' handleClick={() => setReturnModal(false)}/>
                    </div>
                </div>
                
          </div>
         
          
        </div>
      </CustomModal>
    </div>
  )
}

export default AssetInfromation