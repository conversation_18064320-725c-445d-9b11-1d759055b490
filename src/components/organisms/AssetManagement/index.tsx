import StatisticsCard from "../../atoms/Cards/StatisticsCard";
import { FaListCheck } from "react-icons/fa6";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { FiPlus } from "react-icons/fi";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useEffect, useState } from "react";
import AssignedAsset from "./AssignedAsset";
import { useRecoilState, useRecoilValue } from "recoil";
import { getAssetAnalytics } from "../../../api/assetManagement";
import { DollarCircle, Note1, NoteAdd } from "iconsax-react";
import { getAsssetAnalyticsAtom } from "../../../recoil/atom/assets";
import AllAssets from "./AllAssets";
import Loader from "../../atoms/Loader";
import RequestList from "./RequestList";
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";


const AssetManagement = () => {
  const navigate = useNavigate();
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("activeTab");
  const [isFetching, setIsFetching] = useState(false);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [, setAssetAnalyticAtom] = useRecoilState(getAsssetAnalyticsAtom);
  const assetAnalyticValue = useRecoilValue(getAsssetAnalyticsAtom);

  const fetchAssetAnalytics = () => {
    setIsFetching(true);
    getAssetAnalytics().then((res) => {
      if (res.success) {
        setIsFetching(false);
        setAssetAnalyticAtom(res.data);
      }
    }).catch(() => {
      setIsFetching(false);
      clustarkToast(NotificationTypes.ERROR, "An error occurred while fetching assets analytics.");
    })
  };

  useEffect(() => {
    fetchAssetAnalytics();
  }, []);

  const assetsTab = [
    {
      title: "All assets",
      component: <AllAssets />,
      name: "allAsset",
    },
    {
      title: "Assigned assets",
      component: <AssignedAsset />,
      name: "assignedAsset",
    },
    {
      title: "Asset requests",
      component: <RequestList />,
      name: "request",
    },
  ];

  const assetStats = [
    {
      title: "Total assets",
      value: assetAnalyticValue?.totalAsset,
      icon: <NoteAdd size={24} />,
      iconBackgroundColor: '#3730A399',
      valueText: "Asset",
      cardBackgroundColor: "#EEF2FFCC",
    },
    {
      title: "Assests in use",
      value: assetAnalyticValue?.totalAssetInUse,
      icon: <FaListCheck size={24} />,
      iconBackgroundColor: '#B3AA0199',
      valueText: "Assigned",
      cardBackgroundColor: "#FDFFE8CC",
    },

    {
      title: "Available assets",
      value: assetAnalyticValue?.totalActiveAssets,
      icon: <Note1 size={24} />,
      iconBackgroundColor: '#09778399',
      valueText: "Assigned",
      cardBackgroundColor: "#EBFDFFCC",
    },
    {
      title: "Unavailable assets",
      value: assetAnalyticValue?.totalInactiveAssets,
      icon: <DollarCircle size={24} />,
      iconBackgroundColor: '#A3000099',
      valueText: "Under maintenance",
      cardBackgroundColor: "#FFEDEDCC",
    },
  ];



  useEffect(() => {
    setActiveTab(queryParam || 0)

  }, [queryParam, activeTab]);

  if (isFetching) {
    return <div>
      <Loader />
    </div>
  };

  return (
    <div>
      <div>
        {/* <h1 className="font-poppins font-semibold text-24">Asset Management</h1> */}
        <div className=" grid grid-cols-4 gap-5">
          {assetStats?.map((item, index) => (
            <div key={index}>
              <StatisticsCard
                backgroundColor={item?.cardBackgroundColor}
                key={index}
                title={item.title}
                value={item.value}
                icon={item.icon}
                iconBackgroundColor={item?.iconBackgroundColor}
                valueText={item.valueText}
              />
            </div>
          ))}
        </div>
      </div>

      <div>

        <div className=" bg-[#F4F4F6] flex justify-between mt-10">
          <div className="flex gap-10 py-1 px-1 justify-center items-center  bg-[#F4F4F6] rounded-md">
            {assetsTab.map((item, index) => (
              <div
                key={index}
                onClick={() => { setActiveTab(index); navigate(`/asset-management?activeTab=${index}&tab=${item.name}`) }}
                className={`cursor-pointer px-2 py-2 rounded  ${activeTab == index ? "bg-white" : "bg-none"}`}>
                <p className={`text-neutral-normal font-poppins-medium  px-5 text-center text-16 ${activeTab == index ? "text-purple-normal" : "text-gray-950"} `}> {item.title}</p>
              </div>
            ))}
          </div>
          <div className="bg-black rounded">
            <CustomButton
              className="!w-[250px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md !h-12"
              isTransparent={true}
              handleClick={() => {
                navigate("/asset/new-asset");
              }}
              leftIcon={<FiPlus className="ml-3" size={20} />}
              title="Add new asset"
            />
          </div>
        </div>

        <div>
          {assetsTab[activeTab].component}
        </div>

      </div>

    </div>

  );
};

export default AssetManagement;

// const StatusTag = ({status}) => {
//   return (
//     <div>
//          <p
//           className={`rounded - lg font - poppins - medium text - center py - 1 px - 3 w - fit flex justify - center items - center ${
//             status.toLowerCase().includes("Under Maintenance")
//               ? "text-[#EBB02E] bg-[#FFFDF1]"
//               : status.includes("Returned")
//               ? "bg-[#FFF2EA] text-[#F15046]"
//               :  status.includes("In Use")
//               ? "bg-[#ECFDF3] text-[#027A48]"
//               : "text-[#868686] bg-[#B2BBC62B] "
//           }`}
//         >
//           <span className={`w-2 h-2 rounded-full mr-2 ${
//             status.includes("Under Maintenance")
//               ? "bg-[#EBB02E]"
//               : status.includes("Returned")
//               ? "bg-[#F15046]"
//               : status.includes("In Use")
//               ? "bg-[#027A48]"
//               : "bg-[#868686]"
//           }`} />
//           {status || "--"}{" "}
//         </p>
//     </div>
//   )
// }
