import React, { useEffect, useRef, useState } from 'react';
import CustomTable from '../../atoms/CustomTable/CustomTable';
import OrganizationEmptyState from '../../atoms/Cards/OrganizationEmptyState';
import { useNavigate } from 'react-router-dom';
import { useRecoilState, useRecoilValue } from 'recoil';
import useClickOutside from '../../shared/hooks';
import { getAllAssetsAtom } from '../../../recoil/atom/assets';
import { assignAsset, deleteAsset, getAllAssets } from '../../../api/assetManagement';
import { FaEllipsisV } from 'react-icons/fa';
import FilterDropdown from '../../atoms/Cards/FilterDropdown';
import { ButtonProperties, debounce } from '../../shared/helpers';
import moment from 'moment';
import CustomModal from '../../atoms/CustomModal/CustomModal';
import { MdClose } from 'react-icons/md';
import { Formik, Form } from 'formik';
import FormikCustomSelect from '../../atoms/CustomInput/FormikCustomSelect';
import CustomButton from '../../atoms/CustomButton/CustomButton';
import { getStaffAtom } from '../../../recoil/atom/staff';
import { getStaff } from '../../../api/staff';
import { clustarkToast } from '../../atoms/Toast';
import { NotificationTypes } from '../../shared/helpers/enums';
import Loader from '../../atoms/Loader';
import { Eye, UserEdit } from 'iconsax-react';
import { PiShareFatLight, PiTrash } from 'react-icons/pi';
import { FiPlus } from 'react-icons/fi';

const AllAssets = () => {
  const navigate = useNavigate();
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [rowId, setRowId] = useState("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [assignModal, setAssignModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [staffId, setStaffId] = useState("");
  const [, setStaffAtom] = useRecoilState(getStaffAtom);
  const getStaffValue = useRecoilValue(getStaffAtom);
  const [, setAssetAtom] = useRecoilState(getAllAssetsAtom);
  const assetRequestsValue = useRecoilValue(getAllAssetsAtom);
  const debounceSearch = useRef(debounce((q) => fetchAllAssets(q), 2000)).current;


  const node = useClickOutside(() => {
    setShowDropdown(false);
    setShowDeleteWarn(false);
  });

  const fetchAllAssets = (q?) => {
    setIsFetching(true);
    getAllAssets({ search: q, page: pageNumber }).then((res) => {
      setIsFetching(false);
      if (res.success) {
        setAssetAtom(res.data);
      } else {
        clustarkToast(NotificationTypes.ERROR, "An error occurred while fetching assets.");
      }
    }).catch(() => {
      setIsFetching(false);
      clustarkToast(NotificationTypes.ERROR, "An error occurred while fetching assets.");
    })
  };


  useEffect(() => {
    fetchAllAssets(searchQuery);
  }, [searchQuery, pageNumber]);

  const fetchStaff = async () => {
    getStaff().then((res) => {
      if (res.success) {
        setStaffAtom(res.data);
      }
    })
  };

  useEffect(() => {
    fetchStaff();
  }, []);

  const staffs = getStaffValue?.data?.map((item) => ({
    text: item?.staffPersonalInformations?.first_name + " " + item?.staffPersonalInformations?.last_name,
    value: item.id
  }));

  const handleAssign = () => {
    setIsLoading(true);
    assignAsset({
      assetId: rowId,
      staffId: staffId
    }).then((res) => {
      if (res.success) {
        setIsLoading(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchAllAssets(searchQuery);
        setAssignModal(false);
      }
    }).catch(() => setIsLoading(false))
  };

  if (isFetching && !searchQuery) {
    return <div>
      <Loader />
    </div>
  };

  const handleDeleteAssest = (id) => {
    setIsLoading(true);
    deleteAsset(id).then((res) => {
      setIsLoading(false);
      if (res.success) {
        setShowDropdown(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message)
        fetchAllAssets();
      }
    });
  };

  const columns = [
    {
      Header: "Asset ID",
      accessor: "serial_number",
      Cell: (row: any) => (
        <p>{row.cell.value || "--"}</p>
      ),
    },
    {
      Header: "Asset type",
      accessor: "assetType.name",
      Cell: (row: any) => (
        <p>{row.cell.value || "--"}</p>
      ),
    },

    {
      Header: "Asset name",
      accessor: "name",
      Cell: (row: any) => (
        <p>
          {row.cell.value || "--"}
        </p>
      ),
    },
    {
      Header: "Specification",
      accessor: "specification",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Quantity",
      accessor: "quantity",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Cost",
      accessor: "purchase_cost",
      Cell: (row: any) => <p className='whitespace-nowrap flex gap-2'> {row.cell.row.original.currency && <span className="text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1">{row.cell.row.original.currency}</span>} {`${row.cell?.value?.toLocaleString()}` || "--"}  </p>,
    },

    {
      Header: "Condition",
      accessor: "condition",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },

    {
      Header: "Model",
      accessor: "model",
      Cell: (row: any) => (<p>{row.cell.value || "--"}</p>),
    },
    {
      Header: "Supplier contact",
      accessor: "supplier_contact",
      Cell: (row: any) => (<p>{row.cell.value || "--"}</p>),
    },
    {
      Header: "Purchase date",
      accessor: "purchase_date",
      Cell: (row: any) => <p>{row.cell.value ? moment(row.cell.value).format("DD-MM-YYYY") : "--"} </p>,
    },
    // {
    //   Header: "Status",
    //   accessor: "status",
    //   Cell: (row: any) => (<StatusTag status={row.cell.value}/>),
    // },
    {
      Header: "File",
      accessor: "image",
      Cell: (row: any) => <p>{row.cell.value ? <a target="_blank" className='text-purple-normal' href={row.cell.value}>view file </a> : "--"}</p>,
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.original.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.original.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() => {
                    navigate("/asset/asset-information", { state: { id: row.cell.row.original.id } })
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>
                <li onClick={() => { navigate(`/asset/new-asset`, { state: { isEdit: true, data: row.cell.row.original } }) }} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <UserEdit size={18} />
                  Edit
                </li>
                <li
                  onClick={() => {
                    setAssignModal(true)
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <PiShareFatLight size={18} />
                  Assign Asset
                </li>
                {showDeleteWarn ? (
                  <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                    <div className="flex gap-3">
                      <PiTrash size={18} />
                      Are you sure?
                    </div>
                    <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                      <CustomButton isLoading={isLoading} title="Yes" handleClick={() => handleDeleteAssest(row.cell.row.original.id)} className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer" />
                      <span onClick={() => { setShowDeleteWarn(false) }} className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center">No</span>
                    </div>
                  </li>
                ) : (
                  <li onClick={() => setShowDeleteWarn(true)} className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer">
                    <PiTrash size={18} />
                    Delete
                  </li>
                )}

              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];


  return (
    <div>
      <div className=" my-10 py-[23px]">
        {assetRequestsValue?.data?.length > 0 || searchQuery ? (
          <CustomTable
            data={assetRequestsValue?.data || []}
            meta={assetRequestsValue?.meta || {}}
            columns={columns}
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
            header={
              <div className="flex justify-between items-center h-[60px] px-2">
                <h1>
                  All assets
                </h1>
                
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState
              buttonTitle="Add new asset"
              handleClick={() => navigate("/asset/new-asset")}
            />
          </div>
        )}
      </div>


      <CustomModal
        visibility={assignModal}
        toggleVisibility={setAssignModal}
      >
        <div>
          <div className='flex justify-between bg-purple-light py-[33px] px-10 '>
            <h1 className=" font-poppins-medium text-24 text-purple-normal">
              Assign Asset
            </h1>
            <MdClose
              size={18}
              color='#0C0123'
              className="cursor-pointer"
              onClick={() => setAssignModal(false)}
            />
          </div>
          <div className='mt-8 px-10'>
            {/* <h1 className='text-20 text-neutral-dark font-poppins-medium mb-12'>Select Employee</h1> */}
            <Formik initialValues={{ staff: "" }} onSubmit={handleAssign}>
              {({ values, setFieldValue }) => (
                <Form>
                  <div>
                    <FormikCustomSelect
                      label="Select employee"
                      name="staff"
                      options={staffs}
                      value={values.staff}
                      onChange={(item: { value: string; text: string }) => {
                        setStaffId(item.value);
                        setFieldValue("staff", item.value);
                      }}
                    />

                  </div>

                  <div className='flex justify-end mt-14 mb-8'>
                    <CustomButton isLoading={isLoading} isDisabled={!staffId} type='submit' className='w-[170px]' title='Assign' variant={ButtonProperties.VARIANT.primary.name} handleClick={() => { setAssignModal(false) }} />
                  </div>
                </Form>
              )}
            </Formik>
          </div>


        </div>
      </CustomModal>

    </div>
  )
}

export default AllAssets;