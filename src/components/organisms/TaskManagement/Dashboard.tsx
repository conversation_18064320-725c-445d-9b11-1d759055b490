import { FaCommentDots, FaRegBell } from "react-icons/fa6";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../shared/helpers";
import { HiMiniChevronUpDown } from "react-icons/hi2";
import Avatar from '../../../assets/sample/img1.jpg';
import Avatar2 from '../../../assets/images/randomImage.jpg';
import { PiCirclesThreeFill, PiNotepadFill } from "react-icons/pi";
import { HiDotsHorizontal } from "react-icons/hi";
import { IoCheckmarkDoneSharp } from "react-icons/io5";
import { TbBrandWechat } from "react-icons/tb";
import { BsChatSquareDots } from "react-icons/bs";
import CustomProgressBar from "../../atoms/CustomProgressBar";
import StatusTag from "../../atoms/StatusTag";
import CustomTable from "../../atoms/CustomTable/CustomTable";
import { FaSearch } from "react-icons/fa";
import StatisticsCard from "../../atoms/Cards/StatisticsCard";
import { LuActivity } from "react-icons/lu";
import { RiAttachment2 } from "react-icons/ri";



const Dashboard = () => {

    const allTask = [
        { icon: <PiNotepadFill size={16} color="#3730A3" />, status: 'Total project', count: 18, title: "Total projects", },
        { icon: <PiCirclesThreeFill size={16} color="#3730A3" />, status: 'total team', count: 7, title: "Total team", },
        { icon: <IoCheckmarkDoneSharp size={16} color="#3730A3" />, status: 'total task', count: 5, title: "Total task", },
    ]

    const otherTask = [
        { icon: < FaCommentDots size={16} color="#3730A3" />, status: 'Total comment', count: 10, title: "Total comments", },
        { icon: <LuActivity size={16} color="#3730A3" />, status: 'total activity', count: 2, title: "Total activities", },
        { icon: <RiAttachment2 size={16} color="#3730A3" />, status: 'total attachment', count: 8, title: "Total attachments", },
    ]

    const recentTask = [
        { priority: 'High', progress: 40, color: '#FF0000', title: 'Mobile App Redesign', descr: 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dolores tempora necessitatibus quidem', assignee: [Avatar, Avatar2, Avatar], chat: 1, msg: 2 },
        { priority: 'Low', progress: 60, color: '#FFFF00', title: 'Landing Page Redesign', descr: 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dolores tempora necessitatibus quidem', assignee: [Avatar, Avatar2,], chat: 2, msg: 5 },
        { priority: 'Medium', progress: 20, color: '#00FF00', title: 'Image Upload Page', descr: 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dolores tempora necessitatibus quidem', assignee: [Avatar, Avatar2, Avatar], chat: 3, msg: 6 },
        { priority: 'Low', progress: 10, color: '#0000FF', title: 'File Adjustment', descr: 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dolores tempora necessitatibus quidem', assignee: [Avatar], chat: 2, msg: 3 },
        { priority: 'High', progress: 40, color: '#FF0000', title: 'Mobile App Redesign', descr: 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dolores tempora necessitatibus quidem', assignee: [Avatar, Avatar2, Avatar], chat: 1, msg: 2 },
        { priority: 'Low', progress: 60, color: '#FFFF00', title: 'Landing Page Redesign', descr: 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dolores tempora necessitatibus quidem', assignee: [Avatar, Avatar2,], chat: 2, msg: 5 },
        { priority: 'Medium', progress: 20, color: '#00FF00', title: 'Image Upload Page', descr: 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dolores tempora necessitatibus quidem', assignee: [Avatar, Avatar2, Avatar], chat: 3, msg: 6 },
        { priority: 'Low', progress: 10, color: '#0000FF', title: 'File Adjustment', descr: 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dolores tempora necessitatibus quidem', assignee: [Avatar], chat: 2, msg: 3 },
    ]

    const taskTable = [
        { taskname: 'Mobile App Redesign', startdate: 'May 5, 2025', enddate: 'june 5, 2025', color: '#FF0000', assignee: [Avatar, Avatar2, Avatar], attatchment: Avatar2, priority: 'High', progress: 40, },
        { taskname: 'Landing Page Redesign', startdate: 'May 2, 2025', enddate: 'june 5, 2025', color: '#00FF00', assignee: [Avatar2,], attatchment: Avatar2, priority: 'MEDIUM', progress: 20, },
        { taskname: 'Image Upload Page', startdate: 'May 1, 2025', enddate: 'june 5, 2025', color: '#0000FF', assignee: [Avatar2, Avatar], attatchment: Avatar2, priority: 'LOW', progress: 70, },
    ]

    const columns = [
        {
            Header: "Task Name",
            accessor: "taskname",
            Cell: (row: any) => (
                <p className="text-[#101828]">
                    {row.cell.value || "--"}
                </p>
            ),
        },
        {
            Header: "Start Date",
            accessor: "startdate",
            // Cell: (row: any) => <p className='lowercase'>{row.cell.value || "--"} </p>,
        },
        {
            Header: "End Date",
            accessor: "enddate",
            // Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
        },
        {
            Header: "Assignee",
            accessor: "assignee",
            Cell: (row: any) => <div className="flex relative">
                {row.cell.value.map((item, index) => (
                    <div
                        key={index}
                        className="h-[20px] w-[20px] overflow-hidden rounded-full absolute"
                        style={{ left: `${index * 10}px` }}
                    >
                        <img
                            src={item}
                            alt="img"
                            className="h-[100%] w-[100%] object-cover"
                        />
                    </div>
                ))}
            </div>,
        },
        {
            Header: "Progress",
            accessor: "progress",
            Cell: (row: any) => <div className="">
                <CustomProgressBar
                    value={row.cell.value}
                />
            </div>,
        },
        {
            Header: "Attachment",
            accessor: "attatchment",
            Cell: (row: any) => (
                <div>
                    {row.cell.value ? (
                        <a
                            href={row.cell.value}
                            download target="_blank"
                            rel="noopener noreferrer"
                            className="text-purple-dark-active font-bold"
                        >
                            Download
                        </a>
                    ) : (
                        '--'
                    )}
                </div>
            ),
        },
        {
            Header: "Priority",
            accessor: "priority",
            Cell: (row: any) => (<StatusTag status={row.cell.value || ''} />),
        },
    ];


    return (
        <>

            <div className="">

                <div className="flex items-center justify-between border-y-2 p-4">
                    <div className="">
                        <h1 className="font-bold text-[1.5rem] text-purple-dark">Overview</h1>
                        <p className=" text-[0.9rem] text-gray-400" >Monitor all your projects and tasks here</p>
                    </div>

                    <div className="flex items-center gap-6">
                        <div className="">
                            <div className="relative">
                                <span className="absolute top-4 left-3" ><FaSearch size={15} color="gray" /></span>
                                <input
                                    type="text"
                                    placeholder="search tasks"
                                    className="w-[200px] h-[45px] rounded-md p-5 px-10 border-2 border-gray-300 focus:outline-gray-300 focus:ring-0"
                                />
                            </div>
                        </div>

                        <div className="">
                            <CustomButton
                                type="submit"
                                className="!w-[50px] !h-[45px] bg-white hover:bg-transparent !border-2 !border-gray-300 transition-colors duration-200"
                                title={<FaRegBell color="grey" size={18} />}
                                // isLoading={isLoading}
                                handleClick={() => { }}
                                variant={ButtonProperties.VARIANT.primary.name}
                            />
                        </div>

                        <div className="flex items-center gap-4 border-2 border-gray-300 rounded-md px-3 py-2 ">
                            <div className="h-[28px] w-[28px] rounded-md overflow-hidden">
                                <img
                                    src={Avatar2}
                                    alt="avatar"
                                    className="h-[100%] w-[100%] object-cover"
                                />
                            </div>
                            <div className="font-bold text-[0.6rem] border-gray-500 ">
                                <h3 >Devin</h3>
                                <p>UI/UX Designer</p>
                            </div>
                            <div className="">
                                <HiMiniChevronUpDown size={20} color="gray" />
                            </div>
                        </div>

                    </div>

                </div>

                <div className=" grid grid-cols-3 gap-5 mt-5">

                    {allTask?.map(({ icon, status, count, title }, index) => (
                        <div key={index}>
                            <StatisticsCard
                                // backgroundColor={item.color}
                                key={index}
                                title={title}
                                value={count}
                                icon={icon}
                                valueText={status}
                            />
                        </div>
                    ))}

                </div>

                <div className="mt-5 rounded-md border-2 border-gray-30 p-3">

                    <div className="flex items-center justify-between mb-4">
                        <div className="">
                            <h2 className="font-bold text-[1.1rem] text-purple-dark" >Recent Task</h2>
                        </div>
                        <div className="border-2 border-gray-30 rounded-md p-1">
                            <HiDotsHorizontal size={18} color="#4338CA" />
                        </div>
                    </div>

                    <div className="flex gap-5 mb-5 overflow-x-scroll w-[1500px] show-scrollbar py-5 ">
                        {
                            recentTask.map(({ priority, progress, title, descr, assignee, chat, msg }, index) =>
                                <div key={index} className="min-w-[350px]  rounded-md border-2 border-gray-30 py-6 px-6 flex flex-col gap-2 justify-center">
                                    <div className="flex items-center justify-between">
                                        <div className="">
                                            <p><StatusTag status={priority || ''} /></p>
                                        </div>
                                        <div className="">
                                            <HiDotsHorizontal size={18} color="#4338CA" />
                                        </div>
                                    </div>
                                    <div className="flex flex-col gap-2">
                                        <h3 className="font-bold text-[1rem] text-gray-600" >{title}</h3>
                                        <p className="font-medium text-[0.9rem] text-gray-600 " >
                                            {descr}
                                        </p>
                                        <div className="">
                                            <CustomProgressBar
                                                value={progress}
                                            />
                                        </div>

                                        <div className="flex justify-between">
                                            <div className="flex relative">
                                                {assignee.map((item, index) => (
                                                    <div
                                                        key={index}
                                                        className="h-[20px] w-[20px] overflow-hidden rounded-full absolute"
                                                        style={{ left: `${index * 10}px` }}
                                                    >
                                                        <img
                                                            src={item}
                                                            alt="img"
                                                            className="h-[100%] w-[100%] object-cover"
                                                        />
                                                    </div>
                                                ))}
                                            </div>

                                            <div className="flex items-center gap-3">
                                                <div className="flex gap-1 items-start">
                                                    <span><TbBrandWechat size={20} color="#4338CA" /></span>
                                                    <p className="font-medium text-[0.8rem] text-purple-dark-hover ">{chat}</p>
                                                </div>
                                                <div className="flex gap-1 items-start">
                                                    <span><BsChatSquareDots size={16} color="#4338CA" /></span>
                                                    <p className="font-medium text-[0.8rem] text-purple-dark-hover ">{msg}</p>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>)
                        }
                    </div>

                </div>

                <div className=" grid grid-cols-3 gap-5 mt-5">

                    {otherTask?.map(({ icon, status, count, title }, index) => (
                        <div key={index}>
                            <StatisticsCard
                                // backgroundColor={item.color}
                                key={index}
                                title={title}
                                value={count}
                                icon={icon}
                                valueText={status}
                            />
                        </div>
                    ))}

                </div>

                <div className="mt-5">
                    <CustomTable
                        data={taskTable}
                        // meta={'--' || {}}
                        columns={columns}
                        handleFilter={(e) => console.log(e)}
                        header={
                            <div>
                                <h1 className="font-poppins-medium text-purple-dark-active mb-4">
                                    List Task
                                </h1>
                            </div>
                        }
                    />
                </div>

            </div>

        </>
    )
}

export default Dashboard;