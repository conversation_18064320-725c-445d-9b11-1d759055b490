import { useEffect, useState } from "react";
import { FaPlus, FaX, FaXmark } from "react-icons/fa6";
import { Clock, Flag, User } from "iconsax-react";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import AddTask from "./AddTask";
import { ButtonProperties, calculateDaysLeft, getNameInitials, priorityStatus, removeHtmlTags, truncateText } from "../../../shared/helpers";
import { useRecoilState } from "recoil";
import { getIssuesAtom } from "../../../../recoil/atom/TaskManagement";
import moment from "moment";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import { addTaskAssignee, getTeamMemberByProject, removeTaskAssignee, updateTaskPriorityById, updateTaskStatus } from "../../../../api/TaskManagement";
import { clustarkToast } from "../../../atoms/Toast";
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";
import { NotificationTypes } from "../../../shared/helpers/enums";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import CustomCheckBox from "../../../atoms/CustomCheckBox/CustomCheckBox";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";

export default function BoardView() {
  const navigate = useNavigate();
  const [showAddTaskModal, setShowAddTaskModal] = useState<boolean>(false);
  const [selectedPriority, setSelectedPriority] = useState<string>("");
  const [updateLoading, setUpdateLoading] = useState<boolean>(false);
  const [showPriorityModal, setShowPriorityModal] = useState<boolean>(false);
  const [rowId, setRowId] = useState<boolean>(false);
  const [assignees, setAssignees] = useState<any>([]);
  const [teamMembersData, setTeamMembersData] = useState<any>([]);
  const [singleTask, setSingleTask] = useState<any>({});
  const [assigneeModal, setAssigneeModal] = useState<boolean>(false);

  const { fetchIssues } = useUpdateRecoilAtom();
  const [issues, setIssues] = useRecoilState(getIssuesAtom);


  const handleDragStart = (event, item, sourceColumnId, sourceIndex) => {
    event.dataTransfer.setData("text/plain", JSON.stringify({ item, sourceColumnId, sourceIndex }));
    const dragImage = event.target.cloneNode(true);
    dragImage.style.position = 'absolute';
    document.body.appendChild(dragImage);
    event.dataTransfer.setDragImage(dragImage, 0, 0);
    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);
  };

  const handleDrop = (event, targetColumnId, targetIndex) => {
    event.preventDefault();
    const data = JSON.parse(event.dataTransfer.getData("text/plain"));
    const { item, sourceColumnId, sourceIndex } = data;

    setIssues((prevColumns) => {
      return prevColumns.map((col) => {
        let updatedItems = [...col.tasks];
        if (col.status.id == sourceColumnId) {
          updatedItems.splice(sourceIndex, 1);
        }

        if (col.status.id == targetColumnId) {
          updatedItems.splice(targetIndex, 0, item);
        }
        return { ...col, tasks: updatedItems };
      });
    });
    handleTaskStatus(item.id, targetColumnId);
  };

  const handleTaskStatus = (taskId, statusId) => {
    const payload = {
      task_id: taskId,
      status_id: statusId,
    };
    updateTaskStatus(payload)
      .then((res) => {
        if (res.success) {
          clustarkToast(NotificationTypes.SUCCESS, res.message);
          fetchIssues();
        }
      });
  };

  const handleTaskPriority = (taskId) => {
    const payload = {
      task_id: taskId,
      priority: selectedPriority,
    };
    setUpdateLoading(true);
    updateTaskPriorityById(payload)
      .then((res) => {
        if (res.success) {
          setUpdateLoading(false);
          fetchIssues();
          clustarkToast(NotificationTypes.SUCCESS, res.message);
          setShowPriorityModal(false);
        }
        setUpdateLoading(false);
      })
      .catch(() => {
        setUpdateLoading(false);
      });
  };

  const handleAssignee = (assignee) => {
    setTeamMembersData((prev) => prev.filter((m) => m.id !== assignee.id));
    setAssignees((prev) => [...prev, assignee]);
    handleAddAssignee(assignee);
  };

  const removeAssignee = (assignee) => {
    setAssignees((prev) => prev.filter((a) => a.id !== assignee.id));
    setTeamMembersData((prev) => [...prev, assignee]);
    handleRemoveAssignee(assignee);
  };

  const handleRemoveAssignee = (assignee) => {
    setUpdateLoading(true);
    const payload = {
      issue_id: singleTask?.id,
      assignee: assignee.id
    };
    removeTaskAssignee(payload).then((res) => {
      if (res.success) {
        setUpdateLoading(false)
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchIssues();
      };
      setUpdateLoading(false);
    }).catch(() => setUpdateLoading(false));
  };

  const handleAddAssignee = (assignee) => {
    setUpdateLoading(true);
    const payload = {
      issue_id: singleTask?.id,
      assignee: assignee.id
    };
    addTaskAssignee(payload).then((res) => {
      if (res.success) {
        setUpdateLoading(false)
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchIssues();
      };
      setUpdateLoading(false);
    }).catch(() => setUpdateLoading(false));
  }

  const fetchTaskTeamMembers = () => {
    if (singleTask?.task?.project_id) {
      getTeamMemberByProject(singleTask?.task?.project_id).then((res) => {
        const fetchedMembers = res.data?.map((data) => ({
          firstName: data?.staff?.staffPersonalInformations?.first_name,
          lastName: data?.staff?.staffPersonalInformations?.last_name,
          avatar: data?.staff?.staffPersonalInformations?.avatar,
          id: data?.staff?.staffPersonalInformations?.id,
        })) || [];
        const availableMembers = fetchedMembers.filter(
          (member) => !assignees.some((assignee) => assignee.id === member.id)
        );

        setTeamMembersData(availableMembers);
      });
    }
  };

  useEffect(() => {
    fetchTaskTeamMembers()
  }, [singleTask]);

  return (
    <>
      <div className="p-3 flex justify-between items-center">

      </div>
      {issues.length > 0 ? (
        <div className="flex gap-9 p-4">
          {issues?.map(({ status, tasks }, index) => (
            <div
              key={status.id}
              className="rounded min-h-[190px] w-[290px]"
              onDragOver={(e) => e.preventDefault()}
              onDrop={(e) => handleDrop(e, status.id, index)}
            >
              <div className={`flex justify-between px-4 py-3 bg-white shadow-md rounded`} style={{ borderLeft: `3px solid ${status.color}` }}>
                <div className="flex gap-5">
                  <h2 className="text-16 font-poppins-medium text-purple-dark mt-1" style={{ textTransform: "capitalize" }}>{String(status.name)}</h2>
                  <div className="rounded-full border border-neutral-normal w-6 h-6 flex justify-center items-center place-items-center text-neutral-normal bg-gray-200"><span>{tasks.length}</span></div>
                </div>
                <div className="flex gap-3 mt-1">
                  <FaPlus className="cursor-pointer" color="#3730A3" onClick={() => setShowAddTaskModal(true)} />
                </div>
              </div>
              <div className=" mt-4">

                {tasks.map((item, index) => (
                  <div
                    key={index}
                    draggable="true"
                    onDragStart={(e) => handleDragStart(e, item, status.id, index)}
                    className="!bg-white  p-4 rounded shadow-lg mb-3"
                  >
                    <div className="cursor-pointer relative">
                      <div onClick={() => {
                        navigate("/task-management/view-task", {
                          state: { id: item.id },
                        });
                      }}>
                        <p className="text-purple-dark font-poppins-medium capitalize mb-3">{item?.task?.name}</p>
                        <p className="text-neutral-normal first-letter:capitalize">{truncateText(removeHtmlTags(item?.task?.description), 50)}</p>
                      </div>
                      <div>
                        <div className="flex justify-end -space-x-3 mt-6" onClick={() => {
                          setAssigneeModal(true); setRowId(item.id); setSingleTask(item);
                          setAssignees(() => item?.task?.task_assignees?.map((data) => ({
                            firstName: data?.staff?.staffPersonalInformations?.first_name,
                            lastName: data?.staff?.staffPersonalInformations?.last_name,
                            avatar: data?.staff?.staffPersonalInformations?.avatar,
                            id: data?.staff?.staffPersonalInformations?.id,
                          })))
                        }}>
                          {item?.task?.task_assignees?.slice(0, 4).map((item, index) => (
                            <>
                              {item?.staff?.staffPersonalInformations.avatar ? (
                                <>
                                  <div
                                    className="relative w-[25px] h-[25px] rounded-full overflow-hidden border border-gray-300"
                                    key={index}
                                    style={{ zIndex: item?.task?.task_assignees - index }}
                                  >
                                    <img
                                      src={item?.staff?.staffPersonalInformations.avatar}
                                      alt={`team-member-${index}`}
                                      className="w-full h-full rounded-full"
                                    />
                                  </div>
                                </>
                              ) : (
                                <div
                                  className="relative w-[25px] h-[25px] rounded-full overflow-hidden bg-purple-normal text-white flex justify-center items-center"
                                  key={index}
                                  style={{ zIndex: item?.task?.task_assignees - index }}
                                >
                                  <p>{getNameInitials(item?.staff?.staffPersonalInformations?.first_name, item?.staff?.staffPersonalInformations?.last_name)}</p>
                                </div>
                              )}
                            </>
                          ))}
                        </div>
                        <div>
                          {assigneeModal && item?.task?.id === rowId && (
                            <FilterDropdown className="!top-28">
                              <div className="p-6">
                                <div className="flex justify-end">
                                  <FaXmark className="cursor-pointer" color="#3730A3" size={10} onClick={() => setAssigneeModal(false)} />
                                </div>
                                <div className=" px-2">
                                  <h1 className="flex gap-2 font-poppins-medium  text-purple-dark">
                                    <User
                                      size={12}
                                      className="mt-0.5 text-purple-dark"
                                    />
                                    Assigned members
                                  </h1>
                                  <div className="mt-2 overflow-y-scroll show-scrollbar">
                                    {assignees?.length > 0 ? (
                                      <div className="mt-4">
                                        {assignees.map((assignee, index) => (
                                          <div className="flex justify-between">
                                            <div
                                              key={index}
                                              className="flex mt-2 gap-2 items-center cursor-pointer"
                                            >
                                              {assignee?.avatar ? (
                                                <img
                                                  src={assignee?.avatar}
                                                  alt="Assignee"
                                                  className="w-8 h-8 rounded-full"
                                                />
                                              ) : (
                                                <p className="w-8 h-8 flex justify-center items-center rounded-full bg-purple-normal text-white">
                                                  {getNameInitials(
                                                    assignee.firstName,
                                                    assignee.lastName
                                                  )}
                                                </p>
                                              )}
                                              <p className="text-[10px] text-neutral-normal">
                                                {assignee?.firstName}{" "}
                                                {assignee?.lastName}
                                              </p>
                                            </div>
                                            <div className="flex justify-center items-center mt-2 cursor-pointer" onClick={() => { removeAssignee(assignee) }}>
                                              <FaXmark size={8} color="#3730A3"/>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    ) : (
                                      <span className="text-center text-neutral text-12">
                                        No assigned members found.
                                      </span>
                                    )}
                                  </div>
                                </div>
                                <div className="mt-6 p-2">
                                  <h1 className="flex gap-2 font-poppins-medium  text-purple-dark">
                                    <User
                                      size={12}
                                      className="mt-0.5 text-purple-dark"
                                    />
                                    Board members
                                  </h1>
                                  <div className="mt-2 ">
                                    {teamMembersData.length > 0 ? (
                                      <div className="mt-4">
                                        {teamMembersData.map(
                                          (assignee, index) => (
                                            <div>
                                              <div
                                                key={index}
                                                className="flex mt-2 gap-2 items-center cursor-pointer"
                                                onClick={() => { handleAssignee(assignee) }}
                                              >
                                                {assignee.avatar ? (
                                                  <img
                                                    src={assignee.avatar}
                                                    alt="Assignee"
                                                    className="w-8 h-8 rounded-full"
                                                  />
                                                ) : (
                                                  <p className="w-8 h-8 rounded-full flex justify-center items-center bg-purple-normal text-white">
                                                    {getNameInitials(
                                                      assignee.firstName,
                                                      assignee.lastName
                                                    )}
                                                  </p>
                                                )}
                                                <p className="text-[10px] text-neutral-normal">
                                                  {assignee.firstName}{" "}
                                                  {assignee.lastName}
                                                </p>
                                              </div>
                                            </div>
                                          )
                                        )}
                                      </div>
                                    ) : (
                                      <p className="  text-center"><span className="text-neutral text-12">Nothing to show</span>
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </FilterDropdown>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-between gap-6 mt-4 text-neutral-dark">
                      <div className="flex gap-1 cursor-pointer" onClick={() => { setShowPriorityModal(true); setRowId(item.task.id); setSelectedPriority(item?.task?.priority) }}>
                        <Flag size={14} color="#3730A3" />
                        <p className="text-12 capitalize text-purple-dark font-poppins-medium">{item?.task?.priority}</p>
                      </div>
                      <div className="relative">
                        {showPriorityModal && item?.task?.id === rowId && (
                          <FilterDropdown className="!w-[150px]">
                            <div className="p-4">
                            <div className="flex justify-end">
                              <FaXmark className="cursor-pointer" color="#3730A3" size={10} onClick={() => setShowPriorityModal(false)} />
                            </div>
                              <p className="font-poppins-medium text-12">
                                Change priority
                              </p>
                              
                              {priorityStatus?.map((data, index) => (
                                <div
                                  key={index}
                                  className="flex gap-2 items-center capitalize mt-4"
                                >
                                  <CustomCheckBox
                                    name="priority"
                                    id="priority"
                                    onChange={() => {
                                      setSelectedPriority(data);
                                    }}
                                    checked={selectedPriority === data}
                                  />
                                  <p>{data}</p>
                                </div>
                              ))}
                              <div className="flex justify-end mt-4">
                                <CustomButton
                                  isLoading={updateLoading}
                                  className="!h-6 !w-[100%]"
                                  variant={ButtonProperties.VARIANT.primary.name}
                                  title="Save"
                                  handleClick={() =>
                                    handleTaskPriority(item.task.id)
                                  }
                                />
                              </div>
                            </div>
                          </FilterDropdown>
                        )}
                      </div>
                      {/* <div className="flex gap-1">
                      <ImAttachment size={14}/>
                      <p className="text-12">0</p>
                    </div> */}
                      <div className="flex gap-1">
                        <Clock size={14} />
                        {item?.task?.due_date ? (<p className="text-12">{item?.task?.due_date ? calculateDaysLeft(moment().format("YYYY-MM-DD"), item?.task?.due_date) + ' days left' : null} </p>): null}
                      </div>
                    </div>

                  </div>
                ))}
              </div>
              <div>
                <div className="mt-5">
                  <p className="text-neutral-normal font-poppins-medium text-14 cursor-pointer" onClick={() => setShowAddTaskModal(true)}>+ Add a new task</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <OrganizationEmptyState className="!w-full" />
      )}

      <CustomModal cardClassName="!max-w-[70%]" visibility={showAddTaskModal} toggleVisibility={setShowAddTaskModal}>
        <AddTask onClose={setShowAddTaskModal} />
      </CustomModal>
    </>
  );
}
