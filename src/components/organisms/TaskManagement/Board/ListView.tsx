import { <PERSON>a<PERSON>ye, FaFlag, FaPlus, FaRegFlag, FaX } from "react-icons/fa6";
import { FaEllipsisV } from "react-icons/fa";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, getNameInitials, priorityStatus, removeHtmlTags, truncateText } from "../../../shared/helpers";
import { RiBar<PERSON>hart<PERSON>ill, RiLoader2Line } from "react-icons/ri";
import { PiNotepadBold } from "react-icons/pi";
import { MdOutlineAccessTimeFilled, MdPeopleAlt } from "react-icons/md";
import { LuListFilter } from "react-icons/lu";
import CustomCheckBox from "../../../atoms/CustomCheckBox/CustomCheckBox";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import { useEffect, useState } from "react";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../../shared/hooks";
import { useNavigate } from "react-router-dom";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import "react-quill/dist/quill.snow.css";
import AddTask from "./AddTask";
import { useRecoilValue } from "recoil";
import { getIssuesAtom, getLabelsAtom, getStatusAtom } from "../../../../recoil/atom/TaskManagement";
import { Status, User } from "iconsax-react";
import { BiLabel } from "react-icons/bi";
import { addTaskAssignee, getTeamMemberByProject, removeTaskAssignee, updateTaskLabel, updateTaskPriorityById, updateTaskStatus } from "../../../../api/TaskManagement";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import moment from "moment";
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";

const ListView = () => {
  
  const navigate = useNavigate();
  const [rowId, setRowId] = useState(0);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [showPriorityModal, setShowPriorityModal] = useState<boolean>(false);
  const issuesAtomValue = useRecoilValue(getIssuesAtom);
  const [showStatusModal, setShowStatusModal] = useState<boolean>(false);
  const [showLabelModal, setShowLabelModal] = useState<boolean>(false);
  const [assigneeModal, setAssigneeModal] = useState<boolean>(false);
  const [updateLoading, setUpdateLoading] = useState<boolean>(false);
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [statusId, setStatusId] = useState<string>("");
  const [singleTask, setSingleTask] = useState<any>({});
  const [selectedPriority, setSelectedPriority] = useState<string>("");
  const [selectedLabels, setSelectedLabels] = useState<string[]>([]);
  const statusAtomValue = useRecoilValue(getStatusAtom);
  const labelAtomValue = useRecoilValue(getLabelsAtom);

  const { fetchIssues } = useUpdateRecoilAtom();

  const [assignees, setAssignees] = useState<any>([]);
  const [teamMembersData, setTeamMembersData] = useState<any>([]);

  // const hexToRGBA = (hex: string, alpha: number) => {
  //     const r = parseInt(hex.slice(1, 3), 16)
  //     const g = parseInt(hex.slice(3, 5), 16)
  //     const b = parseInt(hex.slice(5, 7), 16)
  //     return `rgba(${r}, ${g}, ${b}, ${alpha})`
  // }

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setShowStatusModal(false);
    setShowLabelModal(false);
    setAssigneeModal(false);
    setRowId(0);
  });

  const handleTaskStatus = (taskId) => {
    const payload = {
      task_id: taskId,
      status_id: statusId,
    };
    setUpdateLoading(true);
    updateTaskStatus(payload)
      .then((res) => {
        if (res.success) {
          setUpdateLoading(false);
          clustarkToast(NotificationTypes.SUCCESS, res.message);
          fetchIssues();
          setShowDropdown(false);
          setShowStatusModal(false);
          setShowLabelModal(false);
        }
        setUpdateLoading(false);
      })
      .catch(() => {
        setUpdateLoading(false);
      });
  };

  const handleTaskPriority = (taskId) => {
    const payload = {
      task_id: taskId,
      priority: selectedPriority,
    };
    setUpdateLoading(true);
    updateTaskPriorityById(payload)
      .then((res) => {
        if (res.success) {
          setUpdateLoading(false);
          fetchIssues();
          clustarkToast(NotificationTypes.SUCCESS, res.message);
          setShowPriorityModal(false);
        }
        setUpdateLoading(false);
      })
      .catch(() => {
        setUpdateLoading(false);
      });
  };

  const handleSelectedLabels = (e, id) => {
    const isChecked = e.target.checked;
    setSelectedLabels((prevState: any) => {
      if (isChecked) {
        return [...prevState, id];
      } else {
        return prevState.filter((item) => item !== id);
      }
    });
  };

  const handleTaskLabel = (taskId) => {
    const payload = {
      issue_id: taskId,
      labels: [1],
    };
    if (selectedLabels.length > 0) {
      setUpdateLoading(true);
      updateTaskLabel(payload)
        .then((res) => {
          if (res.success) {
            setUpdateLoading(false);
            clustarkToast(NotificationTypes.SUCCESS, res.message);
            fetchIssues();
            setShowDropdown(false);
            setShowStatusModal(false);
            setShowLabelModal(false);
          }
          setUpdateLoading(false);
        })
        .catch(() => {
          setUpdateLoading(false);
        });
    }
  };

  const handleAssignee = (assignee) => {
    setTeamMembersData((prev) => prev.filter((m) => m.id !== assignee.id));
    setAssignees((prev) => [...prev, assignee]);
    handleAddAssignee(assignee);
  };

  const removeAssignee = (assignee) => {
    setAssignees((prev) => prev.filter((a) => a.id !== assignee.id));
    setTeamMembersData((prev) => [...prev, assignee]);
    handleRemoveAssignee(assignee);
  };


  const handleRemoveAssignee = (assignee) => {
    setUpdateLoading(true);
    const payload = {
      issue_id: singleTask?.id,
      assignee: assignee.id
    };
    removeTaskAssignee(payload).then((res) => {
      if (res.success) {
        setUpdateLoading(false)
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchIssues();
      };
      setUpdateLoading(false);
    }).catch(() => setUpdateLoading(false));
  };

  const handleAddAssignee = (assignee) => {
    setUpdateLoading(true);
    const payload = {
      issue_id: singleTask?.id,
      assignee: assignee.id
    };
    addTaskAssignee(payload).then((res) => {
      if (res.success) {
        setUpdateLoading(false)
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchIssues();
      };
      setUpdateLoading(false);
    }).catch(() => setUpdateLoading(false));
  }

  const fetchTaskTeamMembers = () => {
    if (singleTask?.task?.project_id) {
      getTeamMemberByProject(singleTask?.task?.project_id).then((res) => {
        const fetchedMembers = res.data?.map((data) => ({
          firstName: data?.staff?.staffPersonalInformations?.first_name,
          lastName: data?.staff?.staffPersonalInformations?.last_name,
          avatar: data?.staff?.staffPersonalInformations?.avatar,
          id: data?.staff?.staffPersonalInformations?.id,
        })) || [];
        const availableMembers = fetchedMembers.filter(
          (member) => !assignees.some((assignee) => assignee.id === member.id)
        );

        setTeamMembersData(availableMembers);
      });
    }
  };

  useEffect(() => {
    fetchTaskTeamMembers()
  }, [singleTask]);

  const columns = [
    {
      Header: () => (
        <>
          <div className="flex items-center gap-2 ">
            <RiBarChartFill size={18} />
            <p className="text-[0.9rem]">Task Nae</p>
          </div>
        </>
      ),
      accessor: "task.name",
      Cell: (row) => (
        <div>
          <div className=" flex items-center ">
            <div className="flex gap-1 items-center">
              <div className="flex gap-1 items-center  cursor-pointer"
                onClick={() => {
                  navigate("/task-management/view-task", {
                    state: { id: row.cell.row.original.id },
                  });
                }}
              >
                <p className="text-[0.8rem] first-letter:capitalize">
                  {" "}
                  {row.cell.value}
                </p>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      Header: () => (
        <>
          <div className="flex items-center gap-2">
            <PiNotepadBold size={18} />
            <p className="text-[0.9rem]"> Description</p>
          </div>
        </>
      ),
      accessor: "task.description",
      Cell: (row) => (
        <div className="">
          <p className="text-[0.8rem] first-letter:capitalize">
            {" "}
            {truncateText(removeHtmlTags(row.cell.value), 30)}
          </p>
        </div>
      ),
    },
    {
      Header: () => (
        <>
          <div className="flex items-center gap-2">
            <MdPeopleAlt size={18} />
            <p className="text-[0.9rem]">People</p>
          </div>
        </>
      ),
      accessor: "task.task_assignees",
      Cell: (row) => (
        <div>
          <div className="" >
            <div className="relative cursor-pointer">
              <div className="flex -space-x-3" onClick={() => {
                setAssigneeModal(true); setRowId(row.cell.row.original.id); setSingleTask(row.cell.row.original);
                setAssignees(() => row.cell.row.original?.task?.task_assignees?.map((data) => ({
                  firstName: data?.staff?.staffPersonalInformations?.first_name,
                  lastName: data?.staff?.staffPersonalInformations?.last_name,
                  avatar: data?.staff?.staffPersonalInformations?.avatar,
                  id: data?.staff?.staffPersonalInformations?.id,
                })))
              }}>
                {row.cell.value.length > 0 ? (
                  <>
                    {row.cell.value?.slice(0, 4).map((item, index) => (
                      <div key={index}>
                        {item?.staff?.staffPersonalInformations.avatar ? (
                          <>
                            <div
                              className="relative w-[30px] h-[30px] rounded-full overflow-hidden border border-gray-300"
                              style={{ zIndex: row.cell.value.length - index }}
                            >
                              <img
                                src={
                                  item?.staff?.staffPersonalInformations.avatar
                                }
                                alt={`team-member-${index}`}
                                className="w-full h-full rounded-full"
                              />
                            </div>
                          </>
                        ) : (
                          <div
                            className="relative w-[30px] h-[30px] rounded-full overflow-hidden bg-purple-normal text-white flex justify-center items-center"
                            key={index}
                            style={{ zIndex: row.cell.value.length - index }}
                          >
                            <p>
                              {getNameInitials(
                                item?.staff?.staffPersonalInformations
                                  ?.first_name,
                                item?.staff?.staffPersonalInformations
                                  ?.last_name
                              )}
                            </p>
                          </div>
                        )}
                      </div>
                    ))}
                  </>
                ) : (
                  <div className="relative w-[35px] h-[35px] rounded-full overflow-hidden bg-purple-light text-neutral-normal flex justify-center items-center">
                    <p className="text-18">?</p>
                  </div>
                )}
              </div>
              <div>
                {assigneeModal && row.cell.row.original.id === rowId && (
                  <FilterDropdown>
                    <div className="p-6" ref={node}>
                      <div className="flex justify-end cursor-pointer">
                        <FaX size={10} onClick={() => setAssigneeModal(false)} />
                      </div>
                      <div className=" px-2">
                        <h1 className="flex gap-2 font-poppins-medium  text-purple-dark">
                          <User
                            size={14}
                            className="mt-0.5 text-purple-dark"
                          />
                          Assigned members
                        </h1>
                        <div className="mt-2 overflow-y-scroll show-scrollbar">
                          {assignees?.length > 0 ? (
                            <div className="mt-4">
                              {assignees.map((assignee, index) => (
                                <div className="flex justify-between">
                                  <div
                                    key={index}
                                    className="flex mt-2 gap-2 items-center cursor-pointer"
                                  >
                                    {assignee?.avatar ? (
                                      <img
                                        src={assignee?.avatar}
                                        alt="Assignee"
                                        className="w-10 h-10 rounded-full"
                                      />
                                    ) : (
                                      <p className="w-10 h-10 flex justify-center items-center rounded-full bg-purple-normal text-white">
                                        {getNameInitials(
                                          assignee.firstName,
                                          assignee.lastName
                                        )}
                                      </p>
                                    )}
                                    <p className="text-[12px] text-neutral-normal">
                                      {assignee?.firstName}{" "}
                                      {assignee?.lastName}
                                    </p>
                                  </div>
                                  <div className="flex justify-center items-center mt-2 cursor-pointer" onClick={() => { removeAssignee(assignee) }}>
                                    <FaX size={8} />
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <span className="text-center text-neutral text-12">
                              No assigned members found.
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="mt-6 p-2">
                        <h1 className="flex gap-2 font-poppins-medium  text-purple-dark">
                          <User
                            size={14}
                            className="mt-0.5 text-purple-dark"
                          />
                          Board members
                        </h1>
                        <div className="mt-2 ">
                          {teamMembersData.length > 0 ? (
                            <div className="mt-4">
                              {teamMembersData.map(
                                (assignee, index) => (
                                  <div>
                                    <div
                                      key={index}
                                      className="flex mt-2 gap-2 items-center cursor-pointer"
                                      onClick={() => { handleAssignee(assignee) }}
                                    >
                                      {assignee.avatar ? (
                                        <img
                                          src={assignee.avatar}
                                          alt="Assignee"
                                          className="w-10 h-10 rounded-full"
                                        />
                                      ) : (
                                        <p className="w-10 h-10 rounded-full flex justify-center items-center bg-purple-normal text-white">
                                          {getNameInitials(
                                            assignee.firstName,
                                            assignee.lastName
                                          )}
                                        </p>
                                      )}
                                      <p className="text-[12px] text-neutral-normal">
                                        {assignee.firstName}{" "}
                                        {assignee.lastName}
                                      </p>
                                    </div>
                                  </div>
                                )
                              )}
                            </div>
                          ) : (
                            <p className="  text-center"><span className="text-neutral text-12">Nothing to show</span>
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </FilterDropdown>
                )}
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      Header: () => (
        <>
          <div className="flex items-center gap-2">
            <LuListFilter size={18} />
            <p className="text-[0.9rem]">Team</p>
          </div>
        </>
      ),
      accessor: "task.team.name",
      Cell: (row) => (
        <div className="">
          <p className="text-[0.8rem] first-letter:capitalize">
            {" "}
            {row.cell.value}
          </p>
        </div>
      ),
    },
    {
      Header: () => (
        <>
          <div className="flex items-center gap-2">
            <MdOutlineAccessTimeFilled size={18} />
            <p className="text-[0.9rem]">Timeline Date</p>
          </div>
        </>
      ),
      accessor: "task.due_date",
      Cell: (row) => (
        <div className="">
          <p className="text-[0.8rem]">
            {" "}
            {row.cell.value ? moment(row.cell.value).format("ll") : "--"}
          </p>
        </div>
      ),
    },
    {
      Header: () => (
        <>
          <div className="flex items-center gap-2">
            <FaFlag size={18} />
            <p className="text-[0.9rem]">Priority</p>
          </div>
        </>
      ),
      accessor: "task.priority",
      Cell: (row) => (
        <div className="relative">
          <div className="cursor-pointer border border-gray-300 rounded-lg p-2 text-center flex justify-center w-[120px]" onClick={() => { setShowPriorityModal(true); setRowId(row.cell.row.original.id); setSelectedPriority(row.cell.value) }}>

            <div className="flex gap-2 items-center" >
              <FaRegFlag size={18} />
              <p className="capitalize"> {row.cell.value || "--"}</p>
            </div>
          </div>
          <div>
            {showPriorityModal && row.cell.row.original.id === rowId && (
              <FilterDropdown className="!w-[150px]">
                <div className="p-4" ref={node}>
                  <p className="font-poppins-medium text-16">
                    Change Priority
                  </p>
                  {priorityStatus?.map((data, index) => (
                    <div
                      key={index}
                      className="flex gap-2 items-center capitalize mt-4"
                    >
                      <CustomCheckBox
                        name="priority"
                        id="priority"
                        onChange={() => {
                          setSelectedPriority(data);
                        }}
                        checked={selectedPriority === data}
                      />
                      <p>{data}</p>
                    </div>
                  ))}
                  <div className="flex justify-end mt-4">
                    <CustomButton
                      isLoading={updateLoading}
                      className="!h-10 !w-16"
                      variant={ButtonProperties.VARIANT.primary.name}
                      title="Save"
                      handleClick={() =>
                        handleTaskPriority(row.cell.row.original.task.id)
                      }
                    />
                  </div>
                </div>
              </FilterDropdown>
            )}
          </div>
        </div>
      ),
    },
    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.original.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.original.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li

                  onClick={() => {
                    navigate("/task-management/view-task", {
                      state: { id: row.cell.row.original.id },
                    });
                  }}

                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <FaEye size={18} />
                  View
                </li>
                <li
                  onClick={() => {
                    setShowStatusModal(!showStatusModal);
                    setShowLabelModal(false);
                    setSelectedStatus(
                      row.cell.row.original.task.task_statuses[0].task_status
                        .name
                    );
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Status size={18} />
                  Change status
                </li>
                {showStatusModal && (
                  <FilterDropdown className="!w-[150px]">
                    <div className="p-4">
                      <p className="font-poppins-medium text-16">
                        Set task status
                      </p>
                      {statusAtomValue?.data?.map((data, index) => (
                        <div
                          key={index}
                          className="flex gap-2 items-center capitalize mt-4"
                        >
                          <CustomCheckBox
                            name="status"
                            id="status"
                            onChange={() => {
                              setSelectedStatus(data?.name);
                              setStatusId(data.id);
                            }}
                            checked={selectedStatus == data?.name}
                          />
                          <div className="flex gap-1">
                            <p
                              className="w-4 h-4 rounded-full mt-0.5"
                              style={{ background: data?.color }}
                            ></p>
                            {data?.name}
                          </div>
                        </div>
                      ))}
                      <div className="flex justify-end mt-4">
                        <CustomButton
                          isLoading={updateLoading}
                          className="!h-10 !w-16"
                          variant={ButtonProperties.VARIANT.primary.name}
                          title="Save"
                          handleClick={() =>
                            handleTaskStatus(row.cell.row.original.task.id)
                          }
                        />
                      </div>
                    </div>
                  </FilterDropdown>
                )}
                <li
                  onClick={() => {
                    setShowLabelModal(!showLabelModal);
                    setShowStatusModal(false);
                    setSelectedLabels(
                      row.cell.row.original.task.task_labels?.map(
                        (data) => data.task_label_id
                      )
                    );
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <BiLabel size={18} />
                  Change label
                </li>
                {showLabelModal && (
                  <FilterDropdown className="!w-[150px]">
                    <div className="p-4">
                      <div className="flex flex-col gap-8">
                        <div className="flex flex-col gap-2">
                          <p className="font-poppins-medium">Labels</p>
                        </div>

                        <div>
                          <div className=" rounded-md">
                            {labelAtomValue?.data?.map((data, index) => (
                              <div key={index}>
                                <div className="flex mb-2 gap-2 items-center ">
                                  <div className="flex gap-2 items-center">
                                    <CustomCheckBox
                                      name="label"
                                      id="label"
                                      onChange={(e) => {
                                        handleSelectedLabels(e, data.id);
                                      }}
                                      checked={selectedLabels.some(
                                        (item: any) => item === data.id
                                      )}
                                    />
                                    <div
                                      className="w-4 h-4 rounded-full "
                                      style={{
                                        backgroundColor: data.color,
                                      }}
                                    ></div>
                                    <p className="text-[1rem] capitalize">
                                      {data.name}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                        <div className="flex justify-end">
                          <CustomButton
                            isLoading={updateLoading}
                            className="!h-10 !w-16"
                            variant={ButtonProperties.VARIANT.primary.name}
                            title="Save"
                            handleClick={() =>
                              handleTaskLabel(row.cell.row.original.task.id)
                            }
                          />
                        </div>
                      </div>
                    </div>
                  </FilterDropdown>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  return (
    <>
      <div className="">
        <div className="flex justify-end">
          <div className="">
            <CustomButton
              leftIcon={<FaPlus size={18} />}
              type="submit"
              title="Add task"
              handleClick={() => {
                setShowModal(true);
              }}
              className="!bg-transparent !text-purple-dark !h-[30px] !w-[150px]"
              // isLoading={isLoading}
              size={ButtonProperties.SIZES.small}
              variant={ButtonProperties.VARIANT.primary.name}
            />
          </div>
        </div>
        <div>
          <div className="mt-5">
            <div className="flex justify-between items-center mb-3"></div>
            {issuesAtomValue?.length > 0 ? (
              <>
                {issuesAtomValue?.map((data, index) => (
                  <div className="mt-6" key={index}>
                    <CustomTable
                      data={data.tasks || []}
                      columns={columns}
                      hideSearch
                      tableClass="!shadow"
                      header={
                        <div>
                          <div className="flex justify-between items-center mb-3">
                            <div className="flex gap-4">
                              <div className="flex gap-3">
                                <div
                                  className={`flex gap-3 h-[40px] items-center cursor-pointer rounded p-3 w-[130px] justify-center text-gray-500 border border-[${data?.status?.color}] bg-[${data?.status?.color}] color-[${data?.status?.color}]`}
                                >
                                  <RiLoader2Line
                                    size={10}
                                    color={`${data?.status?.color}`}
                                    style={{ backgroundColor: `${data?.status?.color}`, borderRadius: "50%" }}
                                  />
                                  <p className="capitalize text-[10px]">
                                    {data?.status?.name}
                                  </p>
                                  <div
                                    className={`flex gap-3 h-[8px] items-center border border-gray-400 rounded-full p-3 w-[15px] justify-center bg-gray-100 `}
                                  >
                                    <p style={{ color: data?.status?.color }}>
                                      {data?.tasks?.length}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className="">
                              {/* <div className="">
                                <CustomButton
                                  leftIcon={<FaPlus size={18} />}
                                  type="submit"
                                  title="Add task"
                                  handleClick={() => {
                                    setShowModal(true);
                                  }}
                                  className="!bg-transparent !text-purple-dark !h-[30px] !w-[150px]"
                                  // isLoading={isLoading}
                                  size={ButtonProperties.SIZES.small}
                                  variant={
                                    ButtonProperties.VARIANT.primary.name
                                  }
                                />
                              </div> */}
                            </div>
                          </div>
                        </div>
                      }
                    />
                  </div>
                ))}
              </>
            ) : (
              <OrganizationEmptyState className="!w-full" />
            )}
          </div>
        </div>
      </div>

      <CustomModal
        cardClassName="!max-w-[70%]"
        visibility={showModal}
        toggleVisibility={setShowModal}
      >
        <AddTask onClose={setShowModal} />
      </CustomModal>
    </>
  );
};

export default ListView;
