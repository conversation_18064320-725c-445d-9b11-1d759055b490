// src/components/AddTask.tsx
import React, { useEffect, useRef, useState } from "react";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { Calendar, CloseCircle, Flag, Status, User } from "iconsax-react";
import CustomTextEditor from "../../../atoms/CustomTextEditor";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import CustomCheckBox from "../../../atoms/CustomCheckBox/CustomCheckBox";
import moment from "moment";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, getNameInitials, priorityStatus, validTypes } from "../../../shared/helpers";
import { Form, Formik } from "formik";
import { useRecoilValue } from "recoil";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import SideModal from "../../../atoms/CustomModal/SideModal";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { getLabelsAtom, getProjectAtom, getStatusAtom } from "../../../../recoil/atom/TaskManagement";
import { createIssue, getTeamMemberByProject } from "../../../../api/TaskManagement";
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";
import { FaX } from "react-icons/fa6";

interface AddTaskProps {
  // onAddTask: (task) => void;
  onClose: Function;
}

const AddTask: React.FC<AddTaskProps> = ({ onClose }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [, setDragging] = useState(false);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [openStartDate, setOpenStartDate] = useState<boolean>(false);
  const [openEndDate, setOpenEndDate] = useState<boolean>(false);
  const [showLabelModal, setShowLabelModal] = useState<boolean>(false);
  const [openPriority, setOpenPriority] = useState<boolean>(false);
  const [openStatus, setOpenStatus] = useState<boolean>(false);
  const [startDate, setStartDate] = useState<moment.Moment | null>(null);
  const [endDate, setEndDate] = useState<moment.Moment | null>(null);
  const [showTeamMemberModal, setShowTeamMemberModal] = useState<boolean>(false);
  const [projectId, setProjectId] = useState<string>("");
  const [statusId, setStatusId] = useState<string>("");
  const [assignees, setAssignees] = useState<any>([]);
  const [teamMembersData, setTeamMembersData] = useState<any>([]);
  const [selectedLabels, setSelectedLabels] = useState<any>([]);
  const projectAtomValue = useRecoilValue(getProjectAtom);
  const statusAtomValue = useRecoilValue(getStatusAtom);
  const labelAtomValue = useRecoilValue(getLabelsAtom);
  const { fetchIssues } = useUpdateRecoilAtom();


  const initialState = {
    title: "",
    description: "",
    priority: "",
    startDate: "",
    endDate: "",
    status: "",
    label: "",
  };

  const projects = projectAtomValue?.data?.map((data) => ({
    text: data?.name,
    value: data?.id,
  }));

  const fetchTaskTeamMembers = () => {
    if (projectId) {
      getTeamMemberByProject(projectId).then((res) => {
        const fetchedMembers = res.data?.map((data) => ({
          firstName: data?.staff?.staffPersonalInformations?.first_name,
          lastName: data?.staff?.staffPersonalInformations?.last_name,
          avatar: data?.staff?.staffPersonalInformations?.avatar,
          id: data?.staff?.staffPersonalInformations?.id,
        })) || [];
        const availableMembers = fetchedMembers.filter(
          (member) => !assignees.some((assignee) => assignee.id === member.id)
        );

        setTeamMembersData(availableMembers);
        setAssignees([])
      });
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files ? event.target.files[0] : null;

    if (file) {
      if (validTypes.includes((file.type))) {
        const reader = new FileReader();
        reader.onload = () => {
          setImagePreview(reader.result as string);
        };
        reader.readAsDataURL(file);
        setSelectedImage(file);
      } else {
        const maxSize = 900 * 1024;
        if (file.size > maxSize) {
          clustarkToast(
            NotificationTypes.ERROR,
            "File size must be less than 900kb."
          );
          return;
        }
        clustarkToast(
          NotificationTypes.ERROR,
          "Please select a valid image file (JPEG, JPG, or PNG)."
        );
      }
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      setSelectedImage(files[0]);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const handleSubmit = (values) => {
    setIsLoading(true);
    const assign = assignees.map((data) => (data.id));
    const labels = selectedLabels.map((data) => (data.id));
    const payload = {
      project_id: projectId,
      name: values.title,
      description: values.description,
      priority: values.priority,
      due_date: endDate ? moment(endDate).format("YYYY-MM-DD") : null,
      labels: labels,
      statuses: [statusId],
      assignees: assign,
      // watchers: [1],
      attachment: selectedImage,
    };
    createIssue(payload).then((res) => {
      if (res.success) {
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        onClose(false);
        fetchIssues();
      }
      setIsLoading(false);
    }).catch(() => {
      setIsLoading(false);
    })
  };

  const handleAssignee = (assignee) => {
    setTeamMembersData((prev) => prev.filter((m) => m.id !== assignee.id));
    setAssignees((prev) => [...prev, assignee]);
  };

  const removeAssignee = (assignee) => {
    setAssignees((prev) => prev.filter((a) => a.id !== assignee.id));
    setTeamMembersData((prev) => [...prev, assignee]);
  };

  useEffect(() => {
    fetchTaskTeamMembers();
  }, [projectId]);

  const handleSelectedLabels = (e, data) => {
    const isChecked = e.target.checked;
    setSelectedLabels((prevState: any) => {
      if (isChecked) {
        return [...prevState, data];
      } else {
        return prevState.filter((item) => item.id !== data.id);
      }
    });
  };

  const DetailRow = ({ label, value }) => (
    <div className="flex items-center gap-2">
      <span className="text-gray-500 w-28">{label}:</span>
      <span className="font-medium text-gray-700">{value}</span>
    </div>
  );



  return (
    <div className=" bg-gradient-to-br from-purple-dark to-purple-3s00 p-10 flex items-center justify-center">
      <div className="bg-white shadow-lg rounded-lg w-full h-full">
        <Formik initialValues={initialState} onSubmit={handleSubmit}>
          {({ values, setFieldValue }) => (
            <Form>
              <div className="flex justify-between items-center px-6 py-4 border-b">
                <div className="flex gap-2">
                  <div className=" rounded-full">
                    <FormikCustomSelect
                      parentContainer="!h-10"
                      name="project"
                      options={projects}
                      placeholder="Select project"
                      onChange={(data) => {
                        setFieldValue("project", data.value);
                        setProjectId(data.value);
                      }}
                      required
                    />
                  </div>

                </div>
              </div>
              <div className="flex p-6 gap-6">
                <div className="flex-1 space-y-4">
                  <FormikCustomInput
                    name="title"
                    placeholder="Title"
                    container="w-full !border-b"
                    inputClassName="w-full focus:outline-none !border-none"
                    required
                  />
                  <div>
                    <CustomTextEditor
                      value={values.description}
                      onChange={(value) => setFieldValue("description", value)}
                      placeholder="Pro tip: Hit 'Enter' for a new paragraph, and 'Shift + Enter' for a simple line break."
                    />
                  </div>
                  <div>
                    <div className="mt-6">
                      <input
                        ref={fileInputRef}
                        type="file"
                        className="hidden"
                        onChange={handleImageUpload}
                      />

                      <p className="mb-4">Attachment upload(optional)</p>
                      <div className="border-2 border-dashed bg-[#B2BBC6] bg-opacity-10 h-[50px] rounded mt-4 flex justify-center items-center">
                        <div
                          onDragOver={(e) => {
                            e.preventDefault();
                            setDragging(true);
                          }}
                          onDragLeave={() => setDragging(false)}
                          onDrop={handleDrop}
                        >
                          <p
                            onClick={triggerFileInput}
                            className="text-neutral-normal text-center mt-5 mb-5"
                          >
                            <span className="font-poppins-medium text-purple-dark cursor-pointer">
                              Click here
                            </span>
                            &nbsp; or Drag and Drop file to upload
                          </p>
                        </div>
                      </div>
                      {imagePreview && (
                        <div className="mt-10">
                          <div className="w-[200px] h-[100px] border p-4 ">
                            <img
                              src={imagePreview || ""}
                              width={200}
                              className="h-[100px] w-[200px] object-cover"
                            />
                            <p className="text-purple-normal mt-1">
                              {selectedImage && selectedImage.name}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="w-80 space-y-3 mb-5">
                  <div className="text-sm text-gray-700 space-y-4 mt-5">
                    <DetailRow
                      label="Assignee"
                      value={
                        <div className=" text-14">
                          <div
                            className="cursor-pointer flex gap-1"
                            onClick={() =>
                              setShowTeamMemberModal(true)
                            }
                          >
                            <div className="flex">
                              {assignees.length > 0 ? (
                                <>
                                  {assignees[0]?.avatar ? (
                                    <img
                                      src={assignees[0]?.avatar}
                                      alt="Assignee"
                                      className="w-10 h-10 rounded-full"
                                    />
                                  ) : (
                                    <p className="w-10 h-10 flex justify-center items-center rounded-full bg-purple-normal text-white">
                                      {getNameInitials(
                                        assignees[0].firstName,
                                        assignees[0].lastName
                                      )}
                                    </p>
                                  )}
                                  {assignees.length > 1 && (
                                    <p className="flex justify-center items-center ml-1">
                                      {" "}
                                      & {assignees.length - 1} others
                                    </p>
                                  )}
                                </>
                              ) : (
                                <p>--</p>
                              )}
                            </div>
                          </div>
                          {showTeamMemberModal && (
                            <div>
                              <SideModal isShowModal={showTeamMemberModal}>
                                <div className="w-[200px]">
                                  <div className="flex justify-end p-4">
                                    <CloseCircle
                                      className="cursor-pointer"
                                      onClick={() =>
                                        setShowTeamMemberModal(false)
                                      }
                                    />
                                  </div>
                                  <div className=" px-2">
                                    <h1 className="flex gap-2 font-poppins-medium  text-purple-dark">
                                      <User
                                        size={14}
                                        className="mt-0.5 text-purple-dark"
                                      />
                                      Assigned members
                                    </h1>
                                    <div className="mt-2  overflow-y-scroll show-scrollbar">
                                      {assignees.length > 0 ? (
                                        <div className="mt-4">
                                          {assignees.map((assignee, index) => (
                                            <div className="flex justify-between">
                                              <div
                                                key={index}
                                                className="flex mt-2 gap-2 items-center cursor-pointer"
                                              >
                                                {assignee?.avatar ? (
                                                  <img
                                                    src={assignee?.avatar}
                                                    alt="Assignee"
                                                    className="w-10 h-10 rounded-full"
                                                  />
                                                ) : (
                                                  <p className="w-10 h-10 flex justify-center items-center rounded-full bg-purple-normal text-white">
                                                    {getNameInitials(
                                                      assignee.firstName,
                                                      assignee.lastName
                                                    )}
                                                  </p>
                                                )}
                                                <p className="text-[12px] text-neutral-normal">
                                                  {assignee?.firstName}{" "}
                                                  {assignee?.lastName}
                                                </p>
                                              </div>
                                              <div className="flex justify-center items-center mt-2 cursor-pointer">
                                                <FaX onClick={() => { removeAssignee(assignee) }} size={8} />
                                              </div>
                                            </div>
                                          ))}
                                        </div>
                                      ) : (
                                        <span className="text-center text-neutral text-12">
                                          No assigned members found.
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                  <div className="mt-6 p-2">
                                    <h1 className="flex gap-2 font-poppins-medium  text-purple-dark">
                                      <User
                                        size={14}
                                        className="mt-0.5 text-purple-dark"
                                      />
                                      Board members
                                    </h1>
                                    <div className="mt-2 ">
                                      {teamMembersData.length > 0 ? (
                                        <div className="mt-4">
                                          {teamMembersData.map(
                                            (assignee, index) => (
                                              <div>
                                                <div
                                                  key={index}
                                                  className="flex mt-2 gap-2 items-center cursor-pointer"
                                                  onClick={() => { handleAssignee(assignee) }}
                                                >
                                                  {assignee.avatar ? (
                                                    <img
                                                      src={assignee.avatar}
                                                      alt="Assignee"
                                                      className="w-10 h-10 rounded-full"
                                                    />
                                                  ) : (
                                                    <p className="w-10 h-10 rounded-full flex justify-center items-center bg-purple-normal text-white">
                                                      {getNameInitials(
                                                        assignee.firstName,
                                                        assignee.lastName
                                                      )}
                                                    </p>
                                                  )}
                                                  <p className="text-[12px] text-neutral-normal">
                                                    {assignee.firstName}{" "}
                                                    {assignee.lastName}
                                                  </p>
                                                </div>
                                              </div>
                                            )
                                          )}
                                        </div>
                                      ) : (
                                        <p className="  text-center"><span className="text-neutral text-12">{projectId ? "Nothing to show" : "choose a project to view board members"}</span>
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </SideModal>
                            </div>
                          )}
                        </div>
                      }
                    />
                    <DetailRow
                      label="Status"
                      value={
                        <div>
                          <div
                            className="flex gap-1 cursor-pointer"
                            onClick={() => setOpenStatus(true)}
                          >
                            <Status size={14} />
                            <p className="capitalize">
                              {values.status || "--"}
                            </p>
                          </div>

                          <SideModal isShowModal={openStatus}>
                            <div className="w-[200px]">
                              <div className="flex justify-end p-4">
                                <CloseCircle
                                  className="cursor-pointer"
                                  onClick={() => setOpenStatus(false)}
                                />
                              </div>
                              <div>
                                <p className="font-poppins-medium text-16 text-purple-dark">
                                  Update status
                                </p>
                                {statusAtomValue?.data?.map((data, index) => (
                                  <div
                                    key={index}
                                    className="flex gap-2 items-center capitalize mt-4"
                                  >
                                    <CustomCheckBox
                                      name="status"
                                      id="status"
                                      onChange={() => {
                                        setFieldValue("status", data?.name);
                                        setStatusId(data.id)
                                        setOpenStatus(false)
                                      }}
                                      checked={
                                        values.status == data?.name && true
                                      }
                                    />
                                    <div className="flex gap-1">
                                      <p
                                        className="w-4 h-4 rounded-full mt-0.5"
                                        style={{ background: data?.color }}
                                      ></p>{" "}
                                      {data?.name}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </SideModal>
                        </div>
                      }
                    />
                    <DetailRow
                      label="Priority"
                      value={
                        <div>
                          <p
                            className="flex gap-1 cursor-pointer capitalize"
                            onClick={() => setOpenPriority(true)}
                          >
                            <Flag size={14} />
                            {values.priority || "--"}
                          </p>
                          <SideModal isShowModal={openPriority}>
                            <div className="w-[200px]">
                              <div className="flex justify-end p-4">
                                <CloseCircle
                                  className="cursor-pointer"
                                  onClick={() => setOpenPriority(false)}
                                />
                              </div>
                              <div>
                                <p className="font-poppins-medium text-16 text-purple-dark">
                                  Priority
                                </p>
                                {priorityStatus.map((data, index) => (
                                  <div
                                    key={index}
                                    className="flex gap-2 items-center mt-4 capitalize"
                                  >
                                    <CustomCheckBox
                                      name="priority"
                                      id="priority"
                                      onChange={() => {
                                        setFieldValue("priority", data);
                                        setOpenPriority(false)
                                      }}
                                      checked={values.priority == data && true}
                                    />
                                    <p>{data}</p>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </SideModal>
                        </div>
                      }
                    />

                    <DetailRow
                      label="Start date"
                      value={
                        <div>
                          <p
                            className="flex gap-1 cursor-pointer"
                            onClick={() => setOpenStartDate(true)}
                          >
                            <Calendar size={14} />
                            {startDate
                              ? moment(startDate || "").format("MMM DD, YYYY")
                              : "--"}
                          </p>

                          <SideModal isShowModal={openStartDate}>
                            <div className="w-[330px]">
                              <div className="flex justify-end p-4">
                                <CloseCircle
                                  className="cursor-pointer"
                                  onClick={() => setOpenStartDate(false)}
                                />
                              </div>
                              <label className="text-purple-dark font-puppins-medium">Choose a start date</label> <br />
                              <div className="mt-2">
                                <FormikCustomDate
                                  name="startDate"
                                  value={startDate}
                                  onChange={(newValue) => {
                                    setStartDate(newValue);
                                    setOpenStartDate(false);
                                  }}
                                  height="30px"
                                  textSize="11px"
                                />
                              </div>
                            </div>
                          </SideModal>
                        </div>
                      }
                    />
                    <DetailRow
                      label="Deadline"
                      value={
                        <div>
                          <p
                            className="flex gap-1 cursor-pointer"
                            onClick={() => setOpenEndDate(true)}
                          >
                            <Calendar size={14} />
                            {endDate
                              ? moment(endDate || "").format("MMM DD, YYYY")
                              : "--"}
                          </p>
                          <SideModal isShowModal={openEndDate}>
                            <div className="w-[330px]">
                              <div className="flex justify-end p-4">
                                <CloseCircle
                                  className="cursor-pointer"
                                  onClick={() => setOpenEndDate(false)}
                                />
                              </div>
                              <label className="text-purple-dark">Choose an end date</label> <br />
                              <div className="mt-2">
                                <FormikCustomDate
                                  value={endDate}
                                  name="endDate"
                                  onChange={(newValue) => {
                                    setEndDate(newValue);
                                    setOpenEndDate(false);
                                  }}
                                  height="30px"
                                  textSize="11px"
                                />
                              </div>
                            </div>
                          </SideModal>
                        </div>
                      }
                    />

                    <DetailRow
                      label="Labels"
                      value={
                        <div>
                          <div className="flex gap-2">

                            {selectedLabels.length > 0 && (
                              <p className="flex justify-center items-center ml-1">
                                <span className="capitalize">{selectedLabels[0].name}</span> {" "}
                                &nbsp; {selectedLabels.length > 1 && (<span> & {selectedLabels.length - 1} others</span>)}
                              </p>
                            )}<br />
                            <p
                              onClick={() => setShowLabelModal(true)}
                              className="text-purple-dark cursor-pointer text-xs hover:underline"
                            >
                              + Add label
                            </p>
                          </div>
                          <SideModal isShowModal={showLabelModal}>
                            <div className="w-[200px]">
                              <div className="flex justify-end p-4">
                                <CloseCircle
                                  className="cursor-pointer"
                                  onClick={() => setShowLabelModal(false)}
                                />
                              </div>
                              <div className="p-4">
                                <div className="flex flex-col gap-8">
                                  <div className="flex flex-col gap-2">
                                    <p className="font-poppins-medium">
                                      Labels
                                    </p>

                                  </div>

                                  <div>
                                    <div className=" rounded-md">
                                      {labelAtomValue?.data?.map(
                                        (data, index) => (
                                          <div key={index}>
                                            <div className="flex mb-2 gap-2 items-center ">
                                              <div className="flex gap-2 items-center">
                                                <CustomCheckBox
                                                  name="label"
                                                  id="label"
                                                  onChange={(e) => {
                                                    setFieldValue(
                                                      "label",
                                                      data.name
                                                    );
                                                    handleSelectedLabels(e, data)
                                                  }}
                                                  checked={selectedLabels.some((item: any) => item.id === data.id)}
                                                />
                                                <div
                                                  className="w-4 h-4 rounded-full "
                                                  style={{
                                                    backgroundColor: data.color,
                                                  }}
                                                ></div>
                                                <p className="text-[1rem] capitalize">
                                                  {data.name}
                                                </p>
                                              </div>
                                            </div>
                                          </div>
                                        )
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </SideModal>
                        </div>
                      }
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end items-center gap-2 px-6 py-4 border-t">
                <CustomButton
                  title="Discard"
                  handleClick={() => {
                    onClose(false);
                  }}
                  variant={ButtonProperties.VARIANT.primary.name}
                  className="!w-[100px]"
                  isTransparent
                />
                <CustomButton
                  title="Save"
                  handleClick={() => { }}
                  variant={ButtonProperties.VARIANT.primary.name}
                  className="!w-[100px]"
                  isLoading={isLoading}
                />
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default AddTask;
