// src/components/AddTask.tsx
import React, { useEffect, useState } from "react";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { Calendar, CloseCircle, Flag, } from "iconsax-react";
import CustomTextEditor from "../../../atoms/CustomTextEditor";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import CustomCheckBox from "../../../atoms/CustomCheckBox/CustomCheckBox";
import moment from "moment";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, getNameInitials, } from "../../../shared/helpers";
import { Form, Formik } from "formik";
import { useRecoilValue } from "recoil";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import SideModal from "../../../atoms/CustomModal/SideModal";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { getProjectAtom, getSingleTaskAtom, } from "../../../../recoil/atom/TaskManagement";
import { getTeamMemberByProject, updateIssue } from "../../../../api/TaskManagement";

interface AddTaskProps {
    onClose: Function;
}

const UpdateTask: React.FC<AddTaskProps> = ({ onClose }) => {
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [openEndDate, setOpenEndDate] = useState<boolean>(false);
    const [openPriority, setOpenPriority] = useState<boolean>(false);
    const [projectId, setProjectId] = useState<string>("");
    const [teamMembers, setTeamMembers] = useState<any>([]);
    const projectAtomValue = useRecoilValue(getProjectAtom);
    const getSingleTaskValue = useRecoilValue(getSingleTaskAtom);
    const [endDate, setEndDate] = useState<moment.Moment | null>(null);

    const initialState = {
        title: getSingleTaskValue?.title || '',
        description: getSingleTaskValue?.task?.description || '',
        priority: getSingleTaskValue?.task?.priority || '',
        endDate:  '',
    };

    const projects = projectAtomValue?.data?.map((data) => ({
        text: data?.name,
        value: data?.id,
    }));

    const handleSubmit = (values) => {

        setIsLoading(true)
        updateIssue({
            issue_id: getSingleTaskValue?.id,
            name: values.title,
            description: values.description,
            priority: values.priority,
            due_date: moment(endDate).format("YYYY-MM-DD"),
        }).then((res) => {
            if (res.success) {
                setIsLoading(false)
                onClose(false)
                clustarkToast(NotificationTypes.SUCCESS, res.message)
            }
        }).catch(() => {
            setIsLoading(false)
            // onClose(false)
        })

    };

    const fetchTaskTeamMembers = () => {
        if (projectId) {
            getTeamMemberByProject(projectId).then((res) => {
                setTeamMembers(res.data);
            });
        }
    };

    const priorityStatus = ["low", "medium", "high", "critical"];

    useEffect(() => {
        fetchTaskTeamMembers();
    }, [projectId]);

    const DetailRow = ({ label, value }) => (
        <div className="flex items-center gap-2">
            <span className="text-gray-500 w-28">{label}:</span>
            <span className="font-medium text-gray-700">{value}</span>
        </div>
    );

    return (
        <div className=" bg-gradient-to-br from-purple-100 to-purple-3s00 p-10 flex items-center justify-center">
            <div className="bg-white shadow-lg rounded-lg w-full h-full">
                <Formik initialValues={initialState} onSubmit={handleSubmit}>
                    {({ values, setFieldValue }) => (
                        <Form>
                            <div className="flex justify-between items-center px-6 py-4 border-b">
                                <div className="flex gap-2">
                                    <div className=" rounded-full">
                                        <FormikCustomSelect
                                            parentContainer="!h-10"
                                            name="project"
                                            options={projects}
                                            placeholder="Select project"
                                            onChange={(data) => {
                                                setFieldValue("project", data.value);
                                                setProjectId(data.value);
                                            }}
                                            value={getSingleTaskValue?.task?.project?.name}
                                            required
                                            disabled
                                        />
                                    </div>

                                </div>
                            </div>

                            <div className="flex p-6 gap-6">
                                <div className="flex-1 space-y-4">
                                    <FormikCustomInput
                                        name="title"
                                        placeholder="Title"
                                        container="w-full !border-b"
                                        inputClassName="w-full focus:outline-none !border-none"
                                        value={values.title}
                                        required
                                    />
                                    <div>
                                        <CustomTextEditor
                                            value={values.description}
                                            onChange={(value) => setFieldValue("description", value)}
                                            placeholder="Pro tip: Hit 'Enter' for a new paragraph, and 'Shift + Enter' for a simple line break."
                                        />
                                    </div>

                                    {/* <div>
                                        <div className="mt-6">
                                            <input
                                                ref={fileInputRef}
                                                type="file"
                                                className="hidden"
                                                onChange={handleImageUpload}
                                            />

                                            <p className="mb-4">Attachment upload(optional)</p>
                                            <div className="border-2 border-dashed bg-[#B2BBC6] bg-opacity-10 h-[50px] rounded mt-4 flex justify-center items-center">
                                                <div
                                                    onDragOver={(e) => {
                                                        e.preventDefault();
                                                        setDragging(true);
                                                    }}
                                                    onDragLeave={() => setDragging(false)}
                                                    onDrop={handleDrop}
                                                >
                                                    <p
                                                        onClick={triggerFileInput}
                                                        className="text-neutral-normal text-center mt-5"
                                                    >
                                                        <span className="font-poppins-medium text-purple-normal cursor-pointer">
                                                            Click here
                                                        </span>
                                                        &nbsp; or Drag and Drop file to upload
                                                    </p>
                                                </div>
                                            </div>
                                            {imagePreview && (
                                                <div className="mt-10">
                                                    <div className="w-[200px] h-[100px] border p-4 ">
                                                        <img
                                                            src={imagePreview || ""}
                                                            width={200}
                                                            className="h-[100px] w-[200px] object-cover"
                                                        />
                                                        <p className="text-purple-normal mt-1">
                                                            {selectedImage && selectedImage.name}
                                                        </p>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div> */}

                                </div>

                                <div className="w-80 space-y-3">
                                    <h3 className="text-sm font-semibold text-gray-600">
                                        Details
                                    </h3>
                                    <div className="text-sm text-gray-700 space-y-4">
                                        <DetailRow
                                            label="Assignee"
                                            value={

                                                <div className="flex -space-x-3">
                                                    {getSingleTaskValue?.task?.task_assignees.map(({ staff }, index) => (
                                                        <div
                                                            className="relative w-[25px] h-[25px] rounded-full overflow-hidden border border-gray-300 bg-purple-light-active flex items-center justify-center"
                                                            key={index}
                                                            style={{ zIndex: getSingleTaskValue?.task?.task_assignees?.length - index }}
                                                        >
                                                            {
                                                                staff?.staffPersonalInformations?.avatar ?
                                                                    <img
                                                                        src={staff?.staffPersonalInformations?.avatar}
                                                                        alt={`team-member-${index}`}
                                                                        className="w-full h-full rounded-full"
                                                                    />
                                                                    :
                                                                    <div className="flex items-center justify-center w-full h-full">
                                                                        <p className="text-white">
                                                                            {getNameInitials(
                                                                                staff?.staffPersonalInformations?.first_name
                                                                            )}
                                                                        </p>
                                                                        <p className="text-white">
                                                                            {getNameInitials(
                                                                                staff?.staffPersonalInformations?.last_name
                                                                            )}
                                                                        </p>
                                                                    </div>
                                                            }

                                                        </div>
                                                    ))}
                                                </div>

                                            }
                                        />
                                        <DetailRow
                                            label="Status"
                                            value={
                                                <div className="flex -space-x-3">
                                                    {getSingleTaskValue?.task?.task_statuses?.map(({ task_status, }, index) => (
                                                        <div
                                                            className="relative w-[25px] h-[25px]  overflow-hidden  flex items-center justify-center"
                                                            key={index}
                                                            style={{ color: task_status.color }}
                                                        >
                                                            <p>{task_status.name}</p>

                                                        </div>
                                                    ))}
                                                </div>
                                            }
                                        />
                                        <DetailRow
                                            label="Priority"
                                            value={
                                                <div>
                                                    <p
                                                        className="flex gap-1 cursor-pointer capitalize"
                                                        onClick={() => setOpenPriority(true)}
                                                    >
                                                        <Flag size={14} />
                                                        {values.priority || "--"}
                                                    </p>
                                                    <SideModal isShowModal={openPriority}>
                                                        <div className="w-[200px]">
                                                            <div className="flex justify-end p-4">
                                                                <CloseCircle
                                                                    className="cursor-pointer"
                                                                    onClick={() => setOpenPriority(false)}
                                                                />
                                                            </div>
                                                            <div>
                                                                <p className="font-poppins-medium text-16">
                                                                    Set task priority
                                                                </p>
                                                                {priorityStatus.map((data, index) => (
                                                                    <div
                                                                        key={index}
                                                                        className="flex gap-2 items-center mt-4 capitalize"
                                                                    >
                                                                        <CustomCheckBox
                                                                            name="priority"
                                                                            id="priority"
                                                                            onChange={() => {
                                                                                setFieldValue("priority", data);
                                                                            }}
                                                                            checked={values.priority == data && true}
                                                                        />
                                                                        <p>{data}</p>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        </div>
                                                    </SideModal>
                                                </div>
                                            }
                                        />

                                        {/* <DetailRow
                                            label="Start date"
                                            value={
                                                <div>
                                                    <p
                                                        className="flex gap-1 cursor-pointer"
                                                        onClick={() => setOpenStartDate(true)}
                                                    >
                                                        <Calendar size={14} />
                                                        {startDate
                                                            ? moment(startDate || "").format("MMM DD, YYYY")
                                                            : "--"}
                                                    </p>

                                                    <SideModal isShowModal={openStartDate}>
                                                        <div className="w-[330px]">
                                                            <div className="flex justify-end p-4">
                                                                <CloseCircle
                                                                    className="cursor-pointer"
                                                                    onClick={() => setOpenStartDate(false)}
                                                                />
                                                            </div>
                                                            <label>Choose a start date</label> <br />
                                                            <div className="mt-2">
                                                                <FormikCustomDate
                                                                    name="startDate"
                                                                    value={startDate}
                                                                    onChange={(newValue) => {
                                                                        setStartDate(newValue);
                                                                        setOpenStartDate(false);
                                                                    }}
                                                                    height="30px"
                                                                    textSize="11px"
                                                                />
                                                            </div>
                                                        </div>
                                                    </SideModal>
                                                </div>
                                            }
                                        /> */}

                                        <DetailRow
                                            label="Deadline"
                                            value={
                                                <div>
                                                    <p
                                                        className="flex gap-1 cursor-pointer"
                                                        onClick={() => setOpenEndDate(true)}
                                                    >
                                                        <Calendar size={14} />
                                                        {endDate
                                                            ? moment(endDate || '').format("MMM DD, YYYY")
                                                            : getSingleTaskValue?.task?.due_date}
                                                    </p>
                                                    <SideModal isShowModal={openEndDate}>
                                                        <div className="w-[330px]">
                                                            <div className="flex justify-end p-4">
                                                                <CloseCircle
                                                                    className="cursor-pointer"
                                                                    onClick={() => setOpenEndDate(false)}
                                                                />
                                                            </div>
                                                            <label>Choose an end date</label> <br />
                                                            <div className="mt-2">
                                                                <FormikCustomDate
                                                                    value={endDate}
                                                                    name="endDate"
                                                                    onChange={(newValue) => {
                                                                        setEndDate(newValue);
                                                                        setOpenEndDate(false);
                                                                    }}
                                                                    height="30px"
                                                                    textSize="11px"
                                                                />
                                                            </div>
                                                        </div>
                                                    </SideModal>
                                                </div>
                                            }
                                        />

                                        <DetailRow
                                            label="Labels"
                                            value={
                                                <div className="flex -space-x-3">
                                                    {getSingleTaskValue?.task?.task_labels?.map(({ task_label, }, index) => (
                                                        <div
                                                            className="relative w-[25px] h-[25px]  overflow-hidden  flex items-center justify-center"
                                                            key={index}
                                                            style={{ color: task_label.color }}
                                                        >
                                                            <p>{task_label.name}</p>

                                                        </div>
                                                    ))}
                                                </div>

                                            }
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="flex justify-end items-center gap-2 px-6 py-4 border-t">
                                <CustomButton
                                    title="Discard"
                                    handleClick={() => {
                                        onClose(false);
                                    }}
                                    variant={ButtonProperties.VARIANT.primary.name}
                                    className="!w-[100px]"
                                    isTransparent
                                />
                                <CustomButton
                                    title="Save"
                                    handleClick={() => { }}
                                    variant={ButtonProperties.VARIANT.primary.name}
                                    className="!w-[100px]"
                                    isLoading={isLoading}
                                />
                            </div>
                        </Form>
                    )}
                </Formik>
            </div>
        </div>
    );
};

export default UpdateTask;
