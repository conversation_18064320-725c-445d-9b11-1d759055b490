import React, { useEffect, useState } from "react";
import { CiGrid2V } from "react-icons/ci";
import { FaListUl } from "react-icons/fa6";
import ListView from "./ListView";
import BoardView from "./BoardView";
import { useNavigate, useSearchParams } from "react-router-dom";
import { getIssues, getLabels, getProjects, getStatus } from "../../../../api/TaskManagement";
import { useRecoilState } from "recoil";
import { getIssuesAtom, getLabelsAtom, getProjectAtom, getStatusAtom } from "../../../../recoil/atom/TaskManagement";
import Loader from "../../../atoms/Loader";


const Board = () => {
  const navigate = useNavigate();
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("activeTab");
  const [activeTab, setActiveTab] = useState<number>(0);
  const [isFetching, setIsFetching] = useState<boolean>(true);
  const [, setProjectAtom] = useRecoilState(getProjectAtom);
  const [, setStatusAtom] = useRecoilState(getStatusAtom);
  const [, setLabelAtom] = useRecoilState(getLabelsAtom);
  const [, setIssuesAtom] = useRecoilState(getIssuesAtom);
  


  const fetchProjects = () => {
    getProjects().then((res) => {
      setProjectAtom(res.data);
    });
  };

  const fetchStatus = () => {
    getStatus().then((res) => {
      setStatusAtom(res.data);
    });
  };

  const fetchLabel = () => {
    getLabels().then((res) => {
      setLabelAtom(res.data);
    });
  };

  const fetchIssues = () => {
    getIssues().then((res) => {
      if(res.success) {
        setIssuesAtom(res.data);
        setIsFetching(false);
      }
    })
  };

  useEffect(() => {
    fetchProjects();
    fetchStatus();
    fetchLabel();
    fetchIssues();
  }, []);



  const boardSwitch = [
    {
      name: "List",
      icon: <FaListUl size={16} />,
      link: "/task-management/task",
      component: <ListView />,
    },
    {
      name: "Board",
      icon: <CiGrid2V size={16} />,
      link: "/task-management/project-board",
      component: <BoardView />,
    },
  ];

  useEffect(() => {
    setActiveTab(queryParam || 0)
  }, [queryParam, activeTab]);

  if(isFetching) {
    return (<Loader/>)
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center border border-gray-300 py-3 px-3 rounded h-[45px] w-[200px] justify-around bg-gray-200  ">
          {boardSwitch.map(({ name, icon }, index) => (
            <div
              key={index}
              className={`flex gap-2 items-center cursor-pointer ${activeTab == index && "bg-white text-purple-dark h-[30px]"} rounded py-2 px-4 `}
              onClick={() => { setActiveTab(index); navigate(`/task-management/project-board?activeTab=${index}&tab=${name}`);}}
            >
              <div className="">{icon}</div>
              <p className="text-[1rem]">{name}</p>
            </div>
          ))}
        </div>
      </div>
      {boardSwitch[activeTab].component}
    </div>
  );
};

export default Board;
