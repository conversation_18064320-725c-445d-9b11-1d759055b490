
import { FiSend } from "react-icons/fi";
import { MdAttachment } from "react-icons/md";
import { useEffect, useRef, useState } from "react";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import CustomCheckBox from "../../../atoms/CustomCheckBox/CustomCheckBox";
import { colorsPallette } from "../../../atoms/CustomColors";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages, getNameInitials, removeHtmlTags } from "../../../shared/helpers";
import { Form, Formik } from "formik";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import * as yup from "yup";
import { addLabels, addTaskAssignee, assignSubtaskById, createSubTask, deleteSubtaskById, getSingleIssue, getSubtaskById, getTaskComment, getTeamMemberByProject, removeTaskAssignee, sendComment, updateSubtask, updateTaskLabelById, updateTaskPriorityById, updateTaskStatusById, } from "../../../../api/TaskManagement";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { useRecoilState, useRecoilValue } from "recoil";
import { getIssuesByIdAtom, getLabelsAtom, getStatusAtom, getSubTaskAtom, getTaskCommentAtom, getTaskTeamByProjectAtom } from "../../../../recoil/atom/TaskManagement";
import CustomQuillTextArea from "../../../atoms/CustomQillTextArea"
import { FaPlus, FaSpinner, FaTrash, FaX, FaXmark } from "react-icons/fa6";
import { useLocation } from "react-router-dom";
import Loader from "../../../atoms/Loader";
import moment from "moment";
import { Flag, Status, User } from "iconsax-react";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../../shared/hooks";
import { BiLabel } from "react-icons/bi";
import GoBack from "../../../atoms/Ui/GoBack";

const ViewTask = () => {

    const location = useLocation();
    const { id } = location?.state || "";

    const [openLable, setOpenLabel] = useState(false);
    const [message, setmessage] = useState("");
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isUpdating, setIsUpdating] = useState<boolean>(false);
    const [isSending, setIsSending] = useState<boolean>(false);
    const [labelColor, setLabelColor] = useState('');
    const [isOpen, setIsOpen] = useState(false);
    const [openStatus, setOpenStatus] = useState(false);
    const [openPriority, setOpenPriority] = useState(false);
    const [openIssue, setOpenIssue] = useState(false);
    const [openAssign, setOpenAssign] = useState(false);
    const [assigneeModal, setAssigneeModal] = useState(false);
    const [openNewAssign, setOpenNewAssign] = useState(false);
    const [openAllActivities, setOpenAllActivities] = useState(false);
    const [isAssigning, setIsAssigning] = useState(false);

    const [selectedImage, setSelectedImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);

    const [assignId, setAssigneeId] = useState("");
    const [SubTaskId, setSubTaskId] = useState("");
    const [SelectedStatus, setSelectedStatus] = useState("");
    const [statusId, setStatusId] = useState("");
    const [SelectedPriority, setSelectedPriority] = useState("");

    // const [selectedSubTask, setSelectedSubTask] = useState<string | null>(null);

    const [selectedAssignee, setSelectedAssignee] = useState<string | null>(null);
    const [selectedLabels, setSelectedLabels] = useState<string[]>([]);
    const fileInputRef = useRef<HTMLInputElement | null>(null);

    const [, setLabelsAtom] = useRecoilState(getLabelsAtom);
    const labelAtomValue = useRecoilValue(getLabelsAtom);

    const [, setTeamMemberAtom] = useRecoilState(getTaskTeamByProjectAtom);
    const getTeamMemberValue = useRecoilValue(getTaskTeamByProjectAtom);

    const getStatusValue = useRecoilValue(getStatusAtom);

    const [, setTaskAtom] = useRecoilState(getIssuesByIdAtom);
    const getTaskValue = useRecoilValue(getIssuesByIdAtom);

    const [, setSubTaskAtom] = useRecoilState(getSubTaskAtom);
    const getSubTaskValue = useRecoilValue(getSubTaskAtom);

    const [, setTaskCommentAtom] = useRecoilState(getTaskCommentAtom);
    const getTaskCommentValue = useRecoilValue(getTaskCommentAtom);

    const node = useClickOutside(() => {
        setAssigneeModal(false);
        setOpenPriority(false);
        setOpenStatus(false);
        setOpenLabel(false);
    });



    const [assignees, setAssignees] = useState<any>(() => getTaskValue?.task_assignees?.map((data) => ({
        firstName: data?.staff?.staffPersonalInformations?.first_name,
        lastName: data?.staff?.staffPersonalInformations?.last_name,
        avatar: data?.staff?.staffPersonalInformations?.avatar,
        id: data?.staff?.staffPersonalInformations?.id,
    })));
    const [teamMembersData, setTeamMembersData] = useState<any>([]);


    const fetchTaskTeamMembers = () => {
        if (getTaskValue.project_id) {
            getTeamMemberByProject(getTaskValue.project_id).then((res) => {
                const fetchedMembers = res.data?.map((data) => ({
                    firstName: data?.staff?.staffPersonalInformations?.first_name,
                    lastName: data?.staff?.staffPersonalInformations?.last_name,
                    avatar: data?.staff?.staffPersonalInformations?.avatar,
                    id: data?.staff?.staffPersonalInformations?.id,
                })) || [];
                const availableMembers = fetchedMembers.filter(
                    (member) => !assignees.some((assignee) => assignee.id === member.id)
                );

                setTeamMembersData(availableMembers);
                // setAssignees([])
            });
        }
    };

    useEffect(() => {
        fetchTaskTeamMembers();
    }, [id])

    const handleAssignee = (assignee) => {
        setTeamMembersData((prev) => prev.filter((m) => m.id !== assignee.id));
        setAssignees((prev) => [...prev, assignee]);
        handleAddAssignee(assignee);
    };

    const removeAssignee = (assignee) => {
        setAssignees((prev) => prev.filter((a) => a.id !== assignee.id));
        setTeamMembersData((prev) => [...prev, assignee]);
        handleRemoveAssignee(assignee);
    };


    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files ? event.target.files[0] : null;

        if (file) {
            const validTypes = ["image/jpeg", "image/png"];
            const maxSize = 900 * 1024;

            if (file.size > maxSize) {
                clustarkToast(
                    NotificationTypes.ERROR,
                    "File size must be less than 900kb."
                );
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
                return;
            }

            if (!validTypes.includes(file.type)) {
                clustarkToast(
                    NotificationTypes.ERROR,
                    "Please select a valid image file (JPEG or PNG)."
                );
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
                return;
            }

            setSelectedImage(file);

            const reader = new FileReader();
            reader.onload = () => {
                setImagePreview(reader.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    const addLabelSchema = yup.object().shape({
        name: yup.string().required(errorMessages.required),
    });

    const Priority = [
        { name: 'low', color: "yellow", },
        { name: 'medium', color: "purple", },
        { name: 'high', color: "pink", },
        { name: 'critical', color: "red", },
    ]

    const initialValues = {
        color: "",
        name: "",
        description: "",
    };

    const handleQuillChange = (value) => {
        setmessage(value);
    };

    const handleSingleChange = (value: string) => {
        setSelectedAssignee(prev => (prev === value ? null : value));
        setOpenAssign(false)
    };

    const getTask = () => {
        setIsLoading(true)
        getSingleIssue(id).then((res) => {
            if (res.success) {
                setTaskAtom(res.data)
                setIsLoading(false)
            }
        })
    }

    const fetchTeamMembers = async () => {
        setIsAssigning(true)
        getTeamMemberByProject(getTaskValue.project_id).then((res) => {
            if (res.success) {
                setTeamMemberAtom(res.data);
                setIsAssigning(false)
            }
        })
    };

    const createLabels = (values) => {
        if (!labelColor) {
            clustarkToast(
                NotificationTypes.ERROR,
                "select a color"
            );
        } else {
            addLabels(
                {
                    name: values.name,
                    description: values.description,
                    color: labelColor,
                }
            ).then((res) => {
                setIsLoading(true)
                if (res.success) {
                    setIsOpen(false)
                    setLabelsAtom(res)
                    setIsLoading(false)
                }
            });
        }
    };

    const handleSelectedLabels = (e, id) => {
        const isChecked = e.target.checked;
        setSelectedLabels((prevState: any) => {
            if (isChecked) {
                return [...prevState, id];
            } else {
                return prevState.filter((item) => item !== id);
            }
        });
    };

    const getSubTask = () => {
        setIsLoading(true)
        getSubtaskById(id).then((res) => {
            if (res.success) {
                setIsLoading(false)
                setSubTaskAtom(res.data)
            }
        }).catch(() => {
            setIsLoading(false)
        })

    }

    const addSubTask = (values) => {
        setIsAssigning(true)
        createSubTask({
            issue_id: getTaskValue.id,
            name: values.title,
            description: values.description,
            assignee: selectedAssignee,
        }).then((res) => {
            if (res.success) {
                setIsAssigning(false)
                setIsOpen(false)
                getSubTask()
                clustarkToast(NotificationTypes.SUCCESS, res.message);
            }
        }).catch(() => {
            setIsAssigning(false)
            setIsOpen(false)
        })

    }

    const completeSubTask = (id) => {
        setIsLoading(true)
        updateSubtask({
            checklist_id: id,
        }).then((res) => {
            if (res.success) {
                setIsLoading(false)
                getSubTask()
                clustarkToast(NotificationTypes.SUCCESS, res.message);
            }
        }).catch(() => {
            setIsLoading(false)
        })
    }

    const handleSubTaskUpdate = (value: string) => {
        completeSubTask(value)
    };

    const deleteSubTask = (id) => {
        setIsLoading(true)
        deleteSubtaskById(id).then((res) => {
            if (res.success) {
                setIsLoading(false)
                getSubTask()
                clustarkToast(NotificationTypes.SUCCESS, res.message);
            }
        }).catch(() => {
            setIsLoading(false)
        })
    }

    const assignSubTask = (taskId) => {
        // setIsLoading(true);
        setAssigneeId(taskId)
        assignSubtaskById({
            checklist_id: SubTaskId,
            assignee_id: taskId,
        }).then((res) => {
            if (res.success) {
                setIsLoading(false)
                getSubTask()
                setOpenNewAssign(false)
                clustarkToast(NotificationTypes.SUCCESS, res.message);
            }
        }).catch(() => {
            setIsLoading(false)
            setOpenNewAssign(false)
        })
    }

    const getAllTaskComment = () => {
        setIsLoading(true)
        getTaskComment(id).then((res) => {
            if (res.success) {
                setIsLoading(false)
                setTaskCommentAtom(res.data)
            }
        }).catch(() => {
            setIsLoading(false)
        })

    }

    const taskComment = () => {

        setIsSending(true);

        const formData = new FormData();
        formData.append("task_id", id);
        formData.append("comment", message);
        if (selectedImage) {
            formData.append("attachment", selectedImage);
        }

        sendComment(formData)
            .then((res) => {
                if (res.success) {
                    setIsSending(false);
                    getAllTaskComment();
                    setImagePreview(null);
                    setmessage('');
                    clustarkToast(NotificationTypes.SUCCESS, res.message);
                }
            })
            .catch(() => {
                setIsSending(false);
            });
    };


    // const unassignSubTask = (taskId) => {
    //     setIsLoading(true)
    //     unAssignSubtaskById({
    //         checklist_id: SubTaskId,
    //         assignee_id: taskId,
    //     }).then((res) => {
    //         if (res.success) {
    //             setIsLoading(false)
    //             getSubTask()
    //             setOpenNewAssign(false)
    //         }
    //     }).catch(() => {
    //         setIsLoading(false)
    //         setOpenNewAssign(false)
    //     })
    // }


    const updateTaskStatus = () => {
        setIsUpdating(true)
        updateTaskStatusById({
            task_id: id,
            status_id: statusId,
        }).then((res) => {
            if (res.success) {
                setIsUpdating(false)
                getTask();
                setOpenStatus(false)
                clustarkToast(NotificationTypes.SUCCESS, res.message);
            }
        }).catch(() => {
            setIsUpdating(false)
            setOpenStatus(false)
        })
    }

    const updateTaskLabel = () => {

        // const oldLabels = getTaskValue?.task_labels?.map((item) => item.task_label.id)

        const payload = {
            issue_id: id,
            labels: [...selectedLabels],
        }

        setIsLoading(true)
        updateTaskLabelById(payload)
            .then((res) => {
                if (res.success) {
                    setIsLoading(false)
                    getTask();
                    setOpenLabel(false)
                    clustarkToast(NotificationTypes.SUCCESS, res.message);
                }
            }).catch(() => {
                setIsLoading(false)
                setOpenLabel(false)
                setSelectedLabels([])
            })

    }

    const updateTaskPriority = () => {
        setIsUpdating(true)
        updateTaskPriorityById({
            task_id: id,
            priority: SelectedPriority,
        }).then((res) => {
            if (res.success) {
                setIsUpdating(false)
                getTask()
                setOpenPriority(false)
                clustarkToast(NotificationTypes.SUCCESS, res.message);
            }
        }).catch(() => {
            setIsUpdating(false)
            setOpenPriority(false)
        })
    };

    const handleRemoveAssignee = (assignee) => {
        setIsUpdating(true);
        const payload = {
            issue_id: id,
            assignee: assignee.id
        };
        removeTaskAssignee(payload).then((res) => {
            if (res.success) {
                setIsUpdating(false)
                clustarkToast(NotificationTypes.SUCCESS, res.message);
                getTask();
            };
            setIsUpdating(false);
        }).catch(() => setIsUpdating(false));
    };

    const handleAddAssignee = (assignee) => {
        console.log(assignee)
        setIsUpdating(true);
        const payload = {
            issue_id: id,
            assignee: assignee.id
        };
        addTaskAssignee(payload).then((res) => {
            if (res.success) {
                setIsUpdating(false)
                clustarkToast(NotificationTypes.SUCCESS, res.message);
                getTask();
            };
            setIsUpdating(false);
        }).catch(() => setIsUpdating(false));
    };

    useEffect(() => {
        getTask()
        getSubTask()
        getAllTaskComment()
    }, [])


    if (isLoading) { return (<Loader />) }

    return (
        <>
            <GoBack />

            <div
                className="flex gap-8 relative w-full mt-4"
            >
                <div className="w-[74%] bg-white rounded-lg py-10">

                    <div className=" flex flex-col w-full mt-10  hide-scrollbar px-10">

                        <div className=" relative flex flex-col">
                            <div className="flex flex-col gap-4">
                                <h2 className="text-[1.4rem] font-bold text-purple-normal first-letter:capitalize">{removeHtmlTags(getTaskValue?.name) || '--'}</h2>
                                <h2 className="text-[1rem]  text-purple-darker first-letter:capitalize">{removeHtmlTags(getTaskValue?.description) || '--'}</h2>
                                {getTaskValue && getTaskValue?.task_attachments &&
                                    getTaskValue?.task_attachments?.length !== 0 ?
                                    getTaskValue.task_attachments.map(({ attachment }, index) =>
                                        <div
                                            className="h-[350px] w-full rounded-md overflow-hidden"
                                            key={index}
                                        >
                                            <img
                                                src={attachment}
                                                alt="avatar"
                                                className="w-full h-full object-cover"
                                            />
                                        </div>)
                                    :
                                    null
                                }
                            </div>

                            <div className="w-full mt-5 mb-3">
                                <div className="text-gray-500">
                                    <div
                                        className="flex items-center gap-2 mb-5 text-[12px] cursor-pointer"
                                        onClick={() => { setOpenIssue(!openIssue); setOpenAssign(false) }}
                                    >
                                        <span><FaPlus size={15} /></span>
                                        <p>Add checklist</p>
                                    </div>

                                    <div className="">
                                        {
                                            openIssue &&
                                            <div className="flex gap-2 border-2 border-gray-100 rounded-md py-4 px-2 mb-4 ">

                                                <Formik
                                                    initialValues={
                                                        { title: "", description: "", }
                                                    }
                                                    onSubmit={(values) => addSubTask(values)}
                                                    enableReinitialize
                                                >
                                                    {({ values, setFieldValue }) => (
                                                        <Form>

                                                            <div className="flex flex-col ">

                                                                <div className="flex flex-col gap-3 font-bold text-[12px] mb-2">

                                                                    <FormikCustomInput
                                                                        id="title"
                                                                        name="title"
                                                                        placeholder="title"
                                                                        type="text"
                                                                        inputClassName="!bg-transparent border-none focus:outline-none focus:ring-0"
                                                                        value={values.title}
                                                                        required
                                                                    />

                                                                    <CustomQuillTextArea
                                                                        value={values.description}
                                                                        handleChange={(value) => setFieldValue("description", value)}
                                                                        className="bg-transparent !w-[650px] !h-[120px] rounded-md p-2 mb-12"
                                                                        placeholder="description"
                                                                    />

                                                                </div>

                                                                <div className="">
                                                                    <div className="flex flex-col gap-5">
                                                                        <div className="flex justify-between items-center w-[650px]">
                                                                            <div className="flex gap-2 ">
                                                                                <div className="flex gap-2 border-dashed border-2 border-gray-200 rounded-full py-1 px-3 cursor-pointer"
                                                                                    onClick={() => { setOpenAssign(!openAssign); fetchTeamMembers() }}>
                                                                                    <span className="mt-1"><FaPlus size={12} /> </span>
                                                                                    {
                                                                                        isAssigning ?
                                                                                            <FaSpinner className="animate-spin" size={18} color="#546881" /> :
                                                                                            <p>Assign</p>
                                                                                    }
                                                                                </div>
                                                                            </div>

                                                                            <div className="flex gap-3 items-center">
                                                                                <p
                                                                                    className="cursor-pointer text-red-700"
                                                                                    onClick={() => { setOpenIssue(false); setOpenAssign(false) }}
                                                                                >
                                                                                    Discard
                                                                                </p>
                                                                                <CustomButton
                                                                                    title="Add"
                                                                                    handleClick={() => { }}
                                                                                    isLoading={isLoading}
                                                                                    size={ButtonProperties.SIZES.small}
                                                                                    variant={ButtonProperties.VARIANT.primary.name}
                                                                                    className="!w-[70px] !h-[9px]"
                                                                                    type="submit"
                                                                                />
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                            </div>

                                                        </Form>
                                                    )}
                                                </Formik>

                                            </div>
                                        }
                                    </div>

                                    <div className="mb-5  rounded border-0 bg-red-100">
                                        <div className="bg-gray-100 text-gray p-4 rounded-md space-y-2">
                                            {getSubTaskValue && getSubTaskValue.length > 0 && (
                                                <>
                                                    {/* Sub-issues Header */}
                                                    <div className="flex items-center justify-between text-sm text-gray-700 mb-2">
                                                        <div className="flex items-center gap-1">
                                                            <span className="text-sm">Sub-issues</span>
                                                        </div>
                                                        <span>{getSubTaskValue.filter(sub => sub.status === 'completed').length}/{getSubTaskValue.length}</span>
                                                    </div>

                                                    {getSubTaskValue.map(({ status, assignee, description, id }, index) => (
                                                        <div key={id || index} className="flex items-center justify-between bg-purple-light px-4 py-3 rounded-md mb-2">
                                                            <div className="flex items-center gap-2">
                                                                <div className="w-4 h-4 rounded-full flex items-center justify-center">
                                                                    <CustomCheckBox
                                                                        label="subtask"
                                                                        checked={status === 'completed'}
                                                                        onChange={() => handleSubTaskUpdate(id)}
                                                                        customClass="!mr-3 !mt-2"
                                                                    />
                                                                </div>
                                                                <p className="text-sm font-medium">
                                                                    <span className="text-gray-700">{removeHtmlTags(description)}</span>
                                                                </p>
                                                            </div>

                                                            <div className="flex items-center gap-2 text-xs capitalize">
                                                                <span
                                                                    className="px-2 py-0.5 text-gray-100 rounded-full text-[8px] captitalize"
                                                                    style={{ backgroundColor: status === 'completed' ? 'green' : 'grey' }}
                                                                >
                                                                    {status}
                                                                </span>
                                                                <div className="relative">
                                                                    <div>
                                                                        {assignee ? (
                                                                            <div
                                                                                className="flex items-center justify-center w-5 h-5 bg-orange-500 text-white text-[8px] font-semibold rounded-full cursor-pointer"
                                                                                onClick={() => {
                                                                                    setOpenNewAssign(true);
                                                                                    setSubTaskId(id);
                                                                                }}
                                                                            >
                                                                                <p className="text-white">
                                                                                    {getNameInitials(assignee?.staffPersonalInformations?.first_name, assignee?.staffPersonalInformations?.last_name)}
                                                                                </p>
                                                                            </div>
                                                                        ) : (
                                                                            <div>
                                                                                <div >
                                                                                    <div
                                                                                        className="px-2 py-0.5 text-gray-100 rounded-full text-[8px] bg-purple-normal cursor-pointer"
                                                                                        onClick={() => {
                                                                                            setOpenNewAssign(!openNewAssign);
                                                                                            setSubTaskId(id);
                                                                                        }}
                                                                                    >
                                                                                        <p>Assign</p>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        )}
                                                                    </div>

                                                                    {openNewAssign && (
                                                                        <FilterDropdown className="!w-[200px]">
                                                                            <div className="flex justify-end p-4">
                                                                                <FaX size={10} className="cursor-pointer" onClick={() => setOpenNewAssign(false)} />
                                                                            </div>
                                                                            <div className="flex items-center justify-center">
                                                                                <div className="px-4">
                                                                                    {
                                                                                        getTeamMemberValue.map(({ staff, staff_id }, index) => (
                                                                                            <div key={index} className="flex items-center space-x-3 mb-3">
                                                                                                <CustomCheckBox
                                                                                                    label='assignee'
                                                                                                    checked={assignId === staff_id}
                                                                                                    onChange={() => { assignSubTask(staff_id); }}
                                                                                                    customClass="!mr-1 !mt-2"
                                                                                                />
                                                                                                <div className="w-5 h-5 flex items-center justify-center bg-blue-500 text-white text-[8px] font-semibold rounded-full">
                                                                                                    <p className="text-white">{getNameInitials(staff?.staffPersonalInformations?.first_name, staff?.staffPersonalInformations?.last_name)}</p>
                                                                                                </div>
                                                                                                <span className="text-gray-800 font-medium">{staff?.staffPersonalInformations?.first_name} &nbsp;{staff?.staffPersonalInformations?.last_name}</span>

                                                                                            </div>
                                                                                        ))
                                                                                    }
                                                                                </div>
                                                                            </div>
                                                                        </FilterDropdown>
                                                                    )}
                                                                </div>


                                                                <span className="cursor-pointer" onClick={() => deleteSubTask(id)}>
                                                                    <FaTrash size={13} color="red" />
                                                                </span>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </>
                                            )}


                                        </div>
                                    </div>
                                </div>
                                <div className="">
                                    <div className="">
                                        <div className="flex justify-between items-center mb-2 border-b border-gray-300 py-2 mt-5 text-purple-light">
                                            <p className="text-[12px] text-gray-500">Activity</p>
                                            <div className="flex items-center gap-2">
                                                <p className="text-gray-500 text-[12px]">Watchers</p>
                                                <div className="flex items-center">
                                                    <div className="w-[15px] h-[15px] rounded-full bg-yellow-400 flex items-center justify-center">
                                                        <p>C</p>
                                                    </div>
                                                    <div className="w-[15px] h-[15px]  rounded-full bg-blue-700 flex items-center justify-center -ml-3">
                                                        <span>P</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex flex-col gap-1 font-semibold text-[0.9rem] text-gray-500">

                                            {(labelAtomValue && getTaskValue?.task_activities?.length <= 10 || openAllActivities)
                                                ? (getTaskValue?.task_activities || []).map(({ activity_description, created_at }, index) => (
                                                    <div key={index}>
                                                        <div className="flex items-center gap-4">
                                                            <div className="w-[18px] h-[18px] rounded-full bg-purple-normal-active"></div>
                                                            <p className="text-[12px]">
                                                                {activity_description}{" "}
                                                                <span className="text-[9px] text-purple-normal-active">
                                                                    {moment(created_at).fromNow() || "--"}
                                                                </span>
                                                            </p>
                                                        </div>
                                                        {index !== (getTaskValue?.task_activities?.length || 0) - 1 && (
                                                            <div className="border-l border-purple-normal-active p-2 ml-[8px] mt-1"></div>
                                                        )}
                                                    </div>
                                                ))
                                                : (getTaskValue?.task_activities || []).slice(-10).map(({ activity_description, created_at }, index) => (
                                                    <div key={index}>
                                                        <div className="flex items-center gap-4">
                                                            <div className="w-[18px] h-[18px] rounded-full bg-purple-normal-active"></div>
                                                            <p className="text-[12px]">
                                                                {activity_description}{" "}
                                                                <span className="text-[9px] text-purple-normal-active">
                                                                    {moment(created_at).fromNow() || "--"}
                                                                </span>
                                                            </p>
                                                        </div>
                                                        {index !== (getTaskValue?.task_activities?.length || 0) - 1 && (
                                                            <div className="border-l border-purple-normal-active p-2 ml-[8px] mt-1"></div>
                                                        )}
                                                    </div>
                                                ))
                                            }


                                            {
                                                getTaskValue?.task_activities?.length > 10 &&
                                                <div className="flex w-full justify-center">
                                                    <CustomButton
                                                        title={openAllActivities ? 'Show less' : 'Show all '}
                                                        handleClick={() => { setOpenAllActivities(!openAllActivities) }}
                                                        isLoading={isLoading}
                                                        size={ButtonProperties.SIZES.small}
                                                        variant={ButtonProperties.VARIANT.primary.name}
                                                        className="!w-[70px] !h-[9px] border-none bg-transparent !text-purple-dark !font-bold text-[1.1rem] hover:bg-transparent"
                                                        type="submit"
                                                    />
                                                </div>
                                            }
                                        </div>

                                        {
                                            getTaskCommentValue &&
                                            getTaskCommentValue?.map(({ comment, user, staff, attachment, created_at }, index) => (
                                                <div
                                                    className="mt-5 border-2 rounded-md py-5 px-3 flex flex-col gap-3"
                                                    key={index}
                                                >

                                                    {
                                                        user == null ?
                                                            <div className="flex gap-2 items-center">
                                                                <div
                                                                    key={index}
                                                                    className="relative w-[25px] h-[25px] rounded-full overflow-hidden border border-gray-300 bg-purple-normal flex items-center justify-center"
                                                                >
                                                                    {
                                                                        staff?.staffPersonalInformations?.avatar ?
                                                                            <img
                                                                                src={staff?.staffPersonalInformations?.avatar}
                                                                                alt={`team-member-${index}`}
                                                                                className="w-full h-full rounded-full"
                                                                            />
                                                                            :
                                                                            <div className="flex items-center justify-center w-full h-full">
                                                                                <p className="text-white">
                                                                                    {getNameInitials(
                                                                                        staff?.staffPersonalInformations?.first_name
                                                                                    )}
                                                                                </p>
                                                                            </div>
                                                                    }
                                                                </div>

                                                                <p>{staff?.staffPersonalInformation?.first_name}</p>
                                                                <p className="text-[9px] text-purple-normal-active">{moment(created_at).fromNow() || "--"}</p>
                                                            </div> :
                                                            <div className="flex gap-2 items-center">
                                                                <div
                                                                    key={index}
                                                                    className="relative w-[25px] h-[25px] rounded-full overflow-hidden border border-gray-300 bg-purple-normal flex items-center justify-center"
                                                                >
                                                                    {
                                                                        user?.avatar ?
                                                                            <img
                                                                                src={user?.avatar}
                                                                                alt={`team-member-${index}`}
                                                                                className="w-full h-full rounded-full"
                                                                            />
                                                                            :
                                                                            <div className="flex items-center justify-center w-full h-full">
                                                                                <p className="text-white">
                                                                                    {getNameInitials(
                                                                                        user?.first_name
                                                                                    )}
                                                                                </p>
                                                                            </div>
                                                                    }
                                                                </div>

                                                                <p>{user?.first_name}</p>
                                                                <p className="text-[9px] text-purple-normal-active">{moment(created_at).fromNow() || "--"}</p>
                                                            </div>
                                                    }

                                                    <div className="">
                                                        <p>{removeHtmlTags(comment) || '--'}</p>
                                                    </div>
                                                    {
                                                        attachment !== null &&
                                                        <div className="w-full h-[250px] rounded overflow-hidden">
                                                            <img src={attachment} alt="avatar" className="w-[100%] h-[100%] object-cover " />
                                                        </div>
                                                    }
                                                </div>
                                            ))
                                        }

                                    </div>
                                </div>
                            </div>

                            <div className="">
                                <div className="relative mt-5 mb-5">
                                    <CustomQuillTextArea
                                        value={message}
                                        handleChange={handleQuillChange}
                                        className="bg-transparent w-full h-[100px] p-2"
                                        placeholder="message"
                                    />

                                    {imagePreview && (
                                        <div className=" absolute bottom-20">
                                            <img
                                                width={100}
                                                height={100}
                                                src={imagePreview}
                                                alt="Selected"
                                                className="bg-gray-50 h-[150px] w-[150px] object-contain border rounded-lg relative"
                                            />
                                            <div
                                                onClick={() => setImagePreview(null)}
                                                className="cursor-pointer absolute -top-1 right-0 text-alert-text-error bg-alert-bg-error p-1 border border-alert-text-error rounded-full"
                                            >
                                                <FaXmark size={10} />
                                            </div>
                                        </div>
                                    )}

                                    <div className="absolute flex gap-2 right-8 text-purple-normal cursor-pointer">
                                        <div>
                                            <p
                                                className="flex gap-1 mb-1 cursor-pointer w-fit"
                                                onClick={handleClick}
                                            >
                                                <MdAttachment color="#546881" size={20} />
                                            </p>
                                            <input
                                                type="file"
                                                ref={fileInputRef}
                                                accept="image/jpg, image/png, image/jpeg"
                                                style={{ display: "none" }}
                                                onChange={handleImageUpload}
                                            />
                                        </div>
                                        {isSending ? (
                                            <FaSpinner className="animate-spin" size={18} color="#546881" />
                                        ) : (
                                            <FiSend onClick={taskComment} color="#546881" size={20} />
                                        )}
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div className="w-[18%] col-span-1 fixed right-10 bg-white rounded-lg px-5 hide-scrollbar" ref={node}>

                    <div className="flex flex-col gap-4  mt-5 mb-5 h-screen">
                        <div
                            className="flex gap-2 items-center cursor-pointer py-1 px-3 rounded-full  "
                            onClick={() => {
                                setOpenStatus(true);
                                setAssigneeModal(false);
                                setOpenPriority(false);
                                setOpenLabel(false);
                                setSelectedStatus(getTaskValue.task_statuses[0]?.task_status?.name);
                            }}
                        >
                            <p className="flex gap-2"><span><Status size={16} /></span>Status: </p> {getTaskValue?.task_statuses?.length > 0 && (
                                <div className="font-bold text-[12px] flex gap-2 capitalize">
                                    <div className="mt-1 w-[12px] h-[12px] rounded-full" style={{ backgroundColor: getTaskValue.task_statuses[0]?.task_status?.color }}></div> {getTaskValue.task_statuses[0]?.task_status?.name || '--'}
                                </div>
                            )}
                        </div>

                        {
                            openStatus &&
                            <FilterDropdown className="">
                                <div className="p-3">
                                    <div className="flex justify-end pr-4 mt-3">
                                        <FaXmark className="cursor-pointer" color="#3730A3" size={10} onClick={() => { setOpenStatus(false); }} />
                                    </div>
                                    <p className="font-poppins-medium text-purple-dark pl-3">
                                        Change Status
                                    </p>
                                    <div className=" flex flex-col gap-2 p-5" >
                                        {
                                            getStatusValue?.data?.length > 0 && getStatusValue?.data?.map(({ name, color, id }, index) => (
                                                <div key={index} className=" flex items-center">
                                                    <div className="flex justify-between items-center w-full ">
                                                        <div className="flex gap-2 items-center ">
                                                            <CustomCheckBox
                                                                label='status'
                                                                checked={name === SelectedStatus}
                                                                onChange={() => { setStatusId(id); setSelectedStatus(name); }}
                                                                customClass="!mr-1 !mt-2"
                                                            />
                                                            <div
                                                                className="w-[10px] h-[10px] rounded-full "
                                                                style={{ backgroundColor: color }}
                                                            >
                                                            </div>
                                                            <p className="text-[11px] capitalize"> {name}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))
                                        }
                                        <div className="flex justify-end mt-4">
                                            <CustomButton
                                                isLoading={isUpdating}
                                                className="!h-8 !w-[100%]"
                                                variant={ButtonProperties.VARIANT.primary.name}
                                                title="Save"
                                                handleClick={updateTaskStatus}
                                            />
                                        </div>

                                    </div>
                                </div>

                            </FilterDropdown>
                        }

                        <div
                            className="flex relative gap-2 cursor-pointer  py-1 px-3 rounded-full"
                            onClick={() => {
                                setOpenPriority(true);
                                setOpenStatus(false);
                                setOpenLabel(false);
                                setAssigneeModal(false);
                                setSelectedPriority(getTaskValue?.priority);
                            }}
                        >
                            <p className="flex gap-2"><span><Flag size={16} /></span>Priority: </p>
                            <p className="font-bold text-[12px] capitalize">{getTaskValue?.priority || "--"}</p>


                        </div>
                        {
                            openPriority &&
                            <FilterDropdown>
                                <div className="p-4">
                                    <div className="flex justify-end">
                                        <FaXmark className="cursor-pointer" color="#3730A3" size={10} onClick={() => setOpenPriority(false)} />
                                    </div>
                                    <p className="font-poppins-medium text-purple-dark">
                                        Change Priority
                                    </p>
                                    <div className=" mb-3 flex flex-col gap-2  p-5" >
                                        {
                                            Priority && Priority.map(({ name, color }, index) => (
                                                <div key={index} className=" flex items-center">
                                                    <div className="flex justify-between items-center w-full">
                                                        <div className="flex gap-2 items-center ">
                                                            <CustomCheckBox
                                                                label='status'
                                                                checked={name === SelectedPriority}
                                                                onChange={() => { setSelectedPriority(name); }}
                                                                customClass="!mr-1 !mt-2"
                                                            />
                                                            <div
                                                                className="w-[10px] h-[10px] rounded-full "
                                                                style={{ backgroundColor: color }}
                                                            >
                                                            </div>
                                                            <p className="text-[11px] capitalize"> {name}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))
                                        }

                                    </div>
                                    <div className="flex justify-end mt-4">
                                        <CustomButton
                                            isLoading={isUpdating}
                                            className="!h-8 !w-[100%]"
                                            variant={ButtonProperties.VARIANT.primary.name}
                                            title="Save"
                                            handleClick={updateTaskPriority}
                                        />
                                    </div>
                                </div>

                            </FilterDropdown>
                        }


                        <div className="flex gap-4 px-3 relative cursor-pointer" onClick={() => { setAssigneeModal(true); setOpenPriority(false); setOpenStatus(false); setOpenLabel(false); }}>
                            <p className="flex gap-2"><span><User size={14} /></span>Assignees: </p>
                            <div className="flex">

                                {
                                    getTaskValue?.task_assignees && getTaskValue?.task_assignees?.length > 0 ?
                                        getTaskValue?.task_assignees?.map(({ staff }, index) => (
                                            <div
                                                key={index}
                                                className="relative w-[25px] h-[25px] rounded-full overflow-hidden border border-gray-300 bg-purple-normal flex items-center justify-center -ml-2"
                                                style={{ zIndex: getTaskValue.task_assignees.length - index }}
                                            >
                                                {
                                                    staff?.staffPersonalInformations?.avatar ? (
                                                        <img
                                                            src={staff?.staffPersonalInformations?.avatar}
                                                            alt={`team-member-${index}`}
                                                            className="w-full h-10 rounded-full"
                                                        />
                                                    ) : (
                                                        <div className="flex items-center justify-center w-full text-white text-xs h-10">
                                                            {getNameInitials(staff?.staffPersonalInformations?.first_name, staff?.staffPersonalInformations?.last_name)}
                                                        </div>
                                                    )
                                                }
                                            </div>
                                        )) :
                                        <p className="text-neutral-light text-12 mt-1">No assignees</p>
                                }
                            </div>

                        </div>
                        {assigneeModal && (
                            <FilterDropdown>
                                <div className="p-6">
                                    <div className="flex justify-end">
                                        <FaXmark className="cursor-pointer" color="#3730A3" size={12} onClick={() => setAssigneeModal(false)} />
                                    </div>
                                    <div className=" px-2">
                                        <h1 className="flex gap-2 font-poppins-medium  text-purple-dark">
                                            <User
                                                size={14}
                                                className="mt-0.5 text-purple-dark"
                                            />
                                            Assigned members
                                        </h1>
                                        <div className="mt-2 overflow-y-scroll show-scrollbar">
                                            {assignees?.length > 0 ? (
                                                <div className="mt-4">
                                                    {assignees.map((assignee, index) => (
                                                        <div className="flex justify-between">
                                                            <div
                                                                key={index}
                                                                className="flex mt-2 gap-2 items-center cursor-pointer"
                                                            >
                                                                {assignee?.avatar ? (
                                                                    <img
                                                                        src={assignee?.avatar}
                                                                        alt="Assignee"
                                                                        className="w-8 h-8 rounded-full"
                                                                    />
                                                                ) : (
                                                                    <p className="w-8 h-8 flex justify-center items-center rounded-full bg-purple-normal text-white">
                                                                        {getNameInitials(
                                                                            assignee.firstName,
                                                                            assignee.lastName
                                                                        )}
                                                                    </p>
                                                                )}
                                                                <p className="text-[12px] text-neutral-normal">
                                                                    {assignee?.firstName}{" "}
                                                                    {assignee?.lastName}
                                                                </p>
                                                            </div>
                                                            <div className="flex justify-center items-center mt-2 cursor-pointer" onClick={() => { removeAssignee(assignee) }}>
                                                                <FaXmark size={8} color="#3730A3" />
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            ) : (
                                                <span className="text-center text-neutral text-12">
                                                    No assigned members found.
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                    <div className="mt-6 p-2">
                                        <h1 className="flex gap-2 font-poppins-medium  text-purple-dark">
                                            <User
                                                size={14}
                                                className="mt-0.5 text-purple-dark"
                                            />
                                            Board members
                                        </h1>
                                        <div className="mt-2 ">
                                            {teamMembersData?.length > 0 ? (
                                                <div className="mt-4">
                                                    {teamMembersData?.map(
                                                        (assignee, index) => (
                                                            <div>
                                                                <div
                                                                    key={index}
                                                                    className="flex mt-2 gap-2 items-center cursor-pointer"
                                                                    onClick={() => { handleAssignee(assignee) }}
                                                                >
                                                                    {assignee.avatar ? (
                                                                        <img
                                                                            src={assignee.avatar}
                                                                            alt="Assignee"
                                                                            className="w-8 h-8 rounded-full"
                                                                        />
                                                                    ) : (
                                                                        <p className="w-8 h-8 rounded-full flex justify-center items-center bg-purple-normal text-white">
                                                                            {getNameInitials(
                                                                                assignee.firstName,
                                                                                assignee.lastName
                                                                            )}
                                                                        </p>
                                                                    )}
                                                                    <p className="text-[12px] text-neutral-normal">
                                                                        {assignee.firstName}{" "}
                                                                        {assignee.lastName}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            ) : (
                                                <p className="  text-center"><span className="text-neutral text-12">Nothing to show</span>
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </FilterDropdown>
                        )}

                        <div className="flex flex-col gap-2 px-4">
                            <p className="font flex gap-2"><BiLabel size={16} />Labels</p>
                            <div className="">
                                <div className="flex gap-2 items-center flex-wrap capitalize">
                                    {labelAtomValue && getTaskValue.task_labels &&
                                        getTaskValue.task_labels.length > 0 ? getTaskValue.task_labels.map(({ label, task_label }, index) => (
                                            <div
                                                key={index}
                                                className="flex gap-2 items-center justify-center border-2 border-gray-300 rounded-full py-1 px-3 "
                                            >
                                                <div
                                                    className={`w-[12px] h-[12px] rounded-full`}
                                                    style={{ backgroundColor: task_label.color }}
                                                ></div>
                                                <p
                                                    className="font-bold text-[10px]"
                                                >
                                                    {label}
                                                </p>
                                            </div>
                                        )) :
                                        <div className="">
                                            <p className="text-neutral-light text-12 text-center">No label added..</p>
                                        </div>
                                    }
                                </div>

                                <div className="relative">

                                    <div className="mt-4">
                                        <CustomButton
                                            title={<FaPlus />}
                                            handleClick={() => {
                                                setOpenLabel(!openLable);
                                                setOpenPriority(false);
                                                setOpenStatus(false);
                                                setSelectedLabels(
                                                    getTaskValue.task_labels?.map(
                                                        (data) => data.task_label_id
                                                    )
                                                );
                                            }}
                                            isLoading={isLoading}
                                            size={ButtonProperties.SIZES.small}
                                            variant={ButtonProperties.VARIANT.primary.name}
                                            className="!w-[20px] !h-[1px] rounded-full "
                                        />


                                    </div>

                                    {openLable && labelAtomValue?.data?.length > 0 && (
                                        <FilterDropdown>
                                            <div className="flex justify-end p-4">
                                                <FaXmark className="cursor-pointer" color="#3730A3" size={12} onClick={() => setOpenLabel(false)} />
                                            </div>
                                            <p className="font-poppins-medium p-4 text-purple-dark">
                                                Labels
                                            </p>
                                            <div className="flex flex-col gap-2 px-4">
                                                {labelAtomValue.data.map((data, index) => (
                                                    <div key={index} className="flex items-center">
                                                        <div className="flex gap-2 items-center">
                                                            <CustomCheckBox
                                                                name="label"
                                                                id="label"
                                                                onChange={(e) => {
                                                                    handleSelectedLabels(e, data.id);
                                                                }}
                                                                checked={selectedLabels.some((item: any) => item === data.id)}
                                                            />
                                                            <div
                                                                className="w-[10px] h-[10px] rounded-full"
                                                                style={{ backgroundColor: data.color }}
                                                            ></div>
                                                            <p className="text-[11px] capitalize">{data.name}</p>
                                                        </div>
                                                    </div>
                                                ))}

                                                <div className="flex justify-end my-4">
                                                    <CustomButton
                                                        isLoading={isUpdating}
                                                        className="!h-8 !w-[100%]"
                                                        variant={ButtonProperties.VARIANT.primary.name}
                                                        title="Save"
                                                        handleClick={updateTaskLabel}
                                                    />
                                                </div>
                                            </div>
                                        </FilterDropdown>
                                    )}
                                </div>


                            </div>
                        </div>

                    </div>


                </div>

                <CustomModal
                    toggleVisibility={setIsOpen}
                    visibility={isOpen}
                >

                    <div className="">

                        <div className="bg-purple-light-hover px-10 py-[28px] flex justify-between  ">
                            <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                                Add Label
                            </h1>
                        </div>

                        <div className="bg-[#F5F5F5] pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
                            <Formik
                                initialValues={initialValues}
                                onSubmit={(values) => { createLabels(values) }}
                                validationSchema={addLabelSchema}
                                enableReinitialize
                            >
                                {({ values, handleChange }) => (
                                    <Form>

                                        <div className="grid grid-cols-1 gap-8 mt-8">

                                            <div>
                                                <FormikCustomInput
                                                    label="Label name *"
                                                    id="name"
                                                    name="name"
                                                    placeholder="e.g. Bug, Feature Request"
                                                    type="text"
                                                    inputClassName="!bg-transparent"
                                                    value={values.name}
                                                />
                                            </div>

                                        </div>

                                        <div className="grid grid-cols-1 gap-8 mt-8">

                                            <div className="grid grid-cols-1 gap-4">
                                                <label htmlFor="description">Description </label>
                                                <textarea
                                                    name="description"
                                                    id="description"
                                                    className="w-full h-[80px] rounded-md bg-transparent border focus:outline-none focus:ring-0 py-2 px-4 "
                                                    placeholder="enter description"
                                                    value={values.description}
                                                    onChange={handleChange}
                                                >
                                                </textarea>
                                            </div>

                                        </div>

                                        <div className="">
                                            <p className="mt-8 text-[1.1rem]">Color *</p>

                                            <div className="grid grid-cols-10 gap-3 mt-5">
                                                {colorsPallette.map((item) => (
                                                    <div className={`h-[40px] w-[40px] rounded-full flex justify-center items-center ${item === labelColor ? "bg-purple-dark-hover" : "bg-none"}`}>
                                                        <div className={`h-[35px] w-[35px] rounded-full flex justify-center items-center ${item === labelColor && "bg-white"} `}>
                                                            <div
                                                                key={item}
                                                                className={`h-[30px] w-[30px] rounded-full cursor-pointer`}
                                                                style={{ backgroundColor: item }}
                                                                onClick={() => { setLabelColor(item) }}
                                                            ></div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                            <p className="text-right mt-2">{labelColor}</p>
                                        </div>

                                        <div className="mt-[120px] flex justify-end">
                                            <CustomButton
                                                type="submit"
                                                title="Add label"
                                                handleClick={() => { }}
                                                isLoading={isLoading}
                                                size={ButtonProperties.SIZES.small}
                                                variant={ButtonProperties.VARIANT.primary.name}
                                            />
                                        </div>

                                    </Form>
                                )}
                            </Formik>
                        </div>
                    </div>

                </CustomModal>

                <CustomModal cardClassName="!max-w-[12%]" visibility={openAssign} toggleVisibility={setOpenAssign}>
                    <div className="bg-black/20 flex items-center justify-center">
                        <div className="bg-purple-light p-6 rounded-lg shadow-xl w-72">
                            {
                                getTeamMemberValue.map(({ staff, staff_id }, index) => (
                                    <div key={index} className="flex items-center space-x-3 mb-3">
                                        <CustomCheckBox
                                            label='assignee'
                                            checked={selectedAssignee === staff_id}
                                            onChange={() => handleSingleChange(staff_id)}
                                            customClass="!mr-3 !mt-2"
                                        />
                                        <div className="w-5 h-5 flex items-center justify-center bg-blue-500 text-white text-[8px] font-semibold rounded-full">
                                            <p className="text-white">
                                                {getNameInitials(
                                                    staff?.staffPersonalInformations?.first_name
                                                )}
                                            </p>
                                            <p className="text-white">
                                                {getNameInitials(
                                                    staff?.staffPersonalInformations?.last_name
                                                )}
                                            </p>
                                        </div>
                                        <span className="text-gray-800 font-medium">{staff?.staffPersonalInformations?.first_name}</span>
                                        <span className="text-gray-800 font-medium">{staff?.staffPersonalInformations?.last_name}</span>
                                    </div>
                                ))
                            }
                        </div>
                    </div>
                </CustomModal>


            </div >
        </>


    )

}


export default ViewTask;