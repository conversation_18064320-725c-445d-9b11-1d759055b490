import { useEffect, useState } from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import { useRecoilState, useRecoilValue } from "recoil";
import { getProjectAtom, getProjectIdAtom, getTaskTeamAtom } from "../../../../recoil/atom/TaskManagement";
import * as yup from "yup";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import { Form, Formik } from "formik";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import GoBack from "../../../atoms/Ui/GoBack";
import moment from "moment";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import { useLocation } from "react-router-dom";
import { AiOutlineEdit } from "react-icons/ai";
import { BiSave } from "react-icons/bi";
import { getProjectById, updateProject } from "../../../../api/TaskManagement";
import Loader from "../../../atoms/Loader";
import StatusTag from "../../../atoms/StatusTag";
import CustomQuillTextArea from "../../../atoms/CustomQillTextArea";


const ViewProject = () => {

    const location = useLocation();
    const { isEdit, id } = location?.state || "";

    const [, setProjectIdAtom] = useRecoilState(getProjectIdAtom);
    const getProjectIdValue = useRecoilValue(getProjectIdAtom);

    const getTeamValue = useRecoilValue(getTaskTeamAtom);

    const [isFetching, setIsFetching] = useState(true);
    const [projectTeam, setProjectTeam] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [showUpdateModal, setShowUpdateModal] = useState(false);
    const [Edit, setEdit] = useState(false);


    const addTaskSchema = yup.object().shape({
        name: yup.string().required(errorMessages.required),
        team: yup.string().required(errorMessages.required),
    });

    const initialValues = {
        name: getProjectIdValue?.name || '',
        team: getProjectIdValue?.team?.name || '',
        start_date: getProjectIdValue?.start_date || '',
        end_date: getProjectIdValue?.end_date || '',
        priority: getProjectIdValue?.priority || '',
        description: getProjectIdValue?.description || '',
    };

    const Priorities = [
        { text: 'URGENT', value: 'URGENT' },
        { text: 'NORMAL', value: 'NORMAL' },
        { text: 'LOWEST', value: 'LOWEST' },
    ]

    const fetchProjectsById = () => {
        setIsFetching(true)
        getProjectById(id).then((res) => {
            if (res.success) {
                setProjectIdAtom(res.data)
                setIsFetching(false)
            } else {
                setIsFetching(false)
            }
        });
    };

    const updateProjectFn = (values) => {

        setIsLoading(true)

        updateProject({
            project_id: id,
            name: values.name,
            description: values.description,
            start_date: values.start_date,
            end_date: values.end_date,
            priority: values.priority,
        })
            .then((res) => {
                if (res.success) {
                    setIsLoading(false)
                    setShowSuccessModal(true)
                } else {
                    setIsLoading(false)
                }
            });
    };

    const allTeam = getTeamValue?.data?.map((item) => {
        return {
            text: item.name,
            value: item.id,
        };
    });

    useEffect(() => {
        fetchProjectsById()
    }, [])

    if (isFetching) {
        return (
            <Loader />
        )
    }



    return (
        <div>
            <GoBack />
            <div className="mt-6">
                <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
                    <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                        {"Edit project"}
                    </h1>
                </div>

                <div className="bg-[#F5F5F5] pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
                    <Formik
                        initialValues={initialValues}
                        onSubmit={(values) => {
                            updateProjectFn(values);
                        }}
                        validationSchema={addTaskSchema}
                        enableReinitialize
                    >
                        {({ values, setFieldValue, handleChange }) => (
                            <Form>

                                <div className="">
                                    <div className="">
                                        <p><StatusTag
                                            status={getProjectIdValue.status} /> </p>
                                    </div>

                                    <div className="flex justify-end">
                                        {isEdit || Edit ? (
                                            <div>
                                                <CustomButton
                                                    leftIcon={<BiSave size={20} />}
                                                    className="!w-[102px] !h-10"
                                                    handleClick={() => { }}
                                                    title="Save"
                                                    type="submit"
                                                    isLoading={isLoading}
                                                    variant={
                                                        ButtonProperties.VARIANT.primary.name
                                                    }
                                                />
                                            </div>
                                        ) : (
                                            <div
                                                className="!w-[87px] !h-10 gap-2 cursor-pointer bg-purple-normal rounded flex justify-center items-center text-white"
                                                onClick={() => {
                                                    setEdit(true);
                                                }}
                                            >
                                                <AiOutlineEdit size={18} /> Edit
                                            </div>
                                        )}
                                    </div>
                                </div>

                                <div className="grid grid-cols-2 gap-8 mt-8">

                                    <div>
                                        <FormikCustomInput
                                            label="Project name *"
                                            id="name"
                                            name="name"
                                            placeholder="enter team name"
                                            type="text"
                                            inputClassName="!bg-transparent"
                                            value={values.name}
                                            disabled={!Edit && !isEdit}
                                        />
                                    </div>

                                    <div>
                                        <FormikCustomSelect
                                            label="Team *"
                                            options={allTeam}
                                            name="team"
                                            placeholder="Select team"
                                            onChange={(item: { value: string; text: string }) => {
                                                setFieldValue("team", item.value);
                                                setProjectTeam(item.value);
                                            }}
                                            value={values.team}
                                            disabled
                                        />
                                    </div>

                                </div>

                                <div className="grid grid-cols-2 gap-8 mt-8">

                                    <div>
                                        <FormikCustomDate
                                            label="Start date "
                                            value={moment(values.start_date)}
                                            inputClassName="border bg-transparent"
                                            name="start_date"
                                            onChange={(date) => {
                                                setFieldValue("start_date", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null);
                                            }}
                                            disabled={!Edit && !isEdit}
                                        />
                                    </div>

                                    <div>
                                        <FormikCustomDate
                                            label="End date "
                                            value={moment(values.end_date)}
                                            inputClassName="border bg-transparent"
                                            name="end_date"
                                            onChange={(date) => {
                                                setFieldValue("end_date", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null);
                                            }}
                                            disabled={!Edit && !isEdit}
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 gap-8 mt-8">

                                    <div>
                                        <FormikCustomSelect
                                            label="Priority "
                                            options={Priorities}
                                            name="priority"
                                            placeholder="Select priority"
                                            onChange={(item: { value: string; text: string }) => {
                                                setFieldValue("priority", item.value);
                                            }}
                                            value={values.priority}
                                            disabled={!Edit && !isEdit}
                                        />
                                    </div>

                                    <div className="flex flex-col gap-3 ">
                                        <label htmlFor="description">Description </label>
                                        <CustomQuillTextArea
                                            value={values.description}
                                            handleChange={handleChange}
                                            className="bg-transparent w-full h-[140px] p-2"
                                            placeholder="description"
                                            readOnly={!Edit && !isEdit}
                                        />

                                    </div>

                                </div>

                                {/* <div className="grid grid-cols-1 gap-8 mt-8">
                                        <div className="flex flex-col gap-2  ">
                                            <p className=" text-[1.1rem]">Color </p>
                                            <div className="grid grid-cols-10 border-2 border-gray-300 rounded-lg p-10 bg-white">
                                                {colorsPallette.map((item) => (
                                                    <div className={`h-[38px] w-[38px] rounded-full flex justify-center items-center ${item === projectColor ? "bg-purple-dark-hover" : "bg-none"}`}>
    
                                                        <div
                                                            key={item}
                                                            className={`h-[30px] w-[30px] rounded-full cursor-pointer`}
                                                            style={{ backgroundColor: item }}
                                                            onClick={() => { setProjectColor(item) }}
                                                        ></div>
    
                                                    </div>
                                                ))}
                                            </div>
                                            <p className="text-right mt-2">{projectColor}</p>
                                        </div>
                                    </div> */}

                            </Form>
                        )}
                    </Formik>
                </div>
            </div>

            <SuccessModal
                visibility={showSuccessModal}
                text="Project updated successfully."
                route="/task-management/projects"
            />

        </div>
    )

}

export default ViewProject;