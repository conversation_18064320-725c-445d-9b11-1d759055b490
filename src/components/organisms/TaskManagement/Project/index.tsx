import { FiPlus } from "react-icons/fi";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import { useNavigate } from "react-router-dom";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { useEffect, useState } from "react";
import { getProjectAtom } from "../../../../recoil/atom/TaskManagement";
import { useRecoilState, useRecoilValue } from "recoil";
import { getProjects, updateProjectStatus } from "../../../../api/TaskManagement";
import moment from "moment";
import { Eye, Receipt, UserEdit } from "iconsax-react";
import { PiTrash } from "react-icons/pi";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../../shared/hooks";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import { Form, Formik } from "formik";
import * as yup from "yup";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import { MdOutlineTipsAndUpdates } from "react-icons/md";
import StatusTag from "../../../atoms/StatusTag";



const Projects = () => {

    const navigate = useNavigate();

    const [, setProjectAtom] = useRecoilState(getProjectAtom);
    const getProjectValue = useRecoilValue(getProjectAtom);

    const [isLoading, setIsLoading] = useState(false);
    const [rowId, setRowId] = useState("");
    const [showDropdown, setShowDropdown] = useState<boolean>(false);
    const [isFetching, setIsFetching] = useState<boolean>(false);
    const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [singleprojectId, setSingleprojectId] = useState('');


    const node = useClickOutside(() => {
        setShowDropdown(false);
        setIsLoading(false);
        setRowId("");
    });

    const updateStatusSchema = yup.object().shape({
        status: yup.string().required(errorMessages.required),
    });

    const allStatus = [
        { text: 'ACTIVE', value: 'active' },
        { text: 'SUSPENDED', value: 'suspended' },
        { text: 'COMPLETED', value: 'completed' },
    ]

    const viewStatus = (id) => {
        setIsOpen(true)
        setSingleprojectId(id)
    }

    const fetchProjects = () => {
        getProjects().then((res) => {
            if (res.success) {
                setProjectAtom(res.data)
            } else {
                setIsLoading(false)
            }
        });
    };

    const updateStatus = async (values) => {
        setIsLoading(true)
        updateProjectStatus({
            project_id: singleprojectId,
            status: values.status,
        }).then((res) => {
            if (res.success) {
                fetchProjects()
                setIsLoading(false)
                setIsOpen(false)
            }
        }).catch(() => {
            setIsLoading(false)
            setIsOpen(false)
        })
    };




    useEffect(() => {
        fetchProjects()
    }, [])


    const columns = [
        {
            Header: "Name",
            accessor: "name",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Team ",
            accessor: "team.name",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Status",
            accessor: "status",
            Cell: (row: any) => <p>{row.cell.value ? <StatusTag status={row.cell.value} /> : "--"}</p>,
        },
        {
            Header: "Priority",
            accessor: "priority",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "State date",
            accessor: "start_date",
            Cell: (row: any) => <p>{moment(row.cell.value).format("DD-MM-YYYY") || "--"}</p>,
        },
        {
            Header: "End date",
            accessor: "end_date",
            Cell: (row: any) => <p>{moment(row.cell.value).format("DD-MM-YYYY") || "--"}</p>,
        },
        {
            Header: "",
            accessor: "action",
            Cell: (row: any) => (
                <div className="relative">
                    <FaEllipsisV
                        onClick={() => {
                            setShowDropdown(!showDropdown);
                            setRowId(row.cell.row.id);
                        }}
                        className="text-[#98A2B3] cursor-pointer"
                    />
                    {showDropdown && row.cell.row.id === rowId && (
                        <FilterDropdown>
                            <ul className="text-14 text-neutral-dark" ref={node}>
                                <li
                                    onClick={() =>
                                        navigate(`/task-management/projects/view-project`, {
                                            state: { id: row.cell.row.original.id },
                                        })
                                    }
                                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                                >
                                    <Eye size={18} />
                                    View
                                </li>
                                <li
                                    onClick={() => {
                                        navigate(`/task-management/projects/view-project`, {
                                            state: { isEdit: true, id: row.cell.row.original.id },
                                        });
                                    }}
                                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                                >
                                    <UserEdit size={18} />
                                    Edit
                                </li>
                                <li
                                    onClick={() => { viewStatus(row.cell.row.original.id) }}
                                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                                >
                                    <MdOutlineTipsAndUpdates size={18} />
                                    Update
                                </li>

                            </ul>
                        </FilterDropdown>
                    )}
                </div>
            ),
        },

    ];


    return (

        <>
            <div>
                {/* <div className="flex justify-end">
                    <CustomButton
                        className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
                        isTransparent={true}
                        handleClick={() => {
                            navigate("/task-management/projects/create-project");
                        }}
                        leftIcon={<FiPlus className="ml-3" size={20} />}
                        title="Create new project"
                    />
                </div> */}
                <div className=" my-10 py-[23px]">
                    {getProjectValue?.data?.length > 0 ? (
                        <CustomTable
                            data={getProjectValue?.data || []}
                            meta={getProjectValue?.meta || {}}
                            columns={columns}
                            header={
                                <div className="flex justify-between items-center h-[60px] px-2">
                                    <h1>
                                        All projects
                                    </h1>
                                    <div className="bg-black">
                                        <CustomButton
                                            className="!w-[250px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md "
                                            isTransparent={true}
                                            handleClick={() => {
                                                navigate("/task-management/projects/create-project");
                                            }}
                                            leftIcon={<FiPlus className="ml-3" size={20} />}
                                            title="Create new project"
                                        />
                                    </div>
                                </div>
                            }
                        />
                    ) : (
                        <div className="flex justify-center items-center py-[120px]">
                            <OrganizationEmptyState
                                buttonTitle="Create project"
                                handleClick={() => navigate("/task-management/projects/create-project")}
                            />
                        </div>
                    )}
                </div>
            </div>

            <CustomModal
                visibility={isOpen}
                toggleVisibility={setIsOpen}
            >

                <div className="">

                    <div className="bg-purple-light-hover px-10 py-[28px] flex justify-between  ">
                        <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                            Update project status
                        </h1>
                    </div>

                    <div className="bg-[#F5F5F5] pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
                        <Formik
                            initialValues={{
                                status: '',
                            }}
                            onSubmit={(values) => { updateStatus(values) }}
                            validationSchema={updateStatusSchema}
                            enableReinitialize
                        >
                            {({ values, setFieldValue }) => (
                                <Form>

                                    <div className="grid grid-cols-1 gap-8 mt-8">

                                        <div>
                                            <FormikCustomSelect
                                                label="Status"
                                                name="status"
                                                options={allStatus}
                                                value={values.status}
                                                onChange={(item: { value: string; text: string }) => {
                                                    setFieldValue("status", item.value);
                                                }}
                                            />
                                        </div>

                                    </div>

                                    <div className="mt-[40px] flex justify-end">
                                        <CustomButton
                                            type="submit"
                                            title="Update"
                                            handleClick={() => { }}
                                            isLoading={isLoading}
                                            size={ButtonProperties.SIZES.small}
                                            variant={ButtonProperties.VARIANT.primary.name}
                                        />
                                    </div>


                                </Form>
                            )}
                        </Formik>
                    </div>
                </div>

            </CustomModal>

        </>

    )
}

export default Projects;