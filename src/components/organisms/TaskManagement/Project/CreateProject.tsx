import { Form, Formik } from "formik";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import GoBack from "../../../atoms/Ui/GoBack";
import * as yup from "yup";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import moment from "moment";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { addProject, getTeam } from "../../../../api/TaskManagement";
import { useRecoilState, useRecoilValue } from "recoil";
import { getProjectAtom, getTaskTeamAtom } from "../../../../recoil/atom/TaskManagement";
import { colorsPallette } from "../../../atoms/CustomColors";
import { useEffect, useState } from "react";
import Loader from "../../../atoms/Loader";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import CustomQuillTextArea from "../../../atoms/CustomQillTextArea";



const CreateProject = () => {

    const [, setProjectAtom] = useRecoilState(getProjectAtom);
    const getProjectValue = useRecoilValue(getProjectAtom);

    const [, setTeamAtom] = useRecoilState(getTaskTeamAtom);
    const getTeamValue = useRecoilValue(getTaskTeamAtom);

    const [projectColor, setProjectColor] = useState('');
    const [projectTeam, setProjectTeam] = useState('');
    const [isFetching, setIsFetching] = useState(true);
    const [isLoading, setIsLoading] = useState(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false)


    const addTaskSchema = yup.object().shape({
        name: yup.string().required(errorMessages.required),
        team: yup.string().required(errorMessages.required),
    });

    // interface AddProjectProps {
    //     name: string,
    //     description: string,
    //     teamLead: string,
    // };

    const initialValues = {
        name: "",
        team: "",
        status: "",
        start_date: "",
        end_date: "",
        priority: "",
        description: "",
    };

    const Priorities = [
        { text: 'URGENT', value: 'URGENT' },
        { text: 'NORMAL', value: 'NORMAL' },
        { text: 'LOWEST', value: 'LOWEST' },
    ]

    const fetchTeams = () => {
        getTeam().then((res) => {
            if (res.success) {
                setTeamAtom(res.data)
                setIsFetching(false)
            } else {
                setIsFetching(false)
            }
        });
    };

    const allTeam = getTeamValue?.data?.map((item) => {
        return {
            text: item.name,
            value: item.id,
        };
    });


    useEffect(() => {
        fetchTeams()
    }, [])

    const createProject = (values) => {

        setIsLoading(true)

        addProject(
            {
                name: values.name,
                description: values.description,
                team_id: projectTeam,
                start_date: values.start_date,
                end_date: values.end_date,
                priority: values.priority,
            }

        ).then((res) => {
            if (res.success) {
                setIsLoading(false);
                setShowSuccessModal(true)
            }
        });
    }


    if (isFetching) {
        return (
            <Loader />
        )
    }


    return (
        <div>
            <GoBack />
            <div className="mt-6">
                <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
                    <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                        {"Create project"}
                    </h1>
                </div>

                <div className="bg-[#F5F5F5] pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
                    <Formik
                        initialValues={initialValues}
                        onSubmit={(values) => { createProject(values) }}
                        validationSchema={addTaskSchema}
                        enableReinitialize
                    >
                        {({ values, setFieldValue, handleChange }) => (
                            <Form>

                                <div className="grid grid-cols-2 gap-8 mt-8">

                                    <div>
                                        <FormikCustomInput
                                            label="Project name *"
                                            id="name"
                                            name="name"
                                            placeholder="enter team name"
                                            type="text"
                                            inputClassName="!bg-transparent"
                                            value={values.name}
                                        />
                                    </div>

                                    <div>
                                        <FormikCustomSelect
                                            label="Team *"
                                            options={allTeam}
                                            name="team"
                                            placeholder="Select team"
                                            onChange={(item: { value: string; text: string }) => {
                                                setFieldValue("team", item.value);
                                                setProjectTeam(item.value);
                                            }}
                                            value={values.team}
                                        />
                                    </div>

                                </div>

                                <div className="grid grid-cols-2 gap-8 mt-8">

                                    <div>
                                        <FormikCustomDate
                                            label="Start date "
                                            value={moment(values.start_date)}
                                            inputClassName="border bg-transparent"
                                            name="start_date"
                                            onChange={(date) => {
                                                setFieldValue("start_date", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null);
                                            }}
                                        />
                                    </div>

                                    <div>
                                        <FormikCustomDate
                                            label="End date "
                                            value={moment(values.end_date)}
                                            inputClassName="border bg-transparent"
                                            name="end_date"
                                            onChange={(date) => {
                                                setFieldValue("end_date", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null);
                                            }}
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 gap-8 mt-8">

                                    <div>
                                        <FormikCustomSelect
                                            label="Priority "
                                            options={Priorities}
                                            name="priority"
                                            placeholder="Select priority"
                                            onChange={(item: { value: string; text: string }) => {
                                                setFieldValue("priority", item.value);
                                            }}
                                            value={values.priority}
                                        />
                                    </div>

                                    <div className="flex flex-col gap-3 ">
                                        <label htmlFor="description">Description </label>
                                        <CustomQuillTextArea
                                            value={values.description}
                                            handleChange={handleChange}
                                            className="bg-transparent w-full h-[140px] p-2"
                                            placeholder="description"
                                        />
                                    </div>

                                </div>

                                {/* <div className="grid grid-cols-1 gap-8 mt-8">
                                    <div className="flex flex-col gap-2  ">
                                        <p className=" text-[1.1rem]">Color </p>
                                        <div className="grid grid-cols-10 border-2 border-gray-300 rounded-lg p-10 bg-white">
                                            {colorsPallette.map((item) => (
                                                <div className={`h-[38px] w-[38px] rounded-full flex justify-center items-center ${item === projectColor ? "bg-purple-dark-hover" : "bg-none"}`}>

                                                    <div
                                                        key={item}
                                                        className={`h-[30px] w-[30px] rounded-full cursor-pointer`}
                                                        style={{ backgroundColor: item }}
                                                        onClick={() => { setProjectColor(item) }}
                                                    ></div>

                                                </div>
                                            ))}
                                        </div>
                                        <p className="text-right mt-2">{projectColor}</p>
                                    </div>
                                </div> */}

                                <div className="mt-[50px] flex justify-end">
                                    <CustomButton
                                        type="submit"
                                        title="Create"
                                        handleClick={() => { }}
                                        isLoading={isLoading}
                                        size={ButtonProperties.SIZES.small}
                                        variant={ButtonProperties.VARIANT.primary.name}
                                    />
                                </div>

                            </Form>
                        )}
                    </Formik>
                </div>
            </div>

            <SuccessModal
                visibility={showSuccessModal}
                text="Project added successfully."
                route="/task-management/projects"
            />

        </div>
    )

}

export default CreateProject;