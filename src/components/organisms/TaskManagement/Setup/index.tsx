import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom';
import Label from './Label';
import TaskStatus from './TaskStatus';

const TaskSetup = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<number>(0);
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("activeTab");


  const setupTabs = [
    {
      title: "Status",
      component: <TaskStatus />,
      name: "LeaveRequests"
    },
    {
      title: "Label",
      component: <Label />,
      name: "LeaveType",
    },
  ];

  useEffect(() => {
    setActiveTab(queryParam || 0)

  }, [queryParam, activeTab]);

  return (
    <div>
      <div className=" bg-[#F4F4F6] flex justify-between">
        <div className="flex gap-10 py-1 px-1 justify-center items-center  bg-[#F4F4F6] rounded-md">
          {setupTabs.map((item, index) => (
            <div
              key={index}
              onClick={() => { setActiveTab(index); navigate(`/task-management/task-setup?activeTab=${index}&tab=${item.name}`) }}
              className={`cursor-pointer px-5 py-2 rounded  ${activeTab == index ? "bg-white" : "bg-none"}`}>
              <p className={`text-neutral-normal font-poppins-medium pb-2 px-5 text-center text-16 ${activeTab == index ? "text-purple-normal" : "text-gray-950"} `}> {item.title}</p>
            </div>
          ))}
        </div>
      </div>

      <div className='mt-10'>
        {setupTabs[activeTab].component}
      </div>
    </div>
  )
}

export default TaskSetup