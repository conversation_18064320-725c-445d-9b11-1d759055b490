import * as yup from "yup";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import { Form, Formik } from "formik";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { FiPlus } from "react-icons/fi";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import { UserEdit } from "iconsax-react";
import { PiTrash } from "react-icons/pi";
import { addStatus, deleteStatus, getStatus, updateStatus } from "../../../../api/TaskManagement";
import { useRecoilState, useRecoilValue } from "recoil";
import { geColorsAtom, getStatusAtom, getStatusDetailAtom } from "../../../../recoil/atom/TaskManagement";
import Loader from "../../../atoms/Loader";
import useClickOutside from "../../../shared/hooks";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { clustarkToast } from "../../../atoms/Toast";
import { colorsPallette } from "../../../atoms/CustomColors";




const TaskStatus = () => {

    const navigate = useNavigate();

    const node = useClickOutside(() => {
        setShowDropdown(false);
        setRowId(0);
    });

    const [isOpen, setIsOpen] = useState(false)
    const [statusColor, setStatusColor] = useState('')
    const [isFetching, setIsFetching] = useState(true)
    const [rowId, setRowId] = useState(0);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [showDropdown, setShowDropdown] = useState<boolean>(false);
    const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
    const [showDetails, setShowDetails] = useState<boolean>(false);

    const [, setColorsAtom] = useRecoilState(geColorsAtom);
    const getColorsValue = useRecoilValue(geColorsAtom);

    const [, setStatusAtom] = useRecoilState(getStatusAtom);
    const getStatusValue = useRecoilValue(getStatusAtom);

    const [, setStatusDetailsAtom] = useRecoilState(getStatusDetailAtom);
    const getStatusDetailsValue = useRecoilValue(getStatusDetailAtom);


    const addStatusSchema = yup.object().shape({
        description: yup.string().required(errorMessages.required),
        name: yup.string().required(errorMessages.required),
    });


    const editStatusSchema = yup.object().shape({
        description: yup.string().required(errorMessages.required),
        name: yup.string().required(errorMessages.required),
    });


    const initialValues = {
        name: "",
        description: "",
    };


    const fetchStatus = () => {
        getStatus().then((res) => {
            if (res.success) {
                setStatusAtom(res.data)
                setIsFetching(false)
            } else {
                setIsFetching(false)
            }
        });
    };


    const creatStatus = (values) => {

        if (!statusColor) {
            clustarkToast(
                NotificationTypes.ERROR,
                "select a color"
            );
        } else {
            setIsLoading(true)
            addStatus(
                {
                    name: values.name,
                    description: values.description,
                    color: statusColor,
                }
            ).then((res) => {
                if (res.success) {
                    setStatusColor('')
                    setIsOpen(false)
                    setStatusAtom(res)
                    fetchStatus()
                    setIsLoading(false)
                }
            });
        }

    };


    const viewStatus = (data: any) => {
        setShowDetails(true);
        setStatusDetailsAtom(data)
    }


    const editStatus = (values) => {

        if (!(statusColor || getStatusDetailsValue.color)) {
            clustarkToast(
                NotificationTypes.ERROR,
                "select a color"
            );
        } else {
            setIsLoading(true)
            updateStatus(
                {
                    status_id: getStatusDetailsValue.id,
                    name: values.name,
                    description: values.description,
                    color: getStatusDetailsValue.color || statusColor,
                }
            ).then((res) => {
                if (res.success) {
                    setShowDetails(false)
                    fetchStatus()
                    setIsLoading(false)
                }
            });
        }

    };


    const removeStatus = (id) => {

        deleteStatus(
            {
                status_id: id,
            }
        ).then((res) => {
            if (res.success) {
                setShowDetails(false)
                fetchStatus()
                setShowDeleteWarn(false)
                setShowDropdown(false)
            }
        });

    }


    useEffect(() => {
        fetchStatus()
    }, [])


    const columns = [
        {
            Header: "Name",
            accessor: "name",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Indicator",
            accessor: "color",
            Cell: (row: any) => (
                <div className="flex items-center gap-2">
                    <div
                        className="h-[15px] w-[15px] rounded-full"
                        style={{ backgroundColor: row.cell.value }}
                    >
                    </div>

                </div>
            ),

        },
        {
            Header: "Description ",
            accessor: "description",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },

        {
            Header: "",
            accessor: "action",
            Cell: (row: any) => (
                <div className="relative">
                    <FaEllipsisV
                        onClick={() => {
                            setShowDropdown(!showDropdown);
                            setRowId(row.cell.row.id);
                        }}
                        className="text-[#98A2B3] cursor-pointer"
                    />
                    {showDropdown && row.cell.row.id === rowId && (
                        <FilterDropdown>
                            <ul className="text-14 text-neutral-dark" ref={node}>
                                <li
                                    onClick={() => { viewStatus(row.cell.row.original) }}
                                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                                >
                                    <UserEdit size={18} />
                                    Edit
                                </li>
                                {showDeleteWarn ? (
                                    <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                                        <div className="flex gap-3">
                                            <PiTrash size={18} />
                                            Are you sure?
                                        </div>
                                        <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                                            <CustomButton
                                                title="Yes"
                                                handleClick={() => { removeStatus(row.cell.row.original.id) }
                                                }
                                                className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer hover:!text-white"
                                            />
                                            <span
                                                onClick={() => {
                                                    setShowDeleteWarn(false);
                                                }}
                                                className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                                            >
                                                No
                                            </span>
                                        </div>
                                    </li>
                                ) : (
                                    <li
                                        onClick={() => setShowDeleteWarn(true)}
                                        className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer"
                                    >
                                        <PiTrash size={18} />
                                        Delete
                                    </li>
                                )}
                            </ul>
                        </FilterDropdown>
                    )}
                </div>
            ),
        },


    ];


    if (isFetching) {
        return (
            <Loader />
        )
    }


    return (
        <>
            <div>
                {/* <div className="flex justify-end">
                    <CustomButton
                        className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
                        isTransparent={true}
                        handleClick={() => {
                            setIsOpen(true)
                        }}
                        leftIcon={<FiPlus className="ml-3" size={20} />}
                        title="Add status"
                    />
                </div> */}
                <div className=" my-10 py-[23px]">
                    {getStatusValue?.data?.length > 0 ? (
                        <CustomTable
                            data={getStatusValue.data || []}
                            meta={getStatusValue?.meta || {}}
                            columns={columns}
                            hideSearch
                            header={
                                <div className="flex justify-between items-center h-[60px] px-2">
                                    <h1>
                                        All status
                                    </h1>
                                    <div className="bg-black">
                                        <CustomButton
                                            className="!w-[250px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md "
                                            isTransparent={true}
                                            handleClick={() => {
                                                setIsOpen(true)
                                            }}
                                            leftIcon={<FiPlus className="ml-3" size={20} />}
                                            title="Add status"
                                        />
                                    </div>
                                </div>
                            }
                        />
                    ) : (
                        <div className="flex justify-center items-center py-[120px]">
                            <OrganizationEmptyState
                                buttonTitle="Create status"
                                handleClick={() => setIsOpen(true)}
                            />
                        </div>
                    )}
                </div>
            </div>
            <CustomModal
                toggleVisibility={setIsOpen}
                visibility={isOpen}
            >

                <div className="">

                    <div className="bg-purple-light-hover px-10 py-[28px] flex justify-between  ">
                        <h1 className="font-poppins-medium text-18 text-purple-normal-hover ">
                            Add status
                        </h1>
                    </div>

                    <div className="bg-[#F5F5F5] pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
                        <Formik
                            initialValues={initialValues}
                            onSubmit={(values) => { creatStatus(values) }}
                            validationSchema={addStatusSchema}
                            enableReinitialize
                        >
                            {({ values, handleChange }) => (

                                <Form>

                                    <div className="grid grid-cols-1 gap-8 mt-8">

                                        <div>
                                            <FormikCustomInput
                                                label="Status name *"
                                                id="name"
                                                name="name"
                                                placeholder="e.g. Bug, Feature Request"
                                                type="text"
                                                inputClassName="!bg-transparent"
                                                value={values.name}
                                            />
                                        </div>

                                    </div>

                                    <div className="grid grid-cols-1 gap-8 mt-8">

                                        <div className="grid grid-cols-1 gap-4">
                                            <label htmlFor="description" className=" text-neutral-dark">Description </label>
                                            <textarea
                                                name="description"
                                                id="description"
                                                className="w-full h-[100px] rounded-md bg-transparent border focus:outline-none focus:ring-0 py-2 px-4 "
                                                placeholder="enter description"
                                                value={values.description}
                                                onChange={handleChange}
                                            >
                                            </textarea>
                                        </div>

                                    </div>

                                    <div className="">
                                        <p className="mt-8 text-[1.1rem]  text-neutral-dark">Color Indicator*</p>
                                        <div className="grid grid-cols-10 gap-3 mt-5">
                                            {colorsPallette.map((item) => (
                                                <div className={`h-[40px] w-[40px] rounded-full flex justify-center items-center ${item === statusColor ? "bg-purple-dark-hover" : "bg-none"}`}>
                                                    <div className={`h-[35px] w-[35px] rounded-full flex justify-center items-center ${item === statusColor && "bg-white"} `}>
                                                        <div
                                                            key={item}
                                                            className={`h-[20px] w-[20px] rounded-full cursor-pointer`}
                                                            style={{ backgroundColor: item }}
                                                            onClick={() => { setStatusColor(item) }}
                                                        ></div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                        <p className="text-right mt-2">{statusColor}</p>
                                    </div>

                                    <div className="mt-[50px] flex justify-end">
                                        <CustomButton
                                            type="submit"
                                            title="Save"
                                            handleClick={() => { }}
                                            isLoading={isLoading}
                                            size={ButtonProperties.SIZES.small}
                                            variant={ButtonProperties.VARIANT.primary.name}
                                        />
                                    </div>

                                </Form>

                            )}
                        </Formik>
                    </div>
                </div>

            </CustomModal>

            <CustomModal
                toggleVisibility={setShowDetails}
                visibility={showDetails}
            >

                <div className="">

                    <div className="bg-purple-light-hover px-10 py-[28px] flex justify-between  ">
                        <h1 className="font-poppins-medium text-18 text-purple-normal-hover ">
                            Edit status
                        </h1>
                    </div>

                    <div className="bg-[#F5F5F5] pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
                        <Formik
                            initialValues={{
                                name: getStatusDetailsValue?.name || '',
                                color: getStatusDetailsValue?.color || '',
                                description: getStatusDetailsValue?.description || '',
                            }}
                            onSubmit={(values) => { editStatus(values) }}
                            validationSchema={editStatusSchema}
                            enableReinitialize
                        >
                            {({ values, handleChange }) => (

                                <Form>

                                    <div className="grid grid-cols-1 gap-8 mt-8">

                                        <div>
                                            <FormikCustomInput
                                                label="Status name *"
                                                id="name"
                                                name="name"
                                                placeholder="e.g. Bug, Feature Request"
                                                type="text"
                                                inputClassName="!bg-transparent"
                                                value={values.name}
                                            />
                                        </div>

                                    </div>

                                    <div className="grid grid-cols-1 gap-8 mt-8">

                                        <div className="grid grid-cols-1 gap-4">
                                            <label htmlFor="description" className=" text-neutral-dark">Description </label>
                                            <textarea
                                                name="description"
                                                id="description"
                                                className="w-full h-[80px] rounded-md bg-transparent border focus:outline-none focus:ring-0 py-2 px-4 "
                                                placeholder="enter description"
                                                value={values.description}
                                                onChange={handleChange}
                                            >
                                            </textarea>
                                        </div>

                                    </div>

                                    <div className="">
                                        <p className="mt-8 text-[1.1rem] text-neutral-dark">Color Indicator *</p>
                                        <div className="grid grid-cols-10 gap-3 mt-5">
                                            {colorsPallette.map((item) => (
                                                <div className={`h-[40px] w-[40px] rounded-full flex justify-center items-center ${item === statusColor ? "bg-purple-dark-hover" : "bg-none"}`}>
                                                    <div className={`h-[35px] w-[35px] rounded-full flex justify-center items-center ${item === statusColor && "bg-white"} `}>
                                                        <div
                                                            key={item}
                                                            className={`h-[30px] w-[30px] rounded-full cursor-pointer`}
                                                            style={{ backgroundColor: item }}
                                                            onClick={() => { setStatusColor(item) }}
                                                        ></div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                        <p className="text-right mt-2">{statusColor}</p>
                                    </div>

                                    <div className="mt-[120px] flex justify-end">
                                        <CustomButton
                                            type="submit"
                                            title="Save"
                                            handleClick={() => { }}
                                            isLoading={isLoading}
                                            size={ButtonProperties.SIZES.small}
                                            variant={ButtonProperties.VARIANT.primary.name}
                                        />
                                    </div>

                                </Form>

                            )}
                        </Formik>
                    </div>
                </div>

            </CustomModal>
        </>
    )
}


export default TaskStatus;