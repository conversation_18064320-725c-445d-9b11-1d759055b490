import { Form, Formik } from "formik";
import * as yup from "yup";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import { FiPlus } from "react-icons/fi";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import { useNavigate } from "react-router-dom";
import { useEffect, useState, } from "react";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import { geColors<PERSON>tom, getLabelDetailAtom, getLabels<PERSON>tom } from "../../../../recoil/atom/TaskManagement";
import { useRecoilState, useRecoilValue } from "recoil";
import { addLabels, deleteLabel, getLabels, updateLabel } from "../../../../api/TaskManagement";
import Loader from "../../../atoms/Loader";
import { UserEdit } from "iconsax-react";
import { PiTrash } from "react-icons/pi";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import { FaEllipsisV } from "react-icons/fa";
import useClickOutside from "../../../shared/hooks";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { colorsPallette } from "../../../atoms/CustomColors";



const Label = () => {

    const navigate = useNavigate();

    const node = useClickOutside(() => {
        setShowDropdown(false);
        setRowId(0);
    });

    const [labelColor, setLabelColor] = useState('')
    const [isOpen, setIsOpen] = useState(false)
    const [isFetching, setIsFetching] = useState(true)
    const [rowId, setRowId] = useState(0);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [showDropdown, setShowDropdown] = useState<boolean>(false);
    const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
    const [showDetails, setShowDetails] = useState<boolean>(false);


    const [, setColorsAtom] = useRecoilState(geColorsAtom);
    const getColorsValue = useRecoilValue(geColorsAtom);

    const [, setLabelsAtom] = useRecoilState(getLabelsAtom);
    const getLabelsValue = useRecoilValue(getLabelsAtom);

    const [, setLabelDetailsAtom] = useRecoilState(getLabelDetailAtom);
    const getLabelDetailsValue = useRecoilValue(getLabelDetailAtom);


    const addLabelSchema = yup.object().shape({
        // description: yup.string().required(errorMessages.required),
        name: yup.string().required(errorMessages.required),
    });

    const editLabelSchema = yup.object().shape({
        // description: yup.string().required(errorMessages.required),
        name: yup.string().required(errorMessages.required),
    });

    const initialValues = {
        color: "",
        name: "",
        description: "",
    };


    const fetchLabels = () => {
        getLabels().then((res) => {
            if (res.success) {
                setLabelsAtom(res.data)
                setIsFetching(false)
            } else {
                setIsFetching(false)
            }
        });
    };

    const createLabels = (values) => {

        if (!labelColor) {
            clustarkToast(
                NotificationTypes.ERROR,
                "select a color"
            );
        } else {
            setIsLoading(true)
            addLabels(
                {
                    name: values.name,
                    description: values.description,
                    color: labelColor,
                }
            ).then((res) => {
                if (res.success) {
                    setIsOpen(false)
                    setLabelsAtom(res)
                    fetchLabels()
                    setIsLoading(false)
                }
            });
        }
    };


    const viewStatus = (data: any) => {
        setShowDetails(true);
        setLabelDetailsAtom(data)
    }


    const editLabels = (values) => {

        if (!(labelColor || getLabelDetailsValue.color)) {
            clustarkToast(
                NotificationTypes.ERROR,
                "select a color"
            );
        } else {
            setIsLoading(true)
            updateLabel(
                {
                    label_id: getLabelDetailsValue.id,
                    name: values.name,
                    description: values.description,
                    color: labelColor || getLabelDetailsValue.color,
                }
            ).then((res) => {
                if (res.success) {
                    setShowDetails(false)
                    fetchLabels()
                    setIsLoading(false)
                }
            });
        }

    };


    const removeLabel = (id) => {

        deleteLabel(
            {
                label_id: id,
            }
        ).then((res) => {
            if (res.success) {
                setShowDetails(false)
                fetchLabels()
                setShowDeleteWarn(false)
                setShowDropdown(false)
            }
        });

    }


    useEffect(() => {
        fetchLabels()
    }, [])


    const columns = [
        {
            Header: "Name",
            accessor: "name",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Indicator",
            accessor: "color",
            Cell: (row: any) => (
                <div className="flex items-center gap-2">
                    <div
                        className="h-[15px] w-[15px] rounded-full"
                        style={{ backgroundColor: row.cell.value }}
                    >
                    </div>
                </div>
            ),
        },
        {
            Header: "Description",
            accessor: "description",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "",
            accessor: "action",
            Cell: (row: any) => (
                <div className="relative">
                    <FaEllipsisV
                        onClick={() => {
                            setShowDropdown(!showDropdown);
                            setRowId(row.cell.row.id);
                        }}
                        className="text-[#98A2B3] cursor-pointer"
                    />
                    {showDropdown && row.cell.row.id === rowId && (
                        <FilterDropdown>
                            <ul className="text-14 text-neutral-dark" ref={node}>
                                <li
                                    onClick={() => { viewStatus(row.cell.row.original) }}
                                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                                >
                                    <UserEdit size={18} />
                                    Edit
                                </li>
                                {showDeleteWarn ? (
                                    <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                                        <div className="flex gap-3">
                                            <PiTrash size={18} />
                                            Are you sure?
                                        </div>
                                        <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                                            <CustomButton
                                                title="Yes"
                                                handleClick={() => { removeLabel(row.cell.row.original.id) }
                                                }
                                                className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer hover:!text-white"
                                            />
                                            <span
                                                onClick={() => {
                                                    setShowDeleteWarn(false);
                                                }}
                                                className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                                            >
                                                No
                                            </span>
                                        </div>
                                    </li>
                                ) : (
                                    <li
                                        onClick={() => setShowDeleteWarn(true)}
                                        className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer"
                                    >
                                        <PiTrash size={18} />
                                        Delete
                                    </li>
                                )}
                            </ul>
                        </FilterDropdown>
                    )}
                </div>
            ),
        },
    ];


    if (isFetching) {
        return (
            <Loader />
        )
    }




    return (

        <>
            <div>

                {/* <div className="flex justify-end">
                    <CustomButton
                        className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
                        isTransparent={true}
                        handleClick={() => {
                            setIsOpen(true)
                        }}
                        leftIcon={<FiPlus className="ml-3" size={20} />}
                        title="Add label"
                    />
                </div> */}

                <div className="] my-10 py-[23px]">
                    {getLabelsValue?.data?.length > 0 ? (
                        <CustomTable
                            data={getLabelsValue.data || []}
                            meta={getLabelsValue?.meta || {}}
                            columns={columns}
                            hideSearch
                            header={
                                <div className="flex justify-between items-center h-[60px] px-2">
                                    <h1>
                                        All labels
                                    </h1>
                                    <div className="bg-black">
                                        <CustomButton
                                            className="!w-[250px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md "
                                            isTransparent={true}
                                            handleClick={() => {
                                                setIsOpen(true)
                                            }}
                                            leftIcon={<FiPlus className="ml-3" size={20} />}
                                            title="Add label"
                                        />
                                    </div>
                                </div>
                            }
                        />
                    ) : (
                        <div className="flex justify-center items-center py-[120px]">
                            <OrganizationEmptyState
                                buttonTitle="Create label"
                                handleClick={() => (setIsOpen(true))}
                            />
                        </div>
                    )}
                </div>
            </div>
            <CustomModal
                toggleVisibility={setIsOpen}
                visibility={isOpen}
            >

                <div className="">

                    <div className="bg-purple-light-hover px-10 py-[28px] flex justify-between  ">
                        <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                            Add Label
                        </h1>
                    </div>

                    <div className="bg-[#F5F5F5] pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
                        <Formik
                            initialValues={initialValues}
                            onSubmit={(values) => { createLabels(values) }}
                            validationSchema={addLabelSchema}
                            enableReinitialize
                        >
                            {({ values, handleChange }) => (
                                <Form>

                                    <div className="grid grid-cols-1 gap-8 mt-8">

                                        <div>
                                            <FormikCustomInput
                                                label="Label name *"
                                                id="name"
                                                name="name"
                                                placeholder="e.g. Bug, Feature Request"
                                                type="text"
                                                inputClassName="!bg-transparent"
                                                value={values.name}
                                            />
                                        </div>

                                    </div>

                                    <div className="grid grid-cols-1 gap-8 mt-8">

                                        <div className="grid grid-cols-1 gap-4">
                                            <label htmlFor="description" className=" text-neutral-dark">Description </label>
                                            <textarea
                                                name="description"
                                                id="description"
                                                className="w-full h-[100px] rounded-md bg-transparent border focus:outline-none focus:ring-0 py-2 px-4 "
                                                placeholder="enter description"
                                                value={values.description}
                                                onChange={handleChange}
                                            >
                                            </textarea>
                                        </div>

                                    </div>

                                    <div className="">
                                        <p className="mt-8 text-[1.1rem] text-neutral-dark">Color indicator *</p>

                                        <div className="grid grid-cols-10 gap-3 mt-5">
                                            {colorsPallette.map((item) => (
                                                <div className={`h-[40px] w-[40px] rounded-full flex justify-center items-center ${item === labelColor ? "bg-purple-dark-hover" : "bg-none"}`}>
                                                    <div className={`h-[35px] w-[35px] rounded-full flex justify-center items-center ${item === labelColor && "bg-white"} `}>
                                                        <div
                                                            key={item}
                                                            className={`h-[20px] w-[20px] rounded-full cursor-pointer`}
                                                            style={{ backgroundColor: item }}
                                                            onClick={() => { setLabelColor(item) }}
                                                        ></div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                        <p className="text-right mt-2">{labelColor}</p>
                                    </div>

                                    <div className="mt-[50px] flex justify-end">
                                        <CustomButton
                                            type="submit"
                                            title="Save"
                                            handleClick={() => { }}
                                            isLoading={isLoading}
                                            size={ButtonProperties.SIZES.small}
                                            variant={ButtonProperties.VARIANT.primary.name}
                                        />
                                    </div>


                                </Form>
                            )}
                        </Formik>
                    </div>
                </div>

            </CustomModal>

            <CustomModal
                toggleVisibility={setShowDetails}
                visibility={showDetails}
            >

                <div className="">

                    <div className="bg-purple-light-hover px-10 py-[28px] flex justify-between  ">
                        <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                            Edit label
                        </h1>
                    </div>

                    <div className="bg-[#F5F5F5] pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
                        <Formik
                            initialValues={{
                                name: getLabelDetailsValue?.name || '',
                                color: getLabelDetailsValue?.color || '',
                                description: getLabelDetailsValue?.description || '',
                            }}
                            onSubmit={(values) => { editLabels(values) }}
                            validationSchema={editLabelSchema}
                            enableReinitialize
                        >
                            {({ values, handleChange }) => (
                                <Form>

                                    <div className="grid grid-cols-2 gap-8 mt-8">
                                        <div>
                                            <FormikCustomInput
                                                label="Label name *"
                                                id="name"
                                                name="name"
                                                placeholder="e.g. Bug, Feature Request"
                                                type="text"
                                                inputClassName="!bg-transparent"
                                                value={values.name}
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 gap-8 mt-8">

                                        <div className="grid grid-cols-1 gap-4">
                                            <label htmlFor="description" className=" text-neutral-dark">Description </label>
                                            <textarea
                                                name="description"
                                                id="description"
                                                className="w-full h-[80px] rounded-md bg-transparent border focus:outline-none focus:ring-0 py-2 px-4 "
                                                placeholder="enter description"
                                                value={values.description}
                                                onChange={handleChange}
                                            >
                                            </textarea>
                                        </div>

                                    </div>

                                    <div className="">
                                        <p className="mt-8 text-[1.1rem] text-neutral-dark">Color indicator*</p>
                                        <div className="grid grid-cols-10 gap-3 mt-5">
                                            {colorsPallette.map((item) => (
                                                <div className={`h-[40px] w-[40px] rounded-full flex justify-center items-center ${item === labelColor ? "bg-purple-dark-hover" : "bg-none"}`}>
                                                    <div className={`h-[35px] w-[35px] rounded-full flex justify-center items-center ${item === labelColor && "bg-white"} `}>
                                                        <div
                                                            key={item}
                                                            className={`h-[20px] w-[20px] rounded-full cursor-pointer`}
                                                            style={{ backgroundColor: item }}
                                                            onClick={() => { setLabelColor(item) }}
                                                        ></div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                        <p className="text-right mt-2">{labelColor || getLabelDetailsValue.color}</p>
                                    </div>

                                    <div className="mt-[50px] flex justify-end">
                                        <CustomButton
                                            type="submit"
                                            title="Update label"
                                            handleClick={() => { }}
                                            isLoading={isLoading}
                                            size={ButtonProperties.SIZES.small}
                                            variant={ButtonProperties.VARIANT.primary.name}
                                        />
                                    </div>


                                </Form>
                            )}
                        </Formik>
                    </div>
                </div>

            </CustomModal>

        </>

    )


}


export default Label;