import { Form, Formik } from "formik";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import { AiOutlineEdit } from "react-icons/ai";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { BiSave } from "react-icons/bi";
import { ButtonProperties, errorMessages, getNameInitials } from "../../../shared/helpers";
import { PiShareFatLight, PiTrash } from "react-icons/pi";
import { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { getStaffAtom } from "../../../../recoil/atom/staff";
import * as yup from "yup";
import { useRecoilState, useRecoilValue } from "recoil";
import { getStaff } from "../../../../api/staff";
import { Edit, Eye, UserEdit } from "iconsax-react";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import { FaEllipsisV, FaPlus } from "react-icons/fa";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import useClickOutside from "../../../shared/hooks";
import { FaTrash } from "react-icons/fa6";
import { addTeamMember, deleteTeamMember, getTeamById, getTeamMember, updateTeam } from "../../../../api/TaskManagement";
import { getTaskTeamByIdAtom, getTaskTeamMemberAtom } from "../../../../recoil/atom/TaskManagement";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import Loader from "../../../atoms/Loader";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";



const TeamDetail = () => {

    const node = useClickOutside(() => {
        setShowDropdown(false);
        setRowId(0);
    });

    const isMounted = useRef(false);
    const navigate = useNavigate();
    const location = useLocation();
    const { isEdit, id } = location?.state || "";


    const handleSubmit = () => { }

    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isEditActive, setIsEditActive] = useState<boolean>(false);
    const [isFetching, setIsFetching] = useState<boolean>(false);
    const [rowId, setRowId] = useState(0);
    const [showDropdown, setShowDropdown] = useState<boolean>(false);
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
    const [teamLeadId, setTeamLeadId] = useState('');
    const [isDone, setIsDone] = useState(false);

    const [, setStaffAtom] = useRecoilState(getStaffAtom);
    const getStaffValue = useRecoilValue(getStaffAtom);

    const [, setTeamIdAtom] = useRecoilState(getTaskTeamByIdAtom);
    const getTeamIdValue = useRecoilValue(getTaskTeamByIdAtom);

    const [, setTeamMemberAtom] = useRecoilState(getTaskTeamMemberAtom);
    const getTeamMemberValue = useRecoilValue(getTaskTeamMemberAtom);


    const editTeamSchema = yup.object().shape({
        name: yup.string().required(errorMessages.required),
    });

    const addTeamMemberSchema = yup.object().shape({
        staff: yup.string().required(errorMessages.required),
    })


    const initialValues = {
        name: getTeamIdValue?.name || '',
        description: getTeamIdValue?.description || '',
        teamLead: getTeamIdValue?.teamLead?.user?.first_name || '',
    };

    const fetchStaff = async () => {
        setIsFetching(true)
        getStaff().then((res) => {
            if (res.success) {
                setStaffAtom(res.data);
                setIsFetching(false)
            }
        })
    };

    const fetchTeam = async () => {
        setIsFetching(true)
        getTeamById(id).then((res) => {
            if (res.success) {
                setTeamIdAtom(res.data);
                setIsFetching(false)

            }
        })
    };

    const fetchTeamMembers = async () => {
        setIsFetching(true)
        getTeamMember(id).then((res) => {
            if (res.success) {
                setTeamMemberAtom(res.data);
                setIsFetching(false)
            }
        })
    };

    const createTeamMember = async (values) => {
        setIsLoading(true)
        addTeamMember({
            team_id: id,
            staff_id: values.staff,
        }).then((res) => {
            if (res.success) {
                fetchTeam();
                setIsLoading(false)
                setIsOpen(false)
                fetchTeamMembers()
            }
        }).catch(() => {
            setIsLoading(false)
            setIsOpen(false)
        })
    };




    const EditTeam = async (values) => {

        setIsLoading(true)

        updateTeam({
            name: values.name,
            description: values.description,
            team_lead_id: teamLeadId,
            team_id: id,
        }).then((res) => {
            if (res.success) {
                fetchTeam();
                setIsLoading(false);
                setIsOpen(false);
                fetchTeamMembers();

                setIsDone(true);

            }
        }).catch(() => {
            setIsLoading(false)
            setIsOpen(false)
        })

    };

    const removeTeamMember = async (staffId) => {

        setIsLoading(true)

        deleteTeamMember({
            team_id: id,
            staff_id: staffId,
        }).then((res) => {
            if (res.success) {
                fetchTeam();
                fetchTeamMembers()
                setShowDeleteWarn(false)
                setIsLoading(false)
            }
        }).catch(() => {
            setIsLoading(false)
            setShowDeleteWarn(false)
        })
    };

    useEffect(() => {
        fetchStaff();
        fetchTeam();
        fetchTeamMembers();
    }, []);


    const staffs = getStaffValue?.data?.map((item) => ({
        text: item?.staffPersonalInformations?.first_name + " " + item?.staffPersonalInformations?.last_name,
        value: item.id
    }));



    const columns = [
        {
            Header: "First name",
            accessor: "staff.user.first_name",
            // Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Last name",
            accessor: "staff.user.last_name",
            // Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Gender",
            accessor: "staff.user.gender",
            // Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Role",
            accessor: "staff.job_title",
            // Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "",
            accessor: "action",
            Cell: (row: any) => (
                <div className="relative">
                    <FaEllipsisV
                        onClick={() => {
                            setShowDropdown(!showDropdown);
                            setRowId(row.cell.row.id);
                        }}
                        className="text-[#98A2B3] cursor-pointer"
                    />
                    {showDropdown && row.cell.row.id === rowId && (
                        <FilterDropdown>
                            <ul className="text-14 text-neutral-dark" ref={node}>

                                {showDeleteWarn ? (
                                    <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                                        <div className="flex gap-3">
                                            <PiTrash size={18} />
                                            Are you sure?
                                        </div>
                                        <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                                            <CustomButton
                                                title="Yes"
                                                isLoading={isLoading}
                                                handleClick={() => { removeTeamMember(row.cell.row.original.id) }
                                                }
                                                className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer hover:!text-white"
                                            />
                                            <span
                                                onClick={() => {
                                                    setShowDeleteWarn(false);
                                                }}
                                                className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                                            >
                                                No
                                            </span>
                                        </div>
                                    </li>
                                ) : (
                                    <li
                                        onClick={() => setShowDeleteWarn(true)}
                                        className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer"
                                    >
                                        <PiTrash size={18} />
                                        Delete
                                    </li>
                                )}
                            </ul>
                        </FilterDropdown>
                    )}
                </div>
            ),
        },

    ];


    if (isFetching) {
        return (
            <Loader />
        )
    }




    return (

        <>
            <div className="">
                <div className="">
                    <div className="">
                        <div>

                            <div className="bg-white px-8 pt-7 pb-16 rounded-xl">

                                <Formik
                                    initialValues={initialValues}
                                    onSubmit={(values) => { EditTeam(values) }}
                                    validationSchema={editTeamSchema}
                                    enableReinitialize
                                >
                                    {({ values, handleChange, setFieldValue }) => (
                                        <Form>
                                            <div>
                                                <div className="flex justify-between border-b pb-6 border-[#E8E8E8] rounded-[10px]">
                                                    <div className="flex gap-6">

                                                        <div className="w-[70px] h-[70px] p-2 bg-purple-light-hover rounded-full">
                                                            <div className=" w-full h-full flex justify-center items-center bg-purple-light-active rounded-full">
                                                                <p className="text-[40px] text-purple-normal font-poppins-medium">
                                                                    {getNameInitials(
                                                                        getTeamIdValue.name || ''
                                                                    )}
                                                                </p>
                                                            </div>
                                                        </div>

                                                        <div className="flex justify-center items-center">
                                                            <p className="ext-neutral-dark font-medium font-poppins-medium text-20" >{getTeamIdValue.name}</p>
                                                        </div>
                                                    </div>
                                                    <div className="flex justify-center gap-4 items-center">

                                                        <div>
                                                            {(isEditActive || isEdit) && !isDone ? (
                                                                <div>
                                                                    <CustomButton
                                                                        leftIcon={<BiSave size={20} />}
                                                                        className="!w-[102px] !h-10"
                                                                        title="Save"
                                                                        type="submit"
                                                                        handleClick={() => { }}
                                                                        isLoading={isLoading}
                                                                        variant={
                                                                            ButtonProperties.VARIANT.primary.name
                                                                        }
                                                                    />
                                                                </div>
                                                            ) :
                                                                (
                                                                    <div
                                                                        className="!w-[87px] !h-10 gap-2 cursor-pointer bg-purple-normal rounded flex justify-center items-center text-white"
                                                                        onClick={() => {
                                                                            setIsEditActive(true);
                                                                        }}
                                                                    >
                                                                        <AiOutlineEdit size={18} /> Edit
                                                                    </div>
                                                                )
                                                            }
                                                        </div>

                                                    </div>
                                                </div>

                                                <div className="mt-8 ">
                                                    <div className="">
                                                        <div className=" pt-[15px] pb-[10px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
                                                            <div className="grid grid-cols-2 gap-8 mt-8">

                                                                <div>
                                                                    <FormikCustomInput
                                                                        label="Team name *"
                                                                        id="name"
                                                                        name="name"
                                                                        placeholder="enter team name"
                                                                        type="text"
                                                                        inputClassName=""
                                                                        value={values.name}
                                                                        disabled={!isEdit && !isEditActive}
                                                                    />
                                                                </div>

                                                                <div>
                                                                    <FormikCustomSelect
                                                                        label="Team lead"
                                                                        name="teamlead"
                                                                        options={staffs}
                                                                        value={values.teamLead}
                                                                        onChange={(item: { value: string; text: string }) => {
                                                                            setFieldValue("teamLead", item.value);
                                                                            setTeamLeadId(item.value || '');
                                                                        }}
                                                                        disabled={!isEdit && !isEditActive}
                                                                    />
                                                                </div>

                                                            </div>

                                                            <div className="grid grid-cols-1 gap-8 mt-8">

                                                                <div className="grid grid-cols-1 gap-4">
                                                                    <label htmlFor="description">Description </label>
                                                                    <textarea
                                                                        name="description"
                                                                        id="description"
                                                                        className="w-full h-[80px] rounded-md  border focus:outline-none focus:ring-0 py-2 px-4 "
                                                                        placeholder="enter description"
                                                                        value={values.description}
                                                                        onChange={handleChange}
                                                                        disabled={!isEdit && !isEditActive}
                                                                    >
                                                                    </textarea>
                                                                </div>

                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </Form>
                                    )}
                                </Formik>

                            </div>



                        </div>

                        <div className="bg-[#F5F5F5] my-10 px-4 py-[23px]">
                            <div className="mb-4 flex justify-end">
                                <CustomButton
                                    leftIcon={<FaPlus size={14} />}
                                    className="!w-[120px] !h-10"
                                    title="Add memeber"
                                    type="submit"
                                    handleClick={() => { setIsOpen(true) }}
                                    variant={ButtonProperties.VARIANT.primary.name}
                                />
                            </div>
                            {getTeamMemberValue?.data?.length > 0 ? (
                                <CustomTable
                                    data={getTeamMemberValue?.data || []}
                                    meta={getTeamMemberValue?.meta || {}}
                                    columns={columns}
                                    header={
                                        <div>
                                            <h1 className="font-poppins-medium text-purple-normal-active mb-4">
                                                Team members
                                            </h1>
                                        </div>
                                    }
                                />
                            ) : (
                                <div className="flex justify-center items-center py-[120px]">
                                    <OrganizationEmptyState
                                        buttonTitle="Create team"
                                        handleClick={() => navigate("/task-management/teams/create-team")}
                                    />
                                </div>
                            )}
                        </div>

                    </div>
                </div >
            </div >

            <CustomModal
                visibility={isOpen}
                toggleVisibility={setIsOpen}
            >

                <div className="">

                    <div className="bg-purple-light-hover px-10 py-[28px] flex justify-between  ">
                        <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                            Add team member
                        </h1>
                    </div>

                    <div className="bg-[#F5F5F5] pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
                        <Formik
                            initialValues={{
                                staff: '',
                            }}
                            onSubmit={(values) => { createTeamMember(values) }}
                            validationSchema={addTeamMemberSchema}
                            enableReinitialize
                        >
                            {({ values, setFieldValue }) => (
                                <Form>

                                    <div className="grid grid-cols-1 gap-8 mt-8">

                                        <div>
                                            <FormikCustomSelect
                                                label="Staff name"
                                                name="staff"
                                                options={staffs}
                                                value={values.staff}
                                                onChange={(item: { value: string; text: string }) => {
                                                    setFieldValue("staff", item.value);
                                                }}
                                            />
                                        </div>

                                    </div>

                                    <div className="mt-[40px] flex justify-end">
                                        <CustomButton
                                            type="submit"
                                            title="Add member"
                                            handleClick={() => { }}
                                            isLoading={isLoading}
                                            size={ButtonProperties.SIZES.small}
                                            variant={ButtonProperties.VARIANT.primary.name}
                                        />
                                    </div>


                                </Form>
                            )}
                        </Formik>
                    </div>
                </div>

            </CustomModal>

        </>

    )

}


export default TeamDetail;