import { useNavigate } from "react-router-dom";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { FiPlus } from "react-icons/fi";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import useClickOutside from "../../../shared/hooks";
import { useEffect, useState } from "react";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import { Eye, UserEdit } from "iconsax-react";
import { PiTrash } from "react-icons/pi";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import { getTeam } from "../../../../api/TaskManagement";
import { useRecoilState, useRecoilValue } from "recoil";
import { getTaskTeamAtom } from "../../../../recoil/atom/TaskManagement";


const Teams = () => {

    const navigate = useNavigate();

    const node = useClickOutside(() => {
        setShowDropdown(false);
        setRowId(0);
    });


    const [isOpen, setIsOpen] = useState(false)
    const [statusColor, setStatusColor] = useState('')
    const [isFetching, setIsFetching] = useState(true)
    const [rowId, setRowId] = useState(0);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [showDropdown, setShowDropdown] = useState<boolean>(false);
    const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
    const [showDetails, setShowDetails] = useState<boolean>(false);

    const [, setTeamAtom] = useRecoilState(getTaskTeamAtom);
    const getTeamValue = useRecoilValue(getTaskTeamAtom);


    const fetchTeams = () => {
        getTeam().then((res) => {
            if (res.success) {
                setTeamAtom(res.data)
                setIsFetching(false)
            } else {
                setIsFetching(false)
            }
        });
    };


    useEffect(() => {
        fetchTeams()
    }, [])



    const columns = [
        {
            Header: "Team name",
            accessor: "name",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Team lead",
            accessor: "teamLead.user.first_name",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        // {
        //     Header: "Member(s)",
        //     accessor: "members",
        //     // Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        // },
        {
            Header: "Description",
            accessor: "description",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "",
            accessor: "action",
            Cell: (row: any) => (
                <div className="relative">
                    <FaEllipsisV
                        onClick={() => {
                            setShowDropdown(!showDropdown);
                            setRowId(row.cell.row.id);
                        }}
                        className="text-[#98A2B3] cursor-pointer"
                    />
                    {showDropdown && row.cell.row.id === rowId && (
                        <FilterDropdown>
                            <ul className="text-14 text-neutral-dark" ref={node}>
                                <li
                                    onClick={() => {
                                        navigate(`/task-management/teams/details`, {
                                            state: { isEdit: "true", id: row.cell.row.original.id },
                                        });
                                    }}
                                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                                >
                                    <UserEdit size={18} />
                                    Edit
                                </li>

                                <li
                                    onClick={() =>
                                        navigate(`/task-management/teams/details`, {
                                            state: { id: row.cell.row.original.id },
                                        })
                                    }
                                    className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px]  cursor-pointer"
                                >
                                    <Eye size={18} />
                                    View
                                </li>

                            </ul>
                        </FilterDropdown>
                    )}
                </div>
            ),
        },

    ];


    return (

        <>
            <div>
                {/* <div className="flex justify-end">
                    <CustomButton
                        className="!w-[210px]  !border-none !font-normal !font-poppins-medium shadow-md"
                        isTransparent={true}
                        handleClick={() => {
                            navigate("/task-management/teams/create-team");
                        }}
                        leftIcon={<FiPlus className="ml-3" size={20} />}
                        title="Create team"
                    />
                </div> */}
                <div className=" my-10 py-[23px]">
                    {getTeamValue?.data?.length > 0 ? (
                        <CustomTable
                            data={getTeamValue.data || []}
                            meta={getTeamValue?.meta || {}}
                            columns={columns}
                            hideSearch
                            header={
                                <div className="flex justify-between items-center h-[60px] px-2">
                                    <h1>
                                        All team
                                    </h1>
                                    <div className="bg-black">
                                        <CustomButton
                                            className="!w-[250px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md "
                                            isTransparent={true}
                                            handleClick={() => {
                                                navigate("/task-management/teams/create-team");
                                            }}
                                            leftIcon={<FiPlus className="ml-3" size={20} />}
                                            title="Create team"
                                        />
                                    </div>
                                </div>
                            }
                        />
                    ) : (
                        <div className="flex justify-center items-center py-[120px]">
                            <OrganizationEmptyState
                                buttonTitle="Create team"
                                handleClick={() => navigate("/task-management/teams/create-team")}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>

    )
}

export default Teams;