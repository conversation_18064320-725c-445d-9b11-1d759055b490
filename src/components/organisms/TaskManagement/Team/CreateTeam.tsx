import { Form, Formik } from "formik";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import FormikCustomPhoneInput from "../../../atoms/CustomInput/FormikCustomPhoneInput";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import GoBack from "../../../atoms/Ui/GoBack";
import * as yup from "yup";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import { useEffect, useState } from "react";
import { useRecoilState, useRecoilValue } from "recoil";
import { getStaffAtom } from "../../../../recoil/atom/staff";
import { getStaff } from "../../../../api/staff";
import { addTeam } from "../../../../api/TaskManagement";
import { getTaskTeamAtom } from "../../../../recoil/atom/TaskManagement";


const CreateTeam = () => {

    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false)

    const [, setStaffAtom] = useRecoilState(getStaffAtom);
    const getStaffValue = useRecoilValue(getStaffAtom);

    const [, setTeamAtom] = useRecoilState(getTaskTeamAtom);
    const getTeamValue = useRecoilValue(getTaskTeamAtom);

    const addTeamSchema = yup.object().shape({
        name: yup.string().required(errorMessages.required),
    });

    // interface AddTeamProps {
    //     name: string,
    //     description: string,
    //     teamLead: string,
    // };

    const initialValues = {
        name: "",
        description: "",
        teamLead: "",
    };

    const fetchStaff = async () => {
        getStaff().then((res) => {
            if (res.success) {
                setStaffAtom(res.data);
            }
        })
    };

    useEffect(() => {
        fetchStaff();
    }, []);

    const staffs = getStaffValue?.data?.map((item) => ({
        text: item?.staffPersonalInformations?.first_name + " " + item?.staffPersonalInformations?.last_name,
        value: item.id
    }));

    const createNewTeam = (values) => {
        setIsLoading(true)
        addTeam(
            {
                name: values.name,
                description: values.description,
                team_lead_id: values.teamLead,
            }
        ).then((res) => {
            if (res.success) {
                setTeamAtom(res.data)
                setIsLoading(false)
                setShowSuccessModal(true)
            }
        }).catch(() => {
            setIsLoading(false);
        });
    }


    return (

        <div>
            <GoBack />
            <div className="mt-6">
                <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
                    <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                        {"Create team"}
                    </h1>
                </div>

                <div className="bg-[#F5F5F5] pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
                    <Formik
                        initialValues={initialValues}
                        onSubmit={(values) => { createNewTeam(values) }}
                        validationSchema={addTeamSchema}
                        enableReinitialize
                    >
                        {({ values, handleChange, setFieldValue }) => (
                            <Form>

                                <div className="grid grid-cols-2 gap-8 mt-8">

                                    <div>
                                        <FormikCustomInput
                                            label="Team name *"
                                            id="name"
                                            name="name"
                                            placeholder="enter team name"
                                            type="text"
                                            inputClassName="!bg-transparent"
                                            value={values.name}
                                        />
                                    </div>

                                    <div>
                                        <FormikCustomSelect
                                            label="Team lead *"
                                            name="teamlead"
                                            options={staffs}
                                            value={values.teamLead}
                                            onChange={(item: { value: string; text: string }) => {
                                                setFieldValue("teamLead", item.value);
                                            }}
                                        />
                                    </div>

                                </div>

                                <div className="grid grid-cols-1 gap-8 mt-8">

                                    <div className="grid grid-cols-1 gap-4">
                                        <label htmlFor="description">Description </label>
                                        <textarea
                                            name="description"
                                            id="description"
                                            className="w-full h-[80px] rounded-md bg-transparent border focus:outline-none focus:ring-0 py-2 px-4 "
                                            placeholder="enter description"
                                            value={values.description}
                                            onChange={handleChange}
                                        >
                                        </textarea>
                                    </div>

                                </div>

                                <div className="mt-[50px] flex justify-end">
                                    <CustomButton
                                        type="submit"
                                        title="Create"
                                        handleClick={() => { }}
                                        isLoading={isLoading}
                                        size={ButtonProperties.SIZES.small}
                                        variant={ButtonProperties.VARIANT.primary.name}
                                    />
                                </div>


                            </Form>
                        )}
                    </Formik>
                </div>
            </div>

            <SuccessModal
                visibility={showSuccessModal}
                text="Team added successfully."
                route="/task-management/teams"
            />

        </div>
    )
}

export default CreateTeam;