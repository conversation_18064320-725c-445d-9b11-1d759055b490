import React, { useEffect, useState } from "react";
import { Note } from "iconsax-react";
import { Doughnut, Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  ChartOptions,
} from "chart.js";
import { useRecoilState, useRecoilValue } from "recoil";
import { getBusinessAnalyticAtom } from "../../../recoil/atom/organizationAtom";
import { getBusinessAnalytics } from "../../../api/organization";
import StatisticsCard from "../../atoms/Cards/StatisticsCard";
import { IoCubeOutline } from "react-icons/io5";
import CurrencyTag from "../../atoms/Ui/CurrencyTag";
import { calculateDaysLeft, currencySymbol } from "../../shared/helpers";
import OpenSquareArrow from "../../../assets/icons/OpenSquareArrow";
import { MdOutlineDoNotDisturbOn } from "react-icons/md";
import CustomLink from "../../atoms/CustomLink/CustomLink";
import { disbursementSummaryAtom, expenseChartTrendAtom, incomeChartTrendAtom, incomeExpenseSummaryAtom, revenueSummaryAtom } from "../../../recoil/atom/accountAnalytics";
import { getDisbursementSummary, getExpenseChartTrend, getIncomeChartTrend, getIncomeExpenseSummary, getRevenueSUmmary } from "../../../api/accountingAnalytics";
import moment from "moment";
import HighchartsReact from "highcharts-react-official";
import Highcharts from "highcharts";
import DashboardSkeleton from "../../atoms/Skeleton/DashboardSkeleton";
import { getContractRemindersAtom, getDashboardStatisticsAtom, getEmployeeCompositionAtom, getEmployeeReminderAtom, getEmployeeStatisticsAtom, getEmploymentTypesAtom, getSalaryStatisticsAtom } from "../../../recoil/atom/dashboard";
import { getContractReminders, getDashboardStatistics, getEmployeeComposition, getEmployeeReminders, getEmployeeStatistics, getEmploymentTypes, getSalaryStatistics } from "../../../api/dashboard";

ChartJS.register(CategoryScale,LinearScale,ArcElement,BarElement,Title,Tooltip,Legend);

const Dashboard = () => {
  const [chartLoading, setChartLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [, setBusinessAnalyticAtom] = useRecoilState(getBusinessAnalyticAtom);
  const [, setIncomeChartTrend] = useRecoilState(incomeChartTrendAtom);
  const [, setExpenseChartTrend] = useRecoilState(expenseChartTrendAtom);
  const [, setDashboardStatisticsAtom] = useRecoilState(getDashboardStatisticsAtom);
  const [, setEmployeeStatisticsAtom] = useRecoilState(getEmployeeStatisticsAtom);
  const [, setEmployeeCompositionAtom] = useRecoilState(getEmployeeCompositionAtom);
  const [, setEmployeeReminderAtom] = useRecoilState(getEmployeeReminderAtom);
  const [, setEmploymentTypeAtom] = useRecoilState(getEmploymentTypesAtom);
  const [, setContractReminderAtom] = useRecoilState(getContractRemindersAtom);
  const [, setSalaryStatisticAtom] = useRecoilState(getSalaryStatisticsAtom);
   const [, setExpenseSummary] = useRecoilState(disbursementSummaryAtom);
   const [, setIncomeSummary] = useRecoilState(revenueSummaryAtom);
   const [, setIncomeExpenseSummary] = useRecoilState(incomeExpenseSummaryAtom);

  const incomeChartTrendValue = useRecoilValue(incomeChartTrendAtom);
  const expenseChartTrendValue = useRecoilValue(expenseChartTrendAtom);
  const dashboardStatsValue = useRecoilValue(getDashboardStatisticsAtom);
  const employeeStatsValue = useRecoilValue(getEmployeeStatisticsAtom);
  const employeeCompositionValue = useRecoilValue(getEmployeeCompositionAtom);
  const employmentTypeValue = useRecoilValue(getEmploymentTypesAtom);
  const employeeReminderValue = useRecoilValue(getEmployeeReminderAtom);
  const contractReminderValue = useRecoilValue(getContractRemindersAtom);
  const salaryStatsValue = useRecoilValue(getSalaryStatisticsAtom);
  const incomeExpenseSummaryValue = useRecoilValue(incomeExpenseSummaryAtom);
  const incomeSummaryValue = useRecoilValue(revenueSummaryAtom);
   const expenseSummaryValue = useRecoilValue(disbursementSummaryAtom);

  const fetchIncomeTrendChart = async () => {
    setChartLoading(true);
    try {
      const res = await getIncomeChartTrend({ from: "", to: "" });
      if (res.success) {
        setIncomeChartTrend(res?.data?.income || res?.data || []);
      } else {
        setIncomeChartTrend([]);
      }
    } catch (error) {
      setIncomeChartTrend([]);
    } finally {
      setChartLoading(false);
    }
  };

  const fetchExpenseSummary = () => {
      getDisbursementSummary({ from: "", to:  "" }).then(
        (res) => {
          if (res.success) {
            setExpenseSummary(res.data);
          }
        }
      );
  };

  const fetchIncomeSummary = () => {
    getRevenueSUmmary({ from: "", to: "" }).then(
      (res) => {
        if (res.success) {
          setIncomeSummary(res.data);
        }
      }
    );
  };
  
  const fetchExpenseIncomeSummary = () => {
    setIsFetching(true);
    getIncomeExpenseSummary().then((res) => {
      if (res.success) {
        setIncomeExpenseSummary(res.data);
      };
      setIsFetching(false);
    });
  };

  const fetchDashboardStats = async () => {
    setIsFetching(true);
   await getDashboardStatistics().then((res) => {
    if(res.success) {
      setDashboardStatisticsAtom(res.data)
      setIsFetching(false);
    }
    setIsFetching(false);
   });
  };

  const fetchEmployeeStats = async () => {
    setIsFetching(true);
   await getEmployeeStatistics().then((res) => {
    if(res.success) {
      setEmployeeStatisticsAtom(res.data)
      setIsFetching(false);
    }
    setIsFetching(false);
   });
  };

  const fetchEmployeeComposition = async () => {
    setIsFetching(true);
   await getEmployeeComposition().then((res) => {
    if(res.success) {
      setEmployeeCompositionAtom(res.data)
      setIsFetching(false);
    }
    setIsFetching(false);
   });
  };

  const fetchEmployeeReminders = async () => {
    setIsFetching(true);
   await getEmployeeReminders().then((res) => {
    if(res.success) {
      setEmployeeReminderAtom(res.data)
      setIsFetching(false);
    }
    setIsFetching(false);
   });
  };

  const fetchEmploymentTypes = async () => {
    setIsFetching(true);
   await getEmploymentTypes().then((res) => {
    if(res.success) {
      setEmploymentTypeAtom(res.data.top_three_employment_types)
      setIsFetching(false);
    }
    setIsFetching(false);
   });
  };

  const fetchContractReminders = async () => {
    setIsFetching(true);
   await getContractReminders().then((res) => {
    if(res.success) {
      setContractReminderAtom(res.data)
      setIsFetching(false);
    }
    setIsFetching(false);
   });
  };

   const fetchSalaryStatistics = async () => {
    setIsFetching(true);
   await getSalaryStatistics().then((res) => {
    if(res.success) {
      setSalaryStatisticAtom(res.data)
      setIsFetching(false);
    }
    setIsFetching(false);
   });
  };


  const fetchExpenseTrendChart = async () => {
    setChartLoading(true);
    try {
      const res = await getExpenseChartTrend({ from: "", to: "" });

      if (res.success) {
        setExpenseChartTrend(res.data);
      } else {
        setExpenseChartTrend([]);
      }
    } catch (error) {
      setExpenseChartTrend([]);
    } finally {
      setChartLoading(false);
    }
  };

  useEffect(() => {
    fetchIncomeTrendChart();
    fetchExpenseTrendChart();
    fetchDashboardStats();
    fetchEmployeeStats();
    fetchEmployeeComposition();
    fetchContractReminders();
    fetchEmploymentTypes();
    fetchEmployeeReminders();
    fetchExpenseSummary();
    fetchExpenseIncomeSummary();
    fetchIncomeSummary();
    fetchSalaryStatistics();
  }, []);

  const totalEmployeeData = {
    labels: ["Male", "Female"],
    datasets: [
      {
        label: "Employees",
        data: [employeeCompositionValue?.male_employees, employeeCompositionValue?.female_employees],
        backgroundColor: ["#3730A3", "#F874AC"],
        borderWidth: [6, 0],
        borderColor: ["#3730A3", "#F874AC"],
        borderRadius: 0,
        cutout: "70%",
      },
    ],
  };

  const employeeTypeData = {
    labels: employmentTypeValue?.map((data) => data?.employment_type) || [],
    datasets: [
      {
        label: "Employees",
        data: employmentTypeValue?.map((data) => data?.count) || [],
        backgroundColor: ["#3730A3", "#6BB56E", "#00B4D8"],
        borderWidth: [6, 4, 0],
        borderColor: ["#3730A3", "#6BB56E", "#00B4D8"],
        borderRadius: 0,
        cutout: "70%",
      },
    ],
  };

  const totalEmployeeOption: ChartOptions<"doughnut"> = {
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        titleFont: {
          size: 14,
          family: "Cabin",
        },
        bodyFont: {
          size: 12,
          family: "Cabin",
        },
      },

    },
    maintainAspectRatio: false,
  };

  const incomeChartOptions = {
    chart: {
      type: "pie",
      options3d: {
        enabled: true,
        alpha: 45,
      },
      backgroundColor: "transparent",
      height: 150,
    },
    title: {
      text: "",
      style: {
        font: {
          size: 12,
          family: "Cabin",
        },
      },
    },
    plotOptions: {
      pie: {
         innerSize: 50,
        depth: 45,
        dataLabels: {
          enabled: true,
        },
      },
    },
    series: [
      {
        name: "Salary",
        data: [
          ['Unpaid', parseInt(salaryStatsValue?.total_salary_unpaid_last_month)],
          ['Paid ', parseInt(salaryStatsValue?.total_salary_paid_last_month)],
          ['Failed ', parseInt(salaryStatsValue?.total_salary_failed_last_month)],
        ] 
      },
    ],
    colors: ["#3730A3", "#6FD195", "#EE5858"],
    credits: {
      enabled: false,
    },
  };

  const overviewStat = [
    {
      title: "Total employees",
      value: dashboardStatsValue?.total_employees?.toLocaleString() || 0,
      icon: <IoCubeOutline className="rotate-90" size={24} />,
      valueText: "Employees",
      color: "!bg-purple-light !text-neutral-dark",
      iconBackgroundColor: "#3730A399",
      parentContainer: "!shadow-none",
    },
    {
      title: "Leave requests",
      value: dashboardStatsValue?.total_leave_requests?.toLocaleString() || 0,
      icon: <OpenSquareArrow />,
      valueText: "Requests",
      color: "!bg-accent-green-light !text-neutral-dark",
      iconBackgroundColor: "#B3AA0199",
      parentContainer: "!shadow-none",
    },
    {
      title: "Total departments",
      value: dashboardStatsValue?.total_departments?.toLocaleString() || 0,
      icon: <Note size={24} />,
      valueText: "Departments",
      color: "!bg-[#EBFDFF] !text-neutral-dark",
      iconBackgroundColor: "#09778399",
      parentContainer: "!shadow-none",
    },
    {
      title: "This month payroll",
      value: dashboardStatsValue?.payroll_amount?.toLocaleString() || 0,
      icon: <MdOutlineDoNotDisturbOn size={24} />,
      valueText: "",
      color: "!bg-accent-orange-light !text-neutral-dark",
      iconBackgroundColor: "#A3000099",
      parentContainer: "!shadow-none",
      currency: <CurrencyTag currency={currencySymbol.NGN_PREFIX} />,
    },
  ];

  const fetchBusinessAnalytic = () => {
    setIsFetching(true)
    getBusinessAnalytics().then((res) => {
      if (res.success) {
        setBusinessAnalyticAtom(res.data);
        setIsFetching(false);
      }
    });
  };

  useEffect(() => {
    fetchBusinessAnalytic();
  }, []);

  const contractReminders = contractReminderValue?.map((data) => (
    `${data?.icon} ${data?.firstName} ${data?.lastName} contract terminates in ${calculateDaysLeft(moment().format(), moment(data?.date).format("DD MMMM YYYY"))} days`
  ));

  const birthdayReminders = employeeReminderValue?.map((data) => (
    `${data?.icon} ${data?.firstName} ${data?.lastName} birthday is in ${calculateDaysLeft(moment().format(), moment(data?.date).format("DD MMMM YYYY"))} days`
  ));

  const reminders = [...contractReminders, ...birthdayReminders]

  const employeeStats = [
    { color: "#3730A3", text: "Total employees", value: employeeStatsValue?.total_employees?.toLocaleString() || 0 },
    { color: "#1E40AF", text: "Active employees", value: employeeStatsValue?.active_employees?.toLocaleString() || 0 },
    { color: "#1E40AF", text: "New employees", value: employeeStatsValue?.new_employees?.toLocaleString() || 0 },
    { color: "#1D4ED8", text: "Employees on leave", value: employeeStatsValue?.employees_on_leave?.toLocaleString() || 0 },
    { color: "#3B82F6", text: "Number of branches", value: employeeStatsValue?.number_of_branches?.toLocaleString() || 0 },
    { color: "#2563EB", text: "Number of teams", value: employeeStatsValue?.team_members_count?.toLocaleString() || 0 },
    { color: "#60A5FA", text: "Number of disputes", value: employeeStatsValue?.number_of_disputes?.toLocaleString() || 0 },
  ];

  const processChartData = () => {
    const allMonths = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    const incomeData = new Array(12).fill(0);
    const expenseData = new Array(12).fill(0);

    incomeChartTrendValue?.forEach((item: { month: string; total: string }) => {
      const monthIndex = moment(item.month).month();
      incomeData[monthIndex] = parseFloat(item.total);
    });

    expenseChartTrendValue?.forEach(
      (item: { month: string; total: string }) => {
        const monthIndex = moment(item.month).month();
        expenseData[monthIndex] = parseFloat(item.total);
      }
    );

    const incomePointRadius = incomeData.map((value: number) =>
      value > 0 ? 6 : 0
    );
    const expensePointRadius = expenseData.map((value: number) =>
      value > 0 ? 6 : 0
    );

    return {
      labels: allMonths,
      datasets: [
        {
          label: "Income",
          data: incomeData,
          borderColor: "#27836D",
          borderWidth: 2,
          tension: 0.4,
          pointRadius: incomePointRadius,
          pointHoverRadius: 6,
          pointBackgroundColor: "#27836D",
          pointBorderColor: "#fff",
          pointBorderWidth: 2,
        },
        {
          label: "Expenses",
          data: expenseData,
          borderColor: "#D14343",
          borderWidth: 2,
          tension: 0.4,
          pointRadius: expensePointRadius,
          pointHoverRadius: 6,
          pointBackgroundColor: "#D14343",
          pointBorderColor: "#fff",
          pointBorderWidth: 2,
        },
      ],
    };
  };

  const chartData = processChartData();
  const getYAxisMax = () => {
    const allTotals = [
      ...incomeChartTrendValue.map((i) => +i.total),
      ...expenseChartTrendValue.map((i) => +i.total),
    ];

    const maxValue = Math.max(...allTotals, 0);
    const padded = maxValue * 1.2;
    return Math.ceil(padded / 1000) * 1000 || 5000;
  };

  const yAxisMax = getYAxisMax();
  const stepSize = Math.max(Math.floor(yAxisMax / 5), 1000);

  const chartOptions: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: "#fff",
        titleColor: "#374151",
        bodyColor: "#374151",
        borderWidth: 1,
        displayColors: false,
        callbacks: {
          label: (context) =>
            `${context.dataset.label}: ₦${context.parsed.y.toLocaleString()}`,
        },
        titleFont: {
          size: 14,
          family: "Cabin",
        },
        bodyFont: {
          size: 12,
          family: "Cabin",
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: "#6B7280",
          font: {
            size: 12,
            family: "Cabin",
          },
        },
        border: {
          display: false,
        },
      },
      y: {
        beginAtZero: true,
        max: yAxisMax,
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
        ticks: {
          color: "#6B7280",
          font: {
            size: 12,
            family: "Cabin",
          },
          stepSize,
        },
        border: {
          display: false,
        },
      },
    },
    elements: {
      point: {
        hoverRadius: 8,
      },
    },
  };

  if(isFetching) {
    return <DashboardSkeleton/>
  }

  return (
    <div>
      <div className="bg-white py-8 px-6 rounded-[10px]">
        <div className="grid grid-cols-4 gap-4">
          {overviewStat.map((item, index) => (
            <div key={index}>
              <StatisticsCard
                backgroundColor={item.color}
                key={index}
                title={item.title}
                value={item.value}
                icon={item.icon}
                valueText={item.valueText}
                iconBackgroundColor={item.iconBackgroundColor}
                parentContainer={item.parentContainer}
                currency={item.currency}
              />
            </div>
          ))}
        </div>
      </div>
      <div className="mt-14">
        <h1 className="bg-white w-fit font-poppins-medium px-4 py-2">
          Finance overview
        </h1>
        <div className="grid grid-cols-12 gap-5 mt-4">
          <div
            style={{ height: "269px", position: "relative" }}
            className=" bg-white p-6 flex items-center justify-center col-span-5"
          >
            <div className="w-full h-full">
              {chartLoading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-pulse text-gray-500">
                    Loading chart data...
                  </div>
                </div>
              ) : (
                <div className="h-[259px]">
                   <div className="flex justify-end text-[#949494]">
                     <p className="w-3.5 border h-fit mt-2 mr-2 border-[#68BDA9]"></p>
                    <div className="">INCOME</div>
                    <p className="w-3.5 border h-fit mt-2 ml-5 mr-2 border-[#F41F22]"></p>
                    <p>EXPENSE</p>
                  </div>
                  <div className="h-[219px]">

                  <Line data={chartData} options={chartOptions} />
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="col-span-4 bg-white">
            <div className="flex flex-col justify-between h-full">
              <div>
                <div>
                <p className="mt-8 ml-10">Salary statistics</p>
                  <HighchartsReact
                    highcharts={Highcharts}
                    options={incomeChartOptions}
                  />
                </div>
              </div>
              <p className="border-t py-4 text-center">Next payment: {moment(salaryStatsValue?.next_payment_date).format("MMMM DD YYYY")}</p>
            </div>
          </div>
          <div className="col-span-3 bg-white py-6 px-4">
            <div className="border ">
              <RevenueStats title="Total income" value={incomeExpenseSummaryValue?.total_income?.toLocaleString() || 0} />
              <RevenueStats title="Total expense" value={incomeExpenseSummaryValue?.total_expense?.toLocaleString() || 0} valueClassName="text-[#DA1414]" />
              <RevenueStats title="Highest income by branch" value={incomeSummaryValue?.highestIncome ? parseInt(incomeSummaryValue?.highestIncome?.[0]?.total || 0)?.toLocaleString(): 0} />
              <RevenueStats title="Highest expense by branch" value={expenseSummaryValue?.highestExpense ? parseInt(expenseSummaryValue?.highestExpense?.[0]?.total || 0)?.toLocaleString(): 0} valueClassName="text-[#DA1414]" />
            </div>
          </div>
        </div>
      </div>

      <div className="mt-14">
        <h1 className="bg-white w-fit font-poppins-medium px-4 py-2">
          Employee statistics
        </h1>
        <div className="mt-4 grid grid-cols-7 gap-6">
          <div className="bg-white h-[20rem] col-span-3 px-10 py-6">
            <div>
              <table className="w-full">
                <tr className="border-b text-neutral-normal">
                  <td className="text-left pb-2">Details</td>
                  <td className="text-right">Numbers</td>
                </tr>
                {employeeStats.map((data, index) => (
                  <tr key={index}>
                    <td className="text-left py-2 flex">
                      <p
                        className="h-3 w-3 rounded-full mt-1 mr-2 bg-gray-300"
                        style={{ backgroundColor: data.color }}
                      />
                      {data.text}
                    </td>
                    <td className="text-right">{data.value}</td>
                  </tr>
                ))}
              </table>
            </div>
          </div>
          <div className="bg-white h-[20rem] col-span-2 ">
            <div className="w-full max-w-xs p-4 bg-white">
              <h3 className="text-sm font-medium text-neutral-700 mb-4">
                Employees composition
              </h3>
              <div className="flex flex-col justify-center items-center">
                <div className="relative h-[150px] mt-14">
                  <div className="absolute top-[40%] left-[40%]">
                    <p className="text-center text-[8px]">Total employee</p>
                    <p className="text-center text-[25px]">{employeeCompositionValue?.total_employees}</p>
                  </div>
                  <Doughnut
                    data={totalEmployeeData}
                    options={totalEmployeeOption}
                  />
                </div>
                <div className="flex justify-center mt-4 gap-6 text-12">
                  <div className="flex items-center gap-1">
                    <div
                      className="w-5 h-2.5 rounded-xl"
                      style={{ backgroundColor: "#3730A3" }}
                    ></div>
                    <span>Male</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div
                      className="w-5 h-2.5 rounded-xl"
                      style={{ backgroundColor: "#F6679B" }}
                    ></div>
                    <span>Female</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="bg-white h-[20rem] col-span-2">
            <div className="w-full max-w-xs p-4 bg-white">
              <h3 className="text-sm font-medium text-neutral-700 mb-4">
                Employees type
              </h3>
              <div className="relative h-[150px] mt-14">
                <div className="absolute top-[40%] left-[40%]">
                  <p className="text-center text-[8px]">Total employee</p>
                  <p className="text-center text-[25px]">{employeeCompositionValue?.total_employees}</p>
                </div>
                <Doughnut
                  data={employeeTypeData}
                  options={totalEmployeeOption}
                />
              </div>
              <div className="flex justify-center mt-4 gap-6 text-12">
                <div className="flex items-center gap-1">
                  <div
                    className="w-5 h-2.5 rounded-xl"
                    style={{ backgroundColor: "#3730A3" }}
                  ></div>
                  <span>Part time</span>
                </div>
                <div className="flex items-center gap-1">
                  <div
                    className="w-5 h-2.5 rounded-xl"
                    style={{ backgroundColor: "#6BB56E" }}
                  ></div>
                  <span>Contract</span>
                </div>
                <div className="flex items-center gap-1">
                  <div
                    className="w-5 h-2.5 rounded-xl"
                    style={{ backgroundColor: "#00B4D8" }}
                  ></div>
                  <span>Fulltime</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-14">
        <div className="flex justify-between bg-purple-light py-3 px-6 rounded-tr-lg rounded-tl-lg">
          <h1 className="font-poppins-medium">Reminder</h1>
          <CustomLink destination="#" text="View all" />
        </div>
        <div className="bg-white">
          {reminders.map((data, index) => (
            <p className="border-b py-4 px-6 text-neutral-normal" key={index}>
              {data}
            </p>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

const RevenueStats = ({title, value, currency, valueClassName}: any) => {
  return (
    <div className="border-b px-6 py-2">
      <h1>{title}</h1>
      <div className="mt-1">
      <CurrencyTag className="!bg-transparent !text-purple-normal border font-semibold" currency={currency || "NGN"}/><span className={`text-16 ${valueClassName}`}>{value}</span>
      </div>
    </div>
  )
}
