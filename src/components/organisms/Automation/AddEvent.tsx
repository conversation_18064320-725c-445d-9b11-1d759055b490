import { Form, Formik } from "formik";
import React from "react";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";
import GoBack from "../../atoms/Ui/GoBack";
import { useNavigate } from "react-router-dom";
import NewsLetterTemplate from "../../../assets/images/promo-newsletter.png";
import EventTemplate from "../../../assets/images/event-email.png";
import EmailMarketing from "../../../assets/images/Starbucks-email-marketing.jpg";
import Keepintouch from "../../../assets/images/keepintouch-email.png";
import LeadEmail from "../../../assets/images/lead-nurturing-email-campaign.png";
import BlankPage from "../../../assets/images/blank-page.png";
import {
  newsletterTemplate,
  welcomeEmailTemplate,
} from "../../shared/helpers/templates";
import { useRecoilState } from "recoil";
import { sampleTemplateAtom } from "../../../recoil/atom/automation";

interface TemplatePreview {
  id: number;
  image: any;
  design: any;
  name: string;
}

const AddEvent = () => {
  const navigate = useNavigate();
  const [, templateAtom] = useRecoilState(sampleTemplateAtom)

  const sampleTemplates = [
    {
      id: 1,
      image: BlankPage,
      design: "",
      name: "Blank Template"
    },
    {
      id: 2,
      image: EventTemplate,
      design: welcomeEmailTemplate,
      name: "Welcome Email Template"
    },
    {
      id: 3,
      image: EmailMarketing,
      design: newsletterTemplate,
    },
    {
      id: 4,
      image: Keepintouch,
      design: welcomeEmailTemplate,
      name: "Follow-up Template"
    },
    {
      id: 5,
      image: LeadEmail,
      design: newsletterTemplate,
      name: "Lead Nurturing Email Campaign"
    },
  ];

  const handleSelectTemplate = (template: TemplatePreview) => {
    templateAtom(template);
    navigate(`/automation/email-trigger?name=${template?.name}`);
  };
  return (
    <div>
      <GoBack />
      <div className="mt-4">
        <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-[33px]">
          Events
        </h1>
      </div>
      {/* <div className="bg-white p-4 rounded-md mt-4">
        <Formik initialValues={{ trigger: "", event: "" }} onSubmit={() => {}}>
          {({ setFieldValue, values }) => (
            <Form>
              <div className="flex gap-6">
                <div>
                  <FormikCustomSelect
                    label="Select Trigger"
                    placeholder="Select Trigger"
                    parentContainer="!h-12"
                    options={[
                      {
                        value: "Employee Registration",
                        text: "Employee Registration",
                      },
                    ]}
                    name="trigger"
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("trigger", item.text);
                    }}
                    value={values.trigger}
                  />
                </div>

                <div>
                  <FormikCustomSelect
                    label="Select Event"
                    placeholder="Select Event"
                    parentContainer="!h-12"
                    options={[{ value: "Email", text: "Email" }]}
                    name="event"
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("event", item.text);
                    }}
                    value={values.event}
                    disabled={!values.trigger}
                  />
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div> */}

      <div className="mt-10 bg-white p-4">
        <div>
          <h1 className="text-20 text-purple-normal font-poppins-medium">
            Templates Samples
          </h1>
          <p>You can choose from available template</p>
        </div>

        <div className="mt-10">
            <h1 className="text-16 font-poppins-medium">Available Templates</h1>
          <div className="mt-4 flex flex-wrap gap-6 ">
            {sampleTemplates.map((template:any) => (
              <div
                className="cursor-pointer"
                onClick={() => handleSelectTemplate(template)}
              >
                <div className="bg-gray-200 h-[220px] w-[200px]">
                  <img
                    width={200}
                    height={220}
                    className="h-[220px] w-[200px] object-cover object-top border"
                    src={template?.image}
                    alt={template?.name}
                  />
                </div>
                <p className="mt-2">{template?.name}</p>
              </div>
            ))}
          </div>

          <div className="mt-10">
            <h1 className="text-16 font-poppins-medium">Recently Used</h1>
            <div className="mt-4 flex flex-wrap gap-6 ">
              <div className="cursor-pointer">
                <div className="bg-gray-200 h-[220px] w-[200px]">
                  <img
                    width={200}
                    height={220}
                    className="h-[220px] w-[200px] object-cover object-top border"
                    src={EventTemplate}
                    alt="newsletter template"
                  />
                </div>
              </div>
              <div className="cursor-pointer">
                <div className="bg-gray-200 h-[220px] w-[200px]">
                  <img
                    width={200}
                    height={220}
                    className="h-[220px] w-[200px] object-cover object-top border"
                    src={EmailMarketing}
                    alt="newsletter template"
                  />
                </div>
              </div>
              <div className="cursor-pointer">
                <div className="bg-gray-200 h-[220px] w-[200px]">
                  <img
                    width={200}
                    height={220}
                    className="h-[220px] w-[200px] object-cover object-top border"
                    src={NewsLetterTemplate}
                    alt="newsletter template"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddEvent;
