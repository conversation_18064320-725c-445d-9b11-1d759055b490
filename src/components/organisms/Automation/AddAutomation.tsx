import React from 'react';
import GoBack from '../../atoms/Ui/GoBack';
import { useNavigate } from 'react-router-dom';
import NewsLetterTemplate from '../../../assets/images/promo-newsletter.png';
import EventTemplate from '../../../assets/images/event-email.png';
import EmailMarketing from '../../../assets/images/Starbucks-email-marketing.jpg';
import Keepintouch from '../../../assets/images/keepintouch-email.png';
import LeadEmail from '../../../assets/images/lead-nurturing-email-campaign.png';
import BlankPage from '../../../assets/images/blank-page.png';
import { newsletterTemplate, welcomeEmailTemplate } from '../../shared/helpers/templates';
import { useRecoilState } from 'recoil';
import { automationFormData, sampleTemplateAtom } from '../../../recoil/atom/automation';
import CustomSelect from '../../atoms/CustomInput/CustomSelect';
import CustomRichTextEditor from '../../atoms/CustomInput/CustomRichTextEditor';
import { ButtonProperties } from '../../shared/helpers';
import CustomButton from '../../atoms/CustomButton/CustomButton';

interface TemplatePreview {
  id: number;
  image: any;
  design: any;
  name: string;
}

const AddAutomation = () => {
  const navigate = useNavigate();
  //const [, templateAtom] = useRecoilState(sampleTemplateAtom);
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [formData, setFormData] = useRecoilState(automationFormData);

  const sampleTemplates = [
    {
      id: 1,
      image: BlankPage,
      design: '',
      name: 'Blank Template',
    },
    {
      id: 2,
      image: EventTemplate,
      design: welcomeEmailTemplate,
      name: 'Welcome Email Template',
    },
    {
      id: 3,
      image: EmailMarketing,
      design: newsletterTemplate,
    },
    {
      id: 4,
      image: Keepintouch,
      design: welcomeEmailTemplate,
      name: 'Follow-up Template',
    },
    {
      id: 5,
      image: LeadEmail,
      design: newsletterTemplate,
      name: 'Lead Nurturing Email Campaign',
    },
  ];

  const handleChange = (key: string, value: any) => {
    setFormData((prev) => ({ ...prev, [key]: value }));
  };

  const handleSelectTemplate = (template: TemplatePreview) => {
    setFormData((prev) => ({ ...prev, template_id: template.id, template: template.design }));
  };
  return (
    <div>
      <GoBack />
      <div className='mt-4'>
        <h1 className='font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-[18px]'>
          Add Automation
        </h1>
      </div>
      <div className='bg-white p-4 rounded-md mt-4'>
        <div>
          <h1 className='text-20 text-purple-normal font-poppins-medium'>Automation trigger</h1>
          <p className='text-gray-500'>Select trigger and action</p>
          <hr className='mt-2 border-gray-50' />
        </div>
        <div className='mt-8'>
          <div className='grid grid-cols-2 gap-4'>
            <div>
              <CustomSelect
                label='Trigger'
                placeholder='Select Trigger'
                parentContainer='!h-12'
                options={[
                  {
                    value: 'employee_registration',
                    text: 'Employee Registration',
                  },
                ]}
                onChange={(item: { value: string; text: string }) => {
                  handleChange('trigger', item.value);
                }}
                value={formData.trigger}
              />
            </div>

            <div>
              <CustomSelect
                label='Action'
                placeholder='Select Action'
                parentContainer='!h-12'
                options={[{ value: 'send_email', text: 'Send Email' }]}
                onChange={(item: { value: string; text: string }) => {
                  handleChange('action', item.value);
                }}
                value={formData.action}
                disabled={!formData.trigger}
              />
            </div>
          </div>
        </div>
      </div>

      {formData.action === 'send_email' && !formData.template_id && (
        <div className='mt-10 bg-white p-4'>
          <div>
            <h1 className='text-20 text-purple-normal font-poppins-medium'>Templates Samples</h1>
            <p className='text-gray-500'>You can choose from available template</p>
            <hr className='mt-2 border-gray-50' />
          </div>

          <div className='mt-10'>
            <h1 className='text-16 font-poppins-medium'>Available Templates</h1>
            <div className='mt-4 flex flex-wrap gap-6 '>
              {sampleTemplates.map((template: any) => (
                <div
                  className='cursor-pointer'
                  key={'temp-' + template?.name}
                  onClick={() => handleSelectTemplate(template)}
                >
                  <div className='bg-gray-200 h-[160px] w-[160px]'>
                    <img
                      width={160}
                      height={160}
                      className='h-[160px] w-[160px] object-cover object-top border'
                      src={template?.image}
                      alt={template?.name}
                    />
                  </div>
                  <p className='mt-2'>{template?.name}</p>
                </div>
              ))}
            </div>

            <div className='mt-10'>
              <h1 className='text-16 font-poppins-medium'>Recently Used</h1>
              <div className='mt-4 flex flex-wrap gap-6 '>
                <div className='cursor-pointer'>
                  <div className='bg-gray-200 h-[160px] w-[160px]'>
                    <img
                      width={200}
                      height={220}
                      className='h-[160px] w-[160px] object-cover object-top border'
                      src={EventTemplate}
                      alt='newsletter template'
                    />
                  </div>
                </div>
                <div className='cursor-pointer'>
                  <div className='bg-gray-200 h-[160px] w-[160px]'>
                    <img
                      width={200}
                      height={220}
                      className='h-[160px] w-[160px] object-cover object-top border'
                      src={EmailMarketing}
                      alt='newsletter template'
                    />
                  </div>
                </div>
                <div className='cursor-pointer'>
                  <div className='bg-gray-200 h-[160px] w-[160px]'>
                    <img
                      width={200}
                      height={220}
                      className='h-[160px] w-[160px] object-cover object-top border'
                      src={NewsLetterTemplate}
                      alt='newsletter template'
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      {formData.action === 'send_email' && !!formData.template_id && (
        <div className='mt-10 bg-white p-4'>
          <div>
            <div className='inline-block'>
              <h1 className='text-20 text-purple-normal font-poppins-medium'>Email template</h1>
              <p className='text-gray-500'>Modify your email template</p>
            </div>
            <button
              className='bg-transparent border text-purple-500 border-purple-500 hover:bg-purple-100 py-1 px-4 rounded float-right'
              onClick={() => setFormData((prev) => ({ ...prev, template_id: null }))}
            >
              Select new template design
            </button>
            <hr className='mt-2 border-gray-50' />
          </div>
          <div className='mt-8'>
            <CustomRichTextEditor
              value={formData.template}
              onChange={(value) => handleChange('template', value)}
            />
          </div>
          <div className='mt-[26px] h-[80px]'>
            <div className='w-[150px] float-right'>
              <CustomButton
                type='submit'
                title='Save Automation'
                handleClick={() => {}}
                isLoading={isLoading}
                variant={ButtonProperties.VARIANT.primary.name}
                isDisabled={formData.template === null || formData.template === ''}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddAutomation;
