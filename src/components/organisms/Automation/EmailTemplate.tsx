import React, { useEffect, useRef, useState } from "react";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../shared/helpers";
import GoBack from "../../atoms/Ui/GoBack";
import "react-quill/dist/quill.snow.css";
import EmailEditor, { EditorRef } from "react-email-editor";
import html2canvas from "html2canvas";
import { Form, Formik } from "formik";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";
import { useRecoilValue } from "recoil";
import { sampleTemplateAtom } from "../../../recoil/atom/automation";
import { BiExpand } from "react-icons/bi";

interface TemplatePreview {
  id: number;
  image: string;
  design: any;
}

const EmailTrigger = () => {
  const emailEditorRef = useRef<EditorRef>(null);
  const editorContainerRef = useRef<HTMLDivElement>(null);
  const [ccEmails, setCCEmails] = useState<string[]>([]);
  const [toEmails, setToEmails] = useState<string[]>([]);
  const [bccEmails, setBccEmails] = useState<string[]>([]);
  const [toInputValue, setToInputValue] = useState("");
  const [ccInputValue, setCCInputValue] = useState("");
  const [bccInputValue, setBccInputValue] = useState("");
  const [templates, setTemplates] = useState<TemplatePreview[]>([]);
  const templateAtomValue = useRecoilValue(sampleTemplateAtom);
  const [expandModal, setExpandModal] = useState<boolean>(false);
  const [emailTemplateDesign, setEmailTemplateDesign] = useState({});

  const initialValues = {
    event: "",
    status: "",
    delayTime: "",
    delayType: "",
    subject: "",
    to: "",
    bcc: "",
    cc: "",
    body: "",
  }

  const exportHtml = () => {
    const unlayer = emailEditorRef.current?.editor;

    unlayer?.exportHtml((data) => {
      const { design, html } = data;
      // console.log("exportHtml", html);
      setEmailTemplateDesign(design);
    });
  };

  const loadTemplate = (design: any) => {
    const unlayer = emailEditorRef.current?.editor;

    if (unlayer) {
      unlayer.loadDesign(design);
    } else {
      console.error("Email Editor is not initialized.");
    }
  };

  const UpdatedloadTemplate = (design: any) => {
    const unlayer = emailEditorRef.current?.editor;

    if (unlayer) {
      unlayer.loadDesign(design);
    } else {
      console.error("Email Editor is not initialized.");
    }
  };

  useEffect(() => {
    if (templateAtomValue) {
      loadTemplate(templateAtomValue.design);
    }
  }, [templateAtomValue]);

  const captureTemplate = async () => {
    const editor = emailEditorRef.current?.editor;

    if (!editor) {
      console.error("Editor not found.");
      return;
    }

    editor.exportHtml(async (data) => {
      const { html, design } = data;

      const container = document.createElement("div");
      container.innerHTML = html;
      container.style.width = "800px";
      document.body.appendChild(container);

      try {
        const canvas = await html2canvas(container, {
          useCORS: true,
        });
        const image = canvas.toDataURL("image/png");

        setTemplates((prev) => [
          ...prev,
          { id: prev.length + 1, image, design },
        ]);
      } catch (error) {
        console.error("Screenshot capture failed:", error);
      } finally {
        document.body.removeChild(container);
      }
    });
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const addEmail = (
    email: string,
    emailArray: any,
    setEmail: any,
    setInput: any
  ) => {
    if (validateEmail(email) && !emailArray.includes(email)) {
      setEmail((prev) => [...prev, email]);
    }
    setInput("");
  };

  const removeEmail = (email: string, setEmail) => {
    setEmail((prev) => prev.filter((e) => e !== email));
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLTextAreaElement>,
    emailArray: any,
    setEmail: any,
    inputValue: string,
    setInputValue: any
  ) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      if (inputValue.trim()) {
        addEmail(inputValue.trim(), emailArray, setEmail, setInputValue);
      }
    }
  };

  const handleSubmit = () =>  {
    // captureTemplate();
    exportHtml();
  }

  return (
    <div>
      <GoBack />
      {/* <button
        onClick={captureTemplate}
        className="bg-blue-500 text-white px-4 py-2 rounded"
      >
        Capture Screenshot
      </button> */}
      {/* <div
        style={{
          display: "flex",
          gap: "10px",
          flexWrap: "wrap",
          marginBottom: "1rem",
        }}
      >
        {templates.map((template) => (
          <div
            key={template.id}
            style={{
              border: "1px solid #ccc",
              borderRadius: "5px",
              overflow: "hidden",
              cursor: "pointer",
            }}
            onClick={() => UpdatedloadTemplate(template?.design)}
          >
            <img
              src={template.image}
              alt={`Template ${template.id}`}
              style={{ width: "200px" }}
            />
          </div>
        ))}
      </div> */}

      <div className="mt-4">
        <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-[33px]">
          Compose Email
        </h1>
        <Formik initialValues={initialValues} onSubmit={handleSubmit}>
          {({ values, setFieldValue }) => (
            <Form>
              <div className="grid grid-cols-3 gap-x-8 gap-y-4 mt-8">
                <div>
                  <FormikCustomSelect
                    label="Select Event"
                    parentContainer="!h-10"
                    placeholder="Select Option"
                    options={[
                      {
                        value: "Employee Registration",
                        text: "Employee Registration",
                      },
                    ]}
                    name="event"
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("event", item.text);
                    }}
                    value={values.event}
                  />
                </div>
                <div>
                  <FormikCustomSelect
                    label="Status"
                    parentContainer="!h-10"
                    placeholder="Select Option"
                    options={[
                      {
                        value: "Active",
                        text: "Active",
                      },
                      {
                        value: "Inactive",
                        text: "Inactive",
                      },
                    ]}
                    name="status"
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("status", item.text);
                    }}
                    value={values.status}
                  />
                </div>
                <div>
                  <label>Delay Period</label>
                  <div className="flex gap-2 mt-4">
                    <div>
                      <div>
                        <FormikCustomInput
                          id="delayTime"
                          name="delayTime"
                          placeholder="2"
                          type="number"
                          inputClassName="!bg-transparent !h-10 !w-14"
                        />
                      </div>
                    </div>

                    <div>
                      <FormikCustomSelect
                        parentContainer="!h-10"
                        placeholder="Select Option"
                        options={[
                          {
                            value: "minutes",
                            text: "Minutes",
                          },
                          {
                            value: "Hour",
                            text: "Hours",
                          },
                          {
                            value: "Week",
                            text: "Week",
                          },
                          {
                            value: "Day",
                            text: "Days",
                          },
                        ]}
                        name="delayType"
                        onChange={(item: { value: string; text: string }) => {
                          setFieldValue("status", item.text);
                        }}
                        value={values.delayType}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div className="grid grid-cols-3 gap-x-8 gap-y-4 mt-4">
                  <div>
                    <label className="block font-medium mb-2">TO</label>
                    <div
                      className="border border-[#B2BBC699] p-2 rounded overflow-y-auto"
                      style={{ maxHeight: "120px" }}
                    >
                      {toEmails.map((email, index) => (
                        <span
                          key={index}
                          className="inline-block bg-purple-light  text-purple-normal rounded px-2 py-1 mr-1 mb-1"
                        >
                          {email}
                          <button
                            className="ml-1 text-red-500 hover:text-red-700"
                            onClick={() => removeEmail(email, setToEmails)}
                          >
                            &times;
                          </button>
                        </span>
                      ))}
                      <textarea
                        className="w-full border-none outline-none bg-transparent resize-none"
                        value={toInputValue}
                        onChange={(e) => setToInputValue(e.target.value)}
                        onKeyDown={(e) =>
                          handleKeyDown(
                            e,
                            toEmails,
                            setToEmails,
                            toInputValue,
                            setToInputValue
                          )
                        }
                        placeholder="Press Enter to add more emails"
                        rows={1}
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block font-medium mb-2">CC</label>
                    <div
                      className="border border-[#B2BBC699] p-2 rounded overflow-y-auto"
                      style={{ maxHeight: "120px" }}
                    >
                      {ccEmails.map((email, index) => (
                        <span
                          key={index}
                          className="inline-block bg-purple-light  text-purple-normal rounded px-2 py-1 mr-1 mb-1"
                        >
                          {email}
                          <button
                            className="ml-1 text-red-500 hover:text-red-700"
                            onClick={() => removeEmail(email, setCCEmails)}
                          >
                            &times;
                          </button>
                        </span>
                      ))}
                      <textarea
                        className="w-full border-none outline-none bg-transparent resize-none"
                        value={ccInputValue}
                        onChange={(e) => setCCInputValue(e.target.value)}
                        onKeyDown={(e) =>
                          handleKeyDown(
                            e,
                            ccEmails,
                            setCCEmails,
                            ccInputValue,
                            setCCInputValue
                          )
                        }
                        placeholder="Press Enter to add more emails"
                        rows={1}
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block font-medium mb-2">BCC</label>
                    <div
                      className="border border-[#B2BBC699] p-2 rounded overflow-y-auto"
                      style={{ maxHeight: "120px" }}
                    >
                      {bccEmails.map((email, index) => (
                        <span
                          key={index}
                          className="inline-block bg-purple-light  text-purple-normal rounded px-2 py-1 mr-1 mb-1"
                        >
                          {email}
                          <button
                            className="ml-1 text-red-500 hover:text-red-700"
                            onClick={() => removeEmail(email, setBccEmails)}
                          >
                            &times;
                          </button>
                        </span>
                      ))}
                      <textarea
                        className="w-full border-none outline-none bg-transparent resize-none"
                        value={bccInputValue}
                        onChange={(e) => setBccInputValue(e.target.value)}
                        onKeyDown={(e) =>
                          handleKeyDown(
                            e,
                            bccEmails,
                            setBccEmails,
                            bccInputValue,
                            setBccInputValue
                          )
                        }
                        placeholder="Press Enter to add more emails"
                        rows={1}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-4">
                <label htmlFor="">Subject</label>
                <FormikCustomInput
                  id="subject"
                  name="subject"
                  placeholder="Subject"
                  type="text"
                  inputClassName="!bg-transparent !h-10 mt-4"
                />
              </div>
              <div className="mt-4">
                <div className="mb-4">
                  <label htmlFor="">Message</label>
                </div>
                {/* <p onClick={exportHtml}>Export HTML</p> */}
                <div
                  className={`email-editor-container ${
                    expandModal ? "full-screen" : ""
                  }`}
                  style={{
                    border: "1px solid #ccc",
                    marginTop: "1rem",
                    padding: expandModal ? "0" : "1rem",
                  }}
                >
                  <div
                   
                    className="flex justify-end  text-purple-normal my-4 pr-10"
                  >
                    <div  onClick={() => setExpandModal((prev) => !prev)}>

                    <BiExpand className="cursor-pointer" size={24} />
                    </div>
                  </div>
                  <div ref={editorContainerRef}>
                    <EmailEditor
                      minHeight={"100vh"}
                      onLoad={() => loadTemplate(templateAtomValue.design)}
                      ref={emailEditorRef}
                      onReady={(emailEditor) =>
                        setEmailTemplateDesign(emailEditor)
                      }
                    />
                  </div>
                </div>
              </div>
              <div className="mt-[120px] flex justify-end">
                <CustomButton
                  type="submit"
                  title="Save"
                  handleClick={() => {}}
                  // isLoading={isLoading}
                  size={ButtonProperties.SIZES.small}
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default EmailTrigger;
