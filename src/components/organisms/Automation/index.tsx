import React from 'react';
import StatisticsCard from '../../atoms/Cards/StatisticsCard';
import { TbBrandStackshare } from 'react-icons/tb';
import { Data } from 'iconsax-react';
import { RiRobot2Line } from 'react-icons/ri';
import CustomButton from '../../atoms/CustomButton/CustomButton';
import { FiPlus } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import CustomTable from '../../atoms/CustomTable/CustomTable';
import { PROTECTED_ROUTE_NAMES } from '../../../routes/protectedRoutes';

const Automation = () => {
  const navigate = useNavigate();

  const automationStats = [
    {
      title: 'Total Events',
      value: 4,
      icon: <TbBrandStackshare className='rotate-90' size={24} />,
      valueText: 'Events',
      color: 'bg-accent-blue-light',
    },
    {
      title: 'Active Events',
      value: 8,
      icon: <Data className='rotate-90' size={24} />,
      valueText: 'Active Events',
      color: 'bg-accent-green-light',
    },
    {
      title: 'Connected Triggers',
      value: 4,
      icon: <RiRobot2Line size={24} />,
      valueText: 'Connected',
      color: 'bg-accent-orange-light',
    },
  ];

  const columns = [
    {
      Header: 'Name',
      accessor: 'name',
    },

    {
      Header: 'Trigger',
      accessor: 'trigger',
    },
    {
      Header: 'Channel',
      accessor: 'channel',
    },
    {
      Header: 'Status',
      accessor: 'status',
    },

    {
      Header: 'Date Created',
      accessor: 'date',
    },
  ];

  const events = [
    {
      name: 'Event 1',
      trigger: 'Webhook',
      channel: 'Slack',
      status: 'Active',
      date: '2022-05-15',
    },
  ];

  return (
    <div>
      <div className=' grid grid-cols-3 gap-5 '>
        {automationStats?.map((item, index) => (
          <div key={index}>
            <StatisticsCard
              backgroundColor={item.color}
              key={index}
              title={item.title}
              value={item.value}
              icon={item.icon}
              valueText={item.valueText}
            />
          </div>
        ))}
      </div>
      <div className='pt-10 flex justify-end'>
        <CustomButton
          className='!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md'
          isTransparent={true}
          handleClick={() => {
            navigate(PROTECTED_ROUTE_NAMES.addAutomation);
          }}
          leftIcon={<FiPlus className='ml-3' size={20} />}
          title='Add Automation'
        />
      </div>
      <div className='bg-[#F5F5F5] my-10 px-4 py-[23px]'>
        <CustomTable
          data={events || []}
          meta={{}}
          columns={columns}
          // handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
          // handleSearch={(search) => {setSearchQuery(search); debounceSearch(search)}}
          header={
            <div>
              <h1 className='font-poppins-medium text-purple-normal-active mb-4'>
                Event Information
              </h1>
            </div>
          }
        />
      </div>
    </div>
  );
};

export default Automation;
