import React, { useEffect, useRef, useState } from "react";
import { Eye, UserEdit } from "iconsax-react";
import OrganizationEmptyState from "../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../atoms/CustomTable/CustomTable";
import { debounce } from "../../shared/helpers";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../shared/hooks";
import { FiPlus } from "react-icons/fi";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import Loader from "../../atoms/Loader";
import { deleteCustomer, getCustomers } from "../../../api/customer";
import { PiTrash } from "react-icons/pi";
import { getCustomersAtom } from "../../../recoil/atom/customers";
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";
import { Form, Formik } from "formik";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";
import { getAllBranchesAtom } from "../../../recoil/atom/organizationAtom";
import useUpdateRecoilAtom from "../../shared/hooks/updateRecoilAtom";



const Customers = () => {
  const navigate = useNavigate();
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [branchName, setBranchName] = useState<string>("");
  const [branchId, setBranchId] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [, setCustomersAtom] = useRecoilState(getCustomersAtom);
  const [rowId, setRowId] = useState(0);
  const customers = useRecoilValue(getCustomersAtom);
  const getEntireBranchesValue = useRecoilValue(getAllBranchesAtom);
  const { fetchEntireBranches } = useUpdateRecoilAtom();


  const debounceSearch = useRef(debounce((q) => fetchCustomers(q), 2000)).current;

  const branches = getEntireBranchesValue?.data?.map((branch) => ({
    text: branch.name,
    value: branch.id,
  })) || [];

  const fetchCustomers = (q?) => {
    setIsFetching(true);
    getCustomers({ page: pageNumber, search: q }, branchId).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setCustomersAtom(res.data)
      }
    });
  };

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const handleDeleteCustomer = (id) => {
    setIsLoading(true);
    deleteCustomer(id).then((res) => {
      setIsLoading(false);
      if (res.success) {
        setShowDropdown(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message)
        fetchCustomers();
      }
    });
  };

  useEffect(() => {
    fetchCustomers(searchQuery);
  }, [pageNumber, branchId]);

  useEffect(() => {
    fetchEntireBranches();
  }, [])

  if (isFetching && !searchQuery) {
    return <div>
      <Loader />
    </div>
  }

  const columns = [
    {
      Header: "Customer name",
      accessor: "name",
      Cell: (row: any) => (<p> {row.cell.value || "--"}</p>)
    },

    {
      Header: "Email",
      accessor: "email",
      Cell: (row: any) => (<p className="lowercase"> {row.cell.value || "--"}</p>),
    },
    {
      Header: "Phone",
      accessor: "phone",
      Cell: (row: any) => (<p>{row.cell.value || "--"} </p>),
    },
    {
      Header: "Address",
      accessor: "address",
      Cell: (row: any) => (<p>{row.cell.value || "--"}</p>),
    },

    {
      Header: "Branch",
      accessor: "branch.name",
      Cell: (row: any) => (<p>{row.cell.value || "--"} </p>),
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative" >
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown >
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li onClick={() => navigate(`/crm/add-customer`, { state: { data: row.cell.row.original } })} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <Eye size={18} />
                  View
                </li>
                <li onClick={() => { navigate(`/crm/add-customer`, { state: { isEdit: true, data: row.cell.row.original } }) }} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <UserEdit size={18} />
                  Edit
                </li>
                {showDeleteWarn ? (
                  <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                    <div className="flex gap-3">
                      <PiTrash size={18} />
                      Are you sure?
                    </div>
                    <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                      <CustomButton isLoading={isLoading} title="Yes" handleClick={() => handleDeleteCustomer(row.cell.row.original.id)} className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer" />
                      <span onClick={() => { setShowDeleteWarn(false) }} className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center">No</span>
                    </div>
                  </li>
                ) : (
                  <li onClick={() => setShowDeleteWarn(true)} className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer">
                    <PiTrash size={18} />
                    Delete
                  </li>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  return (
    <>
      <div>
        {/* <h1 className="font-poppins font-semibold text-24">Customers</h1> */}

        {/* <div className="flex justify-end">
          <CustomButton
            className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
            isTransparent={true}
            handleClick={() => { navigate("/crm/add-customer") }}
            leftIcon={<FiPlus className="ml-3" size={20} />}
            title="Add customer"
          />
        </div> */}
        <div className=" my-10 px-4 py-[23px]">

          {(customers?.data?.length > 0 || searchQuery || branchId) ? (

            <CustomTable
              data={customers?.data || []}
              meta={customers?.meta || {}}
              columns={columns}
              // customFilter={
              //   <div className="pb-4">
              //     <div>
              //       <h1 className="border-b border-neutral-light pl-4 py-3">Modify this view</h1>
              //       <Formik initialValues={{branch: branchName || ""}} onSubmit={() => {}}>
              //        {({setFieldValue, values}) => (
              //         <Form>
              //           <div className="flex px-4 gap-5 py-[17.5px] border-b">
              //             <p className="whitespace-nowrap">Filter by:</p>
              //             <div className="flex gap-2">
              //               <div className="">
              //                 <FormikCustomSelect
              //                   parentContainer="!h-7"
              //                   placeholder="Select Branch"
              //                   optionsParentClassName="!capitalize"
              //                   options={branches}
              //                   name="branch"
              //                   onChange={(item: { value: string; text: string }) => {
              //                     setSearchQuery("");
              //                       setBranchId(item.value);
              //                       setBranchName(item.text);
              //                     setFieldValue("branch", item.text);
              //                   }}
              //                   value={values.branch}
              //                 />
              //               </div>
              //             </div>
              //           </div>
              //             <div className="flex justify-end items-end place-content-end">
              //               <p className=" pt-3 pr-3 cursor-pointer" onClick={() => {setBranchId(""); setBranchName("")}}>Clear Filter</p>
              //             </div>
              //         </Form>
              //        )}
              //       </Formik>

              //     </div>
              //   </div>
              // }
              handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
              handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
             
              header={
                <div className="flex justify-between items-center h-[40px] px-2">
                  <h1>
                    Customers
                  </h1>
                  <div className="bg-black rounded">
                    <CustomButton
                      className="!w-[250px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md !h-10"
                      isTransparent={true}
                      handleClick={() => { navigate("/crm/add-customer") }}
                      leftIcon={<FiPlus className="ml-3" size={20} />}
                      title="Add customer"
                    />
                  </div>
                </div>
              }
            />
          ) : (
            <div className="flex justify-center items-center py-[120px]">
              <OrganizationEmptyState buttonTitle="Add new customer" handleClick={() => navigate("/crm/add-customer")} />
            </div>
          )}
        </div>


      </div>
    </>
  );
};

export default Customers;
