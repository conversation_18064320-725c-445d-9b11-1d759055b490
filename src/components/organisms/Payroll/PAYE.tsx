import React from "react";
import CustomToggle from "../../atoms/CustomToggle/CustomToggle";

const PAYE = () => {


  return (
    <div className="my-10 pb-10 bg-white rounded-bl-[10px] rounded-br-[10px]">
      
      <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-[33px]">
          PAYE (Pay As You Earn) Optional
        </h1>
        <div className="px-6 mt-6">
          <p className="text-16 text-neutral-normal leading-6">
            It's a system where income tax is deducted directly from your employees' salaries. When you run your payroll. ArkHR will automatically 
            calculate the PAYE tax that should be deducted from each employee's salary, based on their earinings and tax band.
          </p>

          <div className="flex justify-between px-6 py-6 bg-gray-100 mt-10 rounded-xl">
            <p className="text-20">Automatic income tax (PAYE)</p>
            <CustomToggle/>
          </div>
        </div>
    </div>
  );
};

export default PAYE;
