import React, { useState } from 'react'
import CustomTable from '../../../../atoms/CustomTable/CustomTable';
import OrganizationEmptyState from '../../../../atoms/Cards/OrganizationEmptyState';
import CustomCheckBox from '../../../../atoms/CustomCheckBox/CustomCheckBox';
import { getNameInitials, truncateText } from '../../../../shared/helpers';
import StatusTag from '../../../../atoms/StatusTag';
import { FaEllipsisV } from 'react-icons/fa';
import FilterDropdown from '../../../../atoms/Cards/FilterDropdown';
import useClickOutside from '../../../../shared/hooks';
import { Eye } from 'iconsax-react';

const Deductions = () => {
    const [rowId, setRowId] = useState(0);
    const [showDropdown, setShowDropdown] = useState<boolean>(false);
    const [pageNumber, setPageNumber] = useState<number>(1);
    
    const node = useClickOutside(() => {
        setShowDropdown(false);
        setRowId(0);
    });

    const sampleData = [
        {
            deductionName: "Health Insurance",
            employeeContribution: "$178",
            employerContribution: "$250",
        }
    ]



    const columns = [
        {
          Header: "Deduction Name",
          accessor: "deductionName",
          Cell: (row: any) => (
            <div>
                {row.cell.value || "--"}
            </div>
          ),
        },
    
        {
          Header: "Employer Contribution",
          accessor: "employerContribution",
          Cell: (row: any) => (
            <p>
              {row.cell.value || "--"}{" "}
            </p>
          ),
        },
        {
          Header: "Employee Contribution",
          accessor: "employeeContribution",
          Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
        },

    
        
      ];
  return (
    <div>
         <div className="bg-[#F5F5F5] my-5 px-4 py-[23px]">
          {sampleData?.length > 0 ? (
            <CustomTable
              data={sampleData || []}
              meta={{}}
              columns={columns}
              handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
              header={
                <div>
                  <h1 className="font-poppins-medium text-purple-dark-active mb-4">
                    Deductions Information
                  </h1>
                </div>
              }
            />
          ) : (
            <div className="flex justify-center items-center py-[120px]">
              <OrganizationEmptyState
                buttonTitle="Nothing to show"
              />
            </div>
          )}
        </div>
    </div>
  )
}

export default Deductions