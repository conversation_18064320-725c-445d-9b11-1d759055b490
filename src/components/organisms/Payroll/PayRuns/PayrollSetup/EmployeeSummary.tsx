import React, { useState } from "react";
import CustomTable from "../../../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../../../atoms/Cards/OrganizationEmptyState";
import CustomCheckBox from "../../../../atoms/CustomCheckBox/CustomCheckBox";
import { ButtonProperties, getNameInitials } from "../../../../shared/helpers";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../../../shared/hooks";
import { Eye, Information } from "iconsax-react";
import { BiBlock } from "react-icons/bi";
import { RiPassPendingLine } from "react-icons/ri";
import CustomModal from "../../../../atoms/CustomModal/CustomModal";
import CustomButton from "../../../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import StatusTag from "../../../../atoms/StatusTag";
import { GoCodeReview } from "react-icons/go";
import RevisedSalaryForm from "../../SalaryRevision/RevisedSalaryForm";

interface componentProps {
  isApproved: boolean;
}

const EmployeeSummary = ({ isApproved }: componentProps) => {
  const navigate = useNavigate();
  const [groupCheck, setGroupCheck] = useState<boolean>(false);
  const [checkedItems, setCheckedItems] = useState<any[]>([]);
  const [rowId, setRowId] = useState(0);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showSkipModal, setShowSkipModal] = useState<boolean>(false);
  const [showWithholdModal, setShowWithholdModal] = useState<boolean>(false);
  const [showReviseSalaryModal, setShowReviseSalaryModal] = useState<boolean>(false);
  const [pageNumber, setPageNumber] = useState<number>(1);

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const sampleData = [
    {
      id: 0,
      employeeName: "John Doe",
      paidDays: "28",
      grossPay: "$2,500",
      deductions: "$120",
      benefits: "$0",
      netPay: "$3,000",
      avatar: "",
      salaryWithhold: false,
      skipSalary: false,
      salaryRevised: false,
    },
    {
      id: 1,
      employeeName: "Jane Smith",
      paidDays: "28",
      grossPay: "$3,500",
      deductions: "$150",
      benefits: "$0",
      netPay: "$3,000",
      avatar: "",
      salaryWithhold: true,
      skipSalary: false,
      salaryRevised: false,
    },
    {
      id: 2,
      employeeName: "Ify Yvonne",
      paidDays: "28",
      grossPay: "$3,500",
      deductions: "$150",
      benefits: "$0",
      netPay: "$3,000",
      avatar: "",
      salaryWithhold: false,
      skipSalary: true,
      salaryRevised: false,
    },
    {
      id: 2,
      employeeName: "Michael Smith",
      paidDays: "28",
      grossPay: "$3,500",
      deductions: "$150",
      benefits: "$0",
      netPay: "$3,000",
      avatar: "",
      salaryWithhold: false,
      skipSalary: false,
      salaryRevised: true,
    },
  ];

  const approvedData = [
    {
      id: 0,
      employeeName: "John Doe",
      paidDays: "28",
      netPay: "$3,000",
      avatar: "",
      mode: "Transfer",
      status: "Pending"
    },
    {
      id: 1,
      employeeName: "Jane Smith",
      paidDays: "28",
      netPay: "$3,000",
      avatar: "",
       mode: "Transfer",
      status: "Paid"
    },
    {
      id: 2,
      employeeName: "Ify Yvonne",
      paidDays: "28",
      netPay: "$3,000",
      avatar: "",
      mode: "Transfer",
      status: "Paid"
    },
  ];

  const handleCheckboxChange = (event, row) => {
    const isChecked = event.target.checked;
    setCheckedItems((prevState: any) => {
      if (isChecked) {
        return [...prevState, row];
      } else {
        setGroupCheck(false);
        return prevState.filter((item) => item.id !== row.id);
      }
    });
  };

  const handleSelectAllChange = (event) => {
    const isChecked = event.target.checked;
    setGroupCheck(isChecked);

    if (isChecked) {
      setCheckedItems(sampleData);
    } else {
      setCheckedItems([]);
    }
  };

  const columns = [
    {
      Header: (
        <div className="flex">
          <CustomCheckBox
            checked={groupCheck}
            onChange={(e: any) => handleSelectAllChange(e)}
            customClass="!mr-3"
          />
          <p className="mt-1">Employee name</p>
        </div>
      ),
      accessor: "employeeName",
      Cell: (row: any) => (
        <div className="flex  items-center">
          <CustomCheckBox
            checked={checkedItems.some(
              (item: any) => item.id === row.cell.row.original.id
            )}
            onChange={(e: any) => {
              handleCheckboxChange(e, row.cell.row.original);
            }}
            customClass="!mr-3"
          />
          <div className="font-poppins-medium flex justify-center items-center gap-3">
            {!row.cell.row.original.avatar ? (
              <p className="w-10 h-10 text-20 rounded-full bg-purple-normal text-white font-poppins-medium font-bold flex justify-center items-center">
                {getNameInitials(row.cell.value)}
              </p>
            ) : (
              <div className="w-[40px] h-[40px] rounded-full overflow-hidden">
                <img
                  src={row.cell.row.original.avatar}
                  alt="avatar"
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div>
              {row.cell.value}{" "}
              {row.cell.row.original.salaryWithhold ? (
                <span className="bg-alert-bg-error text-alert-text-error rounded-md p-1 ml-1">
                  Salary Withhold
                </span>
              ) : (
                ""
              )}{" "}
              {row.cell.row.original.skipSalary ? (
                <span className="bg-accent-blue-light text-accent-blue-normal rounded-md p-1 ml-1">
                  Skipped
                </span>
              ) : (
                ""
              )}
              {row.cell.row.original.salaryRevised ? (
                <span className="bg-accent-orange-light text-[#FFA500] rounded-md p-1 ml-1">
                  Revised Pending
                </span>
              ) : (
                ""
              )}
              
            </div>
          </div>
        </div>
      ),
    },

    {
      Header: "Paid Days",
      accessor: "paidDays",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Gross Pay",
      accessor: "grossPay",
      Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
    },

    {
      Header: "Deductions",
      accessor: "deductions",
      Cell: (row: any) => <p>{row.cell.value.toLowerCase() || "--"} </p>,
    },
    {
      Header: "Benefits",
      accessor: "benefits",
      Cell: (row: any) => <p>{row.cell.value}</p>,
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                  onClick={() => setShowWithholdModal(true)}
                >
                  <RiPassPendingLine size={18} />
                  {row.cell.row.original.salaryWithhold
                    ? "Revert salary withhold"
                    : "Withhold salary"}
                </li>
                {!row.cell.row.original.skipSalary && (

                  <li
                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                    onClick={() => setShowSkipModal(true)}
                  >
                    <BiBlock size={18} />
                    Skip from this payroll
                  </li>
                )}
                {!row.cell.row.original.salaryRevised && (

                  <li
                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                    onClick={() => setShowReviseSalaryModal(true)}
                  >
                    <GoCodeReview size={18} />
                    Revise salary
                  </li>
                )}
                {/* <li
                        className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                    >
                        <BiExit size={18} />
                        Initiate exit process
                    </li> */}
                <li
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                  onClick={() => navigate("/employee/staff-information")}
                >
                  <Eye size={18} />
                  View employee details
                </li>
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  const approvedColumns = [
    {
      Header: (
        <div className="flex">
          <CustomCheckBox
            checked={groupCheck}
            onChange={(e: any) => handleSelectAllChange(e)}
            customClass="!mr-3"
          />
          <p className="mt-1">Employee name</p>
        </div>
      ),
      accessor: "employeeName",
      Cell: (row: any) => (
        <div className="flex  items-center">
          <CustomCheckBox
            checked={checkedItems.some(
              (item: any) => item.id === row.cell.row.original.id
            )}
            onChange={(e: any) => {
              handleCheckboxChange(e, row.cell.row.original);
            }}
            customClass="!mr-3"
          />
          <div className="font-poppins-medium flex justify-center items-center gap-3">
            {!row.cell.row.original.avatar ? (
              <p className="w-10 h-10 text-20 rounded-full bg-purple-normal text-white font-poppins-medium font-bold flex justify-center items-center">
                {getNameInitials(row.cell.value)}
              </p>
            ) : (
              <div className="w-[40px] h-[40px] rounded-full overflow-hidden">
                <img
                  src={row.cell.row.original.avatar}
                  alt="avatar"
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div>
              {row.cell.value}{" "}
              {row.cell.row.original.salaryWithhold ? (
                <span className="bg-alert-bg-error text-alert-text-error rounded-md p-1 ml-1">
                  Salary Withhold
                </span>
              ) : (
                ""
              )}{" "}
              {row.cell.row.original.skipSalary ? (
                <span className="bg-accent-blue-light text-accent-blue-normal rounded-md p-1 ml-1">
                  Skipped
                </span>
              ) : (
                ""
              )}
            </div>
          </div>
        </div>
      ),
    },
    {
      Header: "Paid days",
      accessor: "paidDays",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Net pay",
      accessor: "netPay",
      Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
    },

    {
      Header: "Payment mode",
      accessor: "mode",
      Cell: (row: any) => <p>{row.cell.value.toLowerCase() || "--"} </p>,
    },
    {
      Header: "Payment status",
      accessor: "status",
      Cell: (row: any) => <StatusTag status={row.cell.value} />,
    },
  ];
  return (
    <div>
      {isApproved ? (
        <div>
            <p className='mt-5 text-[#FFA500] flex gap-2'> <Information size={14} className='mt-0.5'/>Pay your employees on 30 Jan 2025. Record it here once you have made the payment.</p>
          <div className="bg-[#F5F5F5] my-5 px-4 py-[23px]">
            {sampleData?.length > 0 ? (
              <CustomTable
                data={approvedData || []}
                meta={{}}
                columns={approvedColumns}
                checkedItems={checkedItems}
                handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
                header={
                  <div>
                    <h1 className="font-poppins-medium text-purple-dark-active mb-4">
                      Period: January 2025 | 29 base days
                    </h1>
                  </div>
                }
              />
            ) : (
              <div className="flex justify-center items-center py-[120px]">
                <OrganizationEmptyState buttonTitle="Nothing to show" />
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="bg-[#F5F5F5] my-5 px-4 py-[23px]">
          {sampleData?.length > 0 ? (
            <CustomTable
              data={sampleData || []}
              meta={{}}
              columns={columns}
              checkedItems={checkedItems}
              handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
              header={
                <div>
                  <h1 className="font-poppins-medium text-purple-dark-active mb-4">
                    Period: January 2025 | 29 base days
                  </h1>
                </div>
              }
            />
          ) : (
            <div className="flex justify-center items-center py-[120px]">
              <OrganizationEmptyState buttonTitle="Nothing to show" />
            </div>
          )}
        </div>
      )}

      <CustomModal
        visibility={showSkipModal}
        toggleVisibility={setShowSkipModal}
      >
        <div>
          <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
            <h1 className="font-poppins-medium text-18 text-purple-dark ">
              Skip employee?
            </h1>
          </div>
          <div className="mt-5 text-[#FFA500] flex gap-2 bg-[#f4f4f4] mx-10 py-2 px-2 rounded-md">
            <Information size={14} className="mt-0.5" /> Once you skip an
            employee(s) from the pay run, you will not be able to pay them later
            for this pay cycle.
          </div>
          <div className="mx-10 mt-6">
            <div className="flex justify-between w-3/4">
              <div>
                <p className="font-poppins-medium ">Employee name</p>
                <p className="mt-2">Ify Yvonne</p>
              </div>
              <div>
                <p className="font-poppins-medium ">Payroll period</p>
                <p className="mt-1">01 Jan, 2025 - 30th Jan, 2025</p>
              </div>
            </div>

            <div className="mt-4">
              <label htmlFor="reason" className="font-poppins-medium">
                Please enter a reason *
              </label>
              <textarea
                className="w-full border mt-2 rounded-md p-3"
                rows={5}
              />
            </div>
            <div className="flex justify-end my-6">
              <CustomButton
                title="Proceed"
                handleClick={() => {}}
                variant={ButtonProperties.VARIANT.primary.name}
                size={ButtonProperties.SIZES.small}
              />
            </div>
          </div>
        </div>
      </CustomModal>

      <CustomModal
        visibility={showWithholdModal}
        toggleVisibility={setShowWithholdModal}
      >
        <div>
          <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
            <h1 className="font-poppins-medium text-18 text-purple-dark ">
              Withhold employee salary?
            </h1>
          </div>
          <div className="mt-5 text-[#FFA500] flex gap-2 bg-[#f4f4f4] mx-10 py-2 px-2 rounded-md">
            <Information size={14} className="mt-0.5" /> Once you withhold an
            employee(s) salary from the pay run, you will not be able to pay
            them for this pay cycle.
          </div>
          <div className="mx-10 mt-6">
            <div className="flex justify-between w-3/4">
              <div>
                <p className="font-poppins-medium ">Employee name</p>
                <p className="mt-2">Ify Yvonne</p>
              </div>
              <div>
                <p className="font-poppins-medium ">Payroll period</p>
                <p className="mt-1">01 Jan, 2025 - 30th Jan, 2025</p>
              </div>
            </div>

            <div className="mt-4">
              <label htmlFor="reason" className="font-poppins-medium">
                Please enter a reason *
              </label>
              <textarea
                className="w-full border mt-2 rounded-md p-3"
                rows={5}
              />
            </div>
            <div className="flex justify-end my-6">
              <CustomButton
                title="Proceed"
                handleClick={() => {}}
                variant={ButtonProperties.VARIANT.primary.name}
                size={ButtonProperties.SIZES.small}
              />
            </div>
          </div>
        </div>
      </CustomModal>

      <CustomModal
        visibility={showReviseSalaryModal}
        toggleVisibility={setShowReviseSalaryModal}
      >
        <div>
          <RevisedSalaryForm/>
        </div>
      </CustomModal>
    </div>
  );
};

export default EmployeeSummary;
