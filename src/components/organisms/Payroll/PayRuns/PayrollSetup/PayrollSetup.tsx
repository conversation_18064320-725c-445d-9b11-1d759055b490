import React, { useEffect, useState } from "react";
import StatisticsCard from "../../../../atoms/Cards/StatisticsCard";
import { GiTakeMyMoney } from "react-icons/gi";
import { useNavigate, useSearchParams } from "react-router-dom";
import EmployeeSummary from "./EmployeeSummary";
import Deductions from "./Deductions";
import { ButtonProperties, currencySymbol } from "../../../../shared/helpers";
import CustomButton from "../../../../atoms/CustomButton/CustomButton";
import { Save2, User } from "iconsax-react";
import StatusTag from "../../../../atoms/StatusTag";
import CustomModal from "../../../../atoms/CustomModal/CustomModal";
import { Form, Formik } from "formik";
import FormikCustomDate from "../../../../atoms/CustomInput/FormikCustomDate";
import moment from "moment";
import FormikCustomSelect from "../../../../atoms/CustomInput/FormikCustomSelect";
import CustomCheckBox from "../../../../atoms/CustomCheckBox/CustomCheckBox";
import { AiOutlineStop } from "react-icons/ai";
import GoBack from "../../../../atoms/Ui/GoBack";

const PayrollSetup = () => {
  const [activeTab, setActiveTab] = useState<number>(0);
  const [showApproveModal, setShowApproveModal] = useState<boolean>(false);
  const [isApproved, setIsApproved] = useState<boolean>(false);
  const [showRecordModal, setShowRecordModal] = useState<boolean>(false);
  const [showRejectModal, setShowRejectModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("activeTab");

  const payrollSetupTab = [
    {
      title: "Employee Summary",
      name: "employee-summary",
      component: <EmployeeSummary isApproved={isApproved} />,
    },
    {
      title: "Deductions",
      name: "deductions",
      component: <Deductions />,
    },
  ];

  useEffect(() => {
    setActiveTab(queryParam || 0);
  }, [queryParam, activeTab]);

  const payrollStats = [
    {
      title: "Total cost",
      value: (50343.0).toLocaleString(),
      icon: <GiTakeMyMoney size={24} />,
      valueText: "",
      color: "bg-accent-green-light",
      currency: currencySymbol.USD,
    },
    {
      title: "Total NET Pay",
      value: (65000.0).toLocaleString(),
      icon: <GiTakeMyMoney size={24} />,
      valueText: "",
      color: "bg-accent-blue-light",
      currency: currencySymbol.USD,
    },

    {
      title: "Total Deductions",
      value: (1400.0).toLocaleString(),
      icon: <GiTakeMyMoney size={24} />,
      valueText: "",
      color: "bg-[#F4EDF9]",
      currency: currencySymbol.USD,
    },
    {
      title: "Total Bonuses",
      value: (2000.0).toLocaleString(),
      icon: <GiTakeMyMoney size={24} />,
      valueText: "",
      color: "bg-accent-orange-light",
      currency: currencySymbol.USD,
    },
    {
      title: "Total Employees",
      value: 10,
      icon: <User size={24} />,
      valueText: "",
      color: "bg-accent-orange-light",
      // currency: currencySymbol.USD
    },
  ];

  return (
    <div>
      <GoBack/>
      <div className="mt-4 grid grid-cols-5 gap-5">
        {payrollStats?.map((item, index) => (
          <div key={index}>
            <StatisticsCard
              backgroundColor={item.color}
              key={index}
              title={item.title}
              value={item.value}
              icon={item.icon}
              valueText={item.valueText}
              currency={item.currency}
            />
          </div>
        ))}
      </div>
      <div className="px-4 bg-white flex justify-between mt-10">
        <div className="flex gap-10 py-6 justify-center items-center">
          {payrollSetupTab.map((item, index) => (
            <div
              key={index}
              onClick={() => {
                setActiveTab(index);
                navigate(
                  `/payroll/pay-runs/setup?activeTab=${index}&tab=${item.name}`
                );
              }}
              className="cursor-pointer"
            >
              <p
                className={`text-neutral-normal font-poppins-medium pb-2 px-5 text-center text-16 ${
                  activeTab == index
                    ? "text-purple-normal"
                    : "text-neutral-normal"
                }`}
              >
                {" "}
                {item.title}
              </p>
              {activeTab == index && (
                <hr
                  className={`border-2  rounded-full ${
                    activeTab == index
                      ? "border-purple-normal"
                      : "border-[#E5E5E5]"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>
      <div className="flex justify-end mt-6">
        <div className="flex gap-4">
          <StatusTag
            className="py-3 text-16 px-8 border"
            status={isApproved ? "approved" : "pending"}
          />
          {isApproved && (

          <CustomButton
            leftIcon={<AiOutlineStop size={14} />}
            title={"Reject approval"}
            isTransparent
            handleClick={() => {
              setShowRejectModal(true);
            }}
            variant={ButtonProperties.VARIANT.primary.name}
            size={ButtonProperties.SIZES.small}
          />
          )}
          <CustomButton
            leftIcon={<Save2 size={14} />}
            title={isApproved ? "Record payment" : "Submit and approve"}
            isTransparent
            handleClick={() => {
              isApproved ? setShowRecordModal(true) : setShowApproveModal(true);
            }}
            variant={ButtonProperties.VARIANT.primary.name}
            size={ButtonProperties.SIZES.small}
          />
        </div>
      </div>
      <div>{payrollSetupTab[activeTab].component}</div>

      <CustomModal
        visibility={showApproveModal}
        toggleVisibility={setShowApproveModal}
      >
        <div>
          <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
            <h1 className="font-poppins-medium text-18 text-purple-dark ">
              Submit and approve pay run
            </h1>
          </div>
          <p className="text-center text-18 mt-10">
            Are you sure you want to approve this pay run.
          </p>
          <div className="mt-14 flex justify-center mb-10">
            <div className="flex gap-5">
              <div>
                <CustomButton
                  type="submit"
                  title="Cancel"
                  handleClick={() => {
                    setShowApproveModal(false);
                  }}
                  size={ButtonProperties.SIZES.small}
                  variant={ButtonProperties.VARIANT.primary.name}
                  isTransparent
                />
              </div>
              <div>
                <CustomButton
                  type="submit"
                  title={"Approve"}
                  handleClick={() => {
                    setIsApproved(true);
                    setShowApproveModal(false);
                  }}
                  // isLoading={isLoading}
                  size={ButtonProperties.SIZES.small}
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </div>
            </div>
          </div>
        </div>
      </CustomModal>

      <CustomModal
        visibility={showRejectModal}
        toggleVisibility={setShowRejectModal}
      >
        <div>
          <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
            <h1 className="font-poppins-medium text-18 text-purple-dark ">
              Reject this approval
            </h1>
          </div>
          <p className="text-center text-18 mt-10">
            Are you sure you want to reject this approval for this pay run.
          </p>
          <div className="mt-14 flex justify-center mb-10">
            <div className="flex gap-5">
              <div>
                <CustomButton
                  type="submit"
                  title="Cancel"
                  handleClick={() => {
                    setShowRejectModal(false);
                  }}
                  size={ButtonProperties.SIZES.small}
                  variant={ButtonProperties.VARIANT.primary.name}
                  isTransparent
                />
              </div>
              <div>
                <CustomButton
                  type="submit"
                  title={"Approve"}
                  handleClick={() => {
                    setShowRejectModal(false);
                  }}
                  // isLoading={isLoading}
                  size={ButtonProperties.SIZES.small}
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </div>
            </div>
          </div>
        </div>
      </CustomModal>

      <CustomModal
        visibility={showRecordModal}
        toggleVisibility={setShowRecordModal}
      >
        <div>
          <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
            <h1 className="font-poppins-medium text-18 text-purple-dark ">
              Record Payment
            </h1>
          </div>
          <div className=" pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
            <Formik
              initialValues={{
                name: "",
                description: "",
                duration: "",
                charge: "",
              }}
              onSubmit={() => {}}
              validationSchema={{}}
              enableReinitialize
            >
              {({ values, setFieldValue }) => (
                <Form>
                  <div className="grid grid-cols-2 gap-5">
                    <div>
                      <div className="text-16 mb-4 text-neutral-dark">
                        <label htmlFor="date">Date paid</label>
                      </div>
                      <FormikCustomDate
                        // value={moment(values.date)}
                        inputClassName="border bg-transparent"
                        name="date"
                        onChange={(date) => {
                          setFieldValue(
                            "date",
                            date
                              ? moment(date).format("YYYY-MM-DD HH:mm:ss")
                              : null
                          );
                        }}
                      />
                    </div>
                    <div>
                      <FormikCustomSelect
                        label="Paid through"
                        // placeholder="Select option"
                        optionsParentClassName="text-12"
                        // value={values.organization}
                        name="paymentMode"
                        options={[
                          {
                            text: "arkHR payroll (Bank Account)",
                            value: "arkHR",
                          },
                        ]}
                        onChange={(item: { value: string; text: string }) => {
                          setFieldValue("paymentMode", item.value);
                        }}
                      />
                    </div>
                  </div>

                  <div className="mt-8">
                    <h1 className="text-neutral-normal font-poppins-medium border-b pb-2">
                      Payment mode
                    </h1>
                    <div className="mt-4 flex gap-4">
                      <p className="bg-neutral-100 rounded-lg px-4 py-1">
                        Cash (9 employees)
                      </p>
                      <p className="bg-neutral-100 rounded-lg px-4 py-1">
                        Cheque (1 employee)
                      </p>
                    </div>
                  </div>
                  <div className="mt-8">
                    <CustomCheckBox/>
                    <span className="ml-3">Send payslip notification email to selected employees</span>

                    <p className="mt-4 bg-ne text-[#FFA500]">Note: An email with a link to view the payslip will be emailed to portal-enabled employees whereas the payslip will be attached along with the email for those who don't have
                    the portal enabled.</p>
                  </div>
                  <div className="mt-14 flex justify-end">
                    <div className="flex gap-5">
                      <div>
                        <CustomButton
                          type="submit"
                          title="Cancel"
                          handleClick={() => {setShowRecordModal(false)}}
                          isLoading={isLoading}
                          size={ButtonProperties.SIZES.small}
                          variant={ButtonProperties.VARIANT.primary.name}
                          isTransparent
                        />
                      </div>
                      <div>
                        <CustomButton
                          type="submit"
                          title="Submit"
                          handleClick={() => {}}
                          isLoading={isLoading}
                          size={ButtonProperties.SIZES.small}
                          variant={ButtonProperties.VARIANT.primary.name}
                        />
                      </div>
                    </div>
                  </div>
                  
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </CustomModal>
    </div>
  );
};

export default PayrollSetup;
