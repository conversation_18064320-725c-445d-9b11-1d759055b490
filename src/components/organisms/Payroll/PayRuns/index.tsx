import React, { useEffect, useState } from 'react'
import RunPayroll from './RunPayroll'
import PayrollHistory from './PayrollHistory'
import { useNavigate, useSearchParams } from 'react-router-dom'

const PayRuns = () => {
  const [activeTab, setActiveTab] = useState<number>(0)
  const navigate = useNavigate();
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("activeTab");

  const payrollTab = [
    {
      title: "Run Payroll",
      name: 'Pay Runs',
      component: <RunPayroll />,
    },
    {
      title: "Payroll History",
      name: 'Payroll History',
      component: <PayrollHistory />,
    },
  ];

  useEffect(() => {
    setActiveTab(queryParam || 0)

  }, [queryParam, activeTab]);

  return (
    <div>

      <div className=" bg-[#F4F4F6] flex justify-between mb-10">
        {/* <div className="flex gap-10 py-1 px-1 justify-center items-center  bg-[#F4F4F6] rounded-md">
          {payrollTab.map((item, index) => (
            <div
              key={index}
              onClick={() => { setActiveTab(index); navigate(`/payroll/pay-runs?activeTab=${index}&tab=${item.name}`) }}
              className={`cursor-pointer px-5 py-2 rounded  ${activeTab == index ? "bg-white" : "bg-none"}`}>
              <p className={`text-neutral-normal font-poppins-medium pb-2 px-5 text-center text-16 ${activeTab == index ? "text-purple-normal" : "text-gray-950"} `}> {item.title}</p>
            </div>
          ))}
        </div> */}
      </div>

      <div>
        {payrollTab[activeTab].component}
      </div>
    </div>
  )
}

export default PayRuns