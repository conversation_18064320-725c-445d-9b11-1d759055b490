import React, { useState } from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import StatusTag from "../../../atoms/StatusTag";
import { Eye, Receipt, UserEdit } from "iconsax-react";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import { FaEllipsisV } from "react-icons/fa";
import useClickOutside from "../../../shared/hooks";
import { PiTrash } from "react-icons/pi";
import { MdRemoveCircleOutline } from "react-icons/md";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import { ButtonProperties } from "../../../shared/helpers";
import { Form, Formik } from "formik";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { useNavigate } from "react-router-dom";
import { FaDownload } from "react-icons/fa6";

const PayrollHistory = () => {
  const navigate = useNavigate();
  const [employeePayrollModal, setEmployeePayrollModal] = useState(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [rowId, setRowId] = useState(0);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setShowDeleteWarn(false);
    setRowId(0);
  });
  const employeePayrollData = [
    {
      id: 1,
      name: "Giffy Onyinye",
      description: "Tax deduction for employee",
      amount: "400,000",
      taxNumber: "DE57789",
      status: "paid",
      employerContribution: "60,000",
      employeeContribution: "40,000",
    },
    {
      id: 1,
      name: "Helen Josie",
      description: "Salary deduction for employee",
      amount: "200,000",
      taxNumber: "RE35799",
      status: "paid",
      employeeContribution: "70,000",
      employerContribution: "50,000",
    },
  ];

  const columns = [
    {
      Header: "Name",
      accessor: "name",
    },
    {
      Header: "Description",
      accessor: "description",
    },
    {
      Header: "Tax number",
      accessor: "taxNumber",
    },
    {
      Header: "Employer contribution",
      accessor: "employerContribution",
      Cell: (row) => <div>{row.cell.value || "--"}</div>,
    },
    {
      Header: "Employee contribution",
      accessor: "employeeContribution",
      Cell: (row) => <div>{row.cell.value || "--"}</div>,
    },
    {
      Header: "Salary ($)",
      accessor: "amount",
      Cell: (row) => <div>{row.cell.value || "--"}</div>,
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: (row) => <StatusTag status={row.cell.value} />,
    },
    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() => {navigate("/payroll/add-payroll-setup")}}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>
                <li
                  onClick={() => {navigate("/payroll/add-payroll-setup")}}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <UserEdit size={18} />
                  Edit
                </li>
                <li
                  onClick={() => {navigate("/payroll/add-payroll-setup")}}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Receipt size={18} />
                  Generate payslip
                </li>
                {showDeleteWarn ? (
                  <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                    <div className="flex gap-3">
                      <PiTrash size={18} />
                      Are you sure?
                    </div>
                    <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                      <CustomButton
                        //   isLoading={isLoading}
                        title="Yes"
                        handleClick={() => {}}
                        className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer"
                      />
                      <span
                        onClick={() => {
                          setShowDeleteWarn(false);
                        }}
                        className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                      >
                        No
                      </span>
                    </div>
                  </li>
                ) : (
                  <li
                    onClick={() => setShowDeleteWarn(true)}
                    className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer"
                  >
                    <MdRemoveCircleOutline size={18} />
                    Deactivate
                  </li>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  return (
    <div>
     

      <div className="bg-[#F5F5F5] my-10 px-4 py-[23px]">
        {employeePayrollData.length > 0 ? (
          <CustomTable
            data={employeePayrollData || []}
            meta={{}}
            columns={columns}
            header={
              <div className="flex justify-between mb-2">
                <h1 className="font-poppins-medium text-purple-normal-active mb-4">
                  Payroll history
                </h1>
                <div>
                <div className="flex gap-4">
                    <CustomButton
                      handleClick={() => {}}
                      isTransparent
                      variant={ButtonProperties.VARIANT.primary.name}
                      title="Export to Excel"
                      leftIcon={<FaDownload/>}
                      className="!h-8 w-fit"
                    />
                    <CustomButton
                      handleClick={() => {}}
                      isTransparent
                      variant={ButtonProperties.VARIANT.primary.name}
                      title="Export to PDF"
                      size={ButtonProperties.SIZES.small}
                      leftIcon={<FaDownload/>}
                      className="!h-8"
                    />
                  </div>
                </div>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState text="You have not added any deductables." />
          </div>
        )}
      </div>

      <CustomModal
        visibility={employeePayrollModal}
        toggleVisibility={setEmployeePayrollModal}
      >
        <div className="mb-10">
          <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-6">
            Add employee payroll
          </h1>
          <div className="px-7">
            <div className="mt-10">
              <Formik
                initialValues={{}}
                onSubmit={() => {}}
                enableReinitialize
                validationSchema={{}}
              >
                {({ setFieldValue }) => (
                  <Form>
                    <div className="mt-8 grid grid-cols-2 gap-6">
                      <div>
                        <FormikCustomSelect
                          label="Select employee *"
                          name="employee"
                          options={[]}
                          onChange={(item: { value: string; text: string }) => {
                            setFieldValue("employee", item.text);
                          }}
                          required
                        />
                      </div>
                      <div>
                        <FormikCustomInput
                          placeholder="Enter net salary"
                          label="Net salary *"
                          inputClassName="bg-transparent"
                          name="netSalary"
                          type="text"
                        />
                      </div>
                    </div>
                    <div className="mt-8 grid grid-cols-2 gap-6">
                      <div>
                        <FormikCustomSelect
                          label="Select deductions *"
                          name="deductions"
                          options={[]}
                          onChange={(item: { value: string; text: string }) => {
                            setFieldValue("deductions", item.text);
                          }}
                          required
                        />
                      </div>
                      <div>
                        <FormikCustomSelect
                          label="Select additions *"
                          name="additions"
                          options={[]}
                          onChange={(item: { value: string; text: string }) => {
                            setFieldValue("additions", item.text);
                          }}
                          required
                        />
                      </div>
                    </div>
                    <div className="mt-8 grid grid-cols-2 gap-6">
                      <div>
                        <FormikCustomInput
                          placeholder="Enter tax state"
                          label="Tax state *"
                          inputClassName="bg-transparent"
                          name="taxState"
                          type="text"
                        />
                      </div>
                      <div>
                        <FormikCustomInput
                          placeholder="Tax number *"
                          label="Tax number *"
                          inputClassName="bg-transparent"
                          name="taxNumber"
                          type="text"
                        />
                      </div>
                    </div>
                    <div className="mt-8 grid grid-cols-2 gap-6">
                      <div>
                        <FormikCustomInput
                          placeholder="Enter employer pension amount"
                          label="Pension employer contribution amount *"
                          inputClassName="bg-transparent"
                          name="employerPension"
                          type="text"
                        />
                      </div>
                      <div>
                        <FormikCustomInput
                          placeholder="Enter employee pension amount *"
                          label="Pension employee contribution amount *"
                          inputClassName="bg-transparent"
                          name="employeePension"
                          type="text"
                        />
                      </div>
                    </div>
                    <div className="mt-8 grid grid-cols-2 gap-6">
                      <div>
                        <FormikCustomInput
                          placeholder="Enter pension code"
                          label="Pension code*"
                          inputClassName="bg-transparent"
                          name="code"
                          type="text"
                        />
                      </div>
                      <div>
                        <FormikCustomInput
                          placeholder="Enter pension pin"
                          label="Pension pin *"
                          inputClassName="bg-transparent"
                          name="pin"
                          type="text"
                        />
                      </div>
                    </div>

                    <div className="flex justify-end mt-20">
                      <CustomButton
                        type="submit"
                        className="!w-[128px] !h-10"
                        title={"Add"}
                        // isLoading={isLoading}
                        handleClick={() => {}}
                        variant={ButtonProperties.VARIANT.primary.name}
                      />
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
          </div>
        </div>
      </CustomModal>
    </div>
  );
};

export default PayrollHistory;
