import React, { useEffect, useMemo, useState } from 'react';
import { getBatches, updateBatchStatus } from '../../../../api/payroll/transaction';
import moment from 'moment';
import CurrencyTag from '../../../atoms/Ui/CurrencyTag';
import CustomButton from '../../../atoms/CustomButton/CustomButton';
import { ButtonProperties } from '../../../shared/helpers';
import { PiMoney } from 'react-icons/pi';

const RunPayroll = () => {
  const [payrollBatch, setPayrollBatch] = useState<any>({});
  const [loading, setLoading] = useState(false);

  const payrollProcessText = useMemo(() => {
    if (payrollBatch.status === 'pending_approval') {
      return 'Pending approval';
    } else if (payrollBatch.status === 'pending_payment') {
      return 'Pending payment';
    } else if (payrollBatch.status === 'processing') {
      return 'Processing';
    } else if (payrollBatch.status === 'completed') {
      return 'Completed';
    }
    return 'Not Generated';
  }, [payrollBatch.status]);

  const fetchPayrollBatches = async () => {
    await getBatches().then((res) => {
      if (res.data.pendingBatches[0]) {
        setPayrollBatch(res.data.pendingBatches[0]);
      } else if (res.data.latestBatches[0]) {
        setPayrollBatch(res.data.latestBatches[0]);
      }
    });
  };

  const updatePayrollBatch = async (status) => {
    setLoading(true);
    await updateBatchStatus(payrollBatch.id, status)
      .then((res) => {
        if (res.success) {
          fetchPayrollBatches();
        }
      })
      .catch((error) => {
        console.error('Error updating payroll batch:', error);
      })
      .then(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    fetchPayrollBatches();
  }, []);

  return (
    <div>
      <div className='rounded-lg'>
        <div className='bg-white py-2 px-6 items-center mb-2'>
          <div className='flex justify-between items-center'>
            <h1 className=' '>Process payroll </h1>
            {/* <StatusTag status='pending' /> */}
            {payrollBatch.status === 'pending_payment' ? (
              <div className='flex justify-end'>
                <CustomButton
                  className='!h-8 px-4'
                  leftIcon={<PiMoney size={14} />}
                  title='Process Now'
                  isLoading={loading}
                  isDisabled={loading}
                  handleClick={() => updatePayrollBatch('processing')}
                  variant={ButtonProperties.VARIANT.secondary.name}
                />
              </div>
            ) : (
              <div className='h-[34px]' />
            )}
          </div>
        </div>

        <div className='py-5 px-5 bg-white h-full'>
          <div className='flex gap-6'>
            <p className='w-[150px] px-4 py-3 bg-purple-light rounded-tr-md rounded-bl-md'>
              Employee's NET pay
            </p>
            <p className='py-3 text-[12px]'>
              <CurrencyTag currency={'NGN'} />
              {new Intl.NumberFormat().format(payrollBatch?.totalAmount ?? 0)}
            </p>
          </div>
          <div className='flex gap-6 mt-1.5'>
            <p className='w-[150px] px-4 py-3 bg-purple-light rounded-tr-md rounded-bl-md'>
              Payment Date
            </p>
            <p className='py-3'>
              {payrollBatch.payment_date
                ? moment(payrollBatch.payment_date).format('D MMMM, YYYY')
                : 'N/A'}
            </p>
          </div>
          <div className='flex gap-6 mt-1.5'>
            <p className='w-[150px] px-4 py-3 bg-purple-light rounded-tr-md rounded-bl-md'>
              Payment Status
            </p>
            <p className='py-3'>{payrollProcessText}</p>
          </div>
          <div className='flex gap-6 mt-1.5'>
            <p className='w-[150px] px-4 py-3 bg-purple-light rounded-tr-md rounded-bl-md'>
              Total Employees
            </p>
            <p className='py-3'>{payrollBatch?.meta?.totalEmployees ?? 0}</p>
          </div>
          {payrollBatch.status !== 'pending_approval' && (
            <div className='flex gap-6 mt-1.5'>
              <p className='w-[150px] px-4 py-3 bg-purple-light rounded-tr-md rounded-bl-md'>
                Paid Employees
              </p>
              <p className='py-3'>{payrollBatch?.meta?.paidEmployees ?? 0}</p>
            </div>
          )}
          {payrollBatch.status === 'pending_approval' && (
            <div className='flex gap-4 mt-6 mb-2'>
              <p className='text-[#FFA500] flex justify-center items-center'>
                Kindly approve this pay run before{' '}
                {moment(payrollBatch.payment_date).format('D MMMM, YYYY ')}
              </p>
              <div className='flex justify-end'>
                <button
                  className={`text-[green] font-semibold font-poppins border hover:bg-[green] border-[green] hover:text-white
                         py-[5px] px-[10px] rounded-[4px] w-[100px] flex justify-center items-center cursor-pointer bg-white ${
                           loading ? 'bg-[green] opacity-70 text-white' : ''
                         }`}
                  onClick={() => updatePayrollBatch('pending_payment')}
                  disabled={loading}
                >
                  Approve
                </button>
              </div>
            </div>
          )}
        </div>
        {/* <div className='mt-4 bg-purple-light py-5 px-6'>
          <p>Next payment date : June 28, 2025</p>
        </div> */}
      </div>
    </div>
  );
};

export default RunPayroll;
