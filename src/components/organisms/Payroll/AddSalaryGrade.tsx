import { Form, Formik } from 'formik';
import React, { useState } from 'react';
import FormikCustomSelect from '../../atoms/CustomInput/FormikCustomSelect';
import FormikCustomInput from '../../atoms/CustomInput/FormikCustomInput';
import CustomButton from '../../atoms/CustomButton/CustomButton';
import { ButtonProperties } from '../../shared/helpers';
import GoBack from '../../atoms/Ui/GoBack';
import { useNavigate } from 'react-router-dom';
import { createSalaryGrade } from '../../../api/payroll/salaryGrade';
import { duration } from 'moment';
import { MdClose } from 'react-icons/md';
import FormikCustomRadioSelect from '../../atoms/CustomInput/FormikCustomRadioSelect';
import { FaTrash } from 'react-icons/fa6';
import { useRecoilValue } from 'recoil';
import { loggedUserAtom } from '../../../recoil/atom/authAtom';
import { PiTrash } from 'react-icons/pi';
import AddSalaryGradeLoaoder from '../../atoms/Skeleton/SalaryLoaders/AddSalaryGradeLoader';

const AddSalaryGrade = () => {

  const navigate = useNavigate();

  const getUserValue = useRecoilValue(loggedUserAtom);
  const userCucrrency = getUserValue?.businesses?.map((item) => item.default_currency)

  const [status, setStatus] = useState<null | 'active' | 'inactive'>('active');
  const [isloading, setIsloading] = useState<boolean>(false);

  const handleStatus = (value: 'active' | 'inactive') => {
    setStatus(prev => (prev === value ? null : value));
  };

  const create = async (values) => {

    setIsloading(true);

    const payload = {
      name: values.name,
      description: values.description,
      frequency: 'monthly',
      amount: values.amount,
      tax_percent: values.tax_percent,
      tax_type: values.tax_type,
      status: status || 'active',
      salary_components: values.salary_components.map(comp => ({
        name: comp.name,
        calculation_type: 'fixed_amount',
        amount: comp.amount,
      })),
    }

    const res = await createSalaryGrade(payload);
    if (res?.status === 201) {
      setIsloading(false)
      navigate('/payroll/salary-grades');
    } else {
      setIsloading(false)
    }

  }

  if (isloading) {
    return (
      <div>
        <AddSalaryGradeLoaoder />
      </div>
    );
  }

  return (
    <div className='mt-'>
      <GoBack />
      <div className=' mt-6 p pb-5 rounded-lg'>
        <div className='mb-10'>
          <h1 className='font-poppins-medium text-20 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-[20px]'>
            Add Salary Grade
          </h1>
          <div className=''>
            <div className=''>
              <Formik
                initialValues={{
                  name: '',
                  description: '',
                  frequency: 'monthly',
                  amount: 0,
                  tax_percent: '',
                  tax_type: 'exclusive',
                  status: 'active',
                  salary_components: [
                    {
                      name: 'Basic',
                      calculation_type: 'fixed_amount',
                      amount: '',
                    },
                  ],
                }}
                onSubmit={(values) => create(values)}
                enableReinitialize
              >
                {({ values, setFieldValue, isSubmitting, submitForm }) => (
                  <Form>

                    <div className='bg-white p-4  '>

                      <div className="text-[1.1rem]">
                        <p>Basic details</p>
                      </div>

                      <div className="">
                        <div className='mt-5 grid grid-cols-7 gap-6'>
                          <div className='col-span-2'>
                            <FormikCustomInput
                              placeholder='Enter name'
                              label='Name *'
                              inputClassName='bg-transparent'
                              name='name'
                            />
                          </div>
                          <div className='col-span-3'>
                            <FormikCustomInput
                              placeholder='Enter description'
                              label='Description *'
                              inputClassName='bg-transparent'
                              name='description'
                            />
                          </div>
                        </div>

                        <div className="flex items-center gap-28 mt-5">
                          <div>
                            <FormikCustomSelect
                              label='Frequency *'
                              name='frequency'
                              options={[
                                { text: 'Weekly', value: 'weekly' },
                                { text: 'Monthly', value: 'monthly' },
                                { text: 'Yearly', value: 'annually' },
                              ]}
                              onChange={(item: { value: string; text: string }) => {
                                setFieldValue('frequency', item.value);
                              }}
                              value={values.frequency}
                              required
                            />
                          </div>
                          <div className="flex items-center gap-5 mt-10">
                            <div className="flex flex-row-reverse gap-3 items-center">
                              <label htmlFor="active">Active</label>
                              <input
                                type="radio"
                                name="status"
                                id="active"
                                value="active"
                                checked={status === 'active'}
                                onChange={() => {
                                  handleStatus('active');
                                  setFieldValue('status', status);
                                }}
                              />
                            </div>
                            <div className="flex flex-row-reverse gap-3 items-center">
                              <label htmlFor="inactive">Inactive</label>
                              <input
                                type="radio"
                                name="status"
                                id="inactive"
                                value={values.status}
                                checked={status === 'inactive'}
                                onChange={() => {
                                  handleStatus('inactive');
                                  setFieldValue('status', status);
                                }}
                              />
                            </div>
                          </div>
                        </div>

                        <div className="mt-10">
                          <div className="text-[1.1rem]">
                            <p >Salary component</p>
                          </div>
                          {values.salary_components.map((val, index) => (
                            <div className="">
                              <div className="flex items-center gap-10 mt-5">
                                <div className="">
                                  <FormikCustomInput
                                    label='Type *'
                                    placeholder='Enter description'
                                    value={val.name}
                                    name='salary_component'
                                    inputClassName='!w-[500px]'
                                    disabled={index === 0}
                                    onChange={(item: { target: { value: string } }) => {
                                      const component = { ...val, name: item.target.value };
                                      const components = values.salary_components;
                                      components[index] = component;
                                      setFieldValue('salary_components', [...components]);
                                    }}
                                  />
                                </div>
                                <div className="">
                                  <FormikCustomInput
                                    label='Amount *'
                                    placeholder='Enter amount'
                                    value={val.amount}
                                    type='number'
                                    inputClassName='!w-[500px]'
                                    name='salary_amount'
                                    onChange={(item: { target: { value: string } }) => {
                                      const component = { ...val, amount: item.target.value };
                                      const components = values.salary_components;
                                      components[index] = component;
                                      setFieldValue('salary_components', [...components]);
                                      setFieldValue(
                                        'amount',
                                        values.salary_components.reduce(
                                          (a, b) => Number(b.amount) + a,
                                          0
                                        )
                                      );
                                    }}
                                  />
                                </div>
                                <div className="flex flex-row-reverse gap-5 items-center mt-10">
                                  <input
                                    type="radio"
                                    id={`fixed-${index}`}
                                    name={`salary_components[${index}].fixed_toggle`}
                                    checked={val.calculation_type === 'fixed_amount'}
                                    onChange={() => {
                                      const updated = [...values.salary_components];
                                      updated[index] = {
                                        ...val,
                                        calculation_type:
                                          val.calculation_type === 'fixed_amount' ? '' : 'fixed_amount',
                                      };
                                      setFieldValue('salary_components', updated);
                                    }}
                                  />
                                  <p>Fixed amount</p>
                                </div>
                                {index != 0 && (
                                  <div className="mt-12">
                                    <button
                                      className=' font-poppins-medium'
                                      disabled={index == 0}
                                      // onClick={() => {
                                      //   setFieldValue(
                                      //     'salary_components',
                                      //     values.salary_components.filter(
                                      //       (value, idx) => index !== idx
                                      //     )
                                      //   );
                                      // }}
                                      onClick={() => {
                                        const updatedComponents = values.salary_components.filter(
                                          (_, idx) => index !== idx
                                        );
                                        const newTotalAmount = updatedComponents.reduce(
                                          (sum, item) => sum + Number(item.amount || 0),
                                          0
                                        );
                                        setFieldValue('salary_components', updatedComponents);
                                        setFieldValue('amount', newTotalAmount);
                                      }}

                                    >
                                      {/* <FaTrash size={15} color='#A0A0A5' /> */}
                                      <PiTrash size={20} color='#A0A0A5' />
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                        <div className="mt-5">
                          <button
                            className='text-purple-normal-hover text-[1.1rem] font-poppins-medium ml-3'
                            onClick={() => {
                              setFieldValue('salary_components', [
                                ...values.salary_components,
                                {
                                  name: '',
                                  calculation_type: 'fixed_amount',
                                  amount: '',
                                },
                              ]);
                            }}
                          >
                            + Add allowance
                          </button>
                        </div>

                      </div>

                      <div className='mt-14 grid grid-cols-2 gap-6'>
                        <div>
                          <FormikCustomInput
                            placeholder='Enter tax percentage '
                            label='Tax percentage *'
                            inputClassName='bg-transparent'
                            name='tax_percent'
                            type='number'
                            required
                          />
                        </div>
                        <div className="flex items-center gap-5 mt-10">
                          <div className="flex flex-row-reverse gap-3 items-center">
                            <label htmlFor="inclusive">Inclusive</label>
                            <input
                              type="radio"
                              name="tax_type"
                              id="inclusive"
                              value="inclusive"
                              checked={values.tax_type === 'inclusive'}
                              onChange={() => setFieldValue('tax_type', 'inclusive')}
                            />
                          </div>
                          <div className="flex flex-row-reverse gap-3 items-center">
                            <label htmlFor="exclusive">Exclusive</label>
                            <input
                              type="radio"
                              name="tax_type"
                              id="exclusive"
                              value="exclusive"
                              checked={values.tax_type === 'exclusive'}
                              onChange={() => setFieldValue('tax_type', 'exclusive')}
                            />
                          </div>
                        </div>
                        <div className="flex items-center gap-2 mt-5">
                          <div className="border px-6 py-3 rounded bg-[#EEF2FF]">
                            <p>Total gross pay</p>
                          </div>
                          <div className="border px-6 py-3 rounded">
                            <p><span className="text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1">{userCucrrency}</span> {values.amount.toLocaleString()}</p>
                          </div>
                        </div>
                      </div>
                      <div className='flex justify-end mt-20'>
                        <CustomButton
                          type='submit'
                          className='!w-[128px] !h-10'
                          title={'Save'}
                          isLoading={isSubmitting}
                          handleClick={() => { }}
                          variant={ButtonProperties.VARIANT.primary.name}
                        />
                      </div>
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
          </div>
        </div>
      </div>
    </div >
  );
};

export default AddSalaryGrade;