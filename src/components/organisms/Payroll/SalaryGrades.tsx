import React, { useEffect, useRef, useState } from 'react';
import { Eye, UserEdit } from 'iconsax-react';
import { useNavigate } from 'react-router-dom';
import Loader from '../../../components/atoms/Loader';
import FilterDropdown from '../../../components/atoms/Cards/FilterDropdown';
import { FaEllipsisV, FaUsers } from 'react-icons/fa';
import { PiTrash } from 'react-icons/pi';
import CustomButton from '../../../components/atoms/CustomButton/CustomButton';
import { FiPlus } from 'react-icons/fi';
import CustomTable from '../../../components/atoms/CustomTable/CustomTable';
import OrganizationEmptyState from '../../../components/atoms/Cards/OrganizationEmptyState';
import {
  deleteSalaryGrade,
  getSalaryGrades,
  manageEmployeesOnSalaryGrade,
} from '../../../api/payroll/salaryGrade';
import CustomModal from '../../atoms/CustomModal/CustomModal';
import useClickOutside from '../../shared/hooks';
import { ButtonProperties } from '../../shared/helpers';
import { getStaff } from '../../../api/staff';
import CustomCheckBox from '../../atoms/CustomCheckBox/CustomCheckBox';
import { MdClose } from 'react-icons/md';
import StatusTag from '../../atoms/StatusTag';
import { IoWarning } from 'react-icons/io5';
import { useRecoilValue } from 'recoil';
import { loggedUserAtom } from '../../../recoil/atom/authAtom';
import MangeSalaryGrades from './ManageSalaryGrades';
import SalaryGradesLoader from '../../atoms/Skeleton/SalaryLoaders/SalaryGradeLoader';

const SalaryGrades = () => {
  const navigate = useNavigate();
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [rowId, setRowId] = useState(0);
  const [salaryGrades, setSalaryGrades] = useState<any>([]);
  const [employeeModal, setEmployeeModal] = useState<boolean>(false);
  const [salaryGrade, setSalaryGrade] = useState<any>(null);
  const [employees, setEmployees] = useState<any>([]);
  const [selectedEmployees, setSelectedEmployees] = useState<any>([]);
  const [isDelete, setIsDelete] = useState<boolean>(false);
  const [staffDetails, setStaffDetails] = useState<any>('');
  const getUserValue = useRecoilValue(loggedUserAtom);
  const [pageNumber, setPageNumber] = useState<number>(1);

  const userCucrrency = getUserValue?.businesses?.map((item) => item.default_currency);

  const fetchSalaryGrades = (q?: string) => {
    setIsFetching(true);
    getSalaryGrades().then((res) => {
      if (res.success) {
        setIsFetching(false);
        setSalaryGrades(res.data);
      }
    });
  };

  // const node = useClickOutside(() => {
  //   setShowDropdown(false);
  //   setRowId(0);
  // });

  const manageEmployees = (row: any) => {
    setSalaryGrade(row.cell.row.original);
    setEmployeeModal(true);
    setShowDropdown(false);
    const salaryGradeId = row.cell.row.original.id;
    const selectedStaffIds = employees
      .filter((s: any) => s.payroll_salary_grade_id == salaryGradeId)
      .map((s: any) => s.id);
    setSelectedEmployees(selectedStaffIds);
  };

  const handleDeleteSalaryGrade = (id: number) => {
    setIsLoading(true);
    deleteSalaryGrade(id)
      .then((res) => {
        setIsLoading(false);
        if (res?.status === 200 || res?.status === 204) {
          setShowDropdown(false);
          fetchSalaryGrades();
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  useEffect(() => {
    fetchSalaryGrades();
  }, []);

  const salaryGradeColumns = [
    {
      Header: 'Title',
      accessor: 'name',
      Cell: (row: any) => <p> {row.cell.value || '--'}</p>,
    },

    {
      Header: 'Amount',
      accessor: 'amount',
      Cell: (row) => (
        <div>
          <span className='text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1'>
            {userCucrrency}
          </span>{' '}
          {new Intl.NumberFormat().format(row.cell.value ?? 0) || '--'}
        </div>
      ),
    },
    {
      Header: 'Frequency',
      accessor: 'frequency',
      Cell: (row: any) => <p className='lowercase'> {row.cell.value || '--'}</p>,
    },
    {
      Header: 'Tax',
      accessor: 'tax_percent',
      Cell: (row: any) => <p>{row.cell.value || '--'} % </p>,
    },
    {
      Header: 'Status',
      accessor: 'status',
      Cell: (row: any) => <StatusTag status={row.cell.value?.toLowerCase() || '--'} />,
    },
    {
      Header: '',
      accessor: 'action',
      Cell: (row: any) => (
        <div className='relative'>
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className='text-[#98A2B3] cursor-pointer'
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className='text-14 text-neutral-dark'>
                <li onClick={() => {}} className='flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer'>
                  <Eye size={18} />
                  View
                </li>
                <li
                  onClick={() => manageEmployees(row)}
                  className='flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer'
                >
                  <FaUsers size={18} />
                  Manage employees
                </li>
                {/* <li
                  onClick={() => {
                    navigate(`/crm/add-customer`, {
                      state: { isEdit: true, data: row.cell.row.original },
                    });
                  }}
                  className='flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer'
                >
                  <UserEdit size={18} />
                  Edit
                </li> */}

                {/* {showDeleteWarn ? (
                  <li className='pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal '>
                    <div className='flex gap-3'>
                      <PiTrash size={18} />
                      Are you sure?
                    </div>
                    <div className='grid grid-cols-2 gap-5 mt-8 ml-2 mr-4'>
                      <CustomButton
                        isLoading={isLoading}
                        title='Yes'
                        handleClick={() => handleDeleteSalaryGrade(row.cell.row.original.id)}
                        className='border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer'
                      />
                      <span
                        onClick={() => {
                          setShowDeleteWarn(false);
                        }}
                        className='text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center'
                      >
                        No
                      </span>
                    </div>
                  </li>
                ) : ( */}
                <li
                  onClick={() => {
                    setIsDelete(true);
                    setStaffDetails(row.cell.row.original.id);
                  }}
                  className='flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer'
                >
                  <PiTrash size={18} />
                  Delete
                </li>
                {/* )} */}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  if (isFetching && !searchQuery && salaryGrades.length === 0) {
    return (
      <div>
        <SalaryGradesLoader />
      </div>
    );
  }

  return (
    <>
      <div>
        {/* <h1 className="font-poppins font-semibold text-24">Customers</h1> */}
        <div className='bg-white flex justify-between items-center px-2 py-2 h-[40px]'>
          <h1 className='ml-2'>Salary grades</h1>
          <div>
            <CustomButton
              className='!w-[160px] !h-[40px] !text-white !bg-purple-dark !border-none !h-10'
              isTransparent={true}
              handleClick={() => {
                navigate('/payroll/add-salary-grade');
              }}
              leftIcon={<FiPlus className='mr-1' size={16} />}
              title='Add salary grade'
            />
          </div>
        </div>
        <div className='mt-2'>
          {salaryGrades?.length > 0 ? (
            <CustomTable
              data={salaryGrades || []}
              columns={salaryGradeColumns}
              // hideSearch
              header={<></>}
            />
          ) : (
            <div className='flex justify-center items-center py-[120px]'>
              <OrganizationEmptyState
                buttonTitle='Add new salary grade'
                handleClick={() => navigate('/payroll/add-salary-grade')}
              />
            </div>
          )}
        </div>
      </div>

      <CustomModal
        visibility={employeeModal}
        toggleVisibility={setEmployeeModal}
        cardClassName='!w-[700px] h-full'
      >
        <MangeSalaryGrades
          name={salaryGrade?.name}
          salaryGradeId={salaryGrade?.id}
          onClose={() => setEmployeeModal(false)}
        />
      </CustomModal>

      <CustomModal visibility={isDelete} toggleVisibility={setIsDelete} cardClassName='!w-[500px]'>
        <div className='flex flex-col justify-end items-center gap-5 py-14 px-5'>
          <div className=' text-gray-700'>
            <div className='flex flex-col justify-end items-center mb-3'>
              <IoWarning color='red' size={35} />
            </div>
            <p className='text-[1.1rem] font-bold'>Delete salary grade</p>
          </div>

          {/* <div className=" text-gray-700">
            <p>You are about to delete <span className='text-red-500 font-bold ml-2'>John doe - Senior engineer</span></p>
            <p className='text-red-500 font-bold ml-2'>with ID #65645455888 <span className='text-gray-700 font-medium ml-2'>from grading list</span></p>
            <p>This action is parmanent and will affect payroll records</p>
          </div> */}

          <div className='flex flex-col justify-center items-center gap-2 text-gray-700'>
            <p>This action will :</p>
            <ul className='flex flex-col justify-center items-center list-disc gap-1'>
              <li>Remove their current salary grade association</li>
              <li>Prevent them from being paid under this grade</li>
              <li>Not affect past payment history</li>
            </ul>
          </div>

          <div className='flex justify-center gap-5'>
            <CustomButton
              className='!w-[210px] !bg-gray-300 !text-gray-800 !border-none !font-normal !font-poppins-medium '
              isTransparent={true}
              handleClick={() => {
                {
                  setIsDelete(false);
                  setShowDropdown(false);
                }
              }}
              title='Discard'
            />

            <CustomButton
              className='!w-[210px] !bg-red-500 !text-white !border-none !font-normal !font-poppins-medium '
              isTransparent={true}
              handleClick={() => {
                handleDeleteSalaryGrade(staffDetails);
                setIsDelete(false);
              }}
              title='yes, confirm'
            />
          </div>
        </div>
      </CustomModal>
    </>
  );
};

export default SalaryGrades;
