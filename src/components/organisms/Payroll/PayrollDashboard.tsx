import { ArrowCircleDown2, ArrowCircleUp2, Note, Lock } from 'iconsax-react';
import React, { useEffect, useState } from 'react';
import CustomButton from '../../atoms/CustomButton/CustomButton';
import { ButtonProperties } from '../../shared/helpers';
import CurrencyTag from '../../atoms/Ui/CurrencyTag';
import CustomTable from '../../atoms/CustomTable/CustomTable';
import moment from 'moment';
import RunPayroll from './PayRuns/RunPayroll';
import StatisticsCard from '../../atoms/Cards/StatisticsCard';
import { PiPlus } from 'react-icons/pi';
import CustomModal from '../../atoms/CustomModal/CustomModal';
import { usePaystackPayment } from 'react-paystack';
import { clustarkToast } from '../../atoms/Toast';
import { NotificationTypes } from '../../shared/helpers/enums';
import { loggedUserAtom } from '../../../recoil/atom/authAtom';
import { useRecoilState, useRecoilValue } from 'recoil';
import OpenSquareArrow from '../../../assets/icons/OpenSquareArrow';
import { IoCubeOutline } from 'react-icons/io5';
import { MdOutlineDoNotDisturbOn } from 'react-icons/md';
import CustomRadioButton from '../../atoms/CustomInput/CustomRadioButton';
import { Form, Formik } from 'formik';
import FormikCustomInput from '../../atoms/CustomInput/FormikCustomInput';
import FormikCustomSelect from '../../atoms/CustomInput/FormikCustomSelect';
import {
  getSavedCardsAtom,
  getWalletBalanceAtom,
  getWalletTransactionsAtom,
} from '../../../recoil/atom/payroll';
import {
  getSavedCards,
  getWalletBalance,
  getWalletTransactions,
  payWithSavedCard,
} from '../../../api/payroll/wallet';
import PayrollDashboardSkeleton from '../../atoms/Skeleton/PayrollDashboardSkeleton';
import { getTransactions } from '../../../api/payroll/transaction';
import StatusTag from '../../atoms/StatusTag';

const PayrollDashboard = () => {
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [fundWalletModal, setFundWalletModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentAmount, setCurrentAmount] = useState<string>('');
  const [paymentOption, setPaymentOption] = useState<string>('card');
  const [, setWalletBalance] = useRecoilState(getWalletBalanceAtom);
  const [, setWalletTransactions] = useRecoilState(getWalletTransactionsAtom);
  const [, setSavedCards] = useRecoilState(getSavedCardsAtom);
  const [transactions, setTransactions] = useState<any>([]);

  const loggedUser = useRecoilValue(loggedUserAtom);
  const walletBalanceValue = useRecoilValue(getWalletBalanceAtom);
  const walletTransactionsValue = useRecoilValue(getWalletTransactionsAtom);
  const savedCardsValue = useRecoilValue(getSavedCardsAtom);

  const fetchWalletBalance = async () => {
    setIsFetching(true);
    await getWalletBalance().then((res) => {
      if (res.success) {
        setWalletBalance(res.data);
        setIsFetching(false);
      }
    });
  };

  const fetchWalletTransactions = async () => {
    setIsFetching(true);
    await getWalletTransactions().then((res) => {
      if (res.success) {
        setWalletTransactions(res.data);
        setIsFetching(false);
      }
    });
  };

  const fetchSavedCards = async () => {
    await getSavedCards().then((res) => {
      if (res.success) {
        setSavedCards(res.data);
      }
    });
  };

  const fetchTransactions = async () => {
    await getTransactions(10, 1).then((res) => {
      if (res.success) {
        setTransactions(res.data.data);
      }
    });
  };

  useEffect(() => {
    fetchWalletBalance();
    fetchWalletTransactions();
    fetchSavedCards();
    fetchTransactions();
  }, []);

  const config: any = {
   channels: [paymentOption],
    reference: new Date().getTime().toString(),
    email: loggedUser?.email,
    amount: Number(currentAmount.replace(/,/g, '')) * 100,
    publicKey: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY,
    metadata: {
      user_id: loggedUser?.id,
      type: 'fund_wallet',
    },
  };

  const initializePayment = usePaystackPayment(config);

  const onSuccess = (reference: any) => {
    console.log(reference);
    clustarkToast(NotificationTypes.SUCCESS, 'Wallet funding was successful');
    setFundWalletModal(false);
    setCurrentAmount('');
  };

  const onClose = () => {
    console.log('first');
  };

  const savedCardsOptions = savedCardsValue?.map((data) => ({
    text: data?.card_type + '****' + data?.card4,
    value: data?.token,
  }));

  const processSavedCardPayment = async (cardToken) => {
    setIsLoading(true);
    const payload = {
      chargeToken: cardToken,
      amount: currentAmount,
      type: 'fund_wallet',
    };
    await payWithSavedCard(payload)
      .then((res) => {
        if (res?.success) {
          setIsLoading(false);
          clustarkToast(NotificationTypes.SUCCESS, 'Wallet funding was successful');
          setFundWalletModal(false);
          fetchWalletBalance();
        }
        setIsLoading(false);
      })
      .catch(() => setIsLoading(false));
  };

  const payrollStats = [
    {
      title: 'Available balance',
      value: walletBalanceValue?.available_balance?.toLocaleString(),
      icon: <IoCubeOutline className='rotate-90' size={24} />,
      valueText: '',
      cardBackgroundColor: '#EEF2FFCC',
      iconBackgroundColor: '#3730A399',
      currency: <CurrencyTag currency={walletBalanceValue?.currency} />,
    },
    {
      title: 'Ledger balance',
      value: walletBalanceValue?.balance?.toLocaleString(),
      icon: <OpenSquareArrow />,
      valueText: '',
      iconBackgroundColor: '#B3AA0199',
      cardBackgroundColor: '#FDFFE8CC',
      currency: <CurrencyTag currency={walletBalanceValue?.currency} />,
    },
    {
      title: 'Active employees',
      value: walletBalanceValue?.activeEmployees?.toLocaleString(),
      icon: <Note size={24} />,
      valueText: '',
      iconBackgroundColor: '#09778399',
      cardBackgroundColor: '#EBFDFFCC',
    },
    {
      title: 'Pending payments',
      value: walletBalanceValue?.pendingPayments?.toLocaleString(),
      icon: <MdOutlineDoNotDisturbOn size={24} />,
      valueText: '',
      iconBackgroundColor: '#A3000099',
      cardBackgroundColor: '#FFEDEDCC',
      currency: <CurrencyTag currency={walletBalanceValue?.currency} />,
    },
  ];

  if (isFetching) {
    return <PayrollDashboardSkeleton />;
  }

  return (
    <>
      <div>
        <div className='bg-white py-4 px-4 rounded-lg'>
          <div className='grid grid-cols-4 gap-5'>
            {payrollStats?.map((item, index) => (
              <div key={`stats-${index}`}>
                <StatisticsCard
                  backgroundColor={item.cardBackgroundColor}
                  title={item.title}
                  value={item.value}
                  icon={item.icon}
                  valueText={item.valueText}
                  currency={item.currency}
                  iconBackgroundColor={item.iconBackgroundColor}
                />
              </div>
            ))}
          </div>
        </div>
        <div className='mt-10 grid grid-cols-2 gap-5'>
          <div>
            <div className='rounded-lg col-span-4 h-[85%]'>
              <div className='bg-white px-4 py-2 flex justify-between items-center'>
                <h1 className=''>Recent transactions</h1>

                <div className='flex justify-end'>
                  <CustomButton
                    className='!h-8 px-4'
                    leftIcon={<PiPlus size={14} />}
                    title='Fund wallet'
                    handleClick={() => {
                      setFundWalletModal(true);
                    }}
                    variant={ButtonProperties.VARIANT.secondary.name}
                    // size={ButtonProperties.SIZES.small}
                  />
                </div>
              </div>
              <div className='bg-white h-full mt-2 px-4'>
                {walletTransactionsValue?.data?.map((data, index) => (
                  <div key={index}>
                    <div className='flex items-center pb-1'>
                      {data?.transaction_type === 'credit' ? (
                        <div>
                          <ArrowCircleUp2 size={24} className='text-[green] mt-4' />
                        </div>
                      ) : (
                        <div>
                          <ArrowCircleDown2 size={24} className='text-[red] mt-4' />
                        </div>
                      )}
                      <div className='ml-4 mt-4'>
                        {data?.transaction_type === 'credit' ? (
                          <p>Wallet funding</p>
                        ) : (
                          <p>Payroll process</p>
                        )}

                        <p className='text-neutral-light text-xs'>
                          {moment().format('DD MMMM YYYY, h:mm A')}
                        </p>
                      </div>
                      <div className='ml-auto mt-4'>
                        <p className='text-neutral-normal'>
                          <CurrencyTag currency='NGN' /> {data?.amount?.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div>
            <RunPayroll />
          </div>
        </div>
        <div>
          <div className='bg-white mt-10 shadow-md rounded-lg'>
              <div className='bg-white px-4 py-2 flex justify-between items-center'>
                <h1 className=''>Recent transactions</h1>
              </div>
            <CustomTable
              data={transactions}
              hideSearch
              columns={[
                {
                  Header: 'Employee Name',
                  accessor: 'staff',
                  Cell: (row) => {
                    return <div>{row.cell.value?.fullName}</div>;
                  },
                },
                {
                  Header: 'Employee ID',
                  accessor: (row) => row?.staff?.staff_identification_tag,
                },
                {
                  Header: 'Transaction Type',
                  accessor: 'transaction_type',
                  Cell: (row) => {
                    return (
                      <div>
                        {row.cell.value === 'salary' ? (
                          <span className='text-green-600'>Salary</span>
                        ) : (
                          <span className='text-blue-600'>Tax</span>
                        )}
                      </div>
                    );
                  },
                },
                {
                  Header: 'Amount',
                  accessor: 'amount',
                  Cell: ({ value }) => {
                    return (
                      <div>
                        <CurrencyTag currency='NGN' />
                        {new Intl.NumberFormat().format(value)}
                      </div>
                    );
                  },
                },
                {
                  Header: 'Bank Name',
                  accessor: 'bank_name',
                },
                {
                  Header: 'Account/Tax Number',
                  accessor: (row) =>
                    row.transaction_type === 'salary' ? row.account_number : row.tax_number,
                },
                {
                  Header: 'Status',
                  accessor: 'status',
                  Cell: (row) => {
                    return <StatusTag status={row.cell.value} />;
                  },
                },
              ]}
            
            />
          </div>
        </div>
      </div>

      <CustomModal
        visibility={fundWalletModal}
        cardClassName='!w-[500px] h-full'
        toggleVisibility={setFundWalletModal}
      >
        <div className='p-6'>
          <Formik
            initialValues={{
              amount: '',
              paymentMethod: '',
              savedPaymentMethod: '',
            }}
            onSubmit={(values) => {
              console.log(values);
              setCurrentAmount(values.amount);
              values.savedPaymentMethod
                ? processSavedCardPayment(values.savedPaymentMethod)
                : initializePayment({ onClose, onSuccess });
            }}
            enableReinitialize
          >
            {({ values, setFieldValue }) => (
              <Form>
                {/* Header */}
                <div className='mb-6'>
                  <h2 className='text-lg font-semibold text-gray-900 mb-2'>Add funds</h2>
                  <p className='text-sm text-gray-600'>
                    Top up your account to ensure smooth payroll processing
                  </p>
                </div>

                {/* Amount to add */}
                <div className='mb-6'>
                  <div className=' flex'>
                    <div className=' text-gray-500 font-medium border-t border-l border-b w-20 rounded-tl-md rounded-bl-md text-center py-4'>
                      ₦
                    </div>
                    <div className='w-full'>
                      <FormikCustomInput
                        name='amount'
                        type='text'
                        placeholder='Enter amount you wish to fund'
                        inputClassName='pl-8 bg-gray-100 !w-full !rounded-none !rounded-tr-md !rounded-br-md'
                        required
                        onChange={(e) => {
                          setFieldValue('amount', e.target.value);
                          setCurrentAmount(e.target.value);
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Payment method */}
                <div className='mb-6'>
                  <label className='block text-sm font-medium text-gray-700 mb-3'>
                    Payment method
                  </label>
                  <div className='grid grid-cols-2 gap-4'>
                    <div className='space-y-3'>
                      <CustomRadioButton
                        name='paymentMethod'
                        value='bank'
                        checked={values.paymentMethod === 'bank'}
                        onChange={(value) => {
                          setFieldValue('paymentMethod', value);
                          setPaymentOption(value);
                        }}
                        label='Bank transfer'
                        disabled={!!values.savedPaymentMethod}
                        required
                      />
                      <CustomRadioButton
                        name='paymentMethod'
                        value='USSD'
                        checked={values.paymentMethod === 'USSD'}
                        onChange={(value) => {
                          setFieldValue('paymentMethod', value);
                          setPaymentOption(value);
                        }}
                        label='USSD'
                        disabled={!!values.savedPaymentMethod}
                        required
                      />
                    </div>
                    <div className='space-y-3'>
                      <CustomRadioButton
                        name='paymentMethod'
                        value='card'
                        checked={values.paymentMethod === 'card'}
                        onChange={(value) => {
                          setFieldValue('paymentMethod', value);
                          setPaymentOption(value);
                        }}
                        label='Debit card'
                        disabled={!!values.savedPaymentMethod}
                        required
                      />
                      <div className='flex items-center gap-2'>
                        <CustomRadioButton
                          name='paymentMethod'
                          value='linked_account'
                          checked={values.paymentMethod === 'linked_account'}
                          onChange={(value) => {
                            setFieldValue('paymentMethod', value);
                            setPaymentOption(values.paymentMethod);
                          }}
                          label='Linked account'
                          disabled
                          required
                        />
                        <span className='text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded'>
                          Coming soon
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Saved payment method */}

                <div className='mb-6'>
                  <FormikCustomSelect
                    label='Saved payment method'
                    name='savedPaymentMethod'
                    placeholder='Choose a source account'
                    options={savedCardsOptions}
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue('savedPaymentMethod', item.value);
                    }}
                    value={values.savedPaymentMethod}
                  />
                </div>

                <div className='mb-6 flex items-center gap-2'>
                  <Lock size={16} className='text-orange-600' />
                  <span className='text-sm '>
                    Your payment details are encrypted and processed securely!
                  </span>
                </div>

                <div className='flex gap-3 mt-10'>
                  <CustomButton
                    type='button'
                    className='!w-full !h-12'
                    title='Cancel'
                    handleClick={() => setFundWalletModal(false)}
                    variant={ButtonProperties.VARIANT.secondary.name}
                    isTransparent
                  />
                  <CustomButton
                    type='submit'
                    className='!w-full !h-12'
                    title='Proceed to payment'
                    isLoading={isLoading}
                    // isDisabled={!values.amount || !values.paymentMethod}
                    handleClick={() => {}}
                    variant={ButtonProperties.VARIANT.primary.name}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </>
  );
};

export default PayrollDashboard;
