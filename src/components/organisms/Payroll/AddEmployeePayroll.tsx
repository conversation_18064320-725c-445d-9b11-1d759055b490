import { Form, Formik } from "formik";
import React, { useState } from "react";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties, days28, getNameInitials } from "../../shared/helpers";
import GoBack from "../../atoms/Ui/GoBack";
import { ArrowDown2, ArrowUp2 } from "iconsax-react";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../shared/hooks";
import { useLocation } from "react-router-dom";

const AddEmployeePayroll = () => {
  const location = useLocation();
  const { isEdit } = location.state || {};
  const [showDeductions, setShowDeductions] = useState(false);
  const [showAdditions, setShowAdditions] = useState(false);
  const [selectedDeductions, setSelectedDeductions] = useState<any>([]);
  const deductions = ["Fitness", "Dues", "HMO", "Tax"];


  const node = useClickOutside(() => {
    setShowDeductions(false);
    setShowAdditions(false);
  });

  const handleSelectedDeductions = (day) => {
    setSelectedDeductions((prevState: any) => {
      const isAlreadySelected = prevState.some((item) => item === day);
      if (isAlreadySelected) {
        return prevState.filter((item) => item !== day);
      } else {
        return [...prevState, day];
      }
    });
  };

  return (
    <div className="mt-">
      <GoBack />
      <div className=" mt-6 p pb-5 rounded-lg">
        <div className="mb-10">
          <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-[33px]">
            Employee payroll setup
          </h1>
          <div className="">
            <div className="mt-10">
              <Formik
                initialValues={{ employee: "" }}
                onSubmit={() => {}}
                enableReinitialize
                validationSchema={{}}
              >
                {({ values, setFieldValue }) => (
                  <Form>
                    {!isEdit && (
                      <div className="bg-white p-4 rounded-lg">
                        <div className="flex justify-end">
                          <div className="flex gap-3">
                            <p className="mt-1">filter by </p>
                            <div>
                              <FormikCustomSelect
                                name="filterType"
                                options={[
                                  { value: "Department", text: "Department" },
                                  { value: "Branch", text: "Branch" },
                                  { value: "Grade", text: "Grade" },
                                ]}
                                onChange={(item: {
                                  value: string;
                                  text: string;
                                }) => {
                                  setFieldValue("filterType", item.text);
                                }}
                                parentContainer="!h-8"
                                required
                              />
                            </div>
                            <div>
                              <FormikCustomSelect
                                name="department"
                                options={[]}
                                onChange={(item: {
                                  value: string;
                                  text: string;
                                }) => {
                                  setFieldValue("department", item.text);
                                }}
                                parentContainer="!h-8"
                                required
                              />
                            </div>
                          </div>
                        </div>
                        <div className=" gap-6">
                          <div>
                            <FormikCustomSelect
                              label="Select employee * (list of employees not on payroll)"
                              name="employee"
                              options={[
                                { value: "Helen Josie", text: "Helen Josie" },
                              ]}
                              onChange={(item: {
                                value: string;
                                text: string;
                              }) => {
                                setFieldValue("employee", item.text);
                              }}
                              required
                            />
                          </div>
                          <div className="flex justify-end mt-4">
                            <CustomButton
                              type="submit"
                              className="!w-[128px] !h-10"
                              title={"View employee"}
                              // isLoading={isLoading}
                              handleClick={() => {}}
                              variant={ButtonProperties.VARIANT.primary.name}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                    {(values.employee ||
                      isEdit) && (
                        <div className="bg-white p-4 mt-8 rounded-lg">
                          <div className="flex justify-center ">
                            <div>
                              <div>

                                <div className="w-[120px] h-[120px] p-2 bg-purple-light-hover rounded-full">
                                  <div className=" w-full h-full flex justify-center items-center bg-purple-light-active rounded-full">
                                    <p className="text-[40px] text-purple-normal font-poppins-medium">
                                      {getNameInitials(
                                        "Helen",
                                        "Josie"
                                      )}
                                    </p>
                                  </div>
                                </div>
                                <p className="text-center mt-2 text-20">Helen Josie</p>
                              </div>

                            </div>
                          </div>
                          <div className="mt-14 grid grid-cols-2 gap-6">
                            <div>
                              <FormikCustomSelect
                                label="Salary duration *"
                                name="salaryDuration"
                                options={[
                                  { text: "Daily", value: "Daily" },
                                  { text: "Weekly", value: "Weekly" },
                                  { text: "Monthly", value: "Monthly" },
                                  { text: "Yearly", value: "Yearly" },
                                ]}
                                onChange={(item: {
                                  value: string;
                                  text: string;
                                }) => {
                                  setFieldValue("salaryDuration", item.text);
                                }}
                                required
                              />
                            </div>
                            <div>
                              <FormikCustomInput
                                placeholder="Enter net salary"
                                label="Net salary *"
                                inputClassName="bg-transparent"
                                name="netSalary"
                                type="text"
                              />
                            </div>
                          </div>
                          <div className="mt-8 grid grid-cols-2 gap-6">
                            <div ref={node}>
                              <div className="text-16 mb-4">
                                <label htmlFor="deductions">
                                  Select deductions *
                                </label>
                              </div>
                              <div className="relative">
                                <div
                                  onClick={() => setShowDeductions(true)}
                                  className={`w-full px-6 flex justify-between items-center rounded h-[47px] border-[0.6px] border-[#B2BBC699]`}
                                >
                                  <div
                                    className="flex flex-wrap gap-3"
                                    onClick={() => {}}
                                  >
                                    {selectedDeductions.map((item, index) => (
                                      <p
                                        key={index}
                                        className="flex justify-between bg-purple-light font-poppins-medium text-purple-normal px-2 w-fit text-center"
                                      >
                                        {item}
                                      </p>
                                    ))}{" "}
                                  </div>
                                  {showDeductions ? (
                                    <ArrowUp2
                                      size={16}
                                      className="text-neutral-dark mt-1 ml-3"
                                    />
                                  ) : (
                                    <ArrowDown2
                                      size={16}
                                      className="text-neutral-dark mt-1 ml-3"
                                    />
                                  )}
                                </div>
                                {showDeductions && (
                                  <FilterDropdown className="!top-16 !right-0">
                                    <div className="flex flex-wrap gap-3 py-4 px-3">
                                      {deductions.map((item, index) => (
                                        <p
                                          className={` text-neutral-normal px-1 w-fit text-center cursor-pointer text-14 rounded-[1px] ${
                                            selectedDeductions?.some(
                                              (value: any) => value === item
                                            )
                                              ? "bg-purple-normal text-white"
                                              : "border-[0.5px] border-neutral-light"
                                          }`}
                                          onClick={() =>
                                            handleSelectedDeductions(item)
                                          }
                                          key={index}
                                        >
                                          {item}
                                        </p>
                                      ))}
                                    </div>
                                  </FilterDropdown>
                                )}
                              </div>
                            </div>
                            <div>
                              <div ref={node}>
                                <div className="text-16 mb-4">
                                  <label htmlFor="deductions">
                                    Select additions *
                                  </label>
                                </div>
                                <div className="relative">
                                  <div
                                    onClick={() => setShowAdditions(true)}
                                    className={`w-full px-6 flex justify-between items-center rounded h-[47px] border-[0.6px] border-[#B2BBC699]`}
                                  >
                                    <div
                                      className="flex flex-wrap gap-3"
                                      onClick={() => {}}
                                    >
                                      {selectedDeductions.map((item, index) => (
                                        <p
                                          key={index}
                                          className="flex justify-between bg-purple-light font-poppins-medium text-purple-normal px-2 w-fit text-center"
                                        >
                                          {item}
                                        </p>
                                      ))}{" "}
                                    </div>
                                    {showAdditions ? (
                                      <ArrowUp2
                                        size={16}
                                        className="text-neutral-dark mt-1 ml-3"
                                      />
                                    ) : (
                                      <ArrowDown2
                                        size={16}
                                        className="text-neutral-dark mt-1 ml-3"
                                      />
                                    )}
                                  </div>
                                  {showAdditions && (
                                    <FilterDropdown className="!top-16 !right-0">
                                      <div className="flex flex-wrap gap-3 py-4 px-3">
                                        {deductions.map((item, index) => (
                                          <p
                                            className={` text-neutral-normal px-1 w-fit text-center cursor-pointer text-14 rounded-[1px] ${
                                              selectedDeductions?.some(
                                                (value: any) => value === item
                                              )
                                                ? "bg-purple-normal text-white"
                                                : "border-[0.5px] border-neutral-light"
                                            }`}
                                            onClick={() =>
                                              handleSelectedDeductions(item)
                                            }
                                            key={index}
                                          >
                                            {item}
                                          </p>
                                        ))}
                                      </div>
                                    </FilterDropdown>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="mt-8 grid grid-cols-2 gap-6">
                            <div>
                              <FormikCustomInput
                                placeholder="Enter tax state"
                                label="Tax state *"
                                inputClassName="bg-transparent"
                                name="taxState"
                                type="text"
                              />
                            </div>
                            <div>
                              <FormikCustomInput
                                placeholder="Tax number *"
                                label="Tax number *"
                                inputClassName="bg-transparent"
                                name="taxNumber"
                                type="text"
                              />
                            </div>
                          </div>
                          <div className="mt-8 grid grid-cols-2 gap-6">
                            <div>
                              <FormikCustomInput
                                placeholder="Enter employer pension amount"
                                label="Pension employer contribution amount *"
                                inputClassName="bg-transparent"
                                name="employerPension"
                                type="text"
                              />
                            </div>
                            <div>
                              <FormikCustomInput
                                placeholder="Enter employee pension amount *"
                                label="Pension employee contribution amount *"
                                inputClassName="bg-transparent"
                                name="employeePension"
                                type="text"
                              />
                            </div>
                          </div>
                          <div className="mt-8 grid grid-cols-2 gap-6">
                            <div>
                              <FormikCustomInput
                                placeholder="Enter pension code"
                                label="Pension code*"
                                inputClassName="bg-transparent"
                                name="code"
                                type="text"
                              />
                            </div>
                            <div>
                              <FormikCustomInput
                                placeholder="Enter pension pin"
                                label="Pension pin *"
                                inputClassName="bg-transparent"
                                name="pin"
                                type="text"
                              />
                            </div>
                          </div>
                          <div className="mt-8 gap-6">
                            <div>
                              <FormikCustomSelect
                                label="Salary pay date *"
                                name="salaryPayDate"
                                options={days28}
                                onChange={(item: {
                                  value: string;
                                  text: string;
                                }) => {
                                  setFieldValue("salaryPayDate", item.text);
                                }}
                                required
                              />
                            </div>
                          </div>

                          <div className="flex justify-end mt-20">
                            <CustomButton
                              type="submit"
                              className="!w-[128px] !h-10"
                              title={"Submit"}
                              // isLoading={isLoading}
                              handleClick={() => {}}
                              variant={ButtonProperties.VARIANT.primary.name}
                            />
                          </div>
                        </div>
                      )}
                  </Form>
                )}
              </Formik>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddEmployeePayroll;
