import React from 'react';
import { NavLink, Outlet, useLocation } from 'react-router-dom';
import { FaCheck } from 'react-icons/fa6';
import { PAYROLL_ROUTE_NAMES } from '../../../routes/tabRoutes';

const Payroll = () => {
  const location = useLocation();
  const { pathname } = location;

  const payrollSettingsTab = [
    {
      path: PAYROLL_ROUTE_NAMES.business,
      title: 'Business Setup',
      completed: true,
      main: 'business',
    },
    { path: PAYROLL_ROUTE_NAMES.bank, title: 'Bank Setup', completed: false, main: 'bank' },
    { path: PAYROLL_ROUTE_NAMES.deductables, title: 'Deductibles', main: 'deductibles' },
    { path: PAYROLL_ROUTE_NAMES.paye, title: 'PAYE', main: 'paye' },
    { path: PAYROLL_ROUTE_NAMES.nhf, title: 'NHF', main: 'nhf' },
    { path: PAYROLL_ROUTE_NAMES.wallet, title: 'Wallet Setup', main: 'virtual-account' },
  ];

  return (
    <div>
      <div className='px-4 mt-5 bg-white flex justify-between'>
        <div className='flex gap-10 py-6'>
          {payrollSettingsTab.map((tabItem) => (
            <NavLink
              key={tabItem.path}
              to={`${tabItem.path}`}
              className={({ isActive }) =>
                `cursor-pointer flex text-16 pb-2 px-5 ${
                  isActive || pathname.includes(tabItem?.main)
                    ? 'text-purple-normal font-semibold border-b-4 rounded-b border-purple-normal'
                    : 'text-neutral-normal'
                }`
              }
            >
              <div>{tabItem.title}</div>
              {tabItem.completed && (
                <span className='bg-alert-text-success ml-3 rounded-full h-5 w-5 flex justify-center items-center'>
                  <FaCheck color='#fff' size={12} />
                </span>
              )}
            </NavLink>
          ))}
        </div>
      </div>
      <Outlet />
    </div>
  );
};

export default Payroll;
