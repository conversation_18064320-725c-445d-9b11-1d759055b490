import React, { useState } from "react";
import { FiPlus } from "react-icons/fi";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import CustomTable from "../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../atoms/Cards/OrganizationEmptyState";
import StatusTag from "../../atoms/StatusTag";
import { ArrowDown2, ArrowUp2, Eye, UserEdit } from "iconsax-react";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import { FaEdit, FaEllipsisV } from "react-icons/fa";
import useClickOutside from "../../shared/hooks";
import { PiTrash } from "react-icons/pi";
import { MdRemoveCircleOutline } from "react-icons/md";
import { ButtonProperties, getNameInitials } from "../../shared/helpers";
import { Form, Formik } from "formik";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import { useNavigate } from "react-router-dom";

const EmployeeSetup = () => {
  const navigate = useNavigate();
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [showEmployee, setShowEmployee] = useState<boolean>(false);
  const [rowId, setRowId] = useState(0);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showDeductions, setShowDeductions] = useState(false);
  const [showAdditions, setShowAdditions] = useState(false);
  const [selectedDeductions, setSelectedDeductions] = useState<any>([]);

  const deductions = ["Fitness", "Dues", "HMO", "Tax"];

  const handleSelectedDeductions = (day) => {
    setSelectedDeductions((prevState: any) => {
      const isAlreadySelected = prevState.some((item) => item === day);
      if (isAlreadySelected) {
        return prevState.filter((item) => item !== day);
      } else {
        return [...prevState, day];
      }
    });
  };

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setShowDeleteWarn(false);
    setRowId(0);
    setShowAdditions(false);
    setShowDeductions(false);
  });
  const employeePayrollData = [
    {
      id: 1,
      name: "Giffy Onyinye",
      staffId: "#CH784",
      amount: "400,000",
      department: "Finance",
      status: "Paid",
      type: "Full-Time",
      lastPayment: "12-01-2025",
    },
    {
      id: 1,
      name: "Helen Josie",
      staffId: "#ST8593",
      amount: "200,000",
      department: "Marketing",
      status: "Unpaid",
      lastPayment: "28-01-2025",
      type: "Part-Time",
    },
  ];

  const columns = [
    {
      Header: "Name",
      accessor: "name",
      Cell: (row) => (
        <div className="cursor-pointer" onClick={() => setShowEmployee(true)}>
          {row.cell.value || "--"}
        </div>
      ),
    },
    {
      Header: "Staff ID",
      accessor: "staffId",
    },
    {
      Header: "Department",
      accessor: "department",
    },
    {
      Header: "Value",
      accessor: "amount",
      Cell: (row) => <div>{row.cell.value || "--"}</div>,
    },
    {
      Header: "Last Payment",
      accessor: "lastPayment",
      Cell: (row) => <div>{row.cell.value || "--"}</div>,
    },
    {
      Header: "Type",
      accessor: "type",
      Cell: (row) => <div>{row.cell.value || "--"}</div>,
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: (row) => <StatusTag status={row.cell.value} />,
    },
    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                 <li
                  onClick={() => {
                    setShowEmployee(true)
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>
                {/* <li
                  onClick={() => {
                    navigate("/payroll/add-payroll-setup"), {state: {isEdit: "true"}};
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <UserEdit size={18} />
                  Edit
                </li>  */}
                {showDeleteWarn ? (
                  <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                    <div className="flex gap-3">
                      <PiTrash size={18} />
                      Are you sure?
                    </div>
                    <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                      <CustomButton
                        //   isLoading={isLoading}
                        title="Yes"
                        handleClick={() => {}}
                        className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer"
                      />
                      <span
                        onClick={() => {
                          setShowDeleteWarn(false);
                        }}
                        className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                      >
                        No
                      </span>
                    </div>
                  </li>
                ) : (
                  <li
                    onClick={() => setShowDeleteWarn(true)}
                    className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer"
                  >
                    <MdRemoveCircleOutline size={18} />
                    Deactivate
                  </li>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  return (
    <div>
      <div className="pt-10 flex justify-end">
        <CustomButton
          className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
          isTransparent={true}
          handleClick={() => navigate("/payroll/add-payroll-setup")}
          leftIcon={<FiPlus className="ml-3" size={20} />}
          title="Add employee payroll"
        />
      </div>

      <div className="">
        {employeePayrollData.length > 0 ? (
          <div className="grid grid-cols-3 gap-6">
            <div className="col-span-2  my-4 px-4 py-[23px]">
              <CustomTable
                data={employeePayrollData || []}
                meta={{}}
                columns={columns}
                header={
                  <div>
                    <h1 className="font-poppins-medium text-purple-normal-active mb-4">
                      Employee payroll Information
                    </h1>
                  </div>
                }
              />
            </div>
            <div className="bg-white shadow-md h-full rounded-lg my-10 px-4 py-[23px]">
              {showEmployee ? (
                <div>
                  <div className="flex justify-between">
                    <div className="flex gap-6">
                      <div>
                        {employeePayrollData.length < 0 ? (
                          <img
                            className="rounded-full w-[80px] h-[80px] object-cover"
                            src={"getStaffByIdValue?.user?.avatar"}
                            alt="User Avatar"
                          />
                        ) : (
                          <div className="w-[80px] h-[80px] p-2 bg-purple-light-hover rounded-full">
                            <div className=" w-full h-full flex justify-center items-center bg-purple-light-active rounded-full">
                              <p className="text-[30px] text-purple-normal font-poppins-medium">
                                {getNameInitials("Giffy", "Onyinye")}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>

                      <div>
                        <h1 className="font-poppins-medium text-purple-normal-active">
                          Giffy Onyinye
                        </h1>
                        <p className="text-neutral-dark text-14 font-medium mt-1">
                          Staff ID:{" "}
                          <span className="rounded-2xl bg-[#F5F6F7] w-fit px-2 py-1 font-poppins-medium">
                            #CH784309
                          </span>
                        </p>
                        <p className="text-neutral-dark text-14 font-medium mt-1">
                          Department: <span className="text-purple-normal">Finance</span> 
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <CustomButton
                        className="!w-[80px] !h-[30px]"
                        leftIcon={<FaEdit />}
                        title="Edit"
                        handleClick={() => {}}
                        variant={ButtonProperties.VARIANT.primary.name}
                      />
                    </div>
                  </div>
                  <div className="mt-10">
                    <Formik
                      initialValues={{}}
                      onSubmit={() => {}}
                      enableReinitialize
                      validationSchema={{}}
                    >
                      {({ setFieldValue }) => (
                        <Form>
                          <div className="mt-8">
                            <div>
                              <FormikCustomInput
                                placeholder="Enter net salary"
                                label="Net salary *"
                                inputClassName="bg-transparent"
                                name="netSalary"
                                type="text"
                              />
                            </div>
                          </div>
                          <div className="mt-8 grid grid-cols-2 gap-6">
                            <div ref={node}>
                              <div className="text-16 mb-4">
                                <label htmlFor="deductions">
                                  Select deductions *
                                </label>
                              </div>
                              <div className="relative">
                                <div
                                  onClick={() => setShowDeductions(true)}
                                  className={`w-full px-6 flex justify-between items-center rounded h-[47px] border-[0.6px] border-[#B2BBC699]`}
                                >
                                  <div
                                    className="flex flex-wrap gap-3"
                                    onClick={() => {}}
                                  >
                                    {selectedDeductions.map((item, index) => (
                                      <p
                                        key={index}
                                        className="flex justify-between bg-purple-light font-poppins-medium text-purple-normal px-2 w-fit text-center"
                                      >
                                        {item}
                                      </p>
                                    ))}{" "}
                                  </div>
                                  {showDeductions ? (
                                    <ArrowUp2
                                      size={16}
                                      className="text-neutral-dark mt-1 ml-3"
                                    />
                                  ) : (
                                    <ArrowDown2
                                      size={16}
                                      className="text-neutral-dark mt-1 ml-3"
                                    />
                                  )}
                                </div>
                                {showDeductions && (
                                  <FilterDropdown className="!top-16 !right-0">
                                    <div className="flex flex-wrap gap-3 py-4 px-3">
                                      {deductions.map((item, index) => (
                                        <p
                                          className={` text-neutral-normal px-1 w-fit text-center cursor-pointer text-14 rounded-[1px] ${
                                            selectedDeductions?.some(
                                              (value: any) => value === item
                                            )
                                              ? "bg-purple-normal text-white"
                                              : "border-[0.5px] border-neutral-light"
                                          }`}
                                          onClick={() =>
                                            handleSelectedDeductions(item)
                                          }
                                          key={index}
                                        >
                                          {item}
                                        </p>
                                      ))}
                                    </div>
                                  </FilterDropdown>
                                )}
                              </div>
                            </div>
                            <div>
                              <div ref={node}>
                                <div className="text-16 mb-4">
                                  <label htmlFor="deductions">
                                    Select additions *
                                  </label>
                                </div>
                                <div className="relative">
                                  <div
                                    onClick={() => setShowAdditions(true)}
                                    className={`w-full px-6 flex justify-between items-center rounded h-[47px] border-[0.6px] border-[#B2BBC699]`}
                                  >
                                    <div
                                      className="flex flex-wrap gap-3"
                                      onClick={() => {}}
                                    >
                                      {selectedDeductions.map((item, index) => (
                                        <p
                                          key={index}
                                          className="flex justify-between bg-purple-light font-poppins-medium text-purple-normal px-2 w-fit text-center"
                                        >
                                          {item}
                                        </p>
                                      ))}{" "}
                                    </div>
                                    {showAdditions ? (
                                      <ArrowUp2
                                        size={16}
                                        className="text-neutral-dark mt-1 ml-3"
                                      />
                                    ) : (
                                      <ArrowDown2
                                        size={16}
                                        className="text-neutral-dark mt-1 ml-3"
                                      />
                                    )}
                                  </div>
                                  {showAdditions && (
                                    <FilterDropdown className="!top-16 !right-0">
                                      <div className="flex flex-wrap gap-3 py-4 px-3">
                                        {deductions.map((item, index) => (
                                          <p
                                            className={` text-neutral-normal px-1 w-fit text-center cursor-pointer text-14 rounded-[1px] ${
                                              selectedDeductions?.some(
                                                (value: any) => value === item
                                              )
                                                ? "bg-purple-normal text-white"
                                                : "border-[0.5px] border-neutral-light"
                                            }`}
                                            onClick={() =>
                                              handleSelectedDeductions(item)
                                            }
                                            key={index}
                                          >
                                            {item}
                                          </p>
                                        ))}
                                      </div>
                                    </FilterDropdown>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="mt-8 grid grid-cols-2 gap-6">
                            <div>
                              <FormikCustomInput
                                placeholder="Enter tax state"
                                label="Tax state *"
                                inputClassName="bg-transparent"
                                name="taxState"
                                type="text"
                              />
                            </div>
                            <div>
                              <FormikCustomInput
                                placeholder="Tax number *"
                                label="Tax number *"
                                inputClassName="bg-transparent"
                                name="taxNumber"
                                type="text"
                              />
                            </div>
                          </div>
                          <div className="mt-8 grid grid-cols-2 gap-6">
                            <div>
                              <FormikCustomInput
                                placeholder="Enter employer pension amount"
                                label="Pension employer contribution amount *"
                                inputClassName="bg-transparent"
                                name="employerPension"
                                type="text"
                              />
                            </div>
                            <div>
                              <FormikCustomInput
                                placeholder="Enter employee pension amount *"
                                label="Pension employee contribution amount *"
                                inputClassName="bg-transparent"
                                name="employeePension"
                                type="text"
                              />
                            </div>
                          </div>
                          <div className="mt-8 grid grid-cols-2 gap-6">
                            <div>
                              <FormikCustomInput
                                placeholder="Enter pension code"
                                label="Pension code*"
                                inputClassName="bg-transparent"
                                name="code"
                                type="text"
                              />
                            </div>
                            <div>
                              <FormikCustomInput
                                placeholder="Enter pension pin"
                                label="Pension pin *"
                                inputClassName="bg-transparent"
                                name="pin"
                                type="text"
                              />
                            </div>
                          </div>
                        </Form>
                      )}
                    </Formik>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="flex justify-center items-center py-[120px]">
                    <OrganizationEmptyState text="Select an employeee to view details" />
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState text="You have not added any deductables." />
          </div>
        )}
      </div>
    </div>
  );
};

export default EmployeeSetup;
