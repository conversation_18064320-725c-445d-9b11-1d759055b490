import React from "react";
import CustomToggle from "../../atoms/CustomToggle/CustomToggle";

const NHF = () => {


  return (
    <div className="my-10 pb-10 bg-white rounded-bl-[10px] rounded-br-[10px]">
      
      <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-[33px]">
          NHF (National Housing Fund)
        </h1>
        <div className="px-6 mt-6">
          <p className="text-16 text-neutral-normal leading-6">
            National Housing Fund (NHF) contributions are applicable to Nigerian employees earning a minimum of NGN 30, 000 per annum (national minimum wage and above). 
            The employer is required to deduct 2.5% of the monthly income from employees and rmit it tot the Federal Mortgage Bank of Nigeria within one month of deduction. 
            Private sector employees are now excluded from complusory compliance with this obligation and may hence forth contribute the requisite 2.5% of their monthly income to the NHF voluntarily.
          </p>

          <div className="flex justify-between px-6 py-6 bg-gray-100 mt-10 rounded-xl">
            <p className="text-20">Enable NHF for all employee</p>
            <CustomToggle/>
          </div>
        </div>
    </div>
  );
};

export default NHF;
