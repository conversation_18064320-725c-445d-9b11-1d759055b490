import { Form, Formik } from "formik";
import React, { useState } from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../shared/helpers";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";

const PauseLoan = (closeModal) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  return (
    <div>
      <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
        <h1 className="font-poppins-medium text-18 text-purple-dark ">
          Pause loan installment
        </h1>
      </div>
      <div className=" pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
        <Formik
          initialValues={{
            name: "",
            description: "",
            duration: "",
            charge: "",
          }}
          onSubmit={() => {}}
          validationSchema={{}}
          enableReinitialize
        >
          {({ values, setFieldValue }) => (
            <Form>
              <div>
                <div className=" flex gap-5">
                  <label htmlFor="immediately">
                    <input
                      type="radio"
                      name="immediately"
                      className="!mr-3 accent-purple-normal !outline-none"
                    />
                    Immediately
                  </label>
                  <label htmlFor="On scheduled date">
                    <input
                      type="radio"
                      name="immediately"
                      className="!mr-3 accent-purple-normal !outline-none"
                    />
                    On scheduled date
                  </label>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-5 mt-8">
                <div>
                  <FormikCustomSelect
                    label="Start Month"
                    // placeholder="Select option"
                    optionsParentClassName="text-12"
                    // value={values.organization}
                    name="loanType"
                    options={[{ text: "Car loan", value: "Car loan" }]}
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("loanType", item.value);
                    }}
                  />
                </div>
                <div>
                  <FormikCustomSelect
                    label="Resume On"
                    // placeholder="Select emp"
                    optionsParentClassName="text-12"
                    // value={values.organization}
                    name="employee"
                    options={[{ text: "John Doe", value: "John Doe" }]}
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("employee", item.value);
                    }}
                  />
                </div>
              </div>
              <div>
                <div className="mt-8">
                  <div className="text-16 mb-4 text-neutral-dark">
                    <label htmlFor="reason">Reason</label>
                  </div>
                  <textarea
                    name="reason"
                    id="reason"
                    className="rounded-[4px] w-full hide-scrollbar text-neutral-dark p-3 outline-1 outline-purple-normal-hover border-[0.6px] border-[#B2BBC699]"
                    rows={3}
                    onChange={(e) => setFieldValue("reason", e.target.value)}
                    value={values.description}
                  ></textarea>
                </div>

              </div>
              <div className="mt-14 flex justify-end">
                <div className="flex gap-5">
                  <div>
                    <CustomButton
                      type="submit"
                      title="Cancel"
                      handleClick={() => {
                        closeModal(false);
                      }}
                      isLoading={isLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                      isTransparent
                    />
                  </div>
                  <div>
                    <CustomButton
                      type="submit"
                      title="Submit"
                      handleClick={() => {}}
                      isLoading={isLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                    />
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default PauseLoan;
