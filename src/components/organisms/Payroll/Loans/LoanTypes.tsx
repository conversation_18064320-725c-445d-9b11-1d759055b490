import React, { useState } from "react";
import useClickOutside from "../../../shared/hooks";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import { FaEllipsisV } from "react-icons/fa";
import { Edit2, Eye } from "iconsax-react";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../shared/helpers";
import { FiPlus } from "react-icons/fi";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import { Form, Formik } from "formik";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";

interface loanTypeProps {
  name: string;
  duration: string;
  description: string;
  charge: number;
}

const LoanTypes = () => {
  const [rowId, setRowId] = useState(0);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [openLoanModal, setOpenLoanModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedLoan, setSelectedLoan] = useState<loanTypeProps>({});

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const sampleData = [
    {
      name: "Study Loan",
      description: "Study loan for different programmes.",
      charge: "12.5%",
      duration: "6-24 months",
    },
    {
      name: "Housing Loan",
      description: "Housing loan for staff.",
      charge: "6.5%",
      duration: "12-60months",
    },
    {
      name: "Car Loan",
      description: "Car loan for staff and contract.",
      charge: "10%",
      duration: "7-10months",
    },
  ];

  const columns = [
    {
      Header: "Loan name",
      accessor: "name",
      Cell: (row: any) => (
        <div className="flex  items-center">
          <p>{row.cell.value}</p>
        </div>
      ),
    },

    {
      Header: "Description",
      accessor: "description",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Duration",
      accessor: "duration",
      Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
    },

    {
      Header: "Charge (%)",
      accessor: "charge",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li onClick={() => { setOpenLoanModal(true); setSelectedLoan(row.cell.row.original) }} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <Eye size={18} />
                  View
                </li>
                <li onClick={() => { setOpenLoanModal(true); setSelectedLoan(row.cell.row.original) }} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <Edit2 size={18} />
                  Edit
                </li>
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];
  return (
    <div>
      {/* <div className="flex justify-end mt-8">
        <CustomButton
          className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
          isTransparent={true}
          handleClick={() => {
            setOpenLoanModal(true);
          }}
          leftIcon={<FiPlus className="ml-3" size={20} />}
          title="Add loan type"
        />
      </div> */}
      <div className=" my-10 py-[23px]">
        {sampleData?.length > 0 ? (
          <CustomTable
            data={sampleData || []}
            meta={{}}
            columns={columns}
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            header={
              <div className="flex justify-between items-center h-[60px] px-2">
                <h1>
                  Loan types
                </h1>
                <div className="bg-black">
                  <CustomButton
                    className="!w-[250px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md "
                    isTransparent={true}
                    handleClick={() => {
                      setOpenLoanModal(true);
                    }}
                    leftIcon={<FiPlus className="ml-3" size={20} />}
                    title="Add loan type"
                  />
                </div>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState buttonTitle="Nothing to show." />
          </div>
        )}
      </div>

      <CustomModal
        visibility={openLoanModal}
        toggleVisibility={setOpenLoanModal}
      >
        <div>
          <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
            <h1 className="font-poppins-medium text-18 text-purple-dark ">
              Add loan type
            </h1>
          </div>

          <div className=" pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
            <Formik
              initialValues={{ name: selectedLoan.name || "", description: selectedLoan.description || "", duration: selectedLoan.duration || "", charge: selectedLoan.charge || "" }}
              onSubmit={() => { }}
              validationSchema={{}}
              enableReinitialize
            >
              {({ values, setFieldValue }) => (
                <Form>
                  <div className="mt-4">
                    <div className="mt-4">
                      <FormikCustomInput
                        label="Name *"
                        id="name"
                        name="name"
                        placeholder="Car loan"
                        type="text"
                        inputClassName="!bg-transparent"
                        value={values.name}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-5 mt-4">
                      <div>
                        <FormikCustomInput
                          label="Duration (days) *"
                          id="duration"
                          name="duration"
                          placeholder="365 "
                          type="text"
                          inputClassName="!bg-transparent"
                          value={values.name}
                        />
                      </div>
                      <div>
                        <div>
                          <FormikCustomInput
                            label="Charge (%) *"
                            id="charge"
                            name="charge"
                            placeholder="10.5"
                            type="text"
                            inputClassName="!bg-transparent"
                            value={values.name}
                          />
                        </div>
                      </div>

                    </div>

                    <div className="mt-4">
                      <div className="text-16 mb-4 text-neutral-dark">
                        <label htmlFor="description">Description</label>
                      </div>
                      <textarea
                        name="description"
                        id="description"
                        className="rounded-[4px] w-full hide-scrollbar text-neutral-dark p-3 outline-1 outline-purple-normal-hover border-[0.6px] border-[#B2BBC699]"
                        rows={3}
                        onChange={(e) =>
                          setFieldValue("description", e.target.value)
                        }
                        value={values.description}
                      ></textarea>
                    </div>
                  </div>
                  <div className="mt-12 flex justify-end">
                    <CustomButton
                      type="submit"
                      title="Submit"
                      handleClick={() => { }}
                      isLoading={isLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                    />
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </CustomModal>
    </div>
  );
};

export default LoanTypes;
