import { Form, Formik } from "formik";
import React, { useState } from "react";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../shared/helpers";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import moment from "moment";

const CreateLoan = (data: any, onClose) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  return (
    <div>
      <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
        <h1 className="font-poppins-medium text-18 text-purple-dark ">
          {data ? "Edit loan" : "Add new loan"}
        </h1>
      </div>
      <div className=" pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
        <Formik
          initialValues={{
            name: "",
            description: "",
            duration: "",
            charge: "",
          }}
          onSubmit={() => {}}
          validationSchema={{}}
          enableReinitialize
        >
          {({ values, setFieldValue }) => (
            <Form>
              <div className="grid grid-cols-2 gap-5">
                <div>
                  <FormikCustomSelect
                    label="Select loan type"
                    // placeholder="Select option"
                    optionsParentClassName="text-12"
                    // value={values.organization}
                    name="loanType"
                    options={[{ text: "Car loan", value: "Car loan" }]}
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("loanType", item.value);
                    }}
                  />
                </div>
                <div>
                  <FormikCustomSelect
                    label="Select employee"
                    // placeholder="Select emp"
                    optionsParentClassName="text-12"
                    // value={values.organization}
                    name="employee"
                    options={[{ text: "John Doe", value: "John Doe" }]}
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("employee", item.value);
                    }}
                  />
                </div>
              </div>
              <div>
                <div className="grid grid-cols-2 gap-5 mt-8">
                  <div>
                    <FormikCustomInput
                      label="Loan amount *"
                      id="loanAmount"
                      name="loanAmount"
                      placeholder="Enter amount "
                      type="text"
                      inputClassName="!bg-transparent"
                      value={values.name}
                    />
                  </div>

                  <div>
                    <div className="text-16 mb-4 text-neutral-dark">
                      <label htmlFor="date">Disbursement date</label>
                    </div>
                    <FormikCustomDate
                      // value={moment(values.date)}
                      inputClassName="border bg-transparent"
                      name="date"
                      onChange={(date) => {
                        setFieldValue(
                          "date",
                          date
                            ? moment(date).format("YYYY-MM-DD HH:mm:ss")
                            : null
                        );
                      }}
                    />
                  </div>
                </div>

                <div className="mt-8">
                  <div className="text-16 mb-4 text-neutral-dark">
                    <label htmlFor="reason">Reason</label>
                  </div>
                  <textarea
                    name="reason"
                    id="reason"
                    className="rounded-[4px] w-full hide-scrollbar text-neutral-dark p-3 outline-1 outline-purple-normal-hover border-[0.6px] border-[#B2BBC699]"
                    rows={3}
                    onChange={(e) =>
                      setFieldValue("reason", e.target.value)
                    }
                    value={values.description}
                  ></textarea>
                </div>

                <div className="mt-8">
                  <h1 className="border-b pb-2">Repayments</h1>
                  <div className="grid grid-cols-2 gap-5 mt-8">
                  <div>
                    <FormikCustomInput
                      label="Installment amount *"
                      id="loanAmount"
                      name="loanAmount"
                      placeholder="Enter amount "
                      type="text"
                      inputClassName="!bg-transparent"
                      value={values.name}
                    />
                  </div>

                  <div>
                    <div className="text-16 mb-4 text-neutral-dark">
                      <label htmlFor="date">EMI Deduction Start Date *</label>
                    </div>
                    <FormikCustomDate
                      // value={moment(values.date)}
                      inputClassName="border bg-transparent"
                      name="date"
                      onChange={(date) => {
                        setFieldValue(
                          "date",
                          date
                            ? moment(date).format("YYYY-MM-DD HH:mm:ss")
                            : null
                        );
                      }}
                    />
                  </div>
                </div>
                </div>
              </div>
              <div className="mt-14 flex justify-end">
                <div className="flex gap-5">

                  <div>
                    <CustomButton
                      type="submit"
                      title="Cancel"
                      handleClick={() => {onClose(false)}}
                      isLoading={isLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                      isTransparent
                    />
                  </div>
                  <div>
                    <CustomButton
                      type="submit"
                      title="Submit"
                      handleClick={() => {}}
                      isLoading={isLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                    />
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default CreateLoan;
