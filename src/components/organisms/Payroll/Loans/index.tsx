import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import AllLoans from './AllLoans'
import LoanRepayments from './LoanRepayments'
import LoanTypes from './LoanTypes'

const Loans = () => {
  const [activeTab, setActiveTab] = useState<number>(0)
  const navigate = useNavigate();
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("activeTab");

  const loanTabs = [
    {
      title: "Loan types",
      name: 'Loan types',
      component: <LoanTypes />,
    },
    {
      title: "All loans",
      name: 'Loans',
      component: <AllLoans />,
    },
    // {
    //   title: "Loan repayments",
    //   name: 'Loan repayments',
    //   component: <LoanRepayments />,
    // },
  ];

  useEffect(() => {
    setActiveTab(queryParam || 0)

  }, [queryParam, activeTab]);

  return (
    <div>
      <div className=" bg-[#F4F4F6] flex justify-between">
        <div className="flex gap-10 py-1 px-1 justify-center items-center  bg-[#F4F4F6] rounded-md">
          {loanTabs.map((item, index) => (
            <div
              key={index}
              onClick={() => { setActiveTab(index); navigate(`/payroll/loans?activeTab=${index}&tab=${item.name}`) }}
              className={`cursor-pointer px-5 py-2 rounded  ${activeTab == index ? "bg-white" : "bg-none"}`}>
              <p className={`text-neutral-normal font-poppins-medium pb-2 px-5 text-center text-16 ${activeTab == index ? "text-purple-normal" : "text-gray-950"} `}> {item.title}</p>
            </div>
          ))}
        </div>
      </div>
      <div>
        {loanTabs[activeTab].component}
      </div>
    </div>
  )
}

export default Loans