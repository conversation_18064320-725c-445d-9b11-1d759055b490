import { Clock, Edit2, Money, Note, Timer, Trash, User } from 'iconsax-react'
import React, { useState } from 'react'
import CustomButton from '../../../atoms/CustomButton/CustomButton'
import { ButtonProperties } from '../../../shared/helpers'
import CustomTable from '../../../atoms/CustomTable/CustomTable'
import StatusTag from '../../../atoms/StatusTag'
import { TbCategoryPlus, TbTimeDurationOff } from 'react-icons/tb'
import { RiInstallLine } from 'react-icons/ri'
import { MdNumbers } from 'react-icons/md'
import { FaSortAmountUp } from 'react-icons/fa'
import { ImSortAmountDesc } from 'react-icons/im'
import { AiOutlineStop } from 'react-icons/ai'
import CustomModal from '../../../atoms/CustomModal/CustomModal'
import CreateLoan from './CreateLoan'
import PauseLoan from './PauseLoan'
import RecordPayment from './RecordPayment'
import GoBack from '../../../atoms/Ui/GoBack'

const LoanRepayments = () => {
  const [openDeleteLoanModal, setOpenDeleteLoanModal] = useState<boolean>(false);
  const [openEditLoanModal, setOpenEditLoanModal] = useState<boolean>(false);
  const [openPauseLoanModal, setOpenPauseLoanModal] = useState<boolean>(false);
  const [openRecordLoanModal, setOpenRecordLoanModal] = useState<boolean>(false);
  const columns = [
    {
      Header: "Installment date",
      accessor: "name",
      Cell: (row: any) => (
        <div className="flex  items-center">
          <p>{row.cell.value}</p>
        </div>
      ),
    },

    {
      Header: "Amount paid",
      accessor: "description",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Remaining amount",
      accessor: "duration",
      Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
    },

    {
      Header: "Status",
      accessor: "charge",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },

  ];
  return (
    <div>
      <GoBack/>
      <div className='bg-purple-light flex justify-between  pl-6 pr-4 py-4 rounded-tr-xl rounded-tl-xl mt-4'>
        <h1 className='text-24 text-purple-normal flex justify-center items-center'>Loan information</h1>
        <div className="flex justify-end">
          <div className='flex gap-3'>
            <CustomButton
              handleClick={() => {setOpenEditLoanModal(true)}}
              variant={ButtonProperties.VARIANT.primary.name}
              leftIcon={<Edit2 className="ml-3" size={16} />}
              title="Edit"
              size={ButtonProperties.SIZES.small}
              className='!h-8 !w-fit'
              isTransparent
            />
            <CustomButton
              handleClick={() => {setOpenPauseLoanModal(true)}}
              variant={ButtonProperties.VARIANT.primary.name}
              leftIcon={<AiOutlineStop className="ml-3" size={16} />}
              title="Pause loan"
              size={ButtonProperties.SIZES.small}
              className='!h-8 !w-fit'
              isTransparent
            />
            <CustomButton
              handleClick={() => {setOpenRecordLoanModal(true)}}
              variant={ButtonProperties.VARIANT.primary.name}
              size={ButtonProperties.SIZES.small}
              leftIcon={<RiInstallLine className="ml-3" size={16} />}
              title="Record payment"
              className='!h-8 !w-fit'
              isTransparent
            />
            <CustomButton
              handleClick={() => {setOpenDeleteLoanModal(true)}}
              variant={ButtonProperties.VARIANT.primary.name}
              leftIcon={<Trash className="ml-3" size={16} />}
              title="Delete loan"
              size={ButtonProperties.SIZES.small}
              className='!h-8 !w-fit !bg-alert-text-error !border-alert-text-error'
            />

          </div>
        </div>
      </div>
     {/* <p className='mt-5 bg-white text-[#FFA500] py-1 px-3 flex gap-2 rounded-md'> <Information size={14} className='mt-0.5'/> 
     Loan installment deduction will be paused from April 2025 and will be resumed in June 2025.
     </p> */}
      
      <div className='grid grid-cols-6 gap-8 rounded-lg mt-4'>
        <div className='col-span-4 bg-white'>
        <CustomTable
            data={[]}
            meta={{}}
            columns={columns}
            hideSearch
            // handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            header={
              <div>
                <h1 className="font-poppins-medium text-purple-dark-active mb-4">
                  Repayment information
                </h1>
              </div>
            }
          />
        </div>
        <div className='col-span-2 bg-white  rounded-lg p-4'>
          <div className='flex justify-between border-b border-b-purple-normal pb-2'>

           <h1 className='text-purple-normal font-poppins-medium'>Loan details</h1>
           <StatusTag className='!bg-transparent !py-0' status='open'/>
          </div>
          <div className='mt-6 text-neutral-normal'>

            <div className='flex justify-between'>
              <h1 className='flex gap-1'><User className='mt-1' size={12}/>Employee name</h1>
              <p className='text-neutral-normal font-poppins-medium'>John Doe</p>
            </div>
            <div className='flex justify-between mt-4'>
              <h1 className='flex gap-1'><TbCategoryPlus className='mt-1' size={12}/>Loan type</h1>
              <p className='text-neutral-normal font-poppins-medium'>Car loan</p>
            </div>
            <div className='flex justify-between mt-4'>
              <h1 className='flex gap-1'><MdNumbers className='mt-1' size={12}/>Loan number</h1>
              <p className='text-neutral-normal font-poppins-medium'>LOAN-000277</p>
            </div>
            <div className='flex justify-between mt-4'>
              <h1 className='flex gap-1'><Money className='mt-1' size={12}/>Loan amount</h1>
              <p className='text-neutral-normal font-poppins-medium'>$4,500</p>
            </div>
            <div className='flex justify-between mt-4'>
              <h1 className='flex gap-1'><RiInstallLine className='mt-1' size={12}/>Installment amount</h1>
              <p className='text-neutral-normal font-poppins-medium'>$350</p>
            </div>
            <div className='flex justify-between mt-4'>
              <h1 className='flex gap-1'><Clock className='mt-1' size={12}/>Disbursement date</h1>
              <p className='text-neutral-normal font-poppins-medium'>15 March, 2025</p>
            </div>
            <div className='flex justify-between mt-4'>
              <h1 className='flex gap-1'><TbTimeDurationOff className='mt-1' size={12}/>Loan close date</h1>
              <p className='text-neutral-normal font-poppins-medium'>15 March, 2026</p>
            </div>
            <div className='flex justify-between mt-4'>
              <h1 className='flex gap-1'><Note className='mt-1' size={12}/>Reason</h1>
              <p className='text-neutral-normal font-poppins-medium'>On request service</p>
            </div>
            
          </div>
          <div className='mt-8 text-neutral-normal'>
            <h1 className='border-b border-b-purple-normal pb-2 text-purple-normal font-poppins-medium'>Repayment details</h1>
            <div>
            <div className='flex justify-between mt-4'>
              <h1 className='flex gap-1'><FaSortAmountUp className='mt-1' size={12}/>Amount repaid</h1>
              <p className='text-neutral-normal font-poppins-medium'>$0.00</p>
            </div>
            <div className='flex justify-between mt-4'>
              <h1 className='flex gap-1'><ImSortAmountDesc className='mt-1' size={12}/>Remaining amount</h1>
              <p className='text-neutral-normal font-poppins-medium'>$4,500</p>
            </div>
            <div className='flex justify-between mt-4'>
              <h1 className='flex gap-1'><Timer className='mt-1' size={12}/>Next installment date</h1>
              <p className='text-neutral-normal font-poppins-medium'>15 April, 2025</p>
            </div>
            </div>
          </div>
        </div>
      </div>

      <CustomModal visibility={openPauseLoanModal} toggleVisibility={setOpenPauseLoanModal} > 
        <PauseLoan closeModal={setOpenPauseLoanModal}/>
      </CustomModal>

      <CustomModal visibility={openDeleteLoanModal} toggleVisibility={setOpenDeleteLoanModal} > 
        <div>
          <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
            <h1 className="font-poppins-medium text-18 text-purple-dark ">
              Delete loan record ?
            </h1>
          </div>
          <p className='text-center text-18 mt-10'>Are you sure you want to delete this loan records. This action cannot be undone.</p>
          <div className="mt-14 flex justify-center mb-10">
                <div className="flex gap-5">
                  <div>
                    <CustomButton
                      type="submit"
                      title="Cancel"
                      handleClick={() => { setOpenDeleteLoanModal(false)}}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                      isTransparent
                    />
                  </div>
                  <div>
                    <CustomButton
                      type="submit"
                      title="Delete"
                      handleClick={() => {}}
                      // isLoading={isLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                    />
                  </div>
                </div>
            </div>
        </div>
      </CustomModal>

      <CustomModal visibility={openEditLoanModal} toggleVisibility={setOpenEditLoanModal} > 
        <CreateLoan data={{}} onClose={setOpenEditLoanModal}/>
      </CustomModal>

      <CustomModal visibility={openRecordLoanModal} toggleVisibility={setOpenRecordLoanModal} > 
        <RecordPayment data={{}} onClose={setOpenRecordLoanModal}/>
      </CustomModal>
    </div>
  )
}

export default LoanRepayments