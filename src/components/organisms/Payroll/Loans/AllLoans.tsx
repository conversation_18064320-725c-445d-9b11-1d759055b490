import React, { useState } from 'react'
import useClickOutside from '../../../shared/hooks';
import FilterDropdown from '../../../atoms/Cards/FilterDropdown';
import { FaEllipsisV } from 'react-icons/fa';
import { Edit2, Eye } from 'iconsax-react';
import CustomTable from '../../../atoms/CustomTable/CustomTable';
import OrganizationEmptyState from '../../../atoms/Cards/OrganizationEmptyState';
import StatusTag from '../../../atoms/StatusTag';
import CustomButton from '../../../atoms/CustomButton/CustomButton';
import { FiPlus } from 'react-icons/fi';
import CustomModal from '../../../atoms/CustomModal/CustomModal';
import CreateLoan from './CreateLoan';
import { useNavigate } from 'react-router-dom';
import { useRecoilValue } from 'recoil';
import { loggedUserAtom } from '../../../../recoil/atom/authAtom';

const AllLoans = () => {
  const navigate = useNavigate();
  const [rowId, setRowId] = useState(0);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [openLoanModal, setOpenLoanModal] = useState<boolean>(false);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [selectedLoan, setSelectedLoan] = useState<boolean>(false);
  const getUserValue = useRecoilValue(loggedUserAtom);

  const userCucrrency = getUserValue?.businesses?.map((item) => item.default_currency)

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const sampleData = [
    {
      employeeName: "John Doe",
      loanNumber: "LOAN-#1849",
      loanName: "Car Loan",
      status: "Open",
      loanAmount: "4,500",
      amountRepaid: "0",
      amountLeft: "4,500",
    },
    {
      employeeName: "Alex Warren",
      loanNumber: "LOAN-#1249",
      loanName: "Study Loan",
      status: "Open",
      loanAmount: "10,500",
      amountRepaid: "0",
      amountLeft: "10,500",
    }
  ]

  const columns = [
    {
      Header: "Employee name",
      accessor: "employeeName",
      Cell: (row: any) => (
        <div className="flex  items-center">

          <p>{row.cell.value}</p>
        </div>
      ),
    },

    {
      Header: "Loan number",
      accessor: "loanNumber",
      Cell: (row: any) => (
        <p className="rounded-2xl bg-[#F5F6F7] w-fit px-2 py-1 font-poppins-medium">
          {row.cell.value || "--"}{" "}
        </p>
      ),
    },
    {
      Header: "Loan name",
      accessor: "loanName",
      Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
    },

    {
      Header: "Loan amount",
      accessor: "loanAmount",
      Cell: (row) => <div><span className="text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1">{userCucrrency}</span> {row.cell.value || "--"}</div>,
    },
    {
      Header: "Loan repaid",
      accessor: "amountRepaid",
      Cell: (row) => <div><span className="text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1">{userCucrrency}</span> {row.cell.value || "--"}</div>,
    },
    {
      Header: "Loan left",
      accessor: "amountLeft",
      Cell: (row) => <div><span className="text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1">{userCucrrency}</span> {row.cell.value || "--"}</div>,
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: (row: any) => (<StatusTag status={row.cell.value} />),
    },
    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li onClick={() => { navigate("/payroll/loans/loan-repayment", { state: { data: row.cell.row.original } }) }} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <Eye size={18} />
                  View
                </li>
                <li onClick={() => { navigate("/payroll/loans/loan-repayment", { state: { data: row.cell.row.original } }) }} className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer">
                  <Edit2 size={18} />
                  Edit
                </li>
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },

  ];
  return (
    <div>
      {/* <div className='flex justify-end mt-8'>
        <CustomButton
          className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
          isTransparent={true}
          handleClick={() => { setOpenLoanModal(true) }}
          leftIcon={<FiPlus className="ml-3" size={20} />}
          title="Add new loan"
        />
      </div> */}
      <div className=" my-10 py-[23px]">
        {sampleData?.length > 0 ? (
          <CustomTable
            data={sampleData || []}
            meta={{}}
            columns={columns}
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            header={
              <div className="flex justify-between items-center h-[60px] px-2">
                <h1>
                  All loan
                </h1>
                <div className="bg-black">
                  <CustomButton
                    className="!w-[250px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md "
                    isTransparent={true}
                    handleClick={() => { setOpenLoanModal(true) }}
                    leftIcon={<FiPlus className="ml-3" size={20} />}
                    title="Add new loan"
                  />
                </div>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState
              buttonTitle="Nothing to show"
            />
          </div>
        )}
      </div>

      <CustomModal visibility={openLoanModal} toggleVisibility={setOpenLoanModal}>
        <CreateLoan />
      </CustomModal>
    </div>
  )
}

export default AllLoans