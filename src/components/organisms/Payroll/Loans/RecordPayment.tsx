import { Form, Formik } from "formik";
import React, { useState } from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../shared/helpers";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import moment from "moment";

const RecordPayment = (closeModal) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  return (
    <div>
      <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
        <h1 className="font-poppins-medium text-18 text-purple-dark ">
          Record repayment
        </h1>
      </div>
      <div className=" pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
        <Formik
          initialValues={{
            name: "",
            description: "",
            duration: "",
            charge: "",
          }}
          onSubmit={() => {}}
          validationSchema={{}}
          enableReinitialize
        >
          {({ values, setFieldValue }) => (
            <Form>
              <div className="grid grid-cols-2 gap-5 mt-8">
                  <div>
                    <FormikCustomInput
                      label="Repayment amount *"
                      id="repaymentAmount"
                      name="repaymentAmount"
                      placeholder="Enter amount "
                      type="text"
                      inputClassName="!bg-transparent"
                      value={values.name}
                    />
                  </div>

                  <div>
                    <div className="text-16 mb-4 text-neutral-dark">
                      <label htmlFor="date">Repayment date</label>
                    </div>
                    <FormikCustomDate
                      // value={moment(values.date)}
                      inputClassName="border bg-transparent"
                      name="date"
                      onChange={(date) => {
                        setFieldValue(
                          "date",
                          date
                            ? moment(date).format("YYYY-MM-DD HH:mm:ss")
                            : null
                        );
                      }}
                    />
                  </div>
                </div>
              <div className="grid grid-cols-2 gap-5 mt-8">
                <div>
                  <FormikCustomSelect
                    label="Payment mode"
                    // placeholder="Select option"
                    optionsParentClassName="text-12"
                    // value={values.organization}
                    name="loanType"
                    options={[{ text: "Bank Transfer", value: "Bank Transfer" }]}
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("loanType", item.value);
                    }}
                  />
                </div>
                <div>
                    <FormikCustomInput
                      label="Remaining amount *"
                      id="remainingAmount"
                      name="remainingAmount"
                      placeholder="Enter amount "
                      type="text"
                      inputClassName="!bg-transparent"
                      value={"$4,500"}
                    />
                  </div>
              </div>
              
              <div className="mt-14 flex justify-end">
                <div className="flex gap-5">
                  <div>
                    <CustomButton
                      type="submit"
                      title="Cancel"
                      handleClick={() => {
                        closeModal(false);
                      }}
                      isLoading={isLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                      isTransparent
                    />
                  </div>
                  <div>
                    <CustomButton
                      type="submit"
                      title="Submit"
                      handleClick={() => {}}
                      isLoading={isLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                    />
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default RecordPayment;
