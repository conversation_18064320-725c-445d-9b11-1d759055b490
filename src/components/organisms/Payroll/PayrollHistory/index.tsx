import React from "react";
import { NavLink, Outlet, useLocation } from "react-router-dom";
import { PAYROLL_HISTORY_ROUTE_NAMES } from "../../../../routes/tabRoutes";

const PayrollHistory = () => {
  const location = useLocation();
  const { pathname } = location;

  const payrollHistoryTab = [
    { path: PAYROLL_HISTORY_ROUTE_NAMES.paidEmployees, title: "Paid Employees", main: "paid" },
    { path: PAYROLL_HISTORY_ROUTE_NAMES.unpaidEmployees, title: "Unpaid Employees", main: "unpaid" },
  ];


  return (
    <div>
      <div className=" mt-5  flex justify-between">
      {/* <div className="flex gap-10 px-2 py-1 rounded bg-[#F4F4F6]">
        {payrollHistoryTab.map((tabItem) => (
        <NavLink
          key={tabItem.path}
          to={tabItem.path}
          className={({ isActive }) =>
          `relative cursor-pointer flex items-center justify-center text-16 transition-colors duration-200 px-6 py-3 rounded-b-md
          ${isActive || pathname === tabItem?.main
            ? "text-purple-normal bg-white font-bold"
            : "text-neutral-normal bg-transparent"
          }`
          }
          style={{ minWidth: 150, textAlign: "center" }}
        >
          <span className="w-full">{tabItem.title}</span>
        </NavLink>
        ))}
      </div> */}
      </div>
      <Outlet />
    </div>
  );

};

export default PayrollHistory;
