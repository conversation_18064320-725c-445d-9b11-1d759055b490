import React, { useState, useEffect, useRef } from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import StatusTag from "../../../atoms/StatusTag";
import { Eye, Receipt, UserEdit } from "iconsax-react";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import { FaEllipsisV } from "react-icons/fa";
import useClickOutside from "../../../shared/hooks";
import { PiTrash } from "react-icons/pi";
import { MdRemoveCircleOutline } from "react-icons/md";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import { ButtonProperties } from "../../../shared/helpers";
import { Form, Formik } from "formik";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { useNavigate } from "react-router-dom";
import { FaDownload } from "react-icons/fa6";
import { useRecoilValue } from "recoil";
import { loggedUserAtom } from "../../../../recoil/atom/authAtom";
import PaymentHistoryLoader from "../../../atoms/Skeleton/SalaryLoaders/PaymentHistoryLoader";
import { getTransactions } from '../../../../api/payroll/transaction';
import CurrencyTag from "../../../atoms/Ui/CurrencyTag";
import SalaryGradesLoader from '../../../atoms/Skeleton/SalaryLoaders/SalaryGradeLoader';

const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

const PaidEmployees = () => {
  const navigate = useNavigate();
  const [employeePayrollModal, setEmployeePayrollModal] = useState(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [rowId, setRowId] = useState(0);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const getUserValue = useRecoilValue(loggedUserAtom);
  const [transactions, setTransactions] = useState<any>([]);
  const [isFetching, setIsFetching] = useState<boolean>(true);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<number>(1);


  const userCucrrency = getUserValue?.businesses?.map((item) => item.default_currency)
  const debounceSearch = useRef(debounce((q) => fetchTransactions(q), 2000)).current;

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setShowDeleteWarn(false);
    setRowId(0);
  });

  const fetchTransactions = async (q?: string) => {
    setIsFetching(true)
    await getTransactions({ page: pageNumber, search: q }).then((res) => {
      if (res.success) {
        setTransactions(res.data);
        setIsFetching(false)
      }
    });
  };


  
  useEffect(() => {
    fetchTransactions(searchQuery);
  }, [pageNumber]);

  if (isFetching && !searchQuery && transactions?.data?.length === 0) {
    return (
      <div>
        <SalaryGradesLoader />
      </div>
    );
  }

  const employeePayrollData = [
    {
      id: 1,
      name: "Giffy Onyinye",
      description: "Tax deduction for employee",
      amount: "400,000",
      taxNumber: "DE57789",
      status: "paid",
      employerContribution: "60,000",
      employeeContribution: "40,000",
    },
    {
      id: 1,
      name: "Helen Josie",
      description: "Salary deduction for employee",
      amount: "200,000",
      taxNumber: "RE35799",
      status: "paid",
      employeeContribution: "70,000",
      employerContribution: "50,000",
    },
  ];

  const columns =[
      {
        Header: 'Employee Name',
        accessor: 'staff',
        Cell: (row) => {
          return <div>{row.cell.value?.fullName}</div>;
        },
      },
      {
        Header: 'Employee ID',
        accessor: (row) => row?.staff?.staff_identification_tag,
      },
      {
        Header: 'Transaction Type',
        accessor: 'transaction_type',
        Cell: (row) => {
          return (
            <div>
              {row.cell.value === 'salary' ? (
                <span className='text-green-600'>Salary</span>
              ) : (
                <span className='text-blue-600'>Tax</span>
              )}
            </div>
          );
        },
      },
      {
        Header: 'Amount',
        accessor: 'amount',
        Cell: ({ value }) => {
          return (
            <div>
              <CurrencyTag currency='NGN' />
              {new Intl.NumberFormat().format(value)}
            </div>
          );
        },
      },
      {
        Header: 'Bank Name',
        accessor: 'bank_name',
      },
      {
        Header: 'Account/Tax Number',
        accessor: (row) =>
          row.transaction_type === 'salary' ? row.account_number : row.tax_number,
      },
      {
        Header: 'Status',
        accessor: 'status',
        Cell: (row) => {
          return <StatusTag status={row.cell.value} />;
        },
      },
  ];


  if (isFetching && !searchQuery && transactions?.length === 0) {
    return (
      <div>
        <SalaryGradesLoader />
      </div>
    );
  }

  return (
    <div>
      <div className="px-4 py-[23px]">
        {employeePayrollData.length > 0 ? (
          <CustomTable
            data={transactions?.data || []}
            meta={transactions?.meta}
            columns={columns}
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
            header={
              <div className="flex justify-end mb-2 h-[40px]">
                {/* <h1 className="font-poppins-medium text-purple-normal-active mb-4">
                  Paid employee information
                </h1> */}
                <div>
                  <div className="flex gap-4">
                    {/* <CustomButton
                      handleClick={() => { }}
                      isTransparent
                      // variant={ButtonProperties.VARIANT.primary.name}
                      title="Export to Excel"
                      leftIcon={<FaDownload />}
                      className="!h-8 w-fit px-3 !bg-purple-dark !text-white"
                    /> */}
                    <CustomButton
                      handleClick={() => { }}
                      isTransparent
                      // variant={ButtonProperties.VARIANT.primary.name}
                      title="Export to PDF"
                      size={ButtonProperties.SIZES.small}
                      leftIcon={<FaDownload />}
                      className="!h-7 !bg-purple-dark !text-white"
                    />
                  </div>
                </div>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState text="You have not added any deductables." />
          </div>
        )}
      </div>

      <CustomModal
        visibility={employeePayrollModal}
        toggleVisibility={setEmployeePayrollModal}
      >
        <div className="mb-10">
          <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-6">
            Add employee payroll
          </h1>
          <div className="px-7">
            <div className="mt-10">
              <Formik
                initialValues={{}}
                onSubmit={() => { }}
                enableReinitialize
                validationSchema={{}}
              >
                {({ setFieldValue }) => (
                  <Form>
                    <div className="mt-8 grid grid-cols-2 gap-6">
                      <div>
                        <FormikCustomSelect
                          label="Select employee *"
                          name="employee"
                          options={[]}
                          onChange={(item: { value: string; text: string }) => {
                            setFieldValue("employee", item.text);
                          }}
                          required
                        />
                      </div>
                      <div>
                        <FormikCustomInput
                          placeholder="Enter net salary"
                          label="Net salary *"
                          inputClassName="bg-transparent"
                          name="netSalary"
                          type="text"
                        />
                      </div>
                    </div>
                    <div className="mt-8 grid grid-cols-2 gap-6">
                      <div>
                        <FormikCustomSelect
                          label="Select deductions *"
                          name="deductions"
                          options={[]}
                          onChange={(item: { value: string; text: string }) => {
                            setFieldValue("deductions", item.text);
                          }}
                          required
                        />
                      </div>
                      <div>
                        <FormikCustomSelect
                          label="Select additions *"
                          name="additions"
                          options={[]}
                          onChange={(item: { value: string; text: string }) => {
                            setFieldValue("additions", item.text);
                          }}
                          required
                        />
                      </div>
                    </div>
                    <div className="mt-8 grid grid-cols-2 gap-6">
                      <div>
                        <FormikCustomInput
                          placeholder="Enter tax state"
                          label="Tax state *"
                          inputClassName="bg-transparent"
                          name="taxState"
                          type="text"
                        />
                      </div>
                      <div>
                        <FormikCustomInput
                          placeholder="Tax number *"
                          label="Tax number *"
                          inputClassName="bg-transparent"
                          name="taxNumber"
                          type="text"
                        />
                      </div>
                    </div>
                    <div className="mt-8 grid grid-cols-2 gap-6">
                      <div>
                        <FormikCustomInput
                          placeholder="Enter employer pension amount"
                          label="Pension employer contribution amount *"
                          inputClassName="bg-transparent"
                          name="employerPension"
                          type="text"
                        />
                      </div>
                      <div>
                        <FormikCustomInput
                          placeholder="Enter employee pension amount *"
                          label="Pension employee contribution amount *"
                          inputClassName="bg-transparent"
                          name="employeePension"
                          type="text"
                        />
                      </div>
                    </div>
                    <div className="mt-8 grid grid-cols-2 gap-6">
                      <div>
                        <FormikCustomInput
                          placeholder="Enter pension code"
                          label="Pension code*"
                          inputClassName="bg-transparent"
                          name="code"
                          type="text"
                        />
                      </div>
                      <div>
                        <FormikCustomInput
                          placeholder="Enter pension pin"
                          label="Pension pin *"
                          inputClassName="bg-transparent"
                          name="pin"
                          type="text"
                        />
                      </div>
                    </div>

                    <div className="flex justify-end mt-20">
                      <CustomButton
                        type="submit"
                        className="!w-[128px] !h-10"
                        title={"Add"}
                        // isLoading={isLoading}
                        handleClick={() => { }}
                        variant={ButtonProperties.VARIANT.primary.name}
                      />
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
          </div>
        </div>
      </CustomModal>
    </div>
  );
};

export default PaidEmployees;
