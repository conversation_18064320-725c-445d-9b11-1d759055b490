import { Form, Formik } from 'formik';
import React, { useRef, useState } from 'react';
import FormikCustomInput from '../../atoms/CustomInput/FormikCustomInput';
import CustomButton from '../../atoms/CustomButton/CustomButton';
import { ButtonProperties } from '../../shared/helpers';
import { clustarkToast } from '../../atoms/Toast';
import { NotificationTypes } from '../../shared/helpers/enums';
import FormikCustomSelect from '../../atoms/CustomInput/FormikCustomSelect';
import CustomCheckBox from '../../atoms/CustomCheckBox/CustomCheckBox';
import { setupPayroll } from '../../../api/payroll';
import * as Yup from 'yup';
import { useNavigate } from 'react-router-dom';

const FormSchema = Yup.object().shape({
  org_name: Yup.string().required('Required'),
  org_reg_no: Yup.string().required('Required'),
  org_bvn: Yup.string().required('Required'),
  payroll_day: Yup.number().required('Required'),
  payroll_generation: Yup.number().required('Required'),
  automate_payroll: Yup.boolean(),
  automate_tax: Yup.boolean(),
});

const BusinessSetup = () => {
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [, setDragging] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (
      file &&
      (file.type === 'image/jpeg' || file.type === 'image/png') &&
      file.size <= 900 * 1024
    ) {
      setSelectedFile(file);
    } else {
      clustarkToast(NotificationTypes.ERROR, 'Image must be a .jpg or .png and less than 900kb.');
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };
  return (
    <div className='bg-white mt-10 px-5 pt-1 pb-5 rounded-lg'>
      <div>
        <Formik
          initialValues={{
            automate_payroll: true,
            org_name: '',
            org_reg_no: '',
            org_bvn: '',
            payroll_day: 0,
            payroll_generation: 14,
            automate_tax: true,
          }}
          validationSchema={FormSchema}
          onSubmit={async (values) => {
            const res = await setupPayroll(values);
            if (res?.status === 200) {
              navigate('/payroll/dashboard');
            }
          }}
          enableReinitialize
        >
          {({ values, setFieldValue, isSubmitting }) => (
            <Form>
              <div className='mt-10 grid grid-cols-2 gap-9'>
                <div>
                  <FormikCustomInput
                    placeholder='Enter business name'
                    label='Business registered name *'
                    inputClassName='border bg-transparent'
                    name='org_name'
                    value={values.org_name}
                    onChange={(e) => setFieldValue('org_name', e.target.value)}
                  />
                </div>
                <div>
                  <FormikCustomInput
                    placeholder='Enter registration number '
                    label='Business registration number *'
                    inputClassName='bg-transparent'
                    name='org_reg_no'
                    value={values.org_reg_no}
                    onChange={(e) => setFieldValue('org_reg_no', e.target.value)}
                  />
                </div>
              </div>
              <div className='mt-10 grid grid-cols-2 gap-9'>
                <div>
                  <FormikCustomInput
                    placeholder='Enter BVN'
                    label='Business bank verification *'
                    inputClassName='border bg-transparent'
                    name='org_bvn'
                    value={values.org_bvn}
                    onChange={(e) => setFieldValue('org_bvn', e.target.value)}
                  />
                </div>
                <div>
                  <FormikCustomSelect
                    placeholder='Enter payroll day '
                    label='Payroll day *'
                    inputClassName='bg-transparent'
                    name='payroll_day'
                    options={[
                      ...[...Array(28).keys()].map((i) => ({
                        text: `Day ${i + 1}`,
                        value: i + 1,
                      })),
                      { text: 'Last day of the month', value: 0 },
                    ]}
                    value={values.payroll_day}
                    onChange={(e) => setFieldValue('payroll_day', e.target.value)}
                  ></FormikCustomSelect>
                </div>
              </div>
              <div className='mt-10 grid grid-cols-2 gap-9'>
                <div>
                  <FormikCustomSelect
                    placeholder='Enter payroll generation '
                    label='Payroll generation *'
                    inputClassName='bg-transparent'
                    name='payroll_generation'
                    options={[
                      ...[...Array(13).keys()].map((i) => ({
                        text: `${i + 2} Days before`,
                        value: i + 2,
                      })),
                    ]}
                    value={values.payroll_generation}
                    onChange={(e) => setFieldValue('payroll_generation', e.target.value)}
                  ></FormikCustomSelect>
                </div>
              </div>
              <div className='mt-10 grid grid-cols-2 gap-9'>
                <div>
                  <div className='flex gap-2 mt-3'>
                    <CustomCheckBox
                      name='automate_payroll'
                      checked={values.automate_payroll}
                      onChange={() => setFieldValue('terms', !values.automate_payroll)}
                    />
                    <span className='text-purple-normal font-bold text-[1rem]'>
                      Automate payroll
                    </span>
                  </div>
                </div>
                <div>
                  <div className='flex gap-2 mt-3'>
                    <CustomCheckBox
                      name='automate_tax'
                      checked={values.automate_tax}
                      onChange={() => setFieldValue('terms', !values.automate_tax)}
                    />
                    <span className='text-purple-normal font-bold text-[1rem]'>
                      Automate tax payment
                    </span>
                  </div>
                </div>
              </div>

              <div className='mt-[120px] flex justify-end'>
                <CustomButton
                  type='submit'
                  isLoading={isSubmitting}
                  isDisabled={isSubmitting}
                  title='Save'
                  handleClick={() => {}}
                  className='!w-[170px]'
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default BusinessSetup;
