import { Form, Formik } from "formik";
import React, { useState } from "react";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../shared/helpers";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";

const BankAccountSetup = () => {
  const [isLoading, setIsLoading] = useState(false);

  return (
    <div className="bg-white mt-16 px-5 pt-1 pb-5 rounded-lg">
      <div>
        <Formik
          initialValues={{}}
          validationSchema={{}}
          onSubmit={() => {}}
          enableReinitialize
        >
          {({setFieldValue}) => (
            <Form>
              <div className="mt-10 grid grid-cols-2 gap-9">
              <div>
                    <FormikCustomSelect
                      label="Bank Name *"
                      options={[{text: "First Bank", value: "First Bank"}, {text: "Access Bank", value: "Access Bank"}]}
                      name="bank"
                      placeholder="Select bank"
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("department", item.value);
                      }}
                    />
                  </div>
                <div>
                  <FormikCustomInput
                    placeholder="Enter account number "
                    label="Account number *"
                    inputClassName="border bg-transparent"
                    name="accountNumber"
                    type="text"
                  />
                </div>
              </div>
              <div className="mt-10 grid grid-cols-2 gap-9">
                <div>
                  <FormikCustomInput
                    placeholder="John Doe"
                    label="Account Name *"
                    inputClassName="bg-transparent"
                    name="accountName"
                    type="text"
                    disabled
                  />
                </div>
                <div>
                <FormikCustomSelect
                      label="Account type"
                      options={[{text: "Savings", value: "Savings"}, {text: "Current", value: "Current"}]}
                      name="accountType"
                      placeholder="Select account type"
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("department", item.value);
                      }}
                    />
                </div>
              </div>

              <div className="mt-[120px] flex justify-end">
                <CustomButton
                  type="submit"
                  isLoading={isLoading}
                  title="Submit"
                  handleClick={() => {}}
                  className="!w-[170px]"
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default BankAccountSetup;
