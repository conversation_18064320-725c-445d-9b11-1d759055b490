import { Form, Formik } from "formik";
import React, { useState } from "react";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../shared/helpers";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import moment from "moment";

const RevisedSalaryForm = ( onClose) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  return (
    <div>
      <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
        <h1 className="font-poppins-medium text-18 text-purple-dark ">
          Revise Salary
        </h1>
      </div>
      <div className=" pt-[40px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
        <Formik
          initialValues={{
            previousPay: "4000",
            revisedPay: "",
            lastRevised: "",
            newRevised: "",
            effective: "",
          }}
          onSubmit={() => {}}
          validationSchema={{}}
          enableReinitialize
        >
          {({ values, setFieldValue }) => (
            <Form>
              <div className="grid grid-cols-2 gap-5">
                <div>
                  <FormikCustomInput
                    label="Previous pay ($) *"
                    id="previous"
                    name="previous"
                    placeholder="Enter amount "
                    type="text"
                    inputClassName="!bg-transparent"
                    value={values.previousPay}
                  />
                </div>
                <div>
                  <FormikCustomInput
                    label="Revised pay ($) *"
                    id="revisedPay"
                    name="revisedPay"
                    placeholder="Enter amount "
                    type="text"
                    inputClassName="!bg-transparent"
                    value={values.revisedPay}
                  />
                </div>
              </div>
              <div>


                

                <div className="mt-8">
                  <div className="grid grid-cols-2 gap-5 mt-8">
                    <div>

                      <div className="text-16 mb-4 text-neutral-dark">
                        <label htmlFor="date">Last revision date *</label>
                      </div>
                      <FormikCustomDate
                        // value={moment(values.date)}
                        inputClassName="border bg-transparent"
                        name="date"
                        onChange={(date) => {
                          setFieldValue(
                            "lastRevised",
                            date
                              ? moment(date).format("YYYY-MM-DD HH:mm:ss")
                              : null
                          );
                        }}
                      />
                    </div>

                    <div>
                      <div className="text-16 mb-4 text-neutral-dark">
                        <label htmlFor="date">New effective date *</label>
                      </div>
                      <FormikCustomDate
                        // value={moment(values.date)}
                        inputClassName="border bg-transparent"
                        name="date"
                        onChange={(date) => {
                          setFieldValue(
                            "effective",
                            date
                              ? moment(date).format("YYYY-MM-DD HH:mm:ss")
                              : null
                          );
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-14 flex justify-end">
                <div className="flex gap-5">
                  <div>
                    <CustomButton
                      type="submit"
                      title="Cancel"
                      handleClick={() => {
                        onClose(false);
                      }}
                      isLoading={isLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                      isTransparent
                    />
                  </div>
                  <div>
                    <CustomButton
                      type="submit"
                      title="Save"
                      handleClick={() => {}}
                      isLoading={isLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                    />
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default RevisedSalaryForm;
