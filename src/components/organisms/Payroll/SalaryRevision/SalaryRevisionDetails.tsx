import React, { useState } from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { Edit2, Tick<PERSON>ircle, User, Clock } from "iconsax-react";
import { ButtonProperties } from "../../../shared/helpers";
import { AiOutlineStop } from "react-icons/ai";
import StatusTag from "../../../atoms/StatusTag";
import { MdNumbers } from "react-icons/md";
import { TbTimeDurationOff } from "react-icons/tb";
import { RiInstallLine } from "react-icons/ri";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import RevisedSalaryForm from "./RevisedSalaryForm";
import GoBack from "../../../atoms/Ui/GoBack";

const SalaryRevisionDetails = () => {
    const [openRevisedModal, setOpenRevisedModal] = useState<boolean>(false);
    const [openApproveLoanModal, setOpenApproveLoanModal] = useState<boolean>(false);
    const [isApprove, setIsApprove] = useState<boolean>(false);
  const sampleData = [
    {
      name: "Basic Salary",
      monthlyCost: "$300",
      yearlyCost: "$3,200",
    },
    {
      name: "Cost of Living Allowance",
      monthlyCost: "$100",
      yearlyCost: "$1,200",
    },
    {
      name: "Housing Allowance",
      monthlyCost: "$600",
      yearlyCost: "$6,600",
    },
    {
      name: "Total Allowance",
      monthlyCost: "$1,000",
      yearlyCost: "$12,000",
    },
  ];

  const columns = [
    {
      Header: "Salary component",
      accessor: "name",
      Cell: (row: any) => (
        <div className="flex  items-center">
          <p>{row.cell.value}</p>
        </div>
      ),
    },

    {
      Header: "Monthly",
      accessor: "monthlyCost",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Yearly",
      accessor: "yearlyCost",
      Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
    },

  ];

  return (
    <div>
        <GoBack/>
        <div className="mt-4">
        <h1 className="font-poppins-medium text-18 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-[23px]">
            Salary revision for John Doe
        </h1>

        <div className="flex justify-end gap-4 mt-8">
            <div>
            <CustomButton
                className="!w-fit !h-5"
                isTransparent={true}
                handleClick={() => {setIsApprove(true); setOpenApproveLoanModal(true)}}
                leftIcon={<TickCircle className="ml-3" size={20} />}
                title="Approve"
                variant={ButtonProperties.VARIANT.primary.name}
                size={ButtonProperties.SIZES.small}
            />
            </div>
            <div>
            <CustomButton
                className=" !w-fit !bg-transparent !text-alert-text-error !border-alert-text-error  !h-5"
                variant={ButtonProperties.VARIANT.primary.name}
                size={ButtonProperties.SIZES.small}
                handleClick={() => {setIsApprove(false); setOpenApproveLoanModal(true)}}
                leftIcon={<AiOutlineStop className="ml-3" size={20} />}
                title="Reject"
            />
            </div>
            <div>
            <CustomButton
                className="!w-fit  !h-5"
                isTransparent={true}
                variant={ButtonProperties.VARIANT.primary.name}
                size={ButtonProperties.SIZES.small}
                handleClick={() => {setOpenRevisedModal(true)}}
                leftIcon={<Edit2 className="ml-3" size={20} />}
                title="Edit"
            />
            </div>
        </div>
        <div className="mt-10">
            <div className="grid grid-cols-6 gap-8 rounded-lg mt-4">
            <div className="col-span-4 bg-white">
                <CustomTable
                data={sampleData || []}
                meta={{}}
                columns={columns}
                hideSearch
                // handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
                header={
                    <div>
                    <h1 className="font-poppins-medium text-purple-dark-active mb-4">
                        New salary structure
                    </h1>
                    </div>
                }
                />
            </div>
            <div className="col-span-2 bg-white  rounded-lg p-4">
                <div className="flex justify-between border-b border-b-purple-normal pb-2">
                <h1 className="text-purple-normal font-poppins-medium">
                    Revised details
                </h1>
                <StatusTag className="!bg-transparent !py-0" status="pending" />
                </div>
                <div className="mt-6 text-neutral-normal">
                <div className="flex justify-between">
                    <h1 className="flex gap-1">
                    <User className="mt-1" size={12} />
                    Employee name
                    </h1>
                    <p className="text-neutral-normal font-poppins-medium">
                    John Doe
                    </p>
                </div>
                <div className="flex justify-between mt-4">
                    <h1 className="flex gap-1">
                    <User className="mt-1" size={12} />
                    Previous pay
                    </h1>
                    <p className="text-neutral-normal font-poppins-medium">
                    $3,000
                    </p>
                </div>
                <div className="flex justify-between mt-4">
                    <h1 className="flex gap-1">
                    <MdNumbers className="mt-1" size={12} />
                    New salary
                    </h1>
                    <p className="text-neutral-normal font-poppins-medium">
                    $4,000
                    </p>
                </div>
                <div className="flex justify-between mt-4">
                    <h1 className="flex gap-1">
                    <RiInstallLine className="mt-1" size={12} />
                    Effective from
                    </h1>
                    <p className="text-neutral-normal font-poppins-medium">
                    April, 2025
                    </p>
                </div>
                <div className="flex justify-between mt-4">
                    <h1 className="flex gap-1">
                    <Clock className="mt-1" size={12} />
                    Payout month
                    </h1>
                    <p className="text-neutral-normal font-poppins-medium">
                    March, 2025
                    </p>
                </div>
                <div className="flex justify-between mt-4">
                    <h1 className="flex gap-1">
                    <TbTimeDurationOff className="mt-1" size={12} />
                    Last revision
                    </h1>
                    <p className="text-neutral-normal font-poppins-medium">
                    15 March, 2026
                    </p>
                </div>
                </div>
            </div>
            </div>
        </div>

        <CustomModal visibility={openApproveLoanModal} toggleVisibility={setOpenApproveLoanModal} > 
            <div>
            <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
                <h1 className="font-poppins-medium text-18 text-purple-dark ">
                {isApprove ? "Approve" : "Reject"} revised salary ?
                </h1>
            </div>
            <p className='text-center text-18 mt-10 px-10'>Are you sure you want to {isApprove ? "approve" : "reject"} this new revised salary. This action cannot be undone.</p>
            <div className="mt-14 flex justify-center mb-10">
                    <div className="flex gap-5">
                    <div>
                        <CustomButton
                        type="submit"
                        title="Cancel"
                        handleClick={() => { setOpenApproveLoanModal(false)}}
                        size={ButtonProperties.SIZES.small}
                        variant={ButtonProperties.VARIANT.primary.name}
                        isTransparent
                        />
                    </div>
                    <div>
                        <CustomButton
                        type="submit"
                        title={isApprove ? "Approve"  : "Reject"}
                        handleClick={() => {}}
                        // isLoading={isLoading}
                        size={ButtonProperties.SIZES.small}
                        variant={ButtonProperties.VARIANT.primary.name}
                        />
                    </div>
                    </div>
                </div>
            </div>
        </CustomModal>

        <CustomModal visibility={openRevisedModal} toggleVisibility={setOpenRevisedModal} > 
            <RevisedSalaryForm data={{}} onClose={setOpenRevisedModal}/>
        </CustomModal>
        </div>
    </div>
  );
};

export default SalaryRevisionDetails;
