import React, { useState } from 'react'
import CustomTable from '../../../atoms/CustomTable/CustomTable';
import OrganizationEmptyState from '../../../atoms/Cards/OrganizationEmptyState';
import CustomCheckBox from '../../../atoms/CustomCheckBox/CustomCheckBox';
import { ButtonProperties, getNameInitials, truncateText } from '../../../shared/helpers';
import StatusTag from '../../../atoms/StatusTag';
import { FaEllipsisV } from 'react-icons/fa';
import FilterDropdown from '../../../atoms/Cards/FilterDropdown';
import useClickOutside from '../../../shared/hooks';
import { Edit2, Eye, TickCircle } from 'iconsax-react';
import { AiOutlineStop } from 'react-icons/ai';
import { useNavigate } from 'react-router-dom';
import CustomModal from '../../../atoms/CustomModal/CustomModal';
import CustomButton from '../../../atoms/CustomButton/CustomButton';
import { loggedUserAtom } from '../../../../recoil/atom/authAtom';
import { useRecoilValue } from 'recoil';

const SalaryRevision = () => {
  const navigate = useNavigate();
  const [rowId, setRowId] = useState(0);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [openApproveLoanModal, setOpenApproveLoanModal] = useState<boolean>(false);
  const [isApprove, setIsApprove] = useState<boolean>(false);
  const getUserValue = useRecoilValue(loggedUserAtom);

  const userCucrrency = getUserValue?.businesses?.map((item) => item.default_currency)

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const sampleData = [
    {
      employeeName: "John Doe",
      effective: "Jan, 2025",
      payoutMonth: "Feb, 2025",
      previousPay: "2,000",
      revisedPay: "$2,500",
      status: "pending",
    },

    {
      employeeName: "Jane Smith",
      effective: "Jan, 2025",
      payoutMonth: "Feb, 2025",
      previousPay: "2,000",
      revisedPay: "$2,500",
      status: "approved",
    },
  ]



  const columns = [
    {
      Header: "Employee name",
      accessor: "employeeName",
      Cell: (row: any) => (
        <div>
          {row.cell.value || "--"}
        </div>
      ),
    },
    {
      Header: "Effective from",
      accessor: "effective",
      Cell: (row: any) => (
        <div>
          {row.cell.value || "--"}
        </div>
      ),
    },
    {
      Header: "Payout month",
      accessor: "payoutMonth",
      Cell: (row: any) => (
        <div>
          <p><span className="text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1">{userCucrrency}</span>  {row.cell.value || "--"}</p>
        </div>
      ),
    },

    {
      Header: "Previous pay",
      accessor: "previousPay",
      Cell: (row: any) => (
        <div>
          <p><span className="text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1">{userCucrrency}</span>  {row.cell.value || "--"}</p>
        </div>
      ),
    },
    {
      Header: "Revised pay",
      accessor: "revisedPay",
      Cell: (row: any) => <p><span className="text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1">{userCucrrency}</span> {row.cell.value || "--"}</p>,
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: (row: any) => <StatusTag status={row.cell.value} />,
    },
    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                  onClick={() => { navigate("/payroll/salary-revision-details") }}
                >
                  <Eye size={18} />
                  View
                </li>
                <li
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                  onClick={() => { navigate("/payroll/salary-revision-details") }}
                >
                  <Edit2 size={18} />
                  Edit
                </li>
                {row.cell.row.original.status === "pending" && (
                  <>
                    <li
                      className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                      onClick={() => { setIsApprove(true); setOpenApproveLoanModal(true) }}
                    >
                      <TickCircle size={18} />
                      Approve
                    </li>
                    <li
                      className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                      onClick={() => { setIsApprove(false); setOpenApproveLoanModal(true) }}
                    >
                      <AiOutlineStop size={18} />
                      Reject
                    </li>
                  </>
                )}

              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];
  return (
    <div>
      <div className=" my-10 py-[23px]">
        {sampleData?.length > 0 ? (
          <CustomTable
            data={sampleData || []}
            meta={{}}
            columns={columns}
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            header={
              <div className="flex items-center h-[50px] px-2">
                <h1 className="font-poppins-medium text-center">
                  Salary revision information
                </h1>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState
              buttonTitle="Nothing to show"
            />
          </div>
        )}
      </div>

      <CustomModal visibility={openApproveLoanModal} toggleVisibility={setOpenApproveLoanModal} >
        <div>
          <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
            <h1 className="font-poppins-medium text-18 text-purple-dark ">
              {isApprove ? "Approve" : "Reject"} revised salary ?
            </h1>
          </div>
          <p className='text-center text-18 mt-10 px-10'>Are you sure you want to {isApprove ? "approve" : "reject"} this new revised salary. This action cannot be undone.</p>
          <div className="mt-14 flex justify-center mb-10">
            <div className="flex gap-5">
              <div>
                <CustomButton
                  type="submit"
                  title="Cancel"
                  handleClick={() => { setOpenApproveLoanModal(false) }}
                  size={ButtonProperties.SIZES.small}
                  variant={ButtonProperties.VARIANT.primary.name}
                  isTransparent
                />
              </div>
              <div>
                <CustomButton
                  type="submit"
                  title={isApprove ? "Approve" : "Reject"}
                  handleClick={() => { }}
                  // isLoading={isLoading}
                  size={ButtonProperties.SIZES.small}
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </div>
            </div>
          </div>
        </div>
      </CustomModal>
    </div>
  )
}

export default SalaryRevision