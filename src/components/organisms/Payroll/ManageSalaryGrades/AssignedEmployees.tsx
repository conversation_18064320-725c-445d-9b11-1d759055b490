import { useEffect, useRef, useState } from "react";
import CustomCheckBox from "../../../atoms/CustomCheckBox/CustomCheckBox";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../shared/helpers";


const AssignedEmployees = ({ fetch, debounceSearch, setPageNumber, setSearchQuery, staffs }) => {

    const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
    const [rowId, setRowId] = useState(0);
    const [selectedEmployees, setSelectedEmployees] = useState<any>([]);


    const ManageEmployee = [
        {
            Header: 'Title',
            accessor: 'fullName',
            Cell: (row: any) => (
                <div className="flex items-center gap-4">
                    <CustomCheckBox
                        className='!w-4 !h-4'
                        label=''
                        checked={selectedEmployees.length === staffs.length}
                        name='select_all_employees'
                        onChange={(e) => {
                            console.log(
                                'select all employees',
                                e.target,
                                selectedEmployees.length === staffs.length,
                                selectedEmployees.map((item) => item.id)
                            );
                            if (selectedEmployees.length === staffs.length) {
                                setSelectedEmployees([]);
                            } else {
                                setSelectedEmployees(staffs.map((item) => item.id));
                            }
                        }}
                    />
                    <p> {row.cell.value || '--'} </p>
                </div>
            ),
        },
        {
            Header: 'Employee ID',
            accessor: 'staff_identification_tag',
            Cell: (row: any) => <p> {row.cell.value || '--'}</p>,
        },
        {
            Header: 'Employment department',
            accessor: 'job_title',
            Cell: (row: any) => <p> {row.cell.value || '--'}</p>,
        },
        {
            Header: '',
            accessor: 'action',
            Cell: (row: any) => (
                <div className='relative'>
                    <div
                        className="text-alert-text-error cursor-pointer"
                        onClick={() => {
                            setShowDeleteWarn(!showDeleteWarn);
                            setRowId(row.cell.row.id);
                            // console.log(row.cell.row.id, rowId);
                        }}
                    >
                        <p>Remove</p>
                    </div>

                    {/* {showDeleteWarn && row.cell.row.id === rowId && (
                        <ul className='text-14 text-neutral-dark '>

                            <li className='pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal '>
                                <div className='flex gap-3'>
                                    <PiTrash size={18} />
                                    Are you sure?
                                </div>
                                <div className='grid grid-cols-2 gap-5 mt-8 ml-2 mr-4'>
                                    <CustomButton
                                        isLoading={isLoading}
                                        title='Yes'
                                        handleClick={() => handleDeleteSalaryGrade(row.cell.row.original.id)}
                                        className='border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer'
                                    />
                                    <span
                                        onClick={() => {
                                            setShowDropdown(false);
                                            setShowDeleteWarn(false);
                                        }}
                                        className='text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center'
                                    >
                                        No
                                    </span>
                                </div>
                            </li>

                        </ul>
                    )} */}

                </div>
            ),
        },
    ];



    return (
        <div className=''>
            <div className="">
                <CustomTable
                    data={staffs || []}
                    columns={ManageEmployee}
                    handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
                    handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
                />
            </div>
        </div>
    )
}

export default AssignedEmployees;