import { useCallback, useEffect, useRef, useState } from 'react';
import { getStaff } from '../../../../api/staff';
import CustomTable from '../../../atoms/CustomTable/CustomTable';
import CustomCheckBox from '../../../atoms/CustomCheckBox/CustomCheckBox';
import { manageEmployeesOnSalaryGrade } from '../../../../api/payroll/salaryGrade';
import { ButtonProperties } from '../../../shared/helpers';
import CustomButton from '../../../atoms/CustomButton/CustomButton';

const MangeSalaryGrades = ({ name, salaryGradeId, onClose }) => {
  const [staffs, setStaffs] = useState<any>([]);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const [selectedEmployees, setSelectedEmployees] = useState<any>([]);
  const [savingEmployees, setSavingEmployees] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const saveEmployees = () => {
    setSavingEmployees(true);
    manageEmployeesOnSalaryGrade(salaryGradeId, selectedEmployees)
      .then((res) => {
        if (res !== false) {
          setSavingEmployees(false);
          setSelectedEmployees([]);
          fetchEmployees();
          onClose();
        }
      })
      .catch((error) => {
        console.error('Error saving employees:', error);
      })
      .then(() => {
        setSavingEmployees(false);
      });
  };

  // const node = useClickOutside(() => {
  //   setShowDropdown(false);
  //   setRowId(0);
  // });

  const ManageEmployee = [
    {
      Header: (
        <CustomCheckBox
          className='!w-4 !h-4'
          label=''
          checked={selectedEmployees.length === staffs.length}
          name='select_all_employees'
          onChange={(e) => {
            if (selectedEmployees.length === staffs.length) {
              setSelectedEmployees([]);
            } else {
              setSelectedEmployees(staffs.map((item) => item.id));
            }
          }}
        />
      ),
      accessor: 'id',
      Cell: (row: any) => (
        <div className='flex items-center gap-4'>
          <CustomCheckBox
            className='!w-4 !h-4'
            label=''
            name='select_all_employees'
            checked={selectedEmployees.includes(row.row.original.id)}
            onChange={(e) => {
              if (!selectedEmployees.includes(row.row.original.id)) {
                setSelectedEmployees([...selectedEmployees, row.row.original.id]);
              } else {
                setSelectedEmployees(selectedEmployees.filter((id) => id !== row.row.original.id));
              }
            }}
          />
        </div>
      ),
    },
    {
      Header: 'Title',
      accessor: 'fullName',
    },
    {
      Header: 'Employee ID',
      accessor: 'staff_identification_tag',
    },
    {
      Header: 'Employment department',
      accessor: 'job_title',
      Cell: (row: any) => <p> {row.cell.value || '--'}</p>,
    },
  ];

  const debounce = (func, delay) => {
    let timeoutId;
    return (...args) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => {
        func(...args);
      }, delay);
    };
  };

  const fetchEmployees = useCallback(
    (q?: string) => {
      setLoading(true);
      getStaff({ search: q, page: pageNumber })
        .then((res) => {
          if (res.success) {
            setStaffs(res.data.data);
            const selectedStaffIds = res.data.data
              .filter((s: any) => s.payroll_salary_grade_id == salaryGradeId)
              .map((s: any) => s.id);
            setSelectedEmployees(selectedStaffIds);
            console.log('Fetched employees:', res.data.data, selectedStaffIds);
          }
        })
        .catch((error) => {
          console.error('Error fetching employees:', error);
        })
        .then(() => {
          setLoading(false);
        });
    },
    [salaryGradeId, pageNumber]
  );

  const debounceSearch = useRef(debounce((q) => fetchEmployees(q), 2000)).current;

  useEffect(() => {
    fetchEmployees();
  }, []);

  return (
    <div className='flex flex-col w-full p-5'>
      <div className='mt-1 w-full px-4'>
        <p className='mb-1 text-[1.15rem] font-bold text-[#050510]'>{name}</p>
        <p className='mb-3 font-normal text-[0.85rem] text-light'>
          Manage employees under this salary grade
        </p>
        <hr></hr>
      </div>
      <div>
        <div>
          <CustomTable
            data={staffs}
            columns={ManageEmployee}
            isLoading={loading}
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            handleSearch={(search) => {
              setSearchQuery(search);
              debounceSearch(search);
            }}
          />
        </div>
        <div className='flex justify-end mt-1'>
          <CustomButton
            type='submit'
            className='!w-[128px] !h-10'
            title={'Save'}
            isLoading={savingEmployees}
            handleClick={saveEmployees}
            variant={ButtonProperties.VARIANT.primary.name}
          />
        </div>
      </div>
    </div>
  );
};

export default MangeSalaryGrades;
