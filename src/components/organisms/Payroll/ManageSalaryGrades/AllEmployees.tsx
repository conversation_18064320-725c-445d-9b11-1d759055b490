import { useState } from 'react';
import CustomCheckBox from '../../../atoms/CustomCheckBox/CustomCheckBox';
import CustomTable from '../../../atoms/CustomTable/CustomTable';

const AllEmployees = ({ debounceSearch, setPageNumber, setSearchQuery, staffs }) => {
  const [selectedEmployees, setSelectedEmployees] = useState<any>([]);

  const ManageEmployee = [
    {
      Header: 'Title',
      accessor: 'fullName',
      Cell: (row: any) => (
        <div className='flex items-center gap-4'>
          <CustomCheckBox
            className='!w-4 !h-4'
            label=''
            checked={selectedEmployees.length === staffs.length}
            name='select_all_employees'
            onChange={(e) => {
              console.log(
                'select all employees',
                e.target,
                selectedEmployees.length === staffs.length,
                selectedEmployees.map((item) => item.id)
              );
              if (selectedEmployees.length === staffs.length) {
                setSelectedEmployees([]);
              } else {
                setSelectedEmployees(staffs.map((item) => item.id));
              }
            }}
          />
          <p> {row.cell.value || '--'} </p>
        </div>
      ),
    },
    {
      Header: 'Employee ID',
      accessor: 'staff_identification_tag',
    },
    {
      Header: 'Employment department',
      accessor: 'job_title',
      Cell: (row: any) => <p> {row.cell.value || '--'}</p>,
    },
  ];

  return (
    <div className=''>
      <div className=''>
        <CustomTable
          data={staffs || []}
          columns={ManageEmployee}
          handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
          handleSearch={(search) => {
            setSearchQuery(search);
            debounceSearch(search);
          }}
        />
      </div>
    </div>
  );
};

export default AllEmployees;
