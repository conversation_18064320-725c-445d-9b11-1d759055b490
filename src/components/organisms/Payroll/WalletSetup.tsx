import { Form, Formik } from "formik";
import React from "react";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../shared/helpers";

const WalletSetup = () => {
  return (
    <div className="my-10 pb-10 bg-white rounded-bl-[10px] rounded-br-[10px]">
        <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-[33px]">
          Request for virtual account
        </h1>
      <div className="px-6 mt-6">
        <Formik
          initialValues={{}}
          validationSchema={{}}
          onSubmit={() => {}}
          enableReinitialize
        >
          {({ setFieldValue }) => (
            <Form>
              <div className="mt-10 grid grid-cols-2 gap-9">
                <div>
                <FormikCustomInput
                    placeholder="Enter BVN"
                    label="BVN *"
                    inputClassName="bg-transparent"
                    name="bvn"
                    type="text"
                    disabled
                  />
                </div>
                <div>
                  <FormikCustomInput
                    placeholder="<EMAIL> "
                    label="Email *"
                    inputClassName="border bg-transparent"
                    name="email"
                    type="email"
                  />
                </div>
              </div>
              <div className="mt-10 grid grid-cols-2 gap-9">
                <div>
                  <FormikCustomInput
                    placeholder="John"
                    label="First Name *"
                    inputClassName="bg-transparent"
                    name="firstName"
                    type="text"
                    disabled
                  />
                </div>
                <div>
                <FormikCustomInput
                    placeholder="Doe"
                    label="Last Name *"
                    inputClassName="bg-transparent"
                    name="lastName"
                    type="text"
                    disabled
                  />
                </div>
              </div>

              <div className="mt-[120px] flex justify-end">
                <CustomButton
                  type="submit"
                //   isLoading={isLoading}
                  title="Submit"
                  handleClick={() => {}}
                  className="!w-[170px]"
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default WalletSetup;
