import React, { useState } from "react";
import { FiPlus } from "react-icons/fi";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import CustomTable from "../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../atoms/Cards/OrganizationEmptyState";
import StatusTag from "../../atoms/StatusTag";
import { Eye, UserEdit } from "iconsax-react";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import { FaEllipsisV } from "react-icons/fa";
import useClickOutside from "../../shared/hooks";
import { PiTrash } from "react-icons/pi";
import { MdRemoveCircleOutline } from "react-icons/md";
import CustomModal from "../../atoms/CustomModal/CustomModal";
import { ButtonProperties } from "../../shared/helpers";
import { Form, Formik } from "formik";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";

const Deductables = () => {
  const [deductableModal, setDeductableModal] = useState(false);
  const [action, setAction] = useState("");
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [rowId, setRowId] = useState(0);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setShowDeleteWarn(false);
    setRowId(0);
  });
  const deductablesData = [
    {
      id: 1,
      name: "Tax Deduction",
      description: "Tax deduction for employee",
      amount: "",
      rateType: "percentage",
      status: "active",
      rate: "0.5",
    },
    {
      id: 1,
      name: "Salary Deduction",
      description: "Salary deduction for employee",
      amount: "10,000",
      rateType: "fixed",
      status: "active",
      rate: "",
    },
  ];

  const columns = [
    {
      Header: "Name",
      accessor: "name",
    },
    {
      Header: "Description",
      accessor: "description",
    },
    {
      Header: "Rate Type",
      accessor: "rateType",
    },
    {
      Header: "Rate (%)",
      accessor: "rate",
      Cell: (row) => <div>{row.cell.value || "--"}</div>,
    },
    {
      Header: "Amount ($)",
      accessor: "amount",
      Cell: (row) => <div>{row.cell.value || "--"}</div>,
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: (row) => <StatusTag status={row.cell.value} />,
    },
    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() => {
                    setDeductableModal(true);
                    setAction("view");
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>
                <li
                  onClick={() => {
                    setDeductableModal(true);
                    setAction("edit");
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <UserEdit size={18} />
                  Edit
                </li>
                {showDeleteWarn ? (
                  <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                    <div className="flex gap-3">
                      <PiTrash size={18} />
                      Are you sure?
                    </div>
                    <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                      <CustomButton
                        //   isLoading={isLoading}
                        title="Yes"
                        handleClick={() => {}}
                        className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer"
                      />
                      <span
                        onClick={() => {
                          setShowDeleteWarn(false);
                        }}
                        className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                      >
                        No
                      </span>
                    </div>
                  </li>
                ) : (
                  <li
                    onClick={() => setShowDeleteWarn(true)}
                    className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer"
                  >
                    <MdRemoveCircleOutline size={18} />
                    Deactivate
                  </li>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  return (
    <div>
      <div className="pt-10 flex justify-end">
        <CustomButton
          className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
          isTransparent={true}
          handleClick={() => setDeductableModal(true)}
          leftIcon={<FiPlus className="ml-3" size={20} />}
          title="Add deductible"
        />
      </div>

      <div className="bg-[#F5F5F5] my-10 px-4 py-[23px]">
        {deductablesData.length > 0 ? (
          <CustomTable
            data={deductablesData || []}
            meta={{}}
            columns={columns}
            header={
              <div>
                <h1 className="font-poppins-medium text-purple-normal-active mb-4">
                  Deductibles information
                </h1>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState text="You have not added any deductables." />
          </div>
        )}
      </div>

      <CustomModal
        visibility={deductableModal}
        toggleVisibility={setDeductableModal}
      >
        <div className="mb-10">
          <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-6">
            {action === "view"
              ? "Deductible information"
              : action === "edit"
              ? "Edit deductible"
              : "Add deductible"}
          </h1>
          <div className="px-7">
            <div className="mt-10">
              <Formik
                initialValues={{}}
                onSubmit={() => {}}
                enableReinitialize
                validationSchema={{}}
              >
                {({ setFieldValue }) => (
                  <Form>
                    <div>
                      <FormikCustomInput
                        placeholder="Fitness dues"
                        label="Name *"
                        inputClassName="bg-transparent"
                        name="name"
                        type="text"
                      />
                    </div>
                    <div className="mt-8 grid grid-cols-2 gap-6">
                      <div>
                        <FormikCustomSelect
                          label="Rate type *"
                          name="rateType"
                          options={[
                            { value: "Fixed amount", text: "Fixed amount" },
                            { value: "Percentage", text: "Percentage" },
                          ]}
                          onChange={(item: { value: string; text: string }) => {
                            setFieldValue("rateType", item.text);
                          }}
                          required
                        />
                      </div>
                      <div>
                        <FormikCustomInput
                          placeholder="Value"
                          label="Value *"
                          inputClassName="bg-transparent"
                          name="amount"
                          type="text"
                        />
                      </div>
                    </div>
                    <div className="mt-8">
                      <div className="text-16 mb-4 text-neutral-dark">
                        <label htmlFor="description">Description</label>
                      </div>
                      <textarea
                        name="description"
                        id="description"
                        className="rounded-[4px] w-full hide-scrollbar p-3 outline-1 outline-purple-normal-hover border-[0.6px] border-[#B2BBC699]"
                        rows={3}
                        onChange={(e) =>
                          setFieldValue("description", e.target.value)
                        }
                      ></textarea>
                    </div>

                    {action !== "view" && (
                      <div className="flex justify-end mt-20">
                        <CustomButton
                          type="submit"
                          className="!w-[128px] !h-10"
                          title={action === "edit" ? "Save" : "Add"}
                          // isLoading={isLoading}
                          handleClick={() => {}}
                          variant={ButtonProperties.VARIANT.primary.name}
                        />
                      </div>
                    )}
                  </Form>
                )}
              </Formik>
            </div>
          </div>
        </div>
      </CustomModal>
    </div>
  );
};

export default Deductables;
