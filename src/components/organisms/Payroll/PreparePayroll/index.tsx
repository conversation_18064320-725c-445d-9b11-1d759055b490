import React, { useState } from "react";
import useClickOutside from "../../../shared/hooks";
import { useNavigate } from "react-router-dom";
import StatusTag from "../../../atoms/StatusTag";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import { Eye, UserEdit } from "iconsax-react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { MdPayment } from "react-icons/md";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import { Form, Formik } from "formik";
import CustomCheckBox from "../../../atoms/CustomCheckBox/CustomCheckBox";
import {
  ButtonProperties,
  months,
  truncateText,
} from "../../../shared/helpers";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { FaDownload } from "react-icons/fa6";
import CustomModal from "../../../atoms/CustomModal/CustomModal";

const PreparePayroll = () => {
  const navigate = useNavigate();
  const [groupCheck, setGroupCheck] = useState<boolean>(false);
  const [paymentWarningModal, setPaymentWarningModal] =
    useState<boolean>(false);
  const [checkedItems, setCheckedItems] = useState<any[]>([]);
  const [rowId, setRowId] = useState(0);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const handleCheckboxChange = (event, row) => {
    const isChecked = event.target.checked;
    setCheckedItems((prevState: any) => {
      if (isChecked) {
        return [...prevState, row];
      } else {
        setGroupCheck(false);
        return prevState.filter((item) => item.id !== row.id);
      }
    });
  };

  const handleSelectAllChange = (event) => {
    const isChecked = event.target.checked;
    setGroupCheck(isChecked);

    if (isChecked) {
      setCheckedItems(employeePayrollData);
    } else {
      setCheckedItems([]);
    }
  };

  const employeePayrollData = [
    {
      id: 1,
      name: "Giffy Onyinye",
      description: "30,000",
      amount: "400,000",
      bonus: "15,000",
      status: "unpaid",
      employerContribution: "60,000",
      employeeContribution: "40,000",
    },
    {
      id: 2,
      name: "Helen Josie",
      description: "0",
      amount: "200,000",
      bonus: "4,000",
      status: "unpaid",
      employeeContribution: "70,000",
      employerContribution: "50,000",
    },
    {
      id: 3,
      name: "John Doe",
      description: "0",
      amount: "200,000",
      bonus: "4,000",
      status: "unpaid",
      employeeContribution: "70,000",
      employerContribution: "50,000",
    },
  ];

  const columns = [
    {
      Header: (
        <div className="flex">
          <CustomCheckBox
            checked={groupCheck}
            onChange={(e: any) => handleSelectAllChange(e)}
            customClass="!mr-3"
          />{" "}
          <p className="mt-1">Employee name</p>
        </div>
      ),
      accessor: "name",
      Cell: (row: any) => (
        <div className="flex  items-center">
          <CustomCheckBox
            checked={checkedItems.some(
              (item: any) => item.id === row.cell.row.original.id
            )}
            onChange={(e: any) => {
              handleCheckboxChange(e, row.cell.row.original);
            }}
            customClass="!mr-3"
          />
          <p className="font-poppins-medium text-neutral-dark">
            {truncateText(row.cell.value, 20)}
          </p>
        </div>
      ),
    },
    {
      Header: "Overtime",
      accessor: "description",
    },
    {
      Header: "Bonus",
      accessor: "bonus",
    },
    {
      Header: "Deductions",
      accessor: "employerContribution",
      Cell: (row) => <div>{row.cell.value || "--"}</div>,
    },
    {
      Header: "Net pay",
      accessor: "employeeContribution",
      Cell: (row) => <div>{row.cell.value || "--"}</div>,
    },
    {
      Header: "Basic salary ($)",
      accessor: "amount",
      Cell: (row) => <div>{row.cell.value || "--"}</div>,
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: (row) => <StatusTag status={row.cell.value} />,
    },
    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() => {
                    navigate("/payroll/add-payroll-setup");
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>

                <li
                  onClick={() => {
                    navigate("/payroll/add-payroll-setup", {
                      state: { isEdit: true },
                    });
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <UserEdit size={18} />
                  Manage payroll setup
                </li>
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  return (
    <div>
      <div>
        <div>
          <Formik initialValues={{}} onSubmit={() => {}}>
            {({ setFieldValue }) => (
              <Form>
                <div className="flex justify-between">
                  <div>
                    <FormikCustomSelect
                      label="Select month"
                      name="month"
                      options={months}
                      parentContainer="!h-12"
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("month", item.text);
                      }}
                      required
                    />
                  </div>
                  <div className="mt-6">
                    <CustomButton
                      handleClick={() => {
                        setPaymentWarningModal(true);
                      }}
                      variant={ButtonProperties.VARIANT.primary.name}
                      title="Process Payment"
                      size={ButtonProperties.SIZES.small}
                      leftIcon={<MdPayment />}
                    />
                  </div>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>

      <div className="bg-[#F5F5F5] my-10 px-4 py-[23px]">
        {employeePayrollData.length > 0 ? (
          <CustomTable
            data={employeePayrollData || []}
            meta={{}}
            columns={columns}
            header={
              <div className="flex justify-between mb-2">
                <h1 className="font-poppins-medium text-purple-normal-active mb-4">
                  Prepare payroll
                </h1>
                <div>
                  <div className="flex gap-4">
                    <CustomButton
                      handleClick={() => {}}
                      isTransparent
                      variant={ButtonProperties.VARIANT.primary.name}
                      title="Export to Excel"
                      leftIcon={<FaDownload />}
                      className="!h-8 w-fit"
                    />
                    <CustomButton
                      handleClick={() => {}}
                      isTransparent
                      variant={ButtonProperties.VARIANT.primary.name}
                      title="Export to PDF"
                      size={ButtonProperties.SIZES.small}
                      leftIcon={<FaDownload />}
                      className="!h-8"
                    />
                  </div>
                </div>
              </div>
            }
            checkedItems={checkedItems}
            handleFilter={(e) => console.log(e)}
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState text="You have not added any deductables." />
          </div>
        )}
      </div>
      <CustomModal
        visibility={paymentWarningModal}
        toggleVisibility={setPaymentWarningModal}
      >
        <div className="flex flex-col gap-4">
          <h2 className="text-24 bg-purple-light py-[33px] px-10  font-poppins-medium text-purple-normal">
            Process Payment
          </h2>
          <p className="text-18 mt-10 text-center font-poppins-medium text-[#4A504C]">
            Are you sure you want to process this payroll payment?
          </p>
          <div className="flex justify-center">
            <div className=" mb-8">
              <div className="flex gap-10 mt-10">
                <CustomButton
                  title="Cancel"
                  handleClick={() => {
                    setPaymentWarningModal(false);
                  }}
                  className="!w-[154px]"
                  isTransparent
                  variant={ButtonProperties.VARIANT.primary.name}
                />
                <CustomButton
                  type="submit"
                  title="Proceed"
                  handleClick={() => {}}
                  className={`!w-[154px] !border-none  text-white"`}
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </div>
            </div>
          </div>
        </div>
      </CustomModal>
    </div>
  );
};

export default PreparePayroll;
