import React, { useState } from "react";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../shared/helpers";
import { Form, Formik } from "formik";
import { useNavigate } from "react-router-dom";
import CustomModal from "../../atoms/CustomModal/CustomModal";
import OtpStep2 from "../Authentication/CreateAccount/OtpStep2";

interface Values {
  newEmail: string;
}

const initialState = {
  newEmail: "",
};

const ChangeEmail = ({currentEmail}) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showOtp, setShowOtp] = useState<boolean>(false);
  const [newEmail, setNewEmail] = useState<string>("");

  const handleSubmit = (values: any) => {
    setIsLoading(false);
    setNewEmail(values.newEmail)
    setShowOtp(true);
  };
  return (
    <div className="mb-10">
      <h1 className="font-poppins-medium text-18 rounded-tl-[10px] rounded-tr-[10px] text-purple-dark bg-purple-light pl-7 py-6">
        Email update
      </h1>
      {!showOtp ? (
        <div className="flex flex-col px-7 mt-10">
          <Formik<Values>
            initialValues={initialState}
            onSubmit={handleSubmit}
            enableReinitialize
          >
            {({ values }) => (
              <Form>
                <div>
                  <FormikCustomInput
                    label="Current email"
                    id="currentEmail"
                    name="currentEmail"
                    type="email"
                    value={currentEmail}
                    disabled
                  />
                </div>
                <div className="mt-8">
                  <FormikCustomInput
                    label="New email"
                    id="newEmail"
                    name="newEmail"
                    type="email"
                    value={values.newEmail}
                    required
                  />
                </div>
                <CustomButton
                  className="mt-[56px]"
                  type="submit"
                  title="Update"
                  handleClick={() => {}}
                  isLoading={isLoading}
                  variant={ButtonProperties.VARIANT.primary.name}
                />
              </Form>
            )}
          </Formik>
        </div>
      ) : (
        <div className="mt-10">

          <OtpStep2 destination="/account-setting" handleAction={() => {window.location.reload()}} emailState={newEmail} />
        </div>
      )}
    </div>
  );
};

export default ChangeEmail;
