import React, { useState } from "react";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../shared/helpers";
import { Form, Formik } from "formik";
import { changePasswordSchema } from "../../shared/helpers/schemas";
import { updatePassword } from "../../../api/profile";
import { NotificationTypes } from "../../shared/helpers/enums";
import { clustarkToast } from "../../atoms/Toast";

interface Values {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const initialState = {
  oldPassword: "",
  newPassword: "",
  confirmPassword: "",
};

const ChangePassword = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleSubmit = (values: any) => {
    setIsLoading(false);
    updatePassword({
      oldPassword: values.oldPassword,
      password: values.newPassword,
      password_confirmation: values.confirmPassword,
    }).then((res) => {
      if(res.success){
        clustarkToast(NotificationTypes.SUCCESS, res.message);
      }
    })
  };
  return (
    <div className="mb-10">
      <h1 className="font-poppins-medium text-18 rounded-tl-[10px] rounded-tr-[10px] text-purple-dark bg-purple-light pl-10 py-6">
        Password update
      </h1>
      <div className="flex flex-col px-7 mt-10">
        <Formik<Values>
          initialValues={initialState}
          onSubmit={handleSubmit}
          validationSchema={changePasswordSchema}
          enableReinitialize
        >
          {({ values }) => (
            <Form>
              <div>
                <FormikCustomInput
                  label="Current password"
                  id="oldPassword"
                  name="oldPassword"
                  placeholder="*************"
                  type="password"
                  value={values.oldPassword}
                />
              </div>
              <div className="mt-8">
                <FormikCustomInput
                  label="New password"
                  id="newPassword"
                  name="newPassword"
                  placeholder="*************"
                  type="password"
                  value={values.newPassword}
                />
              </div>
              <div className="mt-8">
                <FormikCustomInput
                  label="Confirm password"
                  id="confirmPassword"
                  name="confirmPassword"
                  placeholder="*************"
                  type="password"
                  value={values.confirmPassword}
                />
              </div>
              <CustomButton
                className="mt-[56px]"
                type="submit"
                title="Update Password"
                handleClick={() => {}}
                isLoading={isLoading}
                variant={ButtonProperties.VARIANT.primary.name}
              />
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default ChangePassword;
