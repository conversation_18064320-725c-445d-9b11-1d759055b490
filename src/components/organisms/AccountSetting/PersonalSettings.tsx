import React, { ChangeEvent, useEffect, useRef, useState } from "react";
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties, getNameInitials } from "../../shared/helpers";
import { Formik, Form } from "formik";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import FormikCustomPhoneInput from "../../atoms/CustomInput/FormikCustomPhoneInput";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";
import { useRecoilState, useRecoilValue } from "recoil";
import CustomModal from "../../atoms/CustomModal/CustomModal";
import ChangePassword from "./ChangePassword";
import ChangeEmail from "./ChangeEmail";
import {
  deleteAvatar,
  getProfileInformation,
  updateAvatar,
  updateProfileInfo,
} from "../../../api/profile";
import { getProfileInfoAtom } from "../../../recoil/atom/profile";
import { FaPenAlt, FaPencilAlt  } from "react-icons/fa";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../shared/hooks";
import Loader from "../../atoms/Loader";

const PersonalSettings = () => {
  const [showPasswordModal, setShowPasswordModal] = useState<boolean>(false);
  const [showEmailModal, setShowEmailModal] = useState<boolean>(false);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState<boolean>(false);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [, setProfileAtom] = useRecoilState(getProfileInfoAtom);
  const getUserValue = useRecoilValue(getProfileInfoAtom);

  const node = useClickOutside(() => {
    setShowDropdown(false);
  });

  const fetchProfileInfo = () => {
    setIsFetching(true);
    getProfileInformation().then((res) => {
      if (res.success) {
        setProfileAtom(res.data);
      }
      setIsFetching(false);
    });
  };

  useEffect(() => {
    fetchProfileInfo();
  }, []);

  const initialState = {
    firstName: getUserValue?.first_name ? getUserValue?.first_name : "",
    lastName: getUserValue?.last_name ? getUserValue?.last_name : "",
    phoneNumber: getUserValue?.phone_number ? getUserValue?.phone_number : "",
    gender: getUserValue?.gender ? getUserValue?.gender : "",
  };

  const handleImageUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (
      file &&
      (file.type === "image/jpeg" || file.type === "image/png") &&
      file.size <= 900 * 1024
    ) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImage(reader.result as string);
      };
      reader.readAsDataURL(file);
      const formData = new FormData();
      formData.append("image", file);
      updateAvatar(formData).then((res) => {
        if (res.success) {
          setShowDropdown(false);
          setShowDeleteWarn(false); 
          clustarkToast(NotificationTypes.SUCCESS, res.message);
        }
      });
    } else {
      clustarkToast(
        NotificationTypes.ERROR,
        "Image must be a .jpg or .png and less than 900kb."
      );
    };
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const handleDeleteAvatar = () => {
    setIsDeleteLoading(true);
    deleteAvatar()
      .then((res) => {
        if (res.success) {
          setIsDeleteLoading(false);
          setProfileImage(null);
          fetchProfileInfo();
          setShowDropdown(false);
          setShowDeleteWarn(false); 
          clustarkToast(NotificationTypes.SUCCESS, res.message);
        }
      })
      .catch(() => {
        setIsDeleteLoading(false);
        setShowDeleteWarn(false); 
      });
  };

  const handleSubmit = (values) => {
    setIsLoading(true);
    updateProfileInfo({
      firstName: values.firstName,
      lastName: values.lastName,
      phoneNumber: values.phoneNumber,
      gender: values.gender,
    }).then((res) => {
      if (res.success) {
        setIsLoading(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchProfileInfo();
      }
    });
  };

  if(isFetching) {
    return (
      <Loader/>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-5 gap-10">
        <div className="col-span-3 mt-10 p-4 bg-white rounded-xl shadow-sm">
          <div>
            <div className="mb-5 ">
              <input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg, image/png"
                onChange={handleImageUpload}
                className="hidden"
              />
              <div className="relative w-fit">
                {profileImage || getUserValue?.avatar ? (
                  <div className="relative">
                    <img
                      src={profileImage || getUserValue?.avatar}
                      alt="Profile"
                      className="w-36 h-36 object-cover rounded-full mx-auto border"
                    />
                    {/* <i className="fa fa-pen"></i> */}
                    <FaPencilAlt 
                      size={30}
                      onClick={() => setShowDropdown(!showDropdown)}
                      className="absolute cursor-pointer top-0 right-4 text-white rounded-full bg-purple-dark border border-purple-dark p-2"
                    />
                  </div>
                ) : (
                  <div className="relative">
                    <p className="w-36 h-36 bg-purple-light-active text-purple-normal font-poppins-medium font-bold rounded-full flex justify-center items-center text-6xl">
                      {getUserValue?.first_name
                        ? getNameInitials(
                            getUserValue?.first_name,
                            getUserValue?.last_name
                          )
                        : ""}
                    </p>
                    <FaPencilAlt 
                      size={30}
                      onClick={() => setShowDropdown(!showDropdown)}
                      className="absolute cursor-pointer top-0 right-4 text-white rounded-full bg-purple-dark border border-purple-dark p-2"
                    />
                  </div>
                )}
                {showDropdown && (
                  <FilterDropdown className="left-36 w-[14rem]">
                    <ul className="text-14 text-neutral-dark" ref={node}>
                      <li
                        onClick={triggerFileInput}
                        className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                      >
                        Change avatar
                      </li>

                      {showDeleteWarn ? (
                        <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                          <div className="flex gap-3">Are you sure?</div>
                          <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                            <CustomButton
                              isLoading={isDeleteLoading}
                              title="Yes"
                              handleClick={handleDeleteAvatar}
                              className="border text-center !h-0 py-2 !bg-white !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer"
                            />
                            <span
                              onClick={() => {
                                setShowDeleteWarn(false);
                              }}
                              className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                            >
                              No
                            </span>
                          </div>
                        </li>
                      ) : (
                        <li
                          onClick={() => setShowDeleteWarn(true)}
                          className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] cursor-pointer  text-alert-text-error"
                        >
                          Remove avatar
                        </li>
                      )}
                    </ul>
                  </FilterDropdown>
                )}
              </div>
              <p className="mt-3 text-xs text-gray-500">
                *Image size should be at least 320px big, and less than 500kb.
                Allowed files .png and .jpg.
              </p>
            </div>
          </div>
          <div>
            <Formik
              initialValues={initialState}
              onSubmit={handleSubmit}
              enableReinitialize
            >
              {({ setFieldValue, values }) => (
                <Form>
                  <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                    <div>
                      <FormikCustomInput
                        label="First name"
                        id="firstName"
                        name="firstName"
                        placeholder="enter your first name"
                        type="text"
                        value={values.firstName}
                        inputClassName="bg-transparent border"
                      />
                    </div>
                    <div>
                      <FormikCustomInput
                        label="Last name"
                        id="lastName"
                        name="lastName"
                        placeholder="enter your last name"
                        type="text"
                        inputClassName="bg-transparent border"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-8 mt-8">
                    <div>
                      <FormikCustomPhoneInput
                        label="Phone number"
                        name="phoneNumber"
                        id="phoneNumber"
                        value={values.phoneNumber}
                        onChange={(value: string) => {
                          setFieldValue("phoneNumber", value);
                        }}
                      />
                    </div>
                    <div>
                      <FormikCustomSelect
                        label="Gender"
                        options={[
                          { text: "Male", value: "Male" },
                          { text: "Female", value: "Female" },
                        ]}
                        name="gender"
                        value={values.gender}
                        placeholder="select gender"
                        onChange={(item: { value: string; text: string }) => {
                          setFieldValue("gender", item.value);
                        }}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <CustomButton
                      className="mt-10 text-16 !w-[180px]"
                      type="submit"
                      title="Update"
                      handleClick={() => {}}
                      isLoading={isLoading}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                    />
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
        <div className="mt-10 col-span-2 ">
          <div className="bg-white rounded-xl px-4 py-6 shadow-sm">
            <h1 className="text-16 font-poppins-medium">Change password</h1>
            <p className="mt-4">
              You can change your password to enhance the security of your
              account. Make sure it's something unique and easy for you to
              remember.
            </p>
            <div className="mt-8">
              <CustomButton
                className="!w-full px-4 py-2 rounded shadow-md"
                handleClick={() => {
                  setShowPasswordModal(true);
                }}
                title="Change password"
                isTransparent
                variant={ButtonProperties.VARIANT.primary.name}
              />
            </div>
          </div>
          <div className="bg-white rounded-xl px-4 py-6 shadow-sm mt-10">
            <h1 className="text-16 font-poppins-medium">Change email</h1>
            <p className="mt-4">
              You can change your email address to ensure you continue receiving
              important account notifications.{" "}
            </p>
            <div className="mt-8">
              <CustomButton
                className="!w-full text-white px-4 py-2 rounded shadow-md"
                handleClick={() => {
                  setShowEmailModal(true);
                }}
                title="Change email"
                variant={ButtonProperties.VARIANT.primary.name}
              />
            </div>
          </div>
        </div>
      </div>
      <CustomModal
        visibility={showPasswordModal}
        toggleVisibility={setShowPasswordModal}
      >
        <ChangePassword />
      </CustomModal>

      <CustomModal visibility={showEmailModal} cancelModal={setShowEmailModal}>
        <ChangeEmail currentEmail={getUserValue.email} />
      </CustomModal>
    </div>
  );
};

export default PersonalSettings;
