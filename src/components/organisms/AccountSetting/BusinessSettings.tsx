import React, { ChangeEvent, useEffect, useRef, useState } from "react";
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties, getNameInitials } from "../../shared/helpers";
import { Formik, Form } from "formik";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import FormikCustomPhoneInput from "../../atoms/CustomInput/FormikCustomPhoneInput";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";
import { useRecoilState, useRecoilValue } from "recoil";
import { defaultBusinessAtom } from "../../../recoil/atom/organizationAtom";
import { Country, State } from "country-state-city";
import CustomModal from "../../atoms/CustomModal/CustomModal";
import DeleteAccount from "./DeleteAccount";
import {
  deleteBusinessAvatar,
  getDefaultBusiness,
  updateBusinessAvatar,
  updateBusinessInfo,
} from "../../../api/organization";
import { FaPencilAlt } from "react-icons/fa";
import useClickOutside from "../../shared/hooks";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import { FaSpinner } from "react-icons/fa6";
import Loader from "../../atoms/Loader";
import { loggedUserAtom } from "../../../recoil/atom/authAtom";


const BusinessSettings = () => {
  let countries = Country.getAllCountries().map((country) => ({
    text: country.name,
    value: country.isoCode,
  }));

  const [showDeleteAccountModal, setShowDeleteAccountModal] =
    useState<boolean>(false);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [selectedCountry, setSelectedCountry] = useState<string>("");
  const [selectedCurrency, setSelectedCurrency] = useState<string>("");
  const [, setSelectedState] = useState<string>("");
  const [isDeleteLoading, setIsDeleteLoading] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [, setDefaultBusiness] = useRecoilState(defaultBusinessAtom);
  const getDefaultBusinessValue = useRecoilValue(defaultBusinessAtom);
  const getLoggedUser = useRecoilValue(loggedUserAtom);


  const defaultCurrency = getLoggedUser.businesses[0]?.default_currency;

  const allCurrency = Country.getAllCountries().map(({ currency, name }, index) => {
    return { currency, name };
  })



  const initialCountry = countries.filter(
    (item) => item.text === getDefaultBusinessValue?.country
  )[0]?.value;

  let states = State.getStatesOfCountry(selectedCountry || initialCountry).map((state) => ({
    text: state.name,
    value: state.isoCode,
  }));

  const node = useClickOutside(() => {
    setShowDropdown(false);
  });

  const fetchDefaultBusiness = () => {
    setIsFetching(true);
    getDefaultBusiness().then((res) => {
      if (res.success) {
        setIsFetching(false);
        setDefaultBusiness(res.data);
      }
    });
  };

  useEffect(() => {
    fetchDefaultBusiness();
  }, []);

  if (isFetching) {
    return <div>
      <Loader />
    </div>
  };

  let initialState = {
    businessName: getDefaultBusinessValue?.name || "",
    businessAddress: getDefaultBusinessValue?.address || "",
    country: getDefaultBusinessValue?.country || "",
    state: getDefaultBusinessValue?.state || "",
    businessPhoneNumber: getDefaultBusinessValue?.business_phone_contact || "",
    businessEmail: getDefaultBusinessValue?.business?.business_email || "",
    website: getDefaultBusinessValue?.business?.business_website || "",
    description: getDefaultBusinessValue?.description || "",
    currency: defaultCurrency?.default_currency || "",
  };

  const handleImageUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (
      file &&
      (file.type === "image/jpeg" || file.type === "image/png") &&
      file.size <= 900 * 1024
    ) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImage(reader.result as string);
      };
      reader.readAsDataURL(file);
      const formData = new FormData();
      formData.append("image", file);
      updateBusinessAvatar(formData).then((res) => {
        if (res.success) {
          setShowDropdown(false);
          setShowDeleteWarn(false);
          clustarkToast(NotificationTypes.SUCCESS, res.message);
        }
      });
    } else {
      clustarkToast(
        NotificationTypes.ERROR,
        "Image must be a .jpg or .png and less than 900kb."
      );
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const handleDeleteAvatar = () => {
    setIsDeleteLoading(true);
    deleteBusinessAvatar()
      .then((res) => {
        if (res.success) {
          setShowDropdown(false);
          setShowDeleteWarn(false);
          setIsDeleteLoading(false);
          setProfileImage(null);
          fetchDefaultBusiness();
          clustarkToast(NotificationTypes.SUCCESS, res.message);
        }
      })
      .catch(() => {
        setIsDeleteLoading(false);
      });
  };

  const handleSubmit = (values) => {

    setIsLoading(true);
    updateBusinessInfo({
      name: values.businessName,
      description: values.description,
      address: values.businessAddress,
      country: values.country,
      state: values.state,
      businessEmail: values.businessEmail,
      business_phone_contact: values.businessPhoneNumber,
      businessWebsite: values.website,
      default_currency: values.currency,
    }).then((res) => {
      if (res.success) {
        setIsLoading(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchDefaultBusiness();
      }
    });
  };




  return (
    <div>
      <div className="grid grid-cols-5 gap-10">
        <div className="col-span-3 mt-10 p-4 bg-white rounded-xl shadow-sm">
          <div>
            <div className="mb-5 ">
              <input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg, image/png"
                onChange={handleImageUpload}
                className="hidden"
              />
              <div className="relative w-fit">
                {profileImage || getDefaultBusinessValue?.avatar ? (
                  <div className="relative">
                    <img
                      src={profileImage || getDefaultBusinessValue?.avatar}
                      alt="Profile"
                      className="w-36 h-36 object-cover rounded-full mx-auto border"
                    />
                    <FaPencilAlt
                      size={30}
                      onClick={() => setShowDropdown(!showDropdown)}
                      className="absolute cursor-pointer top-0 right-4 text-white rounded-full bg-purple-dark border border-purple-dark p-2"
                    />
                  </div>
                ) : (
                  <div className="relative">
                    <p className="w-36 h-36 bg-purple-light-active text-purple-normal font-poppins-medium font-bold rounded-full flex justify-center items-center text-6xl">
                      {getDefaultBusinessValue?.name
                        ? getNameInitials(
                          getDefaultBusinessValue?.name,
                        )
                        : ""}
                    </p>
                    <FaPencilAlt
                      size={30}
                      onClick={() => setShowDropdown(!showDropdown)}
                      className="absolute cursor-pointer top-0 right-4 text-white rounded-full bg-purple-dark border border-purple-dark p-2"
                    />
                  </div>
                )}
                {showDropdown && (
                  <FilterDropdown className="left-36 w-[14rem]">
                    <ul className="text-14 text-neutral-dark" ref={node}>
                      <li
                        onClick={triggerFileInput}
                        className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                      >
                        Change avatar
                      </li>

                      {showDeleteWarn ? (
                        <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                          <div className="flex gap-3">Are you sure?</div>
                          <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                            <CustomButton
                              isLoading={isDeleteLoading}
                              title="Yes"
                              handleClick={handleDeleteAvatar}
                              className="border text-center !h-0 py-2 !bg-white !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer"
                            />
                            <span
                              onClick={() => {
                                setShowDeleteWarn(false);
                              }}
                              className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                            >
                              No
                            </span>
                          </div>
                        </li>
                      ) : (
                        <li
                          onClick={() => setShowDeleteWarn(true)}
                          className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] cursor-pointer text-alert-text-error"
                        >
                          Remove avatar
                        </li>
                      )}
                    </ul>
                  </FilterDropdown>
                )}
              </div>
              <p className="mt-3 text-xs text-gray-500">
                *Image size should be at least 320px big, and less than 500kb.
                Allowed files .png and .jpg.
              </p>
            </div>
          </div>
          <div>
            <Formik
              initialValues={initialState}
              onSubmit={handleSubmit}
              enableReinitialize
            >
              {({ setFieldValue, values }) => (
                <Form>
                  <div className="grid tablet:grid-cols-1 gap-8  mt-8">
                    <div>
                      <FormikCustomInput
                        label="Business name"
                        id="businessName"
                        name="businessName"
                        type="text"
                        inputClassName="bg-transparent border"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-8 mt-8">
                    <div>
                      <FormikCustomPhoneInput
                        label="Business phone number"
                        name="businessPhoneNumber"
                        id="businessPhoneNumber"
                        value={values.businessPhoneNumber}
                        onChange={(value: string) => {
                          setFieldValue("businessPhoneNumber", value);
                        }}
                        onBlur={() => setFieldValue("businessPhoneNumber", values.businessPhoneNumber)}
                      />
                    </div>
                    <div>
                      <FormikCustomInput
                        label="Business email"
                        id="businessEmail"
                        name="businessEmail"
                        type="text"
                        inputClassName="bg-transparent border"
                      />
                    </div>
                  </div>
                  <div className="mt-8 grid grid-cols-2 gap-8">
                    <div>
                      <FormikCustomSelect
                        label="Update default currency"
                        name="currency"
                        value={selectedCurrency || defaultCurrency}
                        onChange={(item: { value: string; text: string }) => {
                          setFieldValue("currency", item.text);
                        }}
                        options={allCurrency.map(({ currency }) => ({ text: currency, value: currency }))}
                      />
                    </div>
                    <div>
                      <FormikCustomInput
                        label="Business website"
                        name="website"
                        id="website"
                        value={values.website}
                        onChange={(e: ChangeEvent<HTMLInputElement>) => {
                          setFieldValue("website", e.target.value);
                        }}
                      />
                    </div>

                  </div>
                  <div className="mt-8 grid grid-cols-2 gap-8">
                    <div>
                      <FormikCustomSelect
                        label="Country"
                        options={countries}
                        name="country"
                        value={selectedCountry || values.country}
                        onChange={(item: { value: string; text: string }) => {
                          setSelectedCountry(item.value);
                          setFieldValue("country", item.text);
                        }}
                      />
                    </div>
                    <div>
                      <FormikCustomSelect
                        label="State"
                        options={states}
                        name="state"
                        onChange={(item: { value: string; text: string }) => {
                          setSelectedState(item.value);
                          setFieldValue("state", item.text);
                        }}
                        value={values.state}
                      />
                    </div>
                  </div>
                  <div className="mt-8">
                    <FormikCustomInput
                      label="Business address"
                      name="businessAddress"
                      id="address"
                    />
                  </div>

                  <div className="mt-8">
                    <div className="text-16 mb-4 text-neutral-dark">
                      <label htmlFor="description">Description</label>
                    </div>
                    <textarea
                      name="description"
                      id="description"
                      className="rounded-[4px] w-full hide-scrollbar p-3 outline-1 outline-purple-normal-hover border-[0.6px] border-[#B2BBC699]"
                      rows={3}
                      onChange={(e) => setFieldValue("description", e.target.value)}
                      value={values.description}
                    ></textarea>
                  </div>

                  <div className="flex justify-end">
                    <CustomButton
                      className="mt-10 text-16 shadow-md !w-[180px]"
                      type="submit"
                      title="Update"
                      handleClick={() => { }}
                      size={ButtonProperties.SIZES.small}
                      variant={ButtonProperties.VARIANT.primary.name}
                      isLoading={isLoading}
                    />
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
        <div className="mt-10 col-span-2 ">
          <div className="bg-white rounded-xl px-4 py-6 shadow-sm">
            <h1 className="text-16 font-poppins-medium text-purple-normal">
              Delete business account
            </h1>
            <p className="mt-5 text-alert-text-error font-semibold">
              Danger zone
            </p>
            <p className="mt-4">
              {" "}
              Your business and all associated data will be permanently delete
              and cannot be recovered and this includes staff, departments,
              branches, departments and everything else{" "}
            </p>
            <div className="mt-8">
              <CustomButton
                className="!w-full px-4 py-2 rounded shadow-md"
                handleClick={() => {
                  setShowDeleteAccountModal(true);
                }}
                title="Delete account"
                variant={ButtonProperties.VARIANT.primary.name}
                // isTransparent
                isDisabled
              />
            </div>
          </div>
        </div>
      </div>
      <CustomModal
        visibility={showDeleteAccountModal}
        toggleVisibility={setShowDeleteAccountModal}
      >
        <DeleteAccount />
      </CustomModal>
    </div>
  );
};

export default BusinessSettings;
