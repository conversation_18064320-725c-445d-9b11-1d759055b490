import React, { useEffect, useState } from 'react'
import PersonalSettings from './PersonalSettings';
import BusinessSettings from './BusinessSettings';
import { useNavigate, useSearchParams } from 'react-router-dom';

const AccountSetting = () => {
  const navigate = useNavigate();
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("activeTab");
  const [activeTab, setActiveTab] = useState<number>(0)

  const settingsTab = [
    {
      title: "Personal settings",
      component: <PersonalSettings />,
      name: "personalSettings"
    },
    {
      title: "Business settings",
      component: <BusinessSettings />,
      name: "businessSettings"
    },
  ];



  useEffect(() => {
    setActiveTab(queryParam || 0)

  }, [queryParam, activeTab]);

  return (
    <div>

      <div className=" bg-[#F4F4F6] flex justify-between">
        <div className="flex gap-10 py-1 px-1 justify-center items-center  bg-[#F4F4F6] rounded-md">
          {settingsTab.map((item, index) => (
            <div
              key={index}
              onClick={() => { setActiveTab(index); navigate(`/account-setting?activeTab=${index}&tab=${item.name}`) }}
              className={`cursor-pointer px-5 py-2 rounded  ${activeTab == index ? "bg-white" : "bg-none"}`}>
              <p className={`text-neutral-normal font-poppins-medium px-5 text-center text-16 ${activeTab == index ? "text-purple-normal" : "text-gray-950"} `}> {item.title}</p>
            </div>
          ))}
        </div>
      </div>

      <div>
        {settingsTab[activeTab].component}
      </div>
    </div>
  )
}

export default AccountSetting