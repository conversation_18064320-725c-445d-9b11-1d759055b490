import React, { useState } from "react";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../shared/helpers";
import { Form, Formik } from "formik";
import { useNavigate } from "react-router-dom";

interface Values {
  password: string;
}

const initialState = {
  password: "",
};

const DeleteAccount = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleSubmit = (values: any) => {
    setIsLoading(false);
    navigate("/");
  };
  return (
    <div className="mb-10">
      <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-normal-hover bg-purple-light pl-10 py-6">
        Delete Account
      </h1>
      <div className="flex flex-col px-7 mt-4">
        <Formik<Values>
          initialValues={initialState}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values }) => (
            <Form>
              <h1 className="text-18">
                Are you sure you want to delete your account?
              </h1>
              <p className="mt-4 mb-2">Enter password to delete account</p>
              <div>
                <FormikCustomInput
                  label="Password"
                  id="password"
                  name="password"
                  placeholder="*************"
                  type="password"
                  value={values.password}
                  required
                />
              </div>
              <CustomButton
                className="mt-[56px]"
                type="submit"
                title="Delete Account"
                isDisabled={true}
                handleClick={() => {}}
                isLoading={isLoading}
                variant={ButtonProperties.VARIANT.primary.name}
              />
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default DeleteAccount;
