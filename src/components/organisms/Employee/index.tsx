import React, { useEffect, useRef, useState } from "react";
import StatisticsCard from "../../atoms/Cards/StatisticsCard";
import { Data, Eye, Profile2User, UserEdit } from "iconsax-react";
import { TbBrandStackshare } from "react-icons/tb";
import OrganizationEmptyState from "../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../atoms/CustomTable/CustomTable";
import { getNameInitials, truncateText } from "../../shared/helpers";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../shared/hooks";
import { FiPlus } from "react-icons/fi";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import CustomModal from "../../atoms/CustomModal/CustomModal";
import AddEmployee from "./AddEmployee";
import { getStaff, getStaffData } from "../../../api/staff";
import { getStaffAtom, getStaffDataAtom } from "../../../recoil/atom/staff";
import { Formik, Form } from "formik";
import FormikCustomSelect from "../../atoms/CustomInput/FormikCustomSelect";
// import FormikCustomCheckboxSelect from "../../atoms/CustomInput/FormikCustomCheckboxSelect";
import { getDepartmentsAtom, getGradeLevelsAtom } from "../../../recoil/atom/organizationAtom";
import useUpdateRecoilAtom from "../../shared/hooks/updateRecoilAtom";
import StatusTag from "../../atoms/StatusTag";
import Loader from "../../atoms/Loader";
import { MdLibraryAdd } from "react-icons/md";
import AddBulkModal from "./AddEmployee/AddBulkModal";

const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

const Employee = () => {
  const navigate = useNavigate();
  // const [groupCheck, setGroupCheck] = useState<boolean>(false);
  // const [checkedItems, setCheckedItems] = useState<any[]>([]);
  const [departmentId, setDepartmentId] = useState<string>("");
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showManagerModal, setShowManagerModal] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [departmentName, setDepartmentName] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [, setStaffAtom] = useRecoilState(getStaffAtom);
  const [rowId, setRowId] = useState(0);


  const getStaffValue = useRecoilValue(getStaffAtom);
  const getGradeLevel = useRecoilValue(getGradeLevelsAtom);
  const getDepartments = useRecoilValue(getDepartmentsAtom);
  const [staffData, setStaffDataAtom] = useRecoilState(getStaffDataAtom);
  const { fetchDepartments } = useUpdateRecoilAtom();




  const gradeLevels = getGradeLevel?.data?.map((item) => ({
    text: item.code,
    value: item.id
  }));


  const departments = getDepartments?.data?.map((item) => ({
    text: item.name,
    value: item.id
  }));

  const fetchStaffData = () => {
    setIsFetching(true);
    getStaffData().then((res) => {
      setIsFetching(false);
      if (res.success) {
        setStaffDataAtom(res.data);
      }
    });
  };

  useEffect(() => {
    fetchStaffData();
    fetchDepartments();
  }, []);

  useEffect(() => {
    fetchDepartments();
  }, []);

  const debounceSearch = useRef(debounce((q) => fetchStaffs(q), 2000)).current;

  // const handleCheckboxChange = (event, row) => {
  //   const isChecked = event.target.checked;
  //   setCheckedItems((prevState: any) => {
  //     if (isChecked) {
  //       return [...prevState, row];
  //     } else {
  //       setGroupCheck(false);
  //       return prevState.filter((item) => item.id !== row.id);
  //     }
  //   });
  // };

  // const handleSelectAllChange = (event) => {
  //   const isChecked = event.target.checked;
  //   setGroupCheck(isChecked);

  //   if (isChecked) {
  //     setCheckedItems(getAllBranchesAtom.data);
  //   } else {
  //     setCheckedItems([]);
  //   }
  // };

  const fetchStaffs = (q?) => {
    setIsFetching(true);
    getStaff({ search: q, page: pageNumber }, { department_id: departmentId }).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setStaffAtom(res.data);
      }
    });
  };

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  useEffect(() => {
    fetchStaffs(searchQuery);
  }, [pageNumber, departmentId]);


  if (isFetching && !searchQuery) {
    return <div>
      <Loader />
    </div>
  };


  const employeeStats = [
    {
      title: "Total employees",
      value: staffData?.totalStaff,
      icon: <TbBrandStackshare className="rotate-90" size={24} />,
      iconBackgroundColor: '#3730A399',
      valueText: "Employee",
      cardBackgroundColor: "#EEF2FFCC",
    },
    {
      title: "Female employees",
      value: staffData?.females,
      icon: <Data className="rotate-90" size={24} />,
      iconBackgroundColor: '#B3AA0199',
      valueText: "Female",
      cardBackgroundColor: "#FDFFE8CC",
    },
    {
      title: "Male employees",
      value: staffData?.males,
      icon: <Profile2User size={24} />,
      iconBackgroundColor: '#09778399',
      valueText: "Male",
      cardBackgroundColor: "#EBFDFFCC",
    },
  ];

  const columns = [
    {
      Header: (
        <div className="flex">
          {/* <CustomCheckBox
          checked={groupCheck}
            onChange={(e: any) => handleSelectAllChange(e)}
            customClass="!mr-3"
          />{" "} */}
          <p className="mt-1">Employee name</p>
        </div>
      ),
      accessor: "staffPersonalInformations.first_name",
      Cell: (row: any) => (
        <div className="flex  items-center">
          {/* <CustomCheckBox checked={checkedItems.some((item:any) => item.id === row.cell.row.original.id)}  onChange={(e:any) => { handleCheckboxChange(e, row.cell.row.original)}} customClass="!mr-3" /> */}
          <div className="font-poppins-medium flex justify-center items-center gap-3">

            {
              !row.cell.row.original.staffPersonalInformations.avatar ?
                <p className="w-10 h-10 text-20 rounded-full bg-purple-dark text-white font-poppins-medium font-bold flex justify-center items-center">
                  {getNameInitials(
                    row.cell.row.original.staffPersonalInformations.first_name,
                    row.cell.row.original.staffPersonalInformations.last_name
                  )}
                </p> :
                <div className="w-[40px] h-[40px] rounded-full overflow-hidden">
                  <img
                    src={row.cell.row.original.staffPersonalInformations.avatar}
                    alt="avatar"
                    className="w-full h-full object-cover"
                  />
                </div>
            }




            <div>
              <div className="flex">
                {truncateText(row.cell.value, 20)}{" "}
                {row.cell.row.original.staffPersonalInformations.last_name}
                {/* {row.cell.row.original.is_hq && (
                  <p className=" w-3.5 h-3.5 -translate-y-2 text-[5px] rounded-full bg-purple-light-active text-purple-dark font-poppins-medium font-bold flex justify-center items-center">
                    HQ
                  </p>
                )} */}
              </div>
              <p className="font-poppins lowercase text-[#6B788E]">
                {truncateText(row.cell.row.original.staffBasicInformations.email, 30)}
              </p>
            </div>
          </div>
        </div>
      ),
    },

    {
      Header: "Employee ID",
      accessor: "staff_identification_tag",
      Cell: (row: any) => (
        <p className="rounded-2xl bg-[#F5F6F7] w-fit px-2 py-1 font-poppins-medium">
          {row.cell.value || "--"}{" "}
        </p>
      ),
    },
    // {
    //   Header: "Department",
    //   accessor: "department.name",
    //   Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
    // },
    {
      Header: "Role",
      accessor: "job_title",
      Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
    },

    {
      Header: "Employment type",
      accessor: "employment_type",
      Cell: (row: any) => <p>{row.cell.value.toLowerCase() || "--"} </p>,
    },
    {
      Header: "Status",
      accessor: "employment_status",
      Cell: (row: any) => (<StatusTag status={row.cell.value} />),
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() =>
                    navigate(`/employee/staff-information`, {
                      state: { id: row.cell.row.original.id },
                    })
                  }
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>
                <li
                  onClick={() => {
                    navigate(`/employee/staff-information`, {
                      state: { isEdit: "true", id: row.cell.row.original.id },
                    });
                  }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <UserEdit size={18} />
                  Edit
                </li>
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];


  return (
    <>
      <div>
        {/* <h1 className="font-poppins font-semibold text-24">Employee</h1> */}
        <div className="grid grid-cols-3 gap-5 ">
          {employeeStats?.map((item, index) => (
            <div key={index}>
              <StatisticsCard
                backgroundColor={item?.cardBackgroundColor}
                key={index}
                title={item.title}
                value={item.value}
                icon={item.icon}
                iconBackgroundColor={item?.iconBackgroundColor}
                valueText={item.valueText}
              />
            </div>
          ))}
        </div>
        {/* <div className="pt-10 gap-2 flex justify-end mt-5">
          <CustomButton
            className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
            isTransparent={true}
            handleClick={() => {
              navigate("/employee/add-bulk-staff");
            }}
            leftIcon={<MdLibraryAdd className="ml-3" size={20} />}
            title="Bulk upload"
          />
          <CustomButton
            className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
            isTransparent={true}
            handleClick={() => {
              navigate("/employee/add-staff");
            }}
            leftIcon={<FiPlus className="ml-3" size={20} />}
            title="Add new employee"
          />
        </div> */}
        <div className=" my-5 py-[23px]">
          {getStaffValue?.data?.length > 0 || searchQuery || departmentId ? (
            <CustomTable
              data={getStaffValue?.data || []}
              meta={getStaffValue?.meta}
              columns={columns}
              // filterListOptions={["State", "Country"]}
              customFilter={
                <div className="pb-3">
                  <div>
                    <h1 className="border-b border-neutral-light pl-4 py-3">Modify this view</h1>
                    <Formik initialValues={{ department: departmentName || "" }} onSubmit={() => { }}>
                      {({ setFieldValue, values }) => (
                        <Form>
                          <div className="flex px-4 gap-5 py-[17.5px] border-b">
                            <p className="whitespace-nowrap">Filter by:</p>
                            <div className="w-full">

                              <div>
                                <div>
                                  <FormikCustomSelect
                                    placeholder="Select Department"
                                    parentContainer="!w-full !h-10"
                                    optionsParentClassName="!capitalize"
                                    options={departments}
                                    name="department"
                                    onChange={(item: { value: string; text: string }) => {
                                      setSearchQuery("");
                                      setDepartmentName(item.text);
                                      setDepartmentId(item.value);
                                      setFieldValue("department", item.text);
                                    }}
                                    value={values.department}
                                  />
                                </div>
                              </div>
                              {/* <div className="">
                              <FormikCustomSelect
                              placeholder="Select Grade Level"
                                parentContainer="!h-7"
                                options={gradeLevels}
                                name="gradeLevel"
                                onChange={(item: { value: string; text: string }) => {
                                  setSearchQuery("");
                                  setFieldValue("gradeLevel", item.text);
                                }}
                                value={values.gradeLevel}
                              />
                            </div> */}

                            </div>
                          </div>
                        </Form>
                      )}
                    </Formik>
                    <div className="flex justify-end items-end place-content-end">
                      <p className=" pt-3 pr-3 cursor-pointer" onClick={() => { setDepartmentId(""); setDepartmentName("") }}>Clear Filter</p>
                    </div>
                  </div>
                </div>
              }
              dropdowmCardClass="!w-[458px]"
              // checkedItems={checkedItems}
              // handleFilter={(e) => console.log(e)}
              handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
              handleSearch={(search) => { setSearchQuery(search); debounceSearch(search) }}
              header={
                <div className="flex justify-between items-center h-[40px] px-2">
                  <h1>
                    Employees
                  </h1>
                  <div className="flex gap-2 ">
                    <div className="bg-black rounded">
                      <CustomButton
                        className="!w-[150px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md  !h-10"
                        isTransparent={true}
                        handleClick={() => {
                          navigate("/employee/add-bulk-staff");
                        }}
                        leftIcon={<MdLibraryAdd className="ml-3" size={20} />}
                        title="Bulk upload"
                      />
                    </div>
                    <div className="bg-black rounded">
                      <CustomButton
                        className="!w-[150px] !text-white !bg-purple-dark !border-none !font-normal !font-poppins-medium shadow-md !h-10"
                        isTransparent={true}
                        handleClick={() => {
                          navigate("/employee/add-staff");
                        }}
                        leftIcon={<FiPlus className="ml-3" size={20} />}
                        title='Add employee'
                      />
                    </div>
                  </div>
                </div>
              }
            />
          ) : (
            <div className="flex justify-center items-center py-[120px]">
              <OrganizationEmptyState
                buttonTitle="Add new Employee"
                handleClick={() => navigate("/employee/add-staff")}
              />
            </div>
          )}
        </div>

        <CustomModal
          visibility={showManagerModal}
          toggleVisibility={setShowManagerModal}
        >
          <AddEmployee />
        </CustomModal>

      </div>
    </>
  );
};

export default Employee;
