import React, { useEffect, useRef, useState } from "react";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import { Eye } from "iconsax-react";
import useClickOutside from "../../shared/hooks";
import CustomTable from "../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../atoms/Cards/OrganizationEmptyState";
import { useRecoilState, useRecoilValue } from "recoil";
import { getEmployeeDisputeAtom } from "../../../recoil/atom/staff";
import {
  changeEmployeeDisputeStatus,
  getEmployeeDisputes,
} from "../../../api/staff";
import { ButtonProperties, debounce, truncateText } from "../../shared/helpers";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import Loader from "../../atoms/Loader";
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";
import StatusTag from "../../atoms/StatusTag";
import CustomModal from "../../atoms/CustomModal/CustomModal";
import { MdClose, MdUpdate } from "react-icons/md";

const EmployeeDispute = () => {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [disputeStatusModal, setDisputeStatusModal] = useState<boolean>(false);
  const [disputeModal, setDisputeModal] = useState<boolean>(false);
  const [singleDispute, setSingleDispute] = useState<any>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [disputeStatus, setDisputeStatus] = useState<string>("");
  const [disputeId, setDisputeId] = useState<string>("");
  const [isFetching] = useState<boolean>(false);
  const [pageNumber, setPageNumber] = useState<number>(0);
  const [rowId, setRowId] = useState(0);
  const [, setEmployeeDisputes] = useRecoilState(getEmployeeDisputeAtom);
  const employeeDisputeValue = useRecoilValue(getEmployeeDisputeAtom);

  const debounceSearch = useRef(
    debounce((q) => fetchEmployeeDisputes(q), 2000)
  ).current;

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  const fetchEmployeeDisputes = (q?) => {
    getEmployeeDisputes({ search: q, page: pageNumber }).then((res) => {
      if (res.success) {
        setEmployeeDisputes(res.data);
      }
    });
  };

  const columns = [
    {
      Header: "Employee name",
      accessor: "staff.staffPersonalInformations.first_name",
      Cell: (row: any) => (
        <p>
          {row.cell.value}{" "}
          {row.cell.row.original?.reportedEmployee?.staffPersonalInformations?.last_name}
        </p>
      ),
    },
    {
      Header: "Reported employee",
      accessor: "reportedEmployee.staffPersonalInformations.first_name",
      Cell: (row: any) => (
        <p>
          {row.cell.value}{" "}
          {row.cell.row.original.reportedEmployee?.staffPersonalInformations?.last_name}
        </p>
      ),
    },

    {
      Header: "Employee id",
      accessor: "reportedEmployee.staff_identification_tag",
      Cell: (row: any) => (
        <p className="rounded-2xl bg-[#F5F6F7] w-fit px-2 py-1 font-poppins-medium">
          {row.cell.value || "--"}{" "}
        </p>
      ),
    },
    {
      Header: "Reason",
      accessor: "reason",
    },
    {
      Header: "Description",
      accessor: "description",
      Cell: (row: any) => <p>{truncateText(row.cell.value, 40)}</p>,
    },

    {
      Header: "Status",
      accessor: "status",
      Cell: (row: any) => <StatusTag status={row.cell.value} />,
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() => { setDisputeModal(true); setSingleDispute(row.cell.row.original) }}
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>
                {(row.cell.row.original.status !== "closed" && row.cell.row.original.status !== "resolved") && (
                  <li
                    onClick={() => { setDisputeStatusModal(true); setDisputeId(row.cell.row.original.id) }}
                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                  >
                    <MdUpdate size={18} />
                    Update dispute
                  </li>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  const handleDisputeStatus = (e) => {
    e.preventDefault()
    setIsLoading(true);
    changeEmployeeDisputeStatus({ dispute_id: disputeId, status: disputeStatus }).then((res) => {
      if (res.success) {
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        fetchEmployeeDisputes();
        setShowDropdown(false);
        setDisputeStatusModal(false);
        setIsLoading(false);
      }
    }).catch(() => setIsLoading(false));
  };

  if (isFetching) {
    return <Loader />;
  }

  useEffect(() => {
    fetchEmployeeDisputes(searchQuery);
  }, [pageNumber, searchQuery]);

  return (
    <div>
      <div className=" my-10 py-[23px]">
        {employeeDisputeValue?.data?.length > 0 || searchQuery ? (
          <CustomTable
            data={employeeDisputeValue?.data || []}
            meta={employeeDisputeValue?.meta}
            columns={columns}
            handlePageChange={(pageNumber: number) => setPageNumber(pageNumber)}
            handleSearch={(search) => {
              setSearchQuery(search);
              debounceSearch(search);
            }}
            header={
              <div className="flex items-center h-[40px] px-2">
                <h1 className="text-center">
                  Disputes
                </h1>
              </div>
            }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState text="No record found. When there is a record, they will appear here." />
          </div>
        )}
      </div>

      <CustomModal
        visibility={disputeStatusModal}
        toggleVisibility={setDisputeStatusModal}
      >
        <div>
          <div className="flex justify-between bg-purple-light py-[33px] px-10 ">
            <h1 className=" font-poppins-medium text-18 text-purple-dark">
              Update dispute status
            </h1>
            <MdClose
              size={18}
              color="#0C0123"
              className="cursor-pointer"
              onClick={() => setDisputeStatusModal(false)}
            />
          </div>
          <div className="mt-8 px-10 pb-10">
            <h1 className="text-18 text-neutral-dark font-poppins-medium mb-4">
              Select Status
            </h1>
            <form onSubmit={handleDisputeStatus}>
              <div className="flex gap-6 text-18">
                <label htmlFor="resolved" className="flex gap-2">
                  <input
                    type="radio"
                    name="resolved"
                    className="!outline-none h-fit mt-2.5 accent-purple-normal"
                    required
                    onChange={() => { setDisputeStatus("resolved"); }}
                  />
                  Resolved
                </label>
                <label htmlFor="under_investigation" className="flex gap-1">
                  <input
                    type="radio"
                    name="resolved"
                    className="!outline-none h-fit mt-2.5 accent-purple-normal"
                    required
                    onChange={() => setDisputeStatus("under_investigation")}
                  />
                  Under investigation
                </label>
                <label htmlFor="in_review" className="flex gap-1">
                  <input
                    type="radio"
                    name="resolved"
                    className="!outline-none h-fit mt-2.5 accent-purple-normal"
                    required
                    onChange={() => setDisputeStatus("in_review")}
                  />
                  In reveiw
                </label>
              </div>
              <div className="mt-10">
                <CustomButton isLoading={isLoading} isDisabled={!disputeStatus} type="submit" variant={ButtonProperties.VARIANT.primary.name} title="Update" handleClick={() => { }} />
              </div>

            </form>
          </div>
        </div>
      </CustomModal>

      <CustomModal
        visibility={disputeModal}
        toggleVisibility={setDisputeModal}
      >

        <div>
          <div className='flex justify-between bg-purple-light py-[33px] px-10 '>
            <h1 className=" font-poppins-medium text-18 text-purple-dark">
              Dispute information
            </h1>
            <MdClose
              size={18}
              color='#0C0123'
              className="cursor-pointer"
              onClick={() => setDisputeModal(false)}
            />

          </div>
          <div className="flex gap-8 mt-9 px-10">
            <h1 className="text-18 text-neutral-normal font-poppins-medium">
              Status
            </h1>
            <p className='capitalize mt-1' ><StatusTag status={singleDispute?.status || "--"} /></p>
          </div>
          <div className="mt-8 text-neutral-normal px-10 pb-10">
            <div className="mt-12">
              <div className="mt-8 grid grid-cols-2">
                <div>
                  <h1 className="text-neutral-dark">Dispute Filed by</h1>
                  <p className="mt-2.5 capitalize">{singleDispute?.staff?.staffPersonalInformations?.first_name || ""} {singleDispute?.staff?.staffPersonalInformations?.last_name || "Nil"}</p>
                </div>
                <div>
                  <h1 className="text-neutral-dark">Reported Employee</h1>
                  <p className="mt-2.5 capitalize">{singleDispute?.reportedEmployee?.staffPersonalInformations?.first_name || ""} {singleDispute?.reportedEmployee?.staffPersonalInformations?.last_name || "Nil"}</p>
                </div>
              </div>
              <div className="mt-8 grid grid-cols-2">
                <div>
                  <h1 className="text-neutral-dark">Job Role</h1>
                  <p className="mt-2.5 capitalize">{singleDispute?.reportedEmployee?.job_title || "Nil"} </p>
                </div>
                <div>
                  <h1 className="text-neutral-dark">Staff ID</h1>
                  <p className="mt-2.5 rounded-2xl bg-[#F5F6F7] w-fit px-2 py-1 font-poppins-medium">{singleDispute?.reportedEmployee?.staff_identification_tag}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 mt-8">
                <div>
                  <h1 className="text-neutral-dark">Reviewer Name</h1>
                  <p className="mt-2.5 capitalize">{singleDispute?.reviewer?.first_name || ""} {singleDispute?.reviewer?.last_name || "Nil"}</p>
                </div>
                <div>
                  <h1 className="text-neutral-dark">Reason</h1>
                  <p className="mt-2.5">{singleDispute?.reason || "Nil"}</p>
                </div>

              </div>
              <div className="mt-8">
                <h1 className="text-neutral-dark">Description</h1>
                <p className="mt-2.5">{singleDispute?.description || "Nil"}</p>
              </div>
              <div className="mt-8">

                <div>
                  <h1 className="text-neutral-dark">Attachment Upload</h1>
                  {singleDispute?.attachment ? (
                    <div className="mt-2.5 ">
                      <div className="flex gap-4">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <rect width="32" height="32" rx="16" fill="#B2BBC6" fill-opacity="0.18" />
                          <path d="M23.4405 14.325C17.9321 13.5683 13.1821 17.6583 13.5013 23.0833M11.418 12.6667C11.418 13.1087 11.5936 13.5326 11.9061 13.8452C12.2187 14.1577 12.6426 14.3333 13.0846 14.3333C13.5267 14.3333 13.9506 14.1577 14.2631 13.8452C14.5757 13.5326 14.7513 13.1087 14.7513 12.6667C14.7513 12.2246 14.5757 11.8007 14.2631 11.4882C13.9506 11.1756 13.5267 11 13.0846 11C12.6426 11 12.2187 11.1756 11.9061 11.4882C11.5936 11.8007 11.418 12.2246 11.418 12.6667Z" stroke="#1D242D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                          <path d="M8.5 16.8868C10.8167 16.5659 12.8958 17.6851 14.02 19.4701" stroke="#1D242D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                          <path d="M8.5 13.8333C8.5 11.9667 8.5 11.0333 8.86333 10.32C9.18291 9.69282 9.69282 9.18291 10.32 8.86333C11.0333 8.5 11.9667 8.5 13.8333 8.5H18.1667C20.0333 8.5 20.9667 8.5 21.68 8.86333C22.3072 9.18291 22.8171 9.69282 23.1367 10.32C23.5 11.0333 23.5 11.9667 23.5 13.8333V18.1667C23.5 20.0333 23.5 20.9667 23.1367 21.68C22.8171 22.3072 22.3072 22.8171 21.68 23.1367C20.9667 23.5 20.0333 23.5 18.1667 23.5H13.8333C11.9667 23.5 11.0333 23.5 10.32 23.1367C9.69282 22.8171 9.18291 22.3072 8.86333 21.68C8.5 20.9667 8.5 20.0333 8.5 18.1667V13.8333Z" stroke="#1D242D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>

                        <a className="mt-2 underline" href={singleDispute?.attachment} target="_blank">click to view file attached</a>

                      </div>
                    </div>

                  ) : (
                    <p>Nil</p>
                  )}
                </div>
              </div>
            </div>
          </div>

        </div>
      </CustomModal>
    </div>
  );
};

export default EmployeeDispute;
