import { Accordion, AccordionDetails, AccordionSummary, Typography } from "@mui/material";
import { ArrowDropDownIcon } from "@mui/x-date-pickers";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { Form, Formik } from "formik";
import { getAllStaffAtom, getStaffByIdAtom, getStaffPersonalInfoAtom } from "../../../../recoil/atom/staff";
import { useRecoilValue } from "recoil";
import { useState } from "react";
import { getGradeLevelsAtom } from "../../../../recoil/atom/organizationAtom";
// import { loggedUserAtom } from "../../../../recoil/atom/authAtom";
// import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";
import moment from "moment";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { BiSave } from "react-icons/bi";
import { useLocation } from "react-router-dom";
import { ButtonProperties } from "../../../shared/helpers";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import { AiOutlineEdit } from "react-icons/ai";
import { updateStaff, getStaffById } from "../../../../api/staff";
import StatusTag from "../../../atoms/StatusTag";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";


const EmploymentInformation = ({ staffid, data }) => {

    const location = useLocation();
    const { isEdit, id } = location?.state || "";

    const [isLoading, setIsLoading] = useState(false);
    const [Edit, setEdit] = useState(false);
    const [showModal, setShowModal] = useState(false);


    const [gradeLevelId, setGradeLevelId] = useState<string>("");
    const [staffId, setStaffId] = useState<string>("");
    const getStaffByIdValue = useRecoilValue(getStaffByIdAtom);
    const getGradeLevelValue = useRecoilValue(getGradeLevelsAtom);


    const getStaffById = async () => {
        await getStaffById();
    }



    const getAllStaff = useRecoilValue(getAllStaffAtom);
    console.log(getStaffByIdValue)

    let initialValues = {
        lineManager: (getStaffByIdValue?.directReport?.user?.first_name || "") + " " + (getStaffByIdValue?.directReport?.user?.last_name || "") || "",
        employeeId: getStaffByIdValue?.staff_identification_tag || "",
        department: getStaffByIdValue?.department?.name || "",
        branch: getStaffByIdValue?.department?.branch?.name || "",
        status: getStaffByIdValue?.employment_status || "",
        employmentType: getStaffByIdValue?.employment_type || "",

        role: getStaffByIdValue?.job_title || "",
        employmentDate: moment(getStaffByIdValue?.start_date).format("YYYY-MM-DD") || "",
        endDate: moment(getStaffByIdValue?.end_date).format("YYYY-MM-DD") || "",
        gradeLevel: getStaffByIdValue?.gradeLevel?.code || "",
    };

    const staffs = getAllStaff?.data?.map((staff) => ({
        text: staff?.user?.first_name + " " + staff?.user?.last_name,
        value: staff.id,
    }));

    const gradeLevels = getGradeLevelValue?.data?.map((item) => ({
        text: item.code,
        value: item.id
    }));


    const allStatus = [
        { text: "ACTIVE", value: "ACTIVE", color: "bg-[#ECFDF3]" },
        { text: "SUSPEND ACCOUNT", value: "SUSPEND_ACCOUNT", color: "bg-[#B2BBC62B]" },
        { text: "DISABLE ACCOUNT", value: "DISABLE_ACCOUNT", color: "bg-[#FFF2EA]" },
        { text: "PENDING", value: "PENDING", color: "bg-[#FFF2EA]" },
    ]


    const editDetails = (values) => {
        console.log(values)

        const payload = {
            job_title: values.role,
            grade_level_id: gradeLevelId || getStaffByIdValue?.grade_level_id,
            employment_type: values.employmentType,
            end_date: moment(values.endDate).format("YYYY-MM-DD HH:mm:ss"),
            start_date: moment(values.employmentDate).format("YYYY-MM-DD HH:mm:ss"),
        }

        if ((values.employmentType === 'PROBATION' && values.endDate === '') || (values.employmentType === 'CONTRACT' && values.endDate === '')) {
            clustarkToast(
                NotificationTypes.ERROR,
                "enter end date."
            );
        } else {

            updateStaff(staffid, payload).then((res) => {
                if (res?.success) {
                    setIsLoading(false);
                    setShowModal(true)
                    data()
                    getStaffById()
                } else {
                    setIsLoading(false);
                }
            })

        }

    }



    return (
        <div>

            <div className=" bg-white py-7 px-8 rounded-xl mt-11">
                <Formik
                    initialValues={initialValues}
                    onSubmit={(values) => { editDetails(values) }}
                    // validationSchema={createNextOfKinSchema}
                    enableReinitialize
                >
                    {({ values, setFieldValue }) => (
                        <Form>
                            <div className="flex justify-end mr-8">
                                {isEdit || Edit ? (
                                    <div>
                                        <CustomButton
                                            leftIcon={<BiSave size={20} />}
                                            className="!w-[180px] !h-10"
                                            title="Save"
                                            type="submit"
                                            handleClick={() => { }}
                                            isLoading={isLoading}
                                            variant={
                                                ButtonProperties.VARIANT.primary.name
                                            }
                                        />
                                    </div>
                                ) : (
                                    <div
                                        className="!w-[180px] !h-10 gap-2 cursor-pointer bg-purple-normal rounded flex justify-center items-center text-white"
                                        onClick={() => {
                                            setEdit(true);
                                        }}
                                    >
                                        <AiOutlineEdit size={18} /> Edit
                                    </div>
                                )}
                            </div>
                            <div className=" grid grid-cols-2 gap-[55px] border-neutral-[#B2BBC6]  bg-white py-7 px-8 rounded-xl">
                                <div>
                                    <div className="grid grid-cols-2 mt-4">
                                        <label
                                            htmlFor="employeeId"
                                            className="flex items-center text-neutral-normal"
                                        >
                                            Employee ID
                                        </label>
                                        <FormikCustomInput
                                            id="employeeId"
                                            name="employeeId"
                                            type="text"
                                            disabled
                                        />
                                    </div>
                                    <div className="grid grid-cols-2 mt-4">
                                        <label
                                            htmlFor="status"
                                            className="flex items-center text-neutral-normal"
                                        >
                                            Status
                                        </label>
                                        <div>
                                            <FormikCustomSelect
                                                options={allStatus}
                                                name="status"
                                                onChange={(item: { value: string; text: string }) => {
                                                    setFieldValue("status", item.value);
                                                }}
                                                value={values.status}
                                                className={`!uppercase ${getStaffByIdValue.employment_status === 'PENDING' ? '!text-[#FFAA33]' : getStaffByIdValue.employment_status === 'ACTIVE' ? '!text-[#027A48]' : '!text-[#F15046]'}`}
                                                disabled
                                            />

                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 mt-4">
                                        <label
                                            htmlFor="department"
                                            className="flex items-center text-neutral-normal"
                                        >
                                            Department
                                        </label>
                                        <div>
                                            <FormikCustomInput
                                                id="department"
                                                name="department"
                                                type="text"
                                                disabled
                                            />

                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 mt-4">
                                        <label
                                            htmlFor="lineManager"
                                            className="flex items-center text-neutral-normal"
                                        >
                                            Line manager
                                        </label>
                                        <div>
                                            <FormikCustomSelect
                                                options={staffs}
                                                name="lineManager"
                                                value={values.lineManager}
                                                onChange={(item: {
                                                    value: string;
                                                    text: string;
                                                }) => {
                                                    setStaffId(item.value);
                                                    setFieldValue("lineManager", item.text);
                                                }}
                                                disabled
                                            />
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 mt-4">
                                        <label
                                            htmlFor="branch"
                                            className="flex items-center text-neutral-normal"
                                        >
                                            Branch
                                        </label>
                                        <div>
                                            <FormikCustomSelect
                                                options={[]}
                                                name="branch"
                                                value={values.branch}
                                                onChange={(item: {
                                                    value: string;
                                                    text: string;
                                                }) => {
                                                    // setSelectedCountry(item.value);
                                                    setFieldValue("branch", item.text);
                                                }}
                                                disabled
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div className="">
                                    <div className="grid grid-cols-2 ">
                                        <label
                                            htmlFor="role"
                                            className="flex items-center text-neutral-normal"
                                        >
                                            Role
                                        </label>
                                        <FormikCustomInput
                                            id="role"
                                            name="role"
                                            type="text"
                                            disabled={!Edit}
                                        />
                                    </div>
                                    <div className="grid grid-cols-2 mt-4">
                                        <label
                                            htmlFor="employmentDate"
                                            className="flex items-center text-neutral-normal"
                                        >
                                            Employment date
                                        </label>
                                        <div className="w-full">
                                            <FormikCustomDate
                                                value={moment(values.employmentDate)}
                                                inputClassName="border bg-transparent"
                                                name="employmentDate"
                                                onChange={(date) => {
                                                    setFieldValue("employmentDate", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                                                }}
                                                disabled={!Edit}
                                            />

                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 mt-4">
                                        <label
                                            htmlFor="endDate"
                                            className="flex items-center text-neutral-normal"
                                        >
                                            End date
                                        </label>
                                        <div className="w-full">
                                            <FormikCustomDate
                                                value={moment(values.endDate)}
                                                inputClassName="border bg-transparent"
                                                name="endDate"
                                                onChange={(date) => {
                                                    setFieldValue("endDate", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                                                }}
                                                disabled={!Edit}
                                            />

                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 mt-4">
                                        <label
                                            htmlFor="gradeLevel"
                                            className="flex items-center text-neutral-normal"
                                        >
                                            Grade level
                                        </label>
                                        <div>
                                            <FormikCustomSelect
                                                options={gradeLevels}
                                                name="gradeLevel"
                                                optionsParentClassName="!uppercase"
                                                className="!uppercase"
                                                value={values.gradeLevel}
                                                onChange={(item: {
                                                    value: string;
                                                    text: string;
                                                }) => {
                                                    setGradeLevelId(item.value);
                                                    setFieldValue("gradeLevel", item.text);
                                                }}
                                                disabled={!Edit}
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 mt-4">
                                        <label
                                            htmlFor="employmentType"
                                            className="flex items-center text-neutral-normal"
                                        >
                                            Employment type
                                        </label>
                                        <div>
                                            <FormikCustomSelect
                                                options={[
                                                    { text: "FULL_TIME", value: "FULL_TIME" },
                                                    { text: "PROBATION", value: "PROBATION" },
                                                    { text: "CONTRACT", value: "CONTRACT" },
                                                    { text: "PART_TIME", value: "PART_TIME" },
                                                ]}
                                                name="employmentType"
                                                value={values.employmentType}
                                                onChange={(item: {
                                                    value: string;
                                                    text: string;
                                                }) => {
                                                    setFieldValue("employmentType", item.value);
                                                }}

                                            />
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </Form>
                    )}
                </Formik>
                <SuccessModal
                    visibility={showModal}
                    toggleVisibility={setShowModal}
                    text="Your changes has been saved"
                    route="/employee/all-staffs"
                />
            </div>

        </div >
    )

}

export default EmploymentInformation;