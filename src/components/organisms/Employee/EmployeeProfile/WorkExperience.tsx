import { Form, Formik } from "formik";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import * as yup from "yup";
import moment from "moment";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import { useRef, useState } from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { clustarkToast } from "../../../atoms/Toast";
import { createWorkExperience, updateWorkExperience, getStaffById } from "../../../../api/staff";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import { FaEllipsisV } from "react-icons/fa";
import { Eye, UserEdit } from "iconsax-react";
import { useRecoilState, useRecoilValue } from "recoil";
import { getStaffPastExperienceAtom, getWorkExperienceAtom } from "../../../../recoil/atom/staff";
import useClickOutside from "../../../shared/hooks";
import { FaPlus } from "react-icons/fa6";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";



const WorkExperience = ({ staffid, data }) => {

    const [isLoading, setIsLoading] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [isOpenEdit, setIsOpenEdit] = useState(false);
    const [rowId, setRowId] = useState(0);
    const [showDropdown, setShowDropdown] = useState<boolean>(false);

    const [fileName, setFileName] = useState('');
    const [, setDragging] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);

    const getStaffPastExperience = useRecoilValue(getStaffPastExperienceAtom);
    const [, setWorkExperienceAtom] = useRecoilState(getWorkExperienceAtom);
    const getWorkExperience = useRecoilValue(getWorkExperienceAtom);


    const fileInputRef = useRef<HTMLInputElement>(null);

    const getStaffById = async () => {
        await getStaffById(staffId);
    }

    const node = useClickOutside(() => {
        setShowDropdown(false);
        setRowId(0);
    });

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (
            file 
        ) {
            setSelectedFile(file);
            setFileName(file.name)
        } 
        // else {
        //     clustarkToast(
        //         NotificationTypes.ERROR,
        //         "Image must be a .jpg or .png and less than 900kb."
        //     );
        // }
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setDragging(false);

        const files = e.dataTransfer.files;
        if (files && files.length > 0) {
            setSelectedFile(files[0]);
            setFileName(files[0].name)
        }
    };

    const triggerFileInput = () => {
        fileInputRef.current?.click();

    };

    const initialValues = {
        company_name: "",
        job_title: "",
        start_date: "",
        end_date: "",
    }

    const createWorkExperienceSchema = yup.object().shape({
        company_name: yup.string().required(errorMessages.required),
        job_title: yup.string().required(errorMessages.required),
    });

    const editInitialValues = {
        company_name: getWorkExperience?.company_name,
        job_title: getWorkExperience?.job_title,
        start_date: getWorkExperience?.start_date || "",
        end_date: getWorkExperience?.end_date || "",
    }


    const handleSubmit = (values) => {

        const formData = new FormData();
        formData.append("staff_id", staffid);
        formData.append("company_name", values.company_name);
        formData.append("job_title", values.job_title);
        formData.append("start_date", moment(values.date).format("YYYY-MM-DD"));
        formData.append("end_date", moment(values.date).format("YYYY-MM-DD"));
        if (selectedFile) {
            formData.append("attachment", selectedFile);
        }

        setIsLoading(true)

        createWorkExperience(formData).then((res) => {
            if (res?.success) {
                setIsLoading(false);
                setIsOpen(false)
                data()
                getStaffById()
            } else {
                setIsLoading(false);
                setIsOpen(false)
            }
        })

    }

    const viewEmployee = (data: any) => {
        setIsOpenEdit(true);
        setWorkExperienceAtom(data)
    }

    const editDetails = (values) => {

        const formData = new FormData();
        formData.append("staff_id", staffid);
        formData.append("staff_experience_id", getWorkExperience?.id);
        formData.append("company_name", values.company_name);
        formData.append("job_title", values.job_title);
        formData.append("start_date", moment(values.date).format("YYYY-MM-DD"));
        formData.append("end_date", moment(values.date).format("YYYY-MM-DD"));
        if (selectedFile) {
            formData.append("attachment", selectedFile);
        }

        setIsLoading(true)

        updateWorkExperience(formData).then((res) => {
            if (res?.success) {
                setIsLoading(false);
                setIsOpen(false)
                data()
                getStaffById()
            } else {
                setIsLoading(false);
                setIsOpen(false)
            }
        })

    }


    const columns = [

        {
            Header: "Company name",
            accessor: "company_name",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Job title",
            accessor: "job_title",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Start date",
            accessor: "start_date",
            Cell: ({ cell }: any) => (
                <p>{cell.value ? moment(cell.value).format("YYYY-MM-DD") : "--"}</p>
            ),
        },
        {
            Header: "End date",
            accessor: "end_date",
            Cell: ({ cell }: any) => (
                <p>{cell.value ? moment(cell.value).format("YYYY-MM-DD") : "--"}</p>
            ),
        },
        {
            Header: "Attatchment",
            accessor: "attachment",
            Cell: ({ cell }: any) =>
                cell.value ? (
                    <a href={cell.value} target="_blank" rel="noopener noreferrer" className="text-purple-normal-active ">
                        view attachment
                    </a>
                ) : (
                    <p>--</p>
                )
        },

        {
            Header: "",
            accessor: "action",
            Cell: (row: any) => (
                <div className="relative">
                    <FaEllipsisV
                        onClick={() => {
                            setShowDropdown(!showDropdown);
                            setRowId(row.cell.row.id);
                        }}
                        className="text-[#98A2B3] cursor-pointer"
                    />
                    {showDropdown && row.cell.row.id === rowId && (
                        <FilterDropdown>
                            <ul className="text-14 text-neutral-dark" ref={node}>

                                <li
                                    onClick={() => { viewEmployee(row.cell.row.original) }}
                                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                                >
                                    <UserEdit size={18} />
                                    Edit
                                </li>
                            </ul>
                        </FilterDropdown>
                    )}
                </div>
            ),
        },
    ];


    return (
        <div className="">

            <div className="border-neutral-[#B2BBC6] mt-11 py-7 px-8 rounded-xl">
                <div className="flex justify-end gap-5">

                    {/* <CustomButton
                        leftIcon={<FaPlus size={15} />}
                        className="!w-[102px] !h-10"
                        title="Add"
                        type="submit"
                        handleClick={() => { setIsOpen(true) }}
                        isLoading={isLoading}
                        variant={
                            ButtonProperties.VARIANT.primary.name
                        }
                    /> */}

                </div>
                <div className=" border-neutral-[#B2BBC6]  rounded-xl">

                    {
                        getStaffPastExperience ?
                            <CustomTable
                                data={getStaffPastExperience || []}
                                columns={columns}
                                hideSearch
                                header={
                                    <div className="flex justify-between items-center h-[45px]">
                                        <h1 className="pl-2">
                                            Work experience
                                        </h1>
                                        <div className=" px-2">
                                            <CustomButton
                                                leftIcon={<FaPlus size={15} />}
                                                className="!w-[180px] !h-10"
                                                title="Add"
                                                type="submit"
                                                handleClick={() => { setIsOpen(true) }}
                                                isLoading={isLoading}
                                                variant={
                                                    ButtonProperties.VARIANT.primary.name
                                                }
                                            />
                                        </div>
                                    </div>
                                }
                            />
                            :
                            <div className="flex justify-center items-center">
                                <OrganizationEmptyState />
                            </div>
                    }

                </div>
            </div>

            <CustomModal
                visibility={isOpen}
                toggleVisibility={setIsOpen}
            >
                <div>
                    <div className="bg-purple-light px-10 py-[28px] flex justify-start rounded-tl-[5px] rounded-tr-[5px] ">
                        <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                            Add work experience
                        </h1>
                    </div>
                    <Formik
                        initialValues={initialValues}
                        onSubmit={(values) => { handleSubmit(values) }}
                        validationSchema={createWorkExperienceSchema}
                        enableReinitialize
                    >
                        {({ values, setFieldValue }) => (
                            <Form>
                                <div className="border-neutral-[#B2BBC6] mt-11 bg-white py-7 px-8 rounded-xl">
                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                                        <div>
                                            <FormikCustomInput
                                                label="Company name "
                                                id="company_name"
                                                name="company_name"
                                                placeholder="enter company"
                                                type="text"
                                                value={values.company_name}
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomInput
                                                label="Job title "
                                                id="job_title"
                                                name="job_title"
                                                placeholder="enter job title"
                                                type="text"
                                                value={values.job_title}
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                    </div>

                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                                        <div>
                                            <FormikCustomDate
                                                label="Start date "
                                                value={moment(values.start_date)}
                                                inputClassName="border bg-transparent"
                                                name="start_date"
                                                onChange={(date) => {
                                                    setFieldValue("start_date", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomDate
                                                label="end date "
                                                value={moment(values.end_date)}
                                                inputClassName="border bg-transparent"
                                                name="end_date"
                                                onChange={(date) => {
                                                    setFieldValue("end_date", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                                                }}
                                            />
                                        </div>
                                    </div>

                                    <div className="border-2 border-dashed bg-[#B2BBC6] bg-opacity-10 h-[178px] rounded mt-4 flex justify-center items-center"
                                        onDragOver={(e) => {
                                            e.preventDefault();
                                            setDragging(true);
                                            e.stopPropagation();
                                        }}
                                        onDragLeave={() => setDragging(false)}
                                        onDrop={handleDrop}
                                    >
                                        <div
                                        >
                                            <div className="flex justify-center items-center">
                                                <svg
                                                    width="40"
                                                    height="40"
                                                    viewBox="0 0 40 40"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    <path
                                                        d="M31.6654 21.6675C31.2233 21.6675 30.7994 21.8431 30.4869 22.1556C30.1743 22.4682 29.9987 22.8921 29.9987 23.3341V23.9675L27.532 21.5008C26.6611 20.6367 25.4839 20.1518 24.257 20.1518C23.0302 20.1518 21.853 20.6367 20.982 21.5008L19.8154 22.6675L15.682 18.5341C14.7989 17.6935 13.6263 17.2246 12.407 17.2246C11.1878 17.2246 10.0152 17.6935 9.13203 18.5341L6.66536 21.0008V11.6675C6.66536 11.2254 6.84096 10.8015 7.15352 10.489C7.46608 10.1764 7.89 10.0008 8.33203 10.0008H19.9987C20.4407 10.0008 20.8646 9.82521 21.1772 9.51265C21.4898 9.20009 21.6654 8.77617 21.6654 8.33414C21.6654 7.89211 21.4898 7.46819 21.1772 7.15563C20.8646 6.84307 20.4407 6.66747 19.9987 6.66747H8.33203C7.00595 6.66747 5.73418 7.19425 4.7965 8.13194C3.85882 9.06962 3.33203 10.3414 3.33203 11.6675V31.6675C3.33203 32.9936 3.85882 34.2653 4.7965 35.203C5.73418 36.1407 7.00595 36.6675 8.33203 36.6675H28.332C29.6581 36.6675 30.9299 36.1407 31.8676 35.203C32.8052 34.2653 33.332 32.9936 33.332 31.6675V23.3341C33.332 22.8921 33.1564 22.4682 32.8439 22.1556C32.5313 21.8431 32.1074 21.6675 31.6654 21.6675ZM8.33203 33.3341C7.89 33.3341 7.46608 33.1585 7.15352 32.846C6.84096 32.5334 6.66536 32.1095 6.66536 31.6675V25.7175L11.4987 20.8841C11.7436 20.6508 12.0688 20.5206 12.407 20.5206C12.7453 20.5206 13.0705 20.6508 13.3154 20.8841L18.5987 26.1675L25.7654 33.3341H8.33203ZM29.9987 31.6675C29.9963 31.9865 29.8911 32.2963 29.6987 32.5508L22.182 25.0008L23.3487 23.8341C23.4682 23.7122 23.6108 23.6153 23.7682 23.5492C23.9256 23.483 24.0946 23.4489 24.2654 23.4489C24.4361 23.4489 24.6051 23.483 24.7625 23.5492C24.9199 23.6153 25.0625 23.7122 25.182 23.8341L29.9987 28.6841V31.6675ZM37.8487 7.1508L32.8487 2.1508C32.6902 1.99907 32.5033 1.88013 32.2987 1.8008C31.8929 1.63411 31.4378 1.63411 31.032 1.8008C30.8274 1.88013 30.6405 1.99907 30.482 2.1508L25.482 7.1508C25.1682 7.46464 24.9919 7.8903 24.9919 8.33414C24.9919 8.77797 25.1682 9.20363 25.482 9.51747C25.7959 9.83131 26.2215 10.0076 26.6654 10.0076C27.1092 10.0076 27.5349 9.83131 27.8487 9.51747L29.9987 7.3508V16.6675C29.9987 17.1095 30.1743 17.5334 30.4869 17.846C30.7994 18.1585 31.2233 18.3341 31.6654 18.3341C32.1074 18.3341 32.5313 18.1585 32.8439 17.846C33.1564 17.5334 33.332 17.1095 33.332 16.6675V7.3508L35.482 9.51747C35.637 9.67368 35.8213 9.79767 36.0244 9.88229C36.2275 9.9669 36.4453 10.0105 36.6654 10.0105C36.8854 10.0105 37.1032 9.9669 37.3063 9.88229C37.5094 9.79767 37.6938 9.67368 37.8487 9.51747C38.0049 9.36253 38.1289 9.1782 38.2135 8.9751C38.2981 8.772 38.3417 8.55416 38.3417 8.33414C38.3417 8.11412 38.2981 7.89627 38.2135 7.69318C38.1289 7.49008 38.0049 7.30574 37.8487 7.1508Z"
                                                        fill="#B2BBC6"
                                                    />
                                                </svg>
                                            </div>
                                            <p
                                                onClick={triggerFileInput}
                                                className="text-neutral-normal text-center mt-5 flex">
                                                <span className="font-poppins-medium text-purple-normal cursor-pointer">
                                                    Click here
                                                </span>
                                                &nbsp; or Drag and Drop file to upload
                                            </p>
                                            <p className="text-purple-normal font-semibold text-center mt-5">
                                                {fileName && fileName}
                                            </p>
                                            <input
                                                ref={fileInputRef}
                                                id="fileInput"
                                                type="file"
                                                onChange={handleFileChange}
                                                style={{ display: "none" }}
                                            />
                                        </div>
                                    </div>
                                    <div className="mt-10 flex justify-end">
                                        <CustomButton
                                            className="!w-[110px] !h-12"
                                            title="Save"
                                            type="submit"
                                            handleClick={() => { }}
                                            isLoading={isLoading}
                                            variant={
                                                ButtonProperties.VARIANT.primary.name
                                            }
                                        />
                                    </div>

                                </div>
                            </Form>
                        )}
                    </Formik>
                </div>
            </CustomModal>

            <CustomModal
                visibility={isOpenEdit}
                toggleVisibility={setIsOpenEdit}
            >
                <div>
                    <div className="bg-purple-light px-10 py-[28px] flex justify-start rounded-tl-[5px] rounded-tr-[5px] ">
                        <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                            Edit work experience
                        </h1>
                    </div>
                    <Formik
                        initialValues={editInitialValues}
                        onSubmit={(values) => { editDetails(values) }}
                        enableReinitialize
                    >
                        {({ values, setFieldValue }) => (
                            <Form>
                                <div className="border-neutral-[#B2BBC6] mt-11 bg-white py-7 px-8 rounded-xl">
                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                                        <div>
                                            <FormikCustomInput
                                                label="Company name "
                                                id="company_name"
                                                name="company_name"
                                                placeholder="enter company"
                                                type="text"
                                                value={values.company_name}
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomInput
                                                label="Job title "
                                                id="job_title"
                                                name="job_title"
                                                placeholder="enter job title"
                                                type="text"
                                                value={values.job_title}
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                    </div>

                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                                        <div>
                                            <FormikCustomDate
                                                label="Start date "
                                                value={moment(values.start_date)}
                                                inputClassName="border bg-transparent"
                                                name="start_date"
                                                onChange={(date) => {
                                                    setFieldValue("start_date", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomDate
                                                label="end date "
                                                value={moment(values.end_date)}
                                                inputClassName="border bg-transparent"
                                                name="end_date"
                                                onChange={(date) => {
                                                    setFieldValue("end_date", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                                                }}
                                            />
                                        </div>
                                    </div>

                                    <div className="border-2 border-dashed bg-[#B2BBC6] bg-opacity-10 h-[178px] rounded mt-4 flex justify-center items-center"
                                        onDragOver={(e) => {
                                            e.preventDefault();
                                            setDragging(true);
                                            e.stopPropagation();
                                        }}
                                        onDragLeave={() => setDragging(false)}
                                        onDrop={handleDrop}
                                    >
                                        <div
                                        >
                                            <div className="flex justify-center items-center">
                                                <svg
                                                    width="40"
                                                    height="40"
                                                    viewBox="0 0 40 40"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    <path
                                                        d="M31.6654 21.6675C31.2233 21.6675 30.7994 21.8431 30.4869 22.1556C30.1743 22.4682 29.9987 22.8921 29.9987 23.3341V23.9675L27.532 21.5008C26.6611 20.6367 25.4839 20.1518 24.257 20.1518C23.0302 20.1518 21.853 20.6367 20.982 21.5008L19.8154 22.6675L15.682 18.5341C14.7989 17.6935 13.6263 17.2246 12.407 17.2246C11.1878 17.2246 10.0152 17.6935 9.13203 18.5341L6.66536 21.0008V11.6675C6.66536 11.2254 6.84096 10.8015 7.15352 10.489C7.46608 10.1764 7.89 10.0008 8.33203 10.0008H19.9987C20.4407 10.0008 20.8646 9.82521 21.1772 9.51265C21.4898 9.20009 21.6654 8.77617 21.6654 8.33414C21.6654 7.89211 21.4898 7.46819 21.1772 7.15563C20.8646 6.84307 20.4407 6.66747 19.9987 6.66747H8.33203C7.00595 6.66747 5.73418 7.19425 4.7965 8.13194C3.85882 9.06962 3.33203 10.3414 3.33203 11.6675V31.6675C3.33203 32.9936 3.85882 34.2653 4.7965 35.203C5.73418 36.1407 7.00595 36.6675 8.33203 36.6675H28.332C29.6581 36.6675 30.9299 36.1407 31.8676 35.203C32.8052 34.2653 33.332 32.9936 33.332 31.6675V23.3341C33.332 22.8921 33.1564 22.4682 32.8439 22.1556C32.5313 21.8431 32.1074 21.6675 31.6654 21.6675ZM8.33203 33.3341C7.89 33.3341 7.46608 33.1585 7.15352 32.846C6.84096 32.5334 6.66536 32.1095 6.66536 31.6675V25.7175L11.4987 20.8841C11.7436 20.6508 12.0688 20.5206 12.407 20.5206C12.7453 20.5206 13.0705 20.6508 13.3154 20.8841L18.5987 26.1675L25.7654 33.3341H8.33203ZM29.9987 31.6675C29.9963 31.9865 29.8911 32.2963 29.6987 32.5508L22.182 25.0008L23.3487 23.8341C23.4682 23.7122 23.6108 23.6153 23.7682 23.5492C23.9256 23.483 24.0946 23.4489 24.2654 23.4489C24.4361 23.4489 24.6051 23.483 24.7625 23.5492C24.9199 23.6153 25.0625 23.7122 25.182 23.8341L29.9987 28.6841V31.6675ZM37.8487 7.1508L32.8487 2.1508C32.6902 1.99907 32.5033 1.88013 32.2987 1.8008C31.8929 1.63411 31.4378 1.63411 31.032 1.8008C30.8274 1.88013 30.6405 1.99907 30.482 2.1508L25.482 7.1508C25.1682 7.46464 24.9919 7.8903 24.9919 8.33414C24.9919 8.77797 25.1682 9.20363 25.482 9.51747C25.7959 9.83131 26.2215 10.0076 26.6654 10.0076C27.1092 10.0076 27.5349 9.83131 27.8487 9.51747L29.9987 7.3508V16.6675C29.9987 17.1095 30.1743 17.5334 30.4869 17.846C30.7994 18.1585 31.2233 18.3341 31.6654 18.3341C32.1074 18.3341 32.5313 18.1585 32.8439 17.846C33.1564 17.5334 33.332 17.1095 33.332 16.6675V7.3508L35.482 9.51747C35.637 9.67368 35.8213 9.79767 36.0244 9.88229C36.2275 9.9669 36.4453 10.0105 36.6654 10.0105C36.8854 10.0105 37.1032 9.9669 37.3063 9.88229C37.5094 9.79767 37.6938 9.67368 37.8487 9.51747C38.0049 9.36253 38.1289 9.1782 38.2135 8.9751C38.2981 8.772 38.3417 8.55416 38.3417 8.33414C38.3417 8.11412 38.2981 7.89627 38.2135 7.69318C38.1289 7.49008 38.0049 7.30574 37.8487 7.1508Z"
                                                        fill="#B2BBC6"
                                                    />
                                                </svg>
                                            </div>
                                            <p
                                                onClick={triggerFileInput}
                                                className="text-neutral-normal text-center mt-5 flex">
                                                <span className="font-poppins-medium text-purple-normal cursor-pointer">
                                                    Click here
                                                </span>
                                                &nbsp; or Drag and Drop file to upload
                                            </p>
                                            <p className="text-purple-normal font-semibold text-center mt-5">
                                                {fileName && fileName}
                                            </p>
                                            <input
                                                ref={fileInputRef}
                                                id="fileInput"
                                                type="file"
                                                onChange={handleFileChange}
                                                style={{ display: "none" }}
                                            />
                                        </div>
                                    </div>
                                    <div className="mt-10 flex justify-end">
                                        <CustomButton
                                            className="!w-[110px] !h-12"
                                            title="Save"
                                            type="submit"
                                            handleClick={() => { }}
                                            isLoading={isLoading}
                                            variant={
                                                ButtonProperties.VARIANT.primary.name
                                            }
                                        />
                                    </div>

                                </div>
                            </Form>
                        )}
                    </Formik>
                </div>
            </CustomModal>

        </div>
    );

}

export default WorkExperience;