// import { Formik, Form } from "formik";
import React, { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import { getAllStaffAtom, getBanksAtom, getDocumentAtom, getEmployeeBankAtom, getStaffAtom, getStaffBasicAtom, getStaffByIdAtom, getStaffEducationAtom, getStaffNextOfKinAtom, getStaffPastExperienceAtom, getStaffPersonalInfoAtom } from "../../../../recoil/atom/staff";
import { getBanks, getStaff, getStaffById, updateStaff } from "../../../../api/staff";
import { ButtonProperties, getNameInitials } from "../../../shared/helpers";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { PiShareFatLight } from "react-icons/pi";
import useUpdateR<PERSON>oil<PERSON>tom from "../../../shared/hooks/updateRecoilAtom";
import Transfer from "../Transfer";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import Loader from "../../../atoms/Loader";
import NextOfKin from "./NextOfKin";
import WorkExperience from "./WorkExperience";
import Education from "./Education";
import PersonalInformation from "./PersonalInformation";
import BasicInformation from "./BasicInformation";
import EmploymentInformation from "./EmploymentInformation";
import EmployeeDocument from "../EmployeeDocument";
import BankDetails from "./BankDetails";





const Profile = () => {
  const isMounted = useRef(false);
  const navigate = useNavigate();
  const location = useLocation();

  const { isEdit, id } = location?.state || "";
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("activeProfileTab");
  const [activeTab, setActiveTab] = useState<number>(0);

  const [showTransferModal, setShowTransferModal] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);

  const [, setStaffByIdAtom] = useRecoilState(getStaffByIdAtom);

  const [, setNextOfKinAtom] = useRecoilState(getStaffNextOfKinAtom);

  const [, setPastExperienceAtom] = useRecoilState(getStaffPastExperienceAtom);

  const [, setEducationAtom] = useRecoilState(getStaffEducationAtom);

  const [, setBasicAtom] = useRecoilState(getStaffBasicAtom);

  const [, setPersonalAtom] = useRecoilState(getStaffPersonalInfoAtom);

  const [, setDocumentAtom] = useRecoilState(getDocumentAtom);

  const [, setBankAtom] = useRecoilState(getBanksAtom);

  const [, setAllBankAtom] = useRecoilState(getEmployeeBankAtom);

  const getStaffByIdValue = useRecoilValue(getStaffByIdAtom);

  const getStaffPersonalInfo = useRecoilValue(getStaffPersonalInfoAtom);


  const { fetchGradeLevel, fetchAllStaff } = useUpdateRecoilAtom();

  const fetchStaffById = () => {

    setIsFetching(true);
    getStaffById(id || getStaffByIdValue.id).then((res) => {
      if (res.success) {

        setIsFetching(false);
        setStaffByIdAtom(res.data.staff)
        setNextOfKinAtom(res.data.nextOfKins);
        setPastExperienceAtom(res.data.pastExperience);
        setEducationAtom(res.data.educationInformation);
        setBasicAtom(res.data.basicInformation);
        setPersonalAtom(res.data.personalInformation)
        setDocumentAtom(res.data.documents)
        setAllBankAtom(res.data.bankAccounts)

      }
    });
  };

  const fetchBanks = () => {
    getBanks().then((res) => {
      if (res?.success) {
        setBankAtom(res.data)
      } else {
        return;
      }
    })
  }

  useEffect(() => {
    fetchBanks();
  }, [])



  useEffect(() => {
    if (isMounted.current) return;
    isMounted.current = true;
    fetchStaffById();
    fetchGradeLevel();
    fetchAllStaff();

    if (!id) {
      navigate("/employee/all-staffs")
    }

  }, [id]);

  const EmployeeTabs = [
    {
      title: "Personal information",
      component: <PersonalInformation
        staffid={id}
        data={fetchStaffById}
      />,
      name: "staff-information"
    },
    {
      title: "Basic information",
      component: <BasicInformation
        staffid={id}
        data={fetchStaffById}
      />,
      name: "basic-information"
    },
    {
      title: "Employment information",
      component: <EmploymentInformation
        staffid={id}
        data={fetchStaffById}
      />,
      name: "employment-information"
    },
    {
      title: "Next of kins",
      component: <NextOfKin
        staffid={id}
        data={fetchStaffById}
      />,
      name: "next-of-kin"
    },
    {
      title: "Education",
      component: <Education
        staffid={id}
        data={fetchStaffById}
      />,
      name: "education-information"
    },
    {
      title: "Work experiences",
      component: <WorkExperience
        staffid={id}
        data={fetchStaffById}
      />,
      name: "past-experience"
    },
    {
      title: "Documents",
      component: <EmployeeDocument
        staffid={id}
        data={fetchStaffById}
      />,
      name: "document"
    },
    {
      title: "Bank details",
      component: <BankDetails
        staffid={id}
        data={fetchStaffById}
      />,
      name: "bank-details"
    },

  ];


  useEffect(() => {
    if (!id) {
      navigate("/employee/all-staffs")
      console.log('no route 01');
    };

    if (activeTab === 1) {
      setActiveTab(1);
    };

    if (!id) {
      navigate("/employee/all-staffs")
    };

    if (!id) {
      navigate("/employee/all-staffs")
    };

    if (!id) {
      navigate("/employee/all-staffs")
    };

  }, [queryParam, activeTab]);


  if (isFetching) {
    return <div>
      <Loader />
    </div>
  };



  return (
    <>
      <div>
        <div className="">
          <div className="">
            <div>
              <div className="bg-white px-8 pt-5 rounded-xl">
                <div>
                  <div className="flex justify-between pb-6  ">
                    <div className="flex gap-6">
                      <div>
                        {getStaffPersonalInfo?.avatar ? (
                          <img
                            className="rounded-full w-[120px] h-[120px] object-cover"
                            src={getStaffPersonalInfo?.avatar}
                            alt="User Avatar"
                          />

                        ) : (

                          <div className="w-[120px] h-[120px] p-2 bg-purple-light-hover rounded-full">
                            <div className=" w-full h-full flex justify-center items-center bg-purple-light-active rounded-full">
                              <p className="text-[40px] text-purple-normal font-poppins-medium">
                                {getNameInitials(
                                  getStaffByIdValue?.staffPersonalInformations?.first_name || "",
                                  getStaffByIdValue?.staffPersonalInformations?.last_name || ""
                                )}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                      <div className="flex justify-center items-center">
                        <div>
                          <p className="text-neutral-dark font-medium font-poppins-medium text-20">
                            {getStaffPersonalInfo?.first_name +
                              " " +
                              getStaffPersonalInfo?.last_name || ""}
                          </p>
                          <p className=" text-neutral-normal">
                            {getStaffByIdValue?.staffBasicInformations?.email || ""}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-center gap-4 items-center">
                      <CustomButton
                        leftIcon={<PiShareFatLight size={20} />}
                        className="!w-[102px] !h-10"
                        title="Transfer"
                        isTransparent
                        type="button"
                        handleClick={() => { setShowTransferModal(true) }}
                        variant={ButtonProperties.VARIANT.primary.name}
                      />
                    </div>
                  </div>
                  <div className="mt-8 ">
                    <div className="">
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <div className="mt-2 bg-gray-300 flex justify-between rounded">

                  <div className="flex py-1 justify-between px-1 items-center w-full  ">

                    {EmployeeTabs.map((item, index) => (
                      <div
                        key={index}
                        onClick={() => { setActiveTab(index); navigate(`/employee/staff-information?activeProfileTab=${index}&tab=${item.name}`, { state: { id: id, } }) }}
                        className={`cursor-pointer flex items-center justify-center p-3 rounded  ${activeTab == index ? "bg-white" : "bg-none"}`}>
                        <p className={`text-neutral-normal font-poppins-medium text-center text-13 ${activeTab == index ? "text-purple-normal" : "text-gray-550"} `}> {item.title}</p>
                      </div>
                    ))}

                  </div>
                </div>
                <div>
                  {EmployeeTabs[activeTab].component}
                </div>
              </div>

            </div>

          </div>
        </div>

        <CustomModal visibility={showTransferModal} toggleVisibility={setShowTransferModal}>
          <Transfer getStaffByIdValue={getStaffByIdValue} />
        </CustomModal>

      </div>
    </>
  );
};

export default Profile;
