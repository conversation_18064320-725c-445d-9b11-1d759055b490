import { Accordion, AccordionDetails, AccordionSummary, Typography } from "@mui/material";
import { ArrowDropDownIcon } from "@mui/x-date-pickers";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import FormikCustomPhoneInput from "../../../atoms/CustomInput/FormikCustomPhoneInput";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../shared/helpers";
import { Form, Formik } from "formik";
import { useRecoilState, useRecoilValue } from "recoil";
import { getStaffBasicAtom } from "../../../../recoil/atom/staff";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { FaEdit, FaSave } from "react-icons/fa";
import moment from "moment";
import { updateBasicInformation, getStaffById } from "../../../../api/staff";
import { useState } from "react";
import { AiOutlineEdit } from "react-icons/ai";
import { BiSave } from "react-icons/bi";
import { useLocation } from "react-router-dom";
import Loader from "../../../atoms/Loader";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";



const BasicInformation = ({ staffid, data }) => {

    const location = useLocation();
    const { isEdit, id } = location?.state || "";

    const [isLoading, setIsLoading] = useState(false);
    const [Edit, setEdit] = useState(false);
    const [showModal, setShowModal] = useState(false);

    const getStaffById = async () => {
        await getStaffById(staffId);
    }


    const [, setBasicAtom] = useRecoilState(getStaffBasicAtom);
    const getBasicInfoValue = useRecoilValue(getStaffBasicAtom);


    const initialValues = {
        phone: getBasicInfoValue?.phone || "",
        email: getBasicInfoValue?.email || "",
        gender: getBasicInfoValue?.gender || "",
    }


    const editDetails = (values) => {

        const payload = {
            staff_id: staffid,
            email: values.email,
            phone: values.phone,
            gender: values.gender,
        }

        setIsLoading(true)
        

        updateBasicInformation(payload).then((res) => {
            if (res?.success) {
                setIsLoading(false);
                setShowModal(true)
                getStaffById()
            } else {
                setIsLoading(false);
            }
        })

    }


    if (isLoading) {
        return <div>
            <Loader />
        </div>
    };


    return (
        <div className="mt-8">


            <div className="">
                <Formik
                    initialValues={initialValues}
                    onSubmit={(values) => { editDetails(values) }}
                    // validationSchema={createNextOfKinSchema}
                    enableReinitialize
                >
                    {({ values, setFieldValue }) => (
                        <Form>
                            <div className="border-neutral-[#B2BBC6]  bg-white px-8 rounded-xl py-5">
                                <div className="flex justify-end">
                                    {isEdit || Edit ? (
                                        <div className="">
                                            <CustomButton
                                                leftIcon={<BiSave size={20} />}
                                                className="!w-[180px] !h-10"
                                                title="Save"
                                                type="submit"
                                                handleClick={() => { }}
                                                isLoading={isLoading}
                                                variant={
                                                    ButtonProperties.VARIANT.primary.name
                                                }
                                            />
                                        </div>
                                    ) : (
                                        <div
                                            className="!w-[180px] !h-10 gap-2 cursor-pointer bg-purple-normal rounded flex justify-center items-center text-white"
                                            onClick={() => {
                                                setEdit(true);
                                            }}
                                        >
                                            <AiOutlineEdit size={18} /> Edit
                                        </div>
                                    )}
                                </div>

                                <div className='grid grid-cols-1 gap-8 mt-8'>
                                    <div>
                                        <FormikCustomInput
                                            label="Email "
                                            id="email"
                                            name="email"
                                            value={values.email}
                                            placeholder="enter email address"
                                            type="email"
                                            inputClassName="bg-transparent border"
                                            disabled={!Edit}
                                        />
                                    </div>
                                </div>
                                <div className='grid grid-cols-2 gap-8 mt-8'>
                                    <div>
                                        <FormikCustomPhoneInput
                                            label="Phone number "
                                            name="phone"
                                            id="phone"
                                            value={values.phone}
                                            onChange={(value: string) => {
                                                setFieldValue("phone", value);
                                            }}
                                            disabled={!Edit}
                                        />
                                    </div>
                                    <div className="grid grid-cols-1 gap-3">
                                        <label
                                            htmlFor="gender"
                                            className="flex items-center text-neutral-normal text-[1rem]"
                                        >
                                            Gender
                                        </label>
                                        <div>
                                            <FormikCustomSelect
                                                options={[
                                                    { text: "Male", value: "Male" },
                                                    { text: "Female", value: "Female" },
                                                ]}
                                                name="gender"
                                                value={values.gender}
                                                onChange={(item: {
                                                    value: string;
                                                    text: string;
                                                }) => {
                                                    setFieldValue("gender", item.value);
                                                }}
                                                disabled={!Edit}
                                            />
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </Form>
                    )}
                </Formik>
            </div>
            <SuccessModal
                visibility={showModal}
                toggleVisibility={setShowModal}
                text="Your changes has been saved"
                route="/employee/all-staffs"
            />

        </div >
    )
}

export default BasicInformation;