import { Form, Formik } from "formik";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { createNextOfKin, updateNextOfKin, getStaffById } from "../../../../api/staff";
import { useState } from "react";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import FormikCustomPhoneInput from "../../../atoms/CustomInput/FormikCustomPhoneInput";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import moment from "moment";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages, } from "../../../shared/helpers";
import { BiSave } from "react-icons/bi";
import * as yup from "yup";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import * as React from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { useRecoilState, useRecoilValue } from 'recoil';
import { getSingleNextOfKinAtom, getStaffNextOfKinAtom } from "../../../../recoil/atom/staff";
import { FaPlus } from "react-icons/fa6";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import { Eye, UserEdit } from "iconsax-react";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import useClickOutside from "../../../shared/hooks";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";



const NextOfKin = ({ staffid, data }) => {

    const [isLoading, setIsLoading] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [isOpenEdit, setIsOpenEdit] = useState(false);
    const [rowId, setRowId] = useState(0);
    const [showDropdown, setShowDropdown] = useState<boolean>(false);

    const getStaffNextOfKin = useRecoilValue(getStaffNextOfKinAtom);
    const [, setSingleNextOfKinAtom] = useRecoilState(getSingleNextOfKinAtom);
    const getSingleNextOfKin = useRecoilValue(getSingleNextOfKinAtom);

    const getStaff = async () => {
        await getStaffById(staffid);
    }

    const relationshipTypes = [
        { text: "BROTHER", value: "BROTHER" },
        { text: "SISTER", value: "SISTER" },
        { text: "COUSIN", value: "COUSIN" },
        { text: "FATHER", value: "FATHER" },
        { text: "MOTHER", value: "MOTHER" },
        { text: "OTHERS", value: "OTHERS" },
    ];

    const node = useClickOutside(() => {
        setShowDropdown(false);
        setRowId(0);
    });

    const createNextOfKinSchema = yup.object().shape({
        firstName: yup.string().required(errorMessages.required),
        lastName: yup.string().required(errorMessages.required),
        phoneNumber: yup.string().required(errorMessages.required),
    });

    const initialValues = {
        firstName: "",
        lastName: "",
        phoneNumber: "",
        email: "",
        relationship: "",
        address: "",
        dob: "",
    }

    const editInitialValues = {
        firstName: getSingleNextOfKin?.first_name || '',
        lastName: getSingleNextOfKin?.last_name || '',
        phoneNumber: getSingleNextOfKin?.phone || '',
        email: getSingleNextOfKin?.email || '',
        relationship: getSingleNextOfKin?.relationship || '',
        address: getSingleNextOfKin?.address || '',
        dob: getSingleNextOfKin?.dob || '',
    }

    const handleSubmit = (values) => {

        const payload = {
            staff_id: staffid,
            first_name: values.firstName,
            last_name: values.lastName,
            phone_number: values.phoneNumber,
            email: values.email,
            relationship: values.relationship,
            address: values.address,
            dob: values.dob ? moment(values.dob).format("YYYY-MM-DD HH:mm:ss") : null,
        }

        setIsLoading(true);

        createNextOfKin(payload).then((res) => {
            if (res?.success) {
                setIsLoading(false);
                setIsOpen(false)
                data()
                getStaff();
            } else {
                setIsLoading(false);
                setIsOpen(false)
            }
        })

    }


    const editDetails = (values) => {

        const payload = {
            staff_id: staffid,
            next_of_kin_id: getSingleNextOfKin?.id,
            first_name: values.firstName,
            last_name: values.lastName,
            phone_number: values.phoneNumber,
            email: values.email,
            relationship: values.relationship,
            address: values.address,
            dob: values.dob ? moment(values.dob).format("YYYY-MM-DD HH:mm:ss") : null,
        }

        setIsLoading(true);

        updateNextOfKin(payload).then((res) => {
            if (res?.success) {
                setIsLoading(false);
                setIsOpenEdit(false)
                data()
                getStaff();
            } else {
                setIsLoading(false);
                setIsOpenEdit(false)
            }
        })

    }


    const viewEmployee = (data: any) => {
        setIsOpenEdit(true);
        setSingleNextOfKinAtom(data);

    }

    const columns = [

        {
            Header: "First Name",
            accessor: "first_name",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Last Name",
            accessor: "last_name",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Relationship",
            accessor: "relationship",
            Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
        },
        {
            Header: "Phone",
            accessor: "phone",
            Cell: (row: any) => <p>{row.cell.value || "--"}</p>,
        },
        {
            Header: "Email",
            accessor: "email",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Address",
            accessor: "address",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },

        {
            Header: "",
            accessor: "action",
            Cell: (row: any) => (
                <div className="relative">
                    <FaEllipsisV
                        onClick={() => {
                            setShowDropdown(!showDropdown);
                            setRowId(row.cell.row.id);
                        }}
                        className="text-[#98A2B3] cursor-pointer"
                    />
                    {showDropdown && row.cell.row.id === rowId && (
                        <FilterDropdown>
                            <ul className="text-14 text-neutral-dark" ref={node}>
                                <li
                                    onClick={() => { viewEmployee(row.cell.row.original) }}
                                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                                >
                                    <UserEdit size={18} />
                                    Edit
                                </li>
                            </ul>
                        </FilterDropdown>
                    )}
                </div>
            ),
        },
    ];



    return (
        <div className="mt-5">
            <div className="border-neutral-[#B2BBC6] mt-11 py-7 px-8 rounded-xl">
                <div className="flex justify-end gap-5">

                </div>
                <div className="border-neutral-[#B2BBC6]    rounded-xl">
                    {
                        getStaffNextOfKin ?
                            <CustomTable
                                data={getStaffNextOfKin || []}
                                // meta={getStaffValue?.meta}
                                columns={columns}
                                hideSearch
                                header={
                                    <div className="flex justify-between items-center  h-[45px]">
                                        <h1 className="pl-2">
                                            Next of kins
                                        </h1>
                                        <div className=" px-2">
                                            <CustomButton
                                                leftIcon={<FaPlus size={15} />}
                                                className="!w-[180px] !h-10"
                                                title="Add"
                                                type="submit"
                                                handleClick={() => { setIsOpen(true) }}
                                                isLoading={isLoading}
                                                variant={
                                                    ButtonProperties.VARIANT.primary.name
                                                }
                                            />
                                        </div>
                                    </div>
                                }
                            /> :
                            <div className="flex justify-center items-center">
                                <OrganizationEmptyState />
                            </div>
                    }
                </div>
            </div>

            <CustomModal
                visibility={isOpen}
                toggleVisibility={setIsOpen}
            >
                <div>
                    <div className="bg-purple-light px-10 py-[28px] flex justify-start rounded-tl-[5px] rounded-tr-[5px] ">
                        <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                            Add next of kin
                        </h1>
                    </div>
                    <Formik
                        initialValues={initialValues}
                        onSubmit={(values) => { handleSubmit(values) }}
                        validationSchema={createNextOfKinSchema}
                        enableReinitialize
                    >
                        {({ values, setFieldValue, handleChange }) => (
                            <Form>
                                <div className="border-neutral-[#B2BBC6]  bg-white py-7 px-8 rounded-xl">

                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                                        <div>
                                            <FormikCustomInput
                                                label="First name *"
                                                id="firstName"
                                                name="firstName"
                                                placeholder="enter first name"
                                                type="text"
                                                value={values.firstName}
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomInput
                                                label="Last name *"
                                                id="lastName"
                                                name="lastName"
                                                placeholder="enter last name"
                                                type="text"
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                    </div>
                                    <div className='grid grid-cols-2 gap-8 mt-8'>
                                        <div>
                                            <FormikCustomPhoneInput
                                                label="Phone number *"
                                                name="phoneNumber"
                                                id="phoneNumber"
                                                value={values.phoneNumber}
                                                onChange={(value: string) => {
                                                    setFieldValue("phoneNumber", value);
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomInput
                                                label="Email *"
                                                id="email"
                                                name="email"
                                                value={values.email}
                                                placeholder="enter email address"
                                                type="email"
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                    </div>
                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                                        <div>
                                            <FormikCustomSelect
                                                label="Relationship *"
                                                id="relationship"
                                                name="relationship"
                                                optionsParentClassName="!capitalize"
                                                options={relationshipTypes}
                                                value={values.relationship}
                                                onChange={(item) => {
                                                    setFieldValue("relationship", item.text);
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomDate
                                                label="D.O.B "
                                                value={moment(values.dob)}
                                                inputClassName="border bg-transparent"
                                                name="dob"
                                                onChange={(date) => {
                                                    setFieldValue("dob", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                                                }}
                                            />
                                        </div>
                                    </div>
                                    <div className="grid tablet:grid-cols-1 gap-8 px-2 mt-8">
                                        <div className="flex flex-col gap-4">
                                            <label htmlFor="">Address *</label>
                                            <textarea
                                                className="border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 h-[100px]"
                                                onChange={handleChange}
                                                name='address'
                                                id='address'
                                                value={values.address}
                                                placeholder="enter address"
                                            ></textarea>
                                        </div>
                                    </div>
                                    <div className="mt-10 flex justify-end">
                                        <CustomButton
                                            className="!w-[110px] !h-12"
                                            title="Save"
                                            type="submit"
                                            handleClick={() => { }}
                                            isLoading={isLoading}
                                            variant={
                                                ButtonProperties.VARIANT.primary.name
                                            }
                                        />
                                    </div>
                                </div>
                            </Form>
                        )}
                    </Formik>
                </div>
            </CustomModal>

            <CustomModal
                visibility={isOpenEdit}
                toggleVisibility={setIsOpenEdit}
            >
                <div>
                    <div className="bg-purple-light px-10 py-[28px] flex justify-start rounded-tl-[5px] rounded-tr-[5px] ">
                        <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                            Edit next of kin
                        </h1>
                    </div>
                    <Formik
                        initialValues={editInitialValues}
                        onSubmit={(values) => { editDetails(values) }}
                        validationSchema={createNextOfKinSchema}
                        enableReinitialize
                    >
                        {({ values, setFieldValue, handleChange }) => (
                            <Form>
                                <div className="border-neutral-[#B2BBC6]  bg-white py-7 px-8 rounded-xl">

                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                                        <div>
                                            <FormikCustomInput
                                                label="First name "
                                                id="firstName"
                                                name="firstName"
                                                placeholder="enter first name"
                                                type="text"
                                                value={values.firstName}
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomInput
                                                label="Last name "
                                                id="lastName"
                                                name="lastName"
                                                placeholder="enter last name"
                                                type="text"
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                    </div>
                                    <div className='grid grid-cols-2 gap-8 mt-8'>
                                        <div>
                                            <FormikCustomPhoneInput
                                                label="Phone number "
                                                name="phoneNumber"
                                                id="phoneNumber"
                                                value={values.phoneNumber}
                                                onChange={(value: string) => {
                                                    setFieldValue("phoneNumber", value);
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomInput
                                                label="Email *"
                                                id="email"
                                                name="email"
                                                value={values.email}
                                                placeholder="enter email address"
                                                type="email"
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                    </div>
                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                                        <div>
                                            <FormikCustomSelect
                                                label="Relationship "
                                                id="relationship"
                                                name="relationship"
                                                optionsParentClassName="!capitalize"
                                                options={relationshipTypes}
                                                value={values.relationship}
                                                onChange={(item) => {
                                                    setFieldValue("relationship", item.text);
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomDate
                                                label="D.O.B "
                                                value={moment(values.dob)}
                                                inputClassName="border bg-transparent"
                                                name="dob"
                                                onChange={(date) => {
                                                    setFieldValue("dob", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                                                }}
                                            />
                                        </div>
                                    </div>
                                    <div className="grid tablet:grid-cols-1 gap-8 px-2 mt-8">
                                        <div className="flex flex-col gap-4">
                                            <label htmlFor="">Address</label>
                                            <textarea
                                                className="border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 h-[100px]"
                                                onChange={handleChange}
                                                name='address'
                                                id='address'
                                                value={values.address}
                                                placeholder="enter address"
                                            ></textarea>
                                        </div>
                                    </div>
                                    <div className="mt-10 flex justify-end">
                                        <CustomButton
                                            className="!w-[110px] !h-12"
                                            title="Save"
                                            type="submit"
                                            handleClick={() => { }}
                                            isLoading={isLoading}
                                            variant={
                                                ButtonProperties.VARIANT.primary.name
                                            }
                                        />
                                    </div>
                                </div>
                            </Form>
                        )}
                    </Formik>
                </div>
            </CustomModal>

        </div>
    );

};

export default NextOfKin;
