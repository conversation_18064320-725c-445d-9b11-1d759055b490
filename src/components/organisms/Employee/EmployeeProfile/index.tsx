import React, { useEffect, useState } from "react";
import { ArrowLeft2, Lock } from "iconsax-react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import Profile from "./Profile";
import Attendance from "./Attendance";
import Projects from "./Projects";
import Leave from "./Leave";
import { FaLock } from "react-icons/fa";

const EmployeeProfile = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<number>(0);
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("activeTab");
  const location = useLocation();
  const { id } = location?.state || "";

  const profileTabs = [
    {
      title: "Profile",
      component: <Profile />,
      name: "profile",
    },
    {
      title: <p className="flex gap-2">Attendance <FaLock /></p>,
      component: <Attendance />,
      name: "attendance",
    },
    {
      title: <p className="flex gap-2">Projects <FaLock /></p>,
      component: <Projects />,
      name: "projects",
    },
    {
      title: "Leave",
      component: <Leave staffId={id} />,
      name: "leave",
    },
  ];

  useEffect(() => {
    setActiveTab(queryParam || 0)

    if (!id) {
      navigate("/employee/all-staffs")
    };
  }, [queryParam, activeTab])

  return (
    <div>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 cursor-pointer w-fit"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6 ">

        <h1 className="font-poppins-medium text-15 rounded-tl-[10px] rounded-tr-[10px] text-purple-dark bg-purple-light pl-10 py-[33px] mb-10">
          Employee profile
        </h1>

        <div className="bg-[#F5F5F5] pt-2 pb-[60px] pr-16 rounded-bl-[10px] rounded-br-[10px]">

          <div className="grid grid-cols-4 gap-10 px-1 bg-gray-300 py-1 rounded">
            {profileTabs.map((item, index) => (
              <div
                key={index}
                onClick={() => { setActiveTab(index); navigate(`/employee/staff-information?activeTab=${index}&tab=${item.name}`, { state: { id: id } }) }}
                className={`cursor-pointer px-2 py-2 rounded flex items-center justify-center  ${activeTab == index ? "bg-white" : "bg-none"}`}>
                <p className={`text-neutral-normal font-poppins-medium pb-2 text-center text-13  py-2 ${activeTab == index ? "text-purple-dark" : "text-gray-550"} `}> {item.title}</p>
              </div>
            ))}
          </div>

          <div className=" rounded-xl mt-16">{profileTabs[activeTab].component}</div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeProfile;
