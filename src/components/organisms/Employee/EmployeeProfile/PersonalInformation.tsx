import { useState } from "react";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import moment from "moment";
import { Country, State } from "country-state-city";
import { getStaffPersonalInfoAtom } from "../../../../recoil/atom/staff";
import { updatePersonalInformation, getStaffById } from "../../../../api/staff";
import { useRecoilValue } from "recoil";
import { Form, Formik } from "formik";
import { Accordion, AccordionDetails, AccordionSummary, Typography } from "@mui/material";
import { ArrowDropDownIcon } from "@mui/x-date-pickers";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { BiSave } from "react-icons/bi";
import { ButtonProperties } from "../../../shared/helpers";
import { AiOutlineEdit } from "react-icons/ai";
import { useLocation } from "react-router-dom";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import Loader from "../../../atoms/Loader";



const PersonalInformation = ({ staffid, data }) => {

    const location = useLocation();
    const { isEdit, id } = location?.state || "";

    const [selectedCountry, setSelectedCountry] = useState<string>("");
    const [, setSelectedState] = useState<string>("");
    const [isLoading, setIsLoading] = useState(false);
    const [Edit, setEdit] = useState(false);
    const [showModal, setShowModal] = useState(false);

    const getStaffPersonalInfo = useRecoilValue(getStaffPersonalInfoAtom);

    const getStaffById = async () => {
        await getStaffById(staffId);
    }

    let countries = Country.getAllCountries().map((country) => ({
        text: country.name,
        value: country.isoCode,
    }));


    let states = State.getStatesOfCountry(selectedCountry || getStaffPersonalInfo?.country).map((state) => ({
        text: state.name,
        value: state.isoCode,
    }));


    let initialValues = {
        firstName: getStaffPersonalInfo?.first_name || "",
        lastName: getStaffPersonalInfo?.last_name || "",
        national_id_number: getStaffPersonalInfo?.national_id_number || "",
        passport_number: getStaffPersonalInfo?.passport_number || "",
        dob: getStaffPersonalInfo?.dob ? moment(getStaffPersonalInfo?.dob).format("YYYY-MM-DD") : "",
        marital_status: getStaffPersonalInfo?.marital_status || "",
        country: getStaffPersonalInfo?.country || "",
        state: getStaffPersonalInfo?.state || "",
        address: getStaffPersonalInfo?.address || "",
        no_of_children: getStaffPersonalInfo?.no_of_children || "",
        religion: getStaffPersonalInfo?.religion || "",
    };


    const editDetails = (values) => {

        const payload = {
            staff_id: staffid,
            first_name: values.firstName,
            last_name: values.lastName,
            passport_number: values.passport_number,
            national_id_number: values.national_id_number,
            country: values.country,
            state: values.state,
            religion: values.religion,
            marital_status: values.marital_status,
            no_of_children: values.no_of_children,
            address: values.address,
            dob: values?.dob ? moment(values.dob).format("YYYY-MM-DD HH:mm:ss") : null,
        }

        setIsLoading(true);

        updatePersonalInformation(payload).then((res) => {
            if (res?.success) {
                setIsLoading(false);
                setShowModal(true)
                getStaffById();
            } else {
                setIsLoading(false);
            }
        })

    }

    if (isLoading) {
        return <div>
            <Loader />
        </div>
    };


    return (

        <div className="mt-8">


            <div className="">
                <Formik
                    initialValues={initialValues}
                    onSubmit={editDetails}
                    enableReinitialize
                >
                    {({ values, setFieldValue }) => (
                        <div className="border-neutral-[#B2BBC6]  bg-white py-7 px-8 rounded-xl">
                            <Form>
                                <div className="">
                                    <div className="flex justify-end">
                                        {isEdit || Edit ? (
                                            <div>
                                                <CustomButton
                                                    leftIcon={<BiSave size={20} />}
                                                    className="!w-[180px] !h-10"
                                                    title="Save"
                                                    type="submit"
                                                    handleClick={() => { }}
                                                    isLoading={isLoading}
                                                    variant={
                                                        ButtonProperties.VARIANT.primary.name
                                                    }
                                                />
                                            </div>
                                        ) : (
                                            <div
                                                className="!w-[180px] !h-10 gap-2 cursor-pointer bg-purple-normal rounded flex justify-center items-center text-white"
                                                onClick={() => {
                                                    setEdit(true);
                                                }}
                                            >
                                                <AiOutlineEdit size={18} /> Edit
                                            </div>
                                        )}
                                    </div>
                                    <div className=" grid grid-cols-2 gap-[55px]">

                                        <div>
                                            <div className="grid grid-cols-2 mt-4">
                                                <label
                                                    htmlFor="firstName"
                                                    className="flex items-center text-neutral-normal"
                                                >
                                                    First name
                                                </label>
                                                <FormikCustomInput
                                                    id="firstName"
                                                    name="firstName"
                                                    type="text"
                                                    disabled={!Edit && !isEdit}
                                                />
                                            </div>
                                            <div className="grid grid-cols-2 mt-4">
                                                <label
                                                    htmlFor="lastName"
                                                    className="flex items-center text-neutral-normal"
                                                >
                                                    Last name
                                                </label>
                                                <FormikCustomInput
                                                    id="lastName"
                                                    name="lastName"
                                                    type="text"
                                                    disabled={!Edit && !isEdit}
                                                />
                                            </div>
                                            <div className="grid grid-cols-2 mt-4">
                                                <label
                                                    htmlFor="national_id_number"
                                                    className="flex items-center text-neutral-normal"
                                                >
                                                    National id number
                                                </label>
                                                <FormikCustomInput
                                                    id="national_id_number"
                                                    name="national_id_number"
                                                    type="text"
                                                    disabled={!Edit && !isEdit}
                                                />
                                            </div>
                                            <div className="grid grid-cols-2 mt-4">
                                                <label
                                                    htmlFor="passport_number"
                                                    className="flex items-center text-neutral-normal"
                                                >
                                                    Passport Number
                                                </label>
                                                <FormikCustomInput
                                                    id="passport_number"
                                                    name="passport_number"
                                                    type="text"
                                                    disabled={!Edit && !isEdit}
                                                />
                                            </div>

                                            <div className="grid grid-cols-2 mt-4">
                                                <label
                                                    htmlFor="dob"
                                                    className="flex items-center text-neutral-normal"
                                                >
                                                    Date of birth
                                                </label>
                                                <div className="w-full">
                                                    <FormikCustomDate
                                                        value={moment(values.dob)}
                                                        inputClassName="border bg-transparent"
                                                        name="dob"
                                                        onChange={(date) => {
                                                            setFieldValue("dob", date ? moment(date).format("YYYY-MM-DD") : null)
                                                        }}
                                                        disabled={!Edit && !isEdit}
                                                    />
                                                </div>
                                            </div>
                                            <div className="grid grid-cols-2 mt-4">
                                            <label
                                                htmlFor="address"
                                                className="flex items-center text-neutral-normal"
                                            >
                                                Address
                                            </label>
                                            <FormikCustomInput
                                                id="address"
                                                name="address"
                                                type="text"
                                                disabled={!Edit && !isEdit}
                                            />
                                            </div>
                                        </div>
                                        <div className="mt-4">
                                            <div className="grid grid-cols-2">
                                                <label
                                                    htmlFor="marital_status"
                                                    className="flex items-center text-neutral-normal"
                                                >
                                                    Marital status
                                                </label>
                                                <div>
                                                    <FormikCustomSelect
                                                        options={[
                                                            { text: "SINGLE", value: "SINGLE" },
                                                            { text: "MARRIED", value: "MARRIED" },
                                                        ]}
                                                        name="marital_status"
                                                        value={values.marital_status}
                                                        onChange={(item: {
                                                            value: string;
                                                            text: string;
                                                        }) => {
                                                            setFieldValue("marital_status", item.text);
                                                        }}
                                                        disabled={!Edit && !isEdit}
                                                    />
                                                </div>
                                            </div>

                                            <div className="grid grid-cols-2 mt-4">
                                                <label
                                                    htmlFor="no_of_children"
                                                    className="flex items-center text-neutral-normal"
                                                >
                                                    No of children
                                                </label>
                                                <FormikCustomInput
                                                    id="no_of_children"
                                                    name="no_of_children"
                                                    type="text"
                                                    disabled={!Edit && !isEdit}
                                                />
                                            </div>
                                            <div className="grid grid-cols-2 mt-4">
                                                <label
                                                    htmlFor="country"
                                                    className="flex items-center text-neutral-normal"
                                                >
                                                    Country
                                                </label>
                                                <div>
                                                    <FormikCustomSelect
                                                        options={countries}
                                                        name="country"
                                                        value={selectedCountry || values.country}
                                                        onChange={(item: {
                                                            value: string;
                                                            text: string;
                                                        }) => {
                                                            setSelectedCountry(item.value);
                                                            setFieldValue("country", item.text);
                                                        }}
                                                        disabled={!Edit && !isEdit}
                                                    />
                                                </div>
                                            </div>
                                            <div className="grid grid-cols-2 mt-4">
                                                <label
                                                    htmlFor="state"
                                                    className="flex items-center text-neutral-normal"
                                                >
                                                    State
                                                </label>
                                                <div>
                                                    <FormikCustomSelect
                                                        options={states}
                                                        name="state"
                                                        onChange={(item: {
                                                            value: string;
                                                            text: string;
                                                        }) => {
                                                            setSelectedState(item.value);
                                                            setFieldValue("state", item.text);
                                                        }}
                                                        value={values.state}
                                                        disabled={!Edit && !isEdit}
                                                    />
                                                </div>
                                            </div>
                                            <div className="grid grid-cols-2 mt-4">
                                                <label
                                                    htmlFor="religion"
                                                    className="flex items-center text-neutral-normal"
                                                >
                                                    Religion
                                                </label>
                                                <div>
                                                    <FormikCustomSelect
                                                        options={[
                                                            { text: "CHRISTIAN", value: "CHRISTIAN" },
                                                            { text: "MUSLIM", value: "MUSLIM" },
                                                            { text: "NONE", value: "NONE" },
                                                        ]}
                                                        name="religion"
                                                        value={values.religion}
                                                        onChange={(item: {
                                                            value: string;
                                                            text: string;
                                                        }) => {
                                                            setFieldValue("religion", item.text);
                                                        }}
                                                        disabled={!Edit && !isEdit}
                                                    />
                                                </div>
                                            </div>
                                            

                                        </div>
                                       
                                    </div>
                                </div>
                            </Form>
                        </div>
                    )}
                </Formik>
            </div>

            <SuccessModal
                visibility={showModal}
                toggleVisibility={setShowModal}
                text="Your changes has been saved"
                route="/employee/all-staffs"
            />

        </div >
    )
}

export default PersonalInformation;