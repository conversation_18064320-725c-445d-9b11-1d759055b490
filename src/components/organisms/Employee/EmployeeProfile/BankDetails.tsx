import { <PERSON><PERSON><PERSON><PERSON>, FaPlus } from "react-icons/fa6";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import { PiTrash } from "react-icons/pi";
import { useEffect, useState } from "react";
import useClickOutside from "../../../shared/hooks";
import { UserEdit } from "iconsax-react";
import moment from "moment";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import { Form, Formik } from "formik";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import * as yup from "yup";
import axios from "axios";
import { useRecoilValue } from "recoil";
import { getBanksAtom, getEmployeeBankAtom, } from "../../../../recoil/atom/staff";
import { createBank, switchBank, validateBank } from "../../../../api/staff";
import { TbSwitch3 } from "react-icons/tb";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import Loader from "../../../atoms/Loader";



const BankDetails = ({ data, staffid }) => {

    const [showDropdown, setShowDropdown] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [rowId, setRowId] = useState("");
    const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
    const [showBankModal, setShowBankModal] = useState<boolean>(false);
    // const [showBankEditModal, setShowBankEditModal] = useState<boolean>(false);
    const [BankId, setbankId] = useState('');
    const [name, setName] = useState('');

    const getAllBanks = useRecoilValue(getBanksAtom);

    const getBankValue = useRecoilValue(getEmployeeBankAtom);

    const node = useClickOutside(() => {
        setShowDropdown(false);
        setIsLoading(false);
        setRowId("");
        setName("");
    });

    const createBankSchema = yup.object().shape({
        bankName: yup.string().required(errorMessages.required),
        bankNumber: yup.string().required(errorMessages.required),
    });

    const initialValues = {
        bankName: "",
        bankNumber: "",
        accountName: "",
    }

    const allBanks = getAllBanks.map((item: any) => ({
        text: item.name,
        value: item.code,
    }));

    const verifyBank = (values) => {
        setIsLoading(true);
        validateBank({
            account_number: values.bankNumber,
            bank_code: BankId
        }).then((res) => {
            if (res?.success) {
                setName(res.data.account_name);
                setIsLoading(false);
            } else {
                setIsLoading(false);
                return;
            }
        })
    }

    const handleVerify = (values) => {
        if (values.bankNumber.length === 10) {
            verifyBank(values)
        }
    }

    const addBank = (values) => {
        setIsLoading(true)
        createBank({
            staff_id: staffid,
            bank_name: values.bankName,
            account_name: name,
            account_number: values.bankNumber,
            bank_code: BankId,
            is_default: false,
        }).then((res) => {
            if (res?.success) {
                setName("");
                setIsLoading(false);
                setShowBankModal(false);
                data();
                clustarkToast(
                    NotificationTypes.SUCCESS,
                    "Bank details added successfully."
                );
            } else {
                setIsLoading(false);
                return;
            }
        })
    }

    const switchDefaultBank = (id) => {
        setIsLoading(true)
        switchBank({
            staff_id: staffid,
            bank_info_id: id,
        }).then((res) => {
            if (res?.success) {
                setName(res.data.account_name);
                setShowDeleteWarn(false);
                setShowDropdown(false);
                setIsLoading(false);
                data();
                clustarkToast(
                    NotificationTypes.SUCCESS,
                    "Bank default changes successfully."
                );
            } else {
                setIsLoading(false);
                return;
            }
        })

    }


    const columns = [

        {
            Header: "Bank Name",
            accessor: "bank_name",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Account Name",
            accessor: "account_name",
            Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
        },
        {
            Header: "Account Number",
            accessor: "account_number",
            Cell:
                (row: any) => <div className="flex gap-3 items-center">
                    <p>{row.cell.value || "--"}</p>
                    {row.cell.row.original.is_default &&
                        <div
                            className="text-white text-[0.7rem] bg-purple-dark px-2  rounded-full"
                        >
                            <span>default</span>
                        </div>
                    }
                </div>,
        },

        {
            Header: "",
            accessor: "action",
            Cell: (row: any) => (
                <div className="relative">
                    <FaEllipsisV
                        onClick={() => {
                            setShowDropdown(!showDropdown);
                            setRowId(row.cell.row.id);
                        }}
                        className="text-[#98A2B3] cursor-pointer"
                    />
                    {showDropdown && row.cell.row.id === rowId && (
                        <FilterDropdown>
                            <ul className="text-14 text-neutral-dark" ref={node}>
                                {showDeleteWarn ? (
                                    <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                                        <div className="flex gap-3">
                                            <PiTrash size={18} />
                                            Are you sure?
                                        </div>
                                        <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                                            <CustomButton
                                                title="Yes"
                                                handleClick={() => {
                                                    switchDefaultBank(row.cell.row.original.id)
                                                }}
                                                className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer hover:!text-white"
                                                isLoading={isLoading}
                                            />
                                            <span
                                                onClick={() => { setShowDeleteWarn(false); }}
                                                className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                                            >
                                                No
                                            </span>
                                        </div>
                                    </li>
                                ) : (
                                    <li
                                        onClick={() => setShowDeleteWarn(true)}
                                        className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-yellow-600 cursor-pointer"
                                    >
                                        <TbSwitch3 size={18} />
                                        Set as default
                                    </li>
                                )}
                            </ul>
                        </FilterDropdown>
                    )}
                </div>
            ),
        },
    ];




    return (
        <>
            <div className="border-neutral-[#B2BBC6] mt-11 py-7 px-8 rounded-xl">
                <div className="flex justify-end gap-5">

                </div>
                <div className="border-neutral-[#B2BBC6]   rounded-xl">

                    {
                        getBankValue ?
                            <CustomTable
                                data={getBankValue || []}
                                // meta={getStaffValue?.meta}
                                columns={columns}
                                hideSearch
                                header={
                                    <div className="flex justify-between items-center h-[45px]">
                                        <h1 className="pl-2">
                                            Bank details
                                        </h1>
                                        <div className=" px-2">
                                            <CustomButton
                                                leftIcon={<FaPlus size={15} />}
                                                className="!w-[180px] !h-10"
                                                title="Add"
                                                type="submit"
                                                handleClick={() => { setShowBankModal(true) }}
                                                isLoading={isLoading}
                                                variant={
                                                    ButtonProperties.VARIANT.primary.name
                                                }
                                            />
                                        </div>
                                    </div>
                                }
                            /> :
                            <div className="flex justify-center items-center">
                                <OrganizationEmptyState />
                            </div>
                    }


                </div>
            </div>

            <CustomModal
                visibility={showBankModal}
                toggleVisibility={setShowBankModal}
            >
                <div ref={node}>
                    <div className="bg-purple-light px-10 py-[28px] flex justify-start rounded-tl-[5px] rounded-tr-[5px] ">
                        <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                            Add bank details
                        </h1>
                    </div>
                    <Formik
                        initialValues={initialValues}
                        onSubmit={(values) => { addBank(values) }}
                        validationSchema={createBankSchema}
                        enableReinitialize
                    >
                        {({ values, setFieldValue, }) => (
                            <Form>
                                <div className="border-neutral-[#B2BBC6]  bg-white py-7 px-8 rounded-xl">

                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                                        <div>
                                            <FormikCustomSelect
                                                label="Bank name *"
                                                id="bankName"
                                                name="bankName"
                                                optionsParentClassName="!capitalize"
                                                options={allBanks}
                                                value={values.bankName}
                                                onChange={(item) => {
                                                    setFieldValue("bankName", item.text);
                                                    setbankId(item.value)
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomInput
                                                label="Account number *"
                                                id="bankNumber"
                                                name="bankNumber"
                                                value={values.bankNumber}
                                                placeholder="enter account number"
                                                type="number"
                                                inputClassName="bg-transparent border"
                                                onChange={(e) => {
                                                    const { value } = e.target;
                                                    setFieldValue("bankNumber", value);
                                                    setFieldValue("accountName", name);
                                                    handleVerify({ ...values, bankNumber: value });
                                                }}
                                            />
                                        </div>
                                    </div>

                                    <div className='grid grid-cols-1 gap-8 mt-8'>
                                        <div>
                                            <FormikCustomInput
                                                label="Account name *"
                                                id="accountName"
                                                name="accountName"
                                                placeholder=" account name"
                                                type="text"
                                                value={name}
                                                inputClassName="bg-transparent border"
                                                disabled
                                            />
                                        </div>
                                    </div>
                                    <div className="mt-10 flex justify-end">
                                        <CustomButton
                                            className="!w-[110px] !h-12"
                                            title="Save"
                                            type="submit"
                                            handleClick={() => { }}
                                            isLoading={isLoading}
                                            variant={
                                                ButtonProperties.VARIANT.primary.name
                                            }
                                        />
                                    </div>
                                </div>
                            </Form>
                        )}
                    </Formik>
                </div>
            </CustomModal>

        </>
    )
}



export default BankDetails;