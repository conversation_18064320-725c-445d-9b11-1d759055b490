import { FaEllipsisV } from "react-icons/fa";
import CustomTable from "../../atoms/CustomTable/CustomTable";
import FilterDropdown from "../../atoms/Cards/FilterDropdown";
import { useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import useClickOutside from "../../shared/hooks";
import { PiTrash } from "react-icons/pi";
import CustomButton from "../../atoms/CustomButton/CustomButton";
import { FaEye, FaPlus } from "react-icons/fa6";
import { MdClose } from "react-icons/md";
import CustomModal from "../../atoms/CustomModal/CustomModal";
import { ButtonProperties, errorMessages, } from "../../shared/helpers";
import { Formik, Form } from "formik";
import FormikCustomInput from "../../atoms/CustomInput/FormikCustomInput";
import { clustarkToast } from "../../atoms/Toast";
import { NotificationTypes } from "../../shared/helpers/enums";
import { createDocument, deleteDocument, } from "../../../api/staff";
import { useRecoilValue } from "recoil";
import { getDocumentAtom, } from "../../../recoil/atom/staff";
import * as yup from "yup";
import OrganizationEmptyState from "../../atoms/Cards/OrganizationEmptyState";


const EmployeeDocument = ({ staffid, data }) => {


    const navigate = useNavigate();

    const [showDropdown, setShowDropdown] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [rowId, setRowId] = useState("");
    const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
    const [showDetails, setShowDetails] = useState<boolean>(false);
    const [isOpen, setIsOpen] = useState(false);
    const [, setDragging] = useState(false);
    const [selectedImage, setSelectedImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [FileName, setfileName] = useState<string | number>('');
    const [documentDetails, setDocumentDetails] = useState<any>({});

    const getDocuments = useRecoilValue(getDocumentAtom);

    const node = useClickOutside(() => {
        setShowDropdown(false);
        setIsLoading(false);
        setRowId("");
    });

    const initialValues = {
        title: "",
    };

    const addDocumentSchema = yup.object().shape({
        title: yup.string().required(errorMessages.required),
    });

    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {

        const file = event.target.files ? event.target.files[0] : null;

        if (file) {
            setfileName(file.name)
            const maxSize = 900 * 1024;
            if (file.size > maxSize) {
                clustarkToast(
                    NotificationTypes.ERROR,
                    "File size must be less than 900kb."
                );
                return;
            }
            const reader = new FileReader();
            reader.onload = () => {
                setImagePreview(reader.result as string);
            };
            reader.readAsDataURL(file);
            setSelectedImage(file);

        }

        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    };


    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setDragging(false);

        const files = e.dataTransfer.files;
        if (files && files.length > 0) {
            setSelectedImage(files[0]);
        }
    };

    const triggerFileInput = () => {
        fileInputRef.current?.click();
    };


    const addDocument = (values) => {

        if (!selectedImage || selectedImage == null) {
            clustarkToast(NotificationTypes.ERROR, 'select a file');
        } else {
            setIsLoading(true);
            const formData = new FormData();
            formData.append('staff_id', staffid);
            formData.append('title', values.title);
            formData.append('document', selectedImage as File);
            createDocument(formData)
                .then((res) => {
                    if (res?.success) {
                        clustarkToast(NotificationTypes.SUCCESS, res.message);
                        setIsOpen(false)
                        setIsLoading(false);
                        data()
                    } else {
                        setIsLoading(false);
                        setIsOpen(true)
                    }
                });
        }

    };

    const removeDocument = (id) => {
        setIsLoading(true);
        deleteDocument(id).then((res) => {
            if (res.success) {
                setShowDetails(false)
                setShowDeleteWarn(false)
                setShowDropdown(false)
                setIsLoading(false);
                data()
            }
        });

    }

    const viewDocument = (data: any) => {
        setShowDetails(true);
        setDocumentDetails(data)
    }


    const columns = [
        {
            Header: "Document name",
            accessor: "document_name",
            Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
        },
        {
            Header: "Document type",
            accessor: "document_type",
            Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
        },
        {
            Header: "Attachment",
            accessor: "document",
            Cell: (row: any) =>
                row.cell.value ? (
                    <a
                        href={row.cell.value}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-700"
                    >
                        Download
                    </a>
                ) : (
                    "--"
                ),
        },
        {
            Header: "",
            accessor: "action",
            Cell: (row: any) => (
                <div className="relative">
                    <FaEllipsisV
                        onClick={() => {
                            setShowDropdown(!showDropdown);
                            setRowId(row.cell.row.id);
                        }}
                        className="text-[#98A2B3] cursor-pointer"
                    />
                    {showDropdown && row.cell.row.id === rowId && (
                        <FilterDropdown>
                            <ul className="text-14 text-neutral-dark" ref={node}>
                                <li
                                    onClick={() => { viewDocument(row.cell.row.original) }}
                                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                                >
                                    <FaEye size={18} />
                                    View
                                </li>
                                {showDeleteWarn ? (
                                    <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                                        <div className="flex gap-3">
                                            <PiTrash size={18} />
                                            Are you sure?
                                        </div>
                                        <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                                            <CustomButton
                                                title="Yes"
                                                handleClick={() => { removeDocument(row.cell.row.original.id) }}
                                                className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer hover:!text-white"
                                                isLoading={isLoading}
                                            />
                                            <span
                                                onClick={() => { setShowDeleteWarn(false); }}
                                                className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                                            >
                                                No
                                            </span>
                                        </div>
                                    </li>
                                ) : (
                                    <li
                                        onClick={() => setShowDeleteWarn(true)}
                                        className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer"
                                    >
                                        <PiTrash size={18} />
                                        Delete
                                    </li>
                                )}
                            </ul>
                        </FilterDropdown>
                    )}
                </div>
            ),
        },
    ];


    // console.log(getDocuments);
    



    return (
        <>
            <div className="border-neutral-[#B2BBC6] mt-11 py-7 px-8 rounded-xl" >

                {/* <div className="flex justify-end gap-5">
                    <CustomButton
                        leftIcon={<FaPlus size={15} />}
                        className="!w-[102px] !h-10"
                        title="Add"
                        type="submit"
                        handleClick={() => { setIsOpen(true) }}
                        variant={
                            ButtonProperties.VARIANT.primary.name
                        }
                    />
                </div> */}

                {
                    getDocuments ?
                        <CustomTable
                            data={getDocuments || []}
                            columns={columns}
                            hideSearch
                            header={
                                <div className="flex justify-between items-center h-[45px]">
                                    <h1 className="pl-2">
                                        Employee documents
                                    </h1>
                                    <div className=" px-2">
                                        <CustomButton
                                            leftIcon={<FaPlus size={15} />}
                                            className="!w-[180px] !h-10"
                                            title="Add"
                                            type="submit"
                                            handleClick={() => { setIsOpen(true) }}
                                            isLoading={isLoading}
                                            variant={
                                                ButtonProperties.VARIANT.primary.name
                                            }
                                        />
                                    </div>
                                </div>
                            }
                        /> :
                        <div className="flex justify-center items-center">
                            <OrganizationEmptyState />
                        </div>
                }

            </div>

            <CustomModal
                visibility={showDetails}
                toggleVisibility={setShowDetails}
            >
                <div>
                    <div className='flex justify-between bg-purple-light py-[33px] px-10 '>
                        <h1 className=" font-poppins-medium text-18 text-purple-dark">
                            Document information
                        </h1>
                        <MdClose
                            size={18}
                            color='#0C0123'
                            className="cursor-pointer"
                            onClick={() => { setShowDetails(false) }}
                        />

                    </div>
                    <div className="mt-8 text-neutral-normal px-10 pb-10">
                        <div className="mt-12">
                            <div className="mt-8 grid grid-cols-2">
                                <div>
                                    <h1 className="text-neutral-dark">Document name</h1>
                                    <p className="mt-2.5 capitalize">{documentDetails.document_name || '--'}</p>
                                </div>
                                <div>
                                    <h1 className="text-neutral-dark">Document type</h1>
                                    <p className="mt-2.5 capitalize">{documentDetails.document_type || '--'}</p>
                                </div>
                            </div>
                            <div className="mt-8">
                                <div>
                                    <h1 className="text-neutral-dark">Document file</h1>
                                    <a className="mt-2 underline" href={documentDetails.document || '--'} target="_blank">click to view document</a>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </CustomModal>

            <CustomModal
                visibility={isOpen}
                toggleVisibility={setIsOpen}
            >
                <div>
                    <div className="bg-purple-light px-10 py-[28px] flex justify-start rounded-tl-[5px] rounded-tr-[5px] ">
                        <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                            Add document
                        </h1>
                    </div>
                    <div>
                        <Formik
                            initialValues={initialValues}
                            onSubmit={(values) => { addDocument(values) }}
                            validationSchema={addDocumentSchema}
                            enableReinitialize
                        >
                            {({ values }) => (
                                <Form>
                                    <div className="border-neutral-[#B2BBC6]  bg-white py-7 px-8 rounded-xl">
                                        <div className="px-2 mt-8">
                                            <div>
                                                <FormikCustomInput
                                                    label="Document name *"
                                                    id="title"
                                                    name="title"
                                                    placeholder="e.g. international passport, national identity, e.t.c."
                                                    type="text"
                                                    inputClassName="!bg-transparent"
                                                    value={values.title}
                                                />
                                            </div>
                                        </div>

                                        <div>
                                            <div className="mt-6">
                                                <input
                                                    ref={fileInputRef}
                                                    type="file"
                                                    className="hidden"
                                                    onChange={handleImageUpload}
                                                />

                                                <p className="mb-4">Document upload (optional)</p>
                                                <div className="border-2 border-dashed bg-[#B2BBC6] bg-opacity-10 h-[150px] rounded mt-4 flex justify-center items-center">
                                                    <div
                                                        onDragOver={(e) => {
                                                            e.preventDefault();
                                                            setDragging(true);
                                                        }}
                                                        onDragLeave={() => setDragging(false)}
                                                        onDrop={handleDrop}
                                                    >
                                                        <p
                                                            onClick={triggerFileInput}
                                                            className="text-neutral-normal text-center mt-5 mb-5"
                                                        >
                                                            <span className="font-poppins-medium text-purple-dark cursor-pointer">
                                                                Click here
                                                            </span>
                                                            &nbsp; or Drag and Drop file to upload
                                                        </p>
                                                        <p>{FileName}</p>
                                                    </div>
                                                </div>
                                                {/* {imagePreview && (
                                                <div className="mt-10">
                                                    <div className="w-[200px] h-[100px] border p-4 ">
                                                        <img
                                                            src={imagePreview || ""}
                                                            width={200}
                                                            className="h-[100px] w-[200px] object-cover"
                                                        />
                                                        <p className="text-purple-normal mt-1">
                                                            {selectedImage && selectedImage.name}
                                                        </p>
                                                    </div>
                                                </div>
                                            )} */}
                                            </div>
                                        </div>

                                        <div className='flex justify-end'>
                                            <CustomButton
                                                className="mt-[80px] text-16"
                                                type="submit"
                                                title="Add"
                                                handleClick={() => { }}
                                                size={ButtonProperties.SIZES.small}
                                                variant={ButtonProperties.VARIANT.primary.name}
                                                isLoading={isLoading}
                                            />
                                        </div>
                                    </div>
                                </Form>
                            )}
                        </Formik >
                    </div >
                </div >
            </CustomModal>

        </>
    )
}

export default EmployeeDocument;