import { Form, Formik } from "formik";
import React, { useEffect, useRef, useState } from "react";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";
import { useRecoilValue } from "recoil";
import {
  getAllBranchesAtom,
  getDepartmentsAtom,
} from "../../../../recoil/atom/organizationAtom";
import {
  transferStaffToBranch,
  transferStaffToDepartment,
} from "../../../../api/staff";
import { getAllStaffAtom } from "../../../../recoil/atom/staff";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import * as yup from "yup";

const Transfer = ({ getStaffByIdValue }) => {
  const isMounted = useRef(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [departmentName, setDepartmentName] = useState("");
  const [branchName, setBranchName] = useState("");
  const [branchId, setBranchId] = useState("");
  const [departmentId, setDepartmentId] = useState("");
  const [lineManagerId, setLineManagerId] = useState("");
  const staffAtom = useRecoilValue(getAllStaffAtom);
  const getDepartmentsValue = useRecoilValue(getDepartmentsAtom);
  const getEntireBranchesValue = useRecoilValue(getAllBranchesAtom);
  const { fetchDepartments, fetchEntireBranches, fetchStaffById, fetchAllStaff } = useUpdateRecoilAtom();

  const transferSchema = yup.object().shape({
    branch: yup
      .string()
      .test((value) => {
        if (activeTab == 0) {
          errorMessages.required;
          return !!value;
        }
        return true;
      }),
    department: yup.string().required(errorMessages.required),
    lineManager: yup.string().required(errorMessages.required),
  });


  const staffs = staffAtom?.data?.map((staff) => ({
    text: staff?.user?.first_name + " " + staff?.user?.last_name,
    value: staff.id,
  }));

  const branches =
    getEntireBranchesValue?.data?.map((branch) => ({
      text: branch.name,
      value: branch.id,
    })) || [];

  const departments = getDepartmentsValue?.data?.map((item) => ({
    text: item.name,
    value: item.id,
  }));

  const initialValues = {
    branch: "",
    department: "",
    lineManager: "",
  };

  useEffect(() => {
    if (isMounted.current) return;
    isMounted.current = true;
    fetchEntireBranches();
  }, [isMounted, activeTab]);

  useEffect(() => {
    fetchAllStaff();
    fetchDepartments(branchId || getStaffByIdValue?.department?.branch?.id);
  }, [branchId, departmentId]);

  const handleSubmit = () => {
    setIsLoading(true);
    if (branchId) {
      const payload = {
        staff_id: getStaffByIdValue.id,
        department_id: departmentId,
        direct_report_id: lineManagerId,
        branch_id: branchId,
        confirm_removal: true,
      };
      transferStaffToBranch(payload).then((response) => {
        setIsLoading(false);
        if (response.success) {
          setShowSuccessModal(true);
          fetchStaffById(getStaffByIdValue.id)
        }
      });
    } else {
      const payload = {
        department_id: departmentId,
        staff_id: getStaffByIdValue.id,
        direct_report_id: lineManagerId,
        confirm_removal: true,
      };
      transferStaffToDepartment(payload).then((response) => {
        setIsLoading(false);
        if (response.success) {
          setShowSuccessModal(true);
          fetchStaffById(getStaffByIdValue.id)
        }
      });
    }
  };

  return (
    <div>
      <h1 className="bg-purple-light text-18 font-poppins-medium text-purple-dark-hover py-[33px] px-10">
        Transfer
      </h1>
      <div className="mt-11 px-10 pb-8">
        <div className="grid grid-cols-2">
          <p
            onClick={() => setActiveTab(0)}
            className={` cursor-pointer border-b-4  pb-2.5 text-16 text-center text-neutral-normal ${activeTab == 0 ? "border-purple-normal" : "border-[#B2BBC666]"
              }`}
          >
            Branch
          </p>
          <p
            onClick={() => setActiveTab(1)}
            className={`cursor-pointer border-b-4  pb-2.5 text-center text-16 ${activeTab == 1 ? "border-purple-normal" : "border-[#B2BBC666]"
              }`}
          >
            Department
          </p>
        </div>

        <div className="mt-16">
          <p className="text-18 font-poppins-medium text-neutral-dark">
            {activeTab === 0
              ? getStaffByIdValue?.department?.branch?.name
              : getStaffByIdValue?.department?.name}
          </p>
          <div className="mt-12">
            <Formik
              initialValues={initialValues}
              onSubmit={handleSubmit}
              validationSchema={transferSchema}
              enableReinitialize
            >
              {({ values, setFieldValue, isValid, dirty }) => (
                <Form>
                  <div>
                    {activeTab == 0 && (
                      <div className="mt-8">
                        <div>
                          <FormikCustomSelect
                            label="Select new branch"
                            placeholder="Choose new branch"
                            options={branches}
                            name="branch"
                            value={values.branch}
                            onChange={(item: {
                              value: string;
                              text: string;
                            }) => {
                              setBranchId(item.value);
                              setBranchName(item.text);
                              setFieldValue("branch", item.text);
                            }}
                          />
                        </div>
                      </div>
                    )}
                    <div className="mt-8">
                      <div>
                        <FormikCustomSelect
                          label="Department"
                          placeholder="Choose department"
                          options={departments}
                          name="department"
                          value={values.department}
                          onChange={(item: { value: string; text: string }) => {
                            setDepartmentId(item.value);
                            setDepartmentName(item.text);
                            setFieldValue("department", item.text);
                          }}
                        />
                      </div>
                    </div>
                    <div className="mt-8">
                      <div>
                        <FormikCustomSelect
                          label="Line manager"
                          placeholder="Choose line manager"
                          options={staffs}
                          name="lineManager"
                          value={values.lineManager}
                          onChange={(item: { value: string; text: string }) => {
                            setLineManagerId(item.value);
                            setFieldValue("lineManager", item.text);
                          }}
                        />
                      </div>
                    </div>

                    <div className="flex justify-end mt-[54px]">
                      <CustomButton
                        className=" text-16"
                        type="submit"
                        title="Transfer"
                        isLoading={isLoading}
                        handleClick={() => { }}
                        size={ButtonProperties.SIZES.small}
                        variant={ButtonProperties.VARIANT.primary.name}
                        isDisabled={!(isValid && dirty)}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
      <SuccessModal
        visibility={showSuccessModal} toggleVisibility={setShowSuccessModal}
        text={`You've successfully transferred ${getStaffByIdValue?.user?.first_name + " " + getStaffByIdValue?.user?.last_name} to the ${activeTab === 0 ? branchName : departmentName} ${activeTab === 0 ? "branch" : "department"}.`}
      />
    </div>
  );
};

export default Transfer;
