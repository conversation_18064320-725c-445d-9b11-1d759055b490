import React, { useEffect, useRef, useState } from "react";
import { Eye } from "iconsax-react";
import OrganizationEmptyState from "../../../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../../../atoms/CustomTable/CustomTable";
import { ButtonProperties } from "../../../../shared/helpers";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../../../shared/hooks";
import { FiUserCheck } from "react-icons/fi";
import CustomButton from "../../../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import CustomModal from "../../../../atoms/CustomModal/CustomModal";
import { AiOutlineStop } from "react-icons/ai";
import { getLeaveApplicationAtom } from "../../../../../recoil/atom/leave";
import { approveLeave, declineLeave, getLeaveApplications } from "../../../../../api/leave";
import moment from "moment";
import StatusTag from "../../../../atoms/StatusTag";
import { clustarkToast } from "../../../../atoms/Toast";
import { NotificationTypes } from "../../../../shared/helpers/enums";
import Loader from "../../../../atoms/Loader";
import useUpdateRecoilAtom from "../../../../shared/hooks/updateRecoilAtom";
import { FaUser } from "react-icons/fa6";
import LeaveLoader from "../../../../atoms/Skeleton/LeaveLoader";


const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

const AllLeaveRequests = () => {
  const navigate = useNavigate();

  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [declineModal, setDeclineModal] = useState<boolean>(false);
  const [approveModal, setApproveModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [approveComment, setApproveComment] = useState<string>("");
  const [declineComment, setDeclineComment] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [rowId, setRowId] = useState("");
  const [id, setId] = useState("");
  const { fetchLeaveAnalytics } = useUpdateRecoilAtom()


  const [, setLeaveAtom] = useRecoilState(getLeaveApplicationAtom);
  const leaveApplicationValue = useRecoilValue(getLeaveApplicationAtom);

  const fetchLeaveApplication = (q) => {
    setIsFetching(true);
    getLeaveApplications({ search: q, page: pageNumber }).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setLeaveAtom(res.data);
      }
    });
  };

  const handleApproval = (e) => {
    e.preventDefault();
    setIsLoading(true);
    approveLeave(id, { approveComment: approveComment }).then((res) => {
      if (res.success) {
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        setApproveModal(false);
        fetchLeaveApplication(searchQuery);
        fetchLeaveAnalytics()
      }
      setIsLoading(false);
    }).catch(() => setIsLoading(false));
  }

  const handleDecline = (e) => {
    e.preventDefault();
    setIsLoading(true);
    declineLeave(id, { declineComment: declineComment }).then((res) => {
      if (res.success) {
        clustarkToast(NotificationTypes.SUCCESS, res.message);
        setDeclineModal(false);
        fetchLeaveApplication(searchQuery);
        fetchLeaveAnalytics()
      }
      setIsLoading(false);
    }).catch(() => setIsLoading(false));
  }

  useEffect(() => {
    fetchLeaveApplication(searchQuery);
  }, [searchQuery, pageNumber]);

  const debounceSearch = useRef(debounce((q) => fetchLeaveApplication(q), 2000)).current;

  const node = useClickOutside(() => {
    setShowDropdown(false);
    setIsLoading(false);
    setRowId("");
  });



  const columns = [
    // {
    //   Header: <input type="checkbox" name="test" id="test" className="w-5 h-5" />,
    //   accessor: "test",
    //   Cell: (row: any) => (
    //     <div className="flex  items-center capitalize">
    //       <input type="checkbox" name="test" id="test" className="w-5 h-5" />
    //     </div>
    //   ),
    // },

    {
      Header: "Employee name",
      accessor: "staff.staffPersonalInformations.first_name",
      Cell: (row: any) => (
        <div className="flex items-center capitalize gap-2" >
          <div className="w-[40px] h-[40px] overflow-hidden rounded-full">
            <img
              src={row.cell.row.original?.staff?.staffPersonalInformations?.avatar}
              alt="staff"
              className="w-full h-full object-cover "
            />
          </div>
          <div className="grid grid-cols-1 gap-1">
            <p className="font-extrabold text-[0.8rem]">{row.cell.value} {row.cell.row.original?.staff?.staffPersonalInformations?.last_name}</p>
            <p className="text-[0.9rem]">{row.cell.row.original?.staff?.staffBasicInformations?.email?.toLowerCase()}</p>
          </div>
        </div>
      ),
    },
    {
      Header: "Time of duration",
      accessor: "staff.staff_identification_tag",
      Cell: (row: any) => (
        <p className="rounded-2xl bg-[#F5F6F7] w-fit px-2 py-1 font-poppins-medium text-[0.9rem] font-bold">
          {row.cell.value || "--"}{" "}
        </p>
      ),
    },
    {
      Header: "Request type",
      accessor: "leaveType.name",
      Cell: (row: any) => <p> {row.cell.value || "--"}</p>,
    },
    {
      Header: "Time of duration",
      accessor: "start_date",
      Cell: (row: any) => (
        <div className="flex flex-col items-center gap-2">
          <div className="flex gap-2">
            <p>{row.cell.value ? moment(row.cell.value).format("DD-MM") : "--"}</p>
            <p>-</p>
            <p>{row.cell.row.original?.end_date ? moment(row.cell.row.original.end_date).format("DD-MM") : "--"}</p>
          </div>
          <div>
            <p className="text-[0.9rem]">{row.cell.row.original?.duration || "--"} days</p>
          </div>
        </div>
      )
    },
    {
      Header: "Eligibility",
      accessor: "leaveType.paid_status",
      Cell: (row: any) => <p>{row.cell.value || "--"} </p>,
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: (row: any) => (
        <StatusTag status={row.cell.value?.toLowerCase() || "--"} />
      ),
    },
    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.original?.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.original.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() =>
                    navigate(`/employee/leave-information`, {
                      state: { id: row.cell.row.original?.id },
                    })
                  }
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>
                {row.cell.row.original.status === "PENDING" && (
                  <>
                    <li
                      onClick={() => {
                        setApproveModal(true);
                        setId(row.cell.row.original.id);
                      }}
                      className="flex text-alert-text-success gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                    >
                      <FiUserCheck size={18} />
                      Approve
                    </li>
                    <li
                      onClick={() => {
                        setDeclineModal(true);
                        setId(row.cell.row.original.id);
                      }}
                      className="flex gap-3 text-alert-text-error pl-2.5 border-b py-2.5 cursor-pointer"
                    >
                      <AiOutlineStop size={18} />
                      Decline
                    </li>
                  </>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  if (isFetching && !searchQuery) {
    return (<div>
      <LeaveLoader />
    </div>);
  }


  return (
    <div>
      <div className=" my-10 py-[23px]">
        {leaveApplicationValue?.data?.length > 0 || searchQuery ? (
          <CustomTable
            data={leaveApplicationValue?.data || []}
            meta={leaveApplicationValue?.meta}
            columns={columns}
            dropdowmCardClass="!w-[458px]"
            placeholder='Search employee by name, or any related keywords'
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            handleSearch={(search) => {
              setSearchQuery(search);
              debounceSearch(search);
            }}
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState
              text="No Leave Requests Available"
            />
          </div>
        )}
      </div>

      <CustomModal
        visibility={declineModal}
        toggleVisibility={setDeclineModal}
      >
        <div>
          <h1 className="font-poppins text-purple-normal-hover font-semibold bg-purple-light px-10 py-[33px] text-24">
            Feedback
          </h1>
          <form onSubmit={handleDecline}>
            <div className="mt-16 px-10">
              <label className="font-poppins-medium" htmlFor="comment">Comment</label>
              <div className="mt-4">
                <textarea onChange={(e) => setDeclineComment(e.target.value)} className="w-full border border-[#B2BBC6] rounded-lg p-4" name="comment" id="comment" cols={30} rows={10} placeholder="Enter reason for declining leave request" required />
              </div>

              <div className="flex justify-end mt-[60px] mb-[30px]">
                <CustomButton type="submit" isLoading={isLoading} size={ButtonProperties.SIZES.small} variant={ButtonProperties.VARIANT.primary.name} handleClick={() => { }} title="Done" />
              </div>
            </div>

          </form>

        </div>
      </CustomModal>

      <CustomModal
        visibility={approveModal}
        toggleVisibility={setApproveModal}
      >
        <div>
          <h1 className="font-poppins text-purple-normal-hover font-semibold bg-purple-light px-10 py-[33px] text-24">
            Feedback
          </h1>
          <form onSubmit={handleApproval}>
            <div className="mt-16 px-10">
              <label className="font-poppins-medium" htmlFor="comment">Comment</label>
              <div className="mt-4">
                <textarea onChange={(e) => setApproveComment(e.target.value)} className="w-full border border-[#B2BBC6] rounded-lg p-4" name="comment" id="comment" cols={30} rows={10} placeholder="Enter reason for approving leave request" required />
              </div>

              <div className="flex justify-end mt-[60px] mb-[30px]">
                <CustomButton type="submit" isLoading={isLoading} size={ButtonProperties.SIZES.small} variant={ButtonProperties.VARIANT.primary.name} handleClick={() => { }} title="Done" />
              </div>
            </div>

          </form>

        </div>
      </CustomModal>

    </div>
  )
}

export default AllLeaveRequests