import { Form, Formik } from "formik";
import { ArrowLeft2 } from "iconsax-react";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import FormikCustomInput from "../../../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../../../atoms/CustomButton/CustomButton";
import CustomModal from "../../../../atoms/CustomModal/CustomModal";
import { ButtonProperties } from "../../../../shared/helpers";
import { approveLeave, getLeaveById } from "../../../../../api/leave";
import moment from "moment";
import { clustarkToast } from "../../../../atoms/Toast";
import { NotificationTypes } from "../../../../shared/helpers/enums";
import Loader from "../../../../atoms/Loader";

const LeaveInformation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = location?.state || "";
  const [leaveData, setLeaveData] = useState<any>({});
  const [declineModal, setDeclineModal] = useState<boolean>(false);
  const [approveModal, setApproveModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [approveComment, setApproveComment] = useState<string>("");
  const [declineComment, setDeclineComment] = useState<string>("");

  const fetchLeave = () => {
    setIsFetching(true);
    getLeaveById(id).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setLeaveData(res.data);
      }
    });
  };

  const handleApproval = (e) => {
    e.preventDefault();
    setIsLoading(true);
    approveLeave(id, { approveComment: approveComment })
      .then((res) => {
        if (res.success) {
          clustarkToast(NotificationTypes.SUCCESS, res.message);
          setApproveModal(false);
          fetchLeave();
        }
        setIsLoading(false);
      })
      .catch(() => setIsLoading(false));
  };

  const handleDecline = (e) => {
    e.preventDefault();
    setIsLoading(true);
    approveLeave(id, { declineComment: declineComment })
      .then((res) => {
        if (res.success) {
          clustarkToast(NotificationTypes.SUCCESS, res.message);
          setApproveModal(false);
          fetchLeave();
        }
        setIsLoading(false);
      })
      .catch(() => setIsLoading(false));
  };

  useEffect(() => {
    if (id) {
      fetchLeave();
    } else {
      navigate("/employee/leave");
    }
  }, [id]);

  const initialValues = {
    employeeName:
      leaveData?.staff?.staffPersonalInformations?.first_name +
        " " +
        leaveData?.staff?.staffPersonalInformations?.last_name || "--",
    employeeId: leaveData?.staff?.staff_identification_tag || "--",
    branch: leaveData?.staff?.department?.branch?.name || "--",
    department: leaveData?.staff?.department?.name || "--",
    role: leaveData?.staff?.job_title || "--",
    employmentDate: leaveData?.staff?.start_date
      ? moment(leaveData?.staff?.start_date).format("DD-MM-YYYY")
      : "--",
    employmentType: leaveData?.staff?.employment_type || "--",
    leaveType: leaveData?.leaveType?.name || "--",
    days: leaveData?.duration || "--",
    status: leaveData?.status || "--",
    approvedBy:
      (leaveData?.approvedByUser?.first_name || "") +
      " " +
      (leaveData?.approvedByUser?.last_name || ""),
    applicationDate: leaveData?.created_at
      ? moment(leaveData?.created_at).format("DD-MM-YYYY")
      : "--",
    startDate: leaveData?.start_date
      ? moment(leaveData?.start_date).format("DD-MM-YYYY")
      : "--",
    endDate: leaveData?.end_date
      ? moment(leaveData?.end_date).format("DD-MM-YYYY")
      : "--",
  };

  const handleSubmit = () => {};

  if (isFetching) {
    return (
      <div>
        <Loader />
      </div>
    );
  }

  return (
    <div>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 cursor-pointer"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6">
        <div className="bg-[#F5F5F5] pb-[60px] pt-6 px-7 rounded-bl-[10px] rounded-br-[10px]">
          <div>
            <Formik
              initialValues={initialValues}
              onSubmit={handleSubmit}
              enableReinitialize
            >
              {({ values }) => (
                <Form>
                  <div className="font-poppins-medium rounded-tl-[10px] rounded-tr-[10px] bg-purple-light pl-10 pr-7 py-[30px]">
                    <div className="flex justify-between">
                      <h1 className="text-18 text-purple-dark">
                        Leave Information
                      </h1>
                      {leaveData?.status === "PENDING" && (
                        <div className="flex gap-5">
                          <CustomButton
                            className="!w-[147px] !bg-alert-text-success"
                            handleClick={() => {
                              setApproveModal(true);
                            }}
                            title="Approve"
                          />
                          <CustomButton
                            className="!w-[147px] !bg-alert-text-error"
                            handleClick={() => setDeclineModal(true)}
                            title="Decline"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="bg-white rounded-xl mt-5 py-[72px] px-[30px]">
                    <div className="grid grid-cols-6">
                      <label
                        htmlFor="employeeName"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Employee Name
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="employeeName"
                          name="employeeName"
                          type="text"
                          value={values.employeeName}
                          disabled
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="employeeId"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Employee ID
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="employeeId"
                          name="employeeId"
                          type="text"
                          disabled
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="branch"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Branch
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="branch"
                          name="branch"
                          type="text"
                          disabled
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="department"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Department
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="department"
                          name="department"
                          type="text"
                          disabled
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="role"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Role
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="role"
                          name="role"
                          type="text"
                          disabled
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="employmentDate"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Employment Date
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="employmentDate"
                          name="employmentDate"
                          type="text"
                          disabled
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="employmentType"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Employment Type
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="employmentType"
                          name="employmentType"
                          type="text"
                          disabled
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="leaveType"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Leave Type
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="leaveType"
                          name="leaveType"
                          type="text"
                          disabled
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="applicationDate"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Application Date
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="applicationDate"
                          name="applicationDate"
                          type="text"
                          disabled
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="days"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Days
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="days"
                          name="days"
                          type="text"
                          disabled
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-6 mt-8">
                      <div className="grid grid-cols-3">
                        <label
                          htmlFor="startDate"
                          className="flex items-center whitespace-nowrap text-neutral-normal"
                        >
                          From
                        </label>
                        <div className="col-span-2">
                          <FormikCustomInput
                            id="startDate"
                            name="startDate"
                            type="text"
                            disabled
                          />
                        </div>
                      </div>
                      <div className="flex gap-16">
                        <label
                          htmlFor="endDate"
                          className="flex items-center whitespace-nowrap text-neutral-normal"
                        >
                          To
                        </label>
                        <div className="w-full">
                          <FormikCustomInput
                            id="endDate"
                            name="endDate"
                            type="text"
                            disabled
                          />
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="approvedBy"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Approved By
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="approvedBy"
                          name="approvedBy"
                          type="text"
                          disabled
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="attachment"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Attachment
                      </label>
                      <div className="col-span-5  bg-[#f4f4f4] px-4 py-5 rounded-md">
                        {leaveData?.attachment ? (
                          <div className="flex gap-7">
                            <p>File Attached</p>
                            <p>
                              <a
                                className="text-purple-normal"
                                target="_blank"
                                href={leaveData?.attachment}
                              >
                                View File
                              </a>
                            </p>
                          </div>
                        ) : (
                          <p>--</p>
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="status"
                        className={`flex items-center whitespace-nowrap text-neutral-normal `}
                      >
                        Status
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          inputClassName={`font-semibold ${
                            leaveData?.status === "APPROVED"
                              ? "bg-alert-bg-success text-alert-text-success"
                              : leaveData?.status === "DECLINED"
                              ? "bg-alert-bg-error text-alert-text-error"
                              : leaveData?.status === "PENDING"
                              ? "bg-alert-bg-warning text-alert-text-warning"
                              : "bg-[#B2BBC62B] text-[#868686]"
                          }`}
                          id="status"
                          name="status"
                          type="text"
                          disabled
                        />
                      </div>
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
      <CustomModal visibility={declineModal} toggleVisibility={setDeclineModal}>
        <div>
          <h1 className="font-poppins text-purple-normal-hover font-semibold bg-purple-light px-10 py-[33px] text-24">
            Feedback
          </h1>
          <form onSubmit={handleDecline}>
            <div className="mt-16 px-10">
              <label className="font-poppins-medium" htmlFor="comment">
                Comment
              </label>
              <div className="mt-4">
                <textarea
                  onChange={(e) => setDeclineComment(e.target.value)}
                  className="w-full border border-[#B2BBC6] rounded-lg p-4"
                  name="comment"
                  id="comment"
                  cols={30}
                  rows={10}
                  placeholder="Enter reason for declining leave request"
                  required
                />
              </div>

              <div className="flex justify-end mt-[60px] mb-[30px]">
                <CustomButton
                  type="submit"
                  isLoading={isLoading}
                  size={ButtonProperties.SIZES.small}
                  variant={ButtonProperties.VARIANT.primary.name}
                  handleClick={() => {}}
                  title="Done"
                />
              </div>
            </div>
          </form>
        </div>
      </CustomModal>

      <CustomModal visibility={approveModal} toggleVisibility={setApproveModal}>
        <div>
          <h1 className="font-poppins text-purple-normal-hover font-semibold bg-purple-light px-10 py-[33px] text-24">
            Feedback
          </h1>
          <form onSubmit={handleApproval}>
            <div className="mt-16 px-10">
              <label className="font-poppins-medium" htmlFor="comment">
                Comment
              </label>
              <div className="mt-4">
                <textarea
                  onChange={(e) => setApproveComment(e.target.value)}
                  className="w-full border border-[#B2BBC6] rounded-lg p-4"
                  name="comment"
                  id="comment"
                  cols={30}
                  rows={10}
                  placeholder="Enter reason for approving leave request"
                  required
                />
              </div>

              <div className="flex justify-end mt-[60px] mb-[30px]">
                <CustomButton
                  type="submit"
                  isLoading={isLoading}
                  size={ButtonProperties.SIZES.small}
                  variant={ButtonProperties.VARIANT.primary.name}
                  handleClick={() => {}}
                  title="Done"
                />
              </div>
            </div>
          </form>
        </div>
      </CustomModal>
    </div>
  );
};

export default LeaveInformation;
