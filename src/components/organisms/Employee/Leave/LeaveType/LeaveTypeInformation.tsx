import { Form, Formik } from "formik";
import { ArrowLeft2 } from "iconsax-react";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import FormikCustomInput from "../../../../atoms/CustomInput/FormikCustomInput";
import CustomButton from "../../../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../../shared/helpers";
import { BiSave } from "react-icons/bi";
import { AiOutlineEdit } from "react-icons/ai";
import FormikCustomSelect from "../../../../atoms/CustomInput/FormikCustomSelect";
import CustomDropdownMultiSelect from "../../../../atoms/CustomInput/CustomDropdownMultiSelect";
import { updateLeaveType } from "../../../../../api/leave";
import SuccessModal from "../../../../atoms/CustomModal/SuccessModal";

const LeaveTypeInformation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { data } = location?.state || "";

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isEditActive, setIsEditActive] = useState<boolean>(false);
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [selectedEligibility, setSelectedEligibility] = useState(data?.eligibilityArray || [] );
  const eligibility = ["PROBATION", "FULL_TIME", "CONTRACT", "PART_TIME"];

  const initialValues = {
    name: data?.name || "",
    description: data?.description || "",
    duration: data?.max_days_duration || "",
    paidStatus: data?.paid_status || "",
  };

  const handleSubmit = (values) => {
    const payload = {
      name: values.name,
      description: values.description,
      eligibility: selectedEligibility,
      duration: values.duration,
      paidStatus: values.paidStatus
    };
    setIsLoading(true);
    updateLeaveType(data?.id, payload).then((res) => {
      if(res.success) {
        setIsEditActive(false);
        setShowSuccessModal(true);
      }
      setIsLoading(false);
    })
  };

  useEffect(() => {
    if (!data) {
      navigate("/employee/leave");
    }
  }, []);


  return (
    <div>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 cursor-pointer"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6">
        <div className="bg-[#F5F5F5] pb-[60px] pt-6 px-7 rounded-bl-[10px] rounded-br-[10px]">
          <div>
            <Formik
              initialValues={initialValues}
              onSubmit={handleSubmit}
              enableReinitialize
            >
              {({ setFieldValue, values }) => (
                <Form>
                  <div className="font-poppins-medium rounded-tl-[10px] rounded-tr-[10px] bg-purple-light pl-10 pr-7 py-[30px]">
                    <div className="flex justify-between">
                      <h1 className="text-18 text-purple-dark">
                        Leave type Information
                      </h1>
                      <div className="flex gap-5">
                        <div>
                          {isEditActive ? (
                            <div>
                              <CustomButton
                                leftIcon={<BiSave size={20} />}
                                className="!w-[102px] !h-10"
                                title="Save"
                                type="submit"
                                handleClick={() => {}}
                                isLoading={isLoading}
                                variant={ButtonProperties.VARIANT.primary.name}
                              />
                            </div>
                          ) : (
                            <div
                              className="!w-[87px] !h-10 gap-2 cursor-pointer bg-purple-normal rounded flex justify-center items-center text-white"
                              onClick={() => {
                                setIsEditActive(true);
                              }}
                            >
                              <AiOutlineEdit size={18} /> Edit
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white rounded-xl mt-5 py-[72px] px-[30px]">
                    <div className="grid grid-cols-6">
                      <label
                        htmlFor="name"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Leave type
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="name"
                          name="name"
                          type="text"
                          disabled={!isEditActive}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="description"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Description
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="description"
                          name="description"
                          type="text"
                          disabled={!isEditActive}
                          maxLength={150}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="eligibility"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Eligibility
                      </label>
                      <div className="col-span-5">
                        <CustomDropdownMultiSelect
                          disabled={!isEditActive}
                          options={eligibility}
                          initialSelected={selectedEligibility}
                          handleSelectedItems={(item) =>
                            setSelectedEligibility(item)
                          }
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="duration"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Duration
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="duration"
                          name="duration"
                          type="number"
                          disabled={!isEditActive}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="paidStatus"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Paid status
                      </label>
                      <div className="col-span-5">
                        <FormikCustomSelect
                          options={[
                            { text: "PAID", value: "PAID" },
                            { text: "UNPAID", value: "UNPAID" },
                          ]}
                          name="paidStatus"
                          onChange={(item: { value: string; text: string }) => {
                            //   setLeaveType(item.value);
                            setFieldValue("paidStatus", item.value);
                          }}
                          value={values.paidStatus}
                          disabled={!isEditActive}
                        />
                      </div>
                    </div>

                    {/* <div className="grid grid-cols-6 mt-8">
                      <label
                        htmlFor="createdBy"
                        className="flex items-center whitespace-nowrap text-neutral-normal"
                      >
                        Created By
                      </label>
                      <div className="col-span-5">
                        <FormikCustomInput
                          id="createdBy"
                          name="createdBy"
                          type="text"
                          disabled
                        />
                      </div>
                    </div> */}
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
      <SuccessModal
        visibility={showSuccessModal}
        toggleVisibility={setShowSuccessModal}
        text="Leave type has been updated successfully! You can view leave details in dashboard."
      />
    </div>
  );
};

export default LeaveTypeInformation;
