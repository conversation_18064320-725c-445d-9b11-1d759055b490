import React, { useEffect, useRef, useState } from "react";
import { Eye } from "iconsax-react";
import OrganizationEmptyState from "../../../../atoms/Cards/OrganizationEmptyState";
import CustomTable from "../../../../atoms/CustomTable/CustomTable";
import { ButtonProperties, truncateText } from "../../../../shared/helpers";
import { FaEllipsisV } from "react-icons/fa";
import FilterDropdown from "../../../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../../../shared/hooks";
import CustomButton from "../../../../atoms/CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import { useRecoilState, useRecoilValue } from "recoil";
import CustomModal from "../../../../atoms/CustomModal/CustomModal";
import { getStaff<PERSON>tom, getStaff<PERSON><PERSON><PERSON><PERSON> } from "../../../../../recoil/atom/staff";
import { getAllBranchesAtom } from "../../../../../recoil/atom/organizationAtom";
import { deleteLeaveType, getLeaveTypes } from "../../../../../api/leave";
import { getLeaveTypesAtom } from "../../../../../recoil/atom/leave";
import { PiTrash } from "react-icons/pi";
import { clustarkToast } from "../../../../atoms/Toast";
import { NotificationTypes } from "../../../../shared/helpers/enums";
import Loader from "../../../../atoms/Loader";
import StatusTag from "../../../../atoms/StatusTag";
import LeaveLoader from "../../../../atoms/Skeleton/LeaveLoader";


const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

const AllLeaveType = () => {
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
  const [declineModal, setDeclineModal] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [branchId, setBranchId] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [rowId, setRowId] = useState(0);

  const getStaffValue = useRecoilValue(getStaffAtom);
  const [, setLeaveTypeAtom] = useRecoilState(getLeaveTypesAtom);
  const getEntireBranchesValue = useRecoilValue(getAllBranchesAtom);
  const leaveTypesValue = useRecoilValue(getLeaveTypesAtom);

  const debounceSearch = useRef(debounce((q) => fetchLeaveTypes(q), 2000)).current;


  const branches = getEntireBranchesValue?.data?.map((branch) => ({
    text: branch.name,
    value: branch.id,
  })) || [];

  const fetchLeaveTypes = (q) => {
    setIsFetching(true);
    getLeaveTypes({ search: q }).then((res) => {
      if (res.success) {
        setIsFetching(false);
        setLeaveTypeAtom(res.data);
      }
    });
  };

  console.log('leave');


  const node = useClickOutside(() => {
    setShowDropdown(false);
    setRowId(0);
  });

  useEffect(() => {
    fetchLeaveTypes(searchQuery);
  }, [pageNumber]);

  const handleDeleteLeave = (id) => {
    setIsLoading(true);
    deleteLeaveType(id).then((res) => {
      setIsLoading(false);
      if (res.success) {
        setShowDropdown(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message)
        fetchLeaveTypes(searchQuery);
      }
    });
  };


  const columns = [
    {
      Header: (
        <div className="flex">
          {/* <CustomCheckBox
          checked={groupCheck}
            onChange={(e: any) => handleSelectAllChange(e)}
            customClass="!mr-3"
          />{" "} */}
          <p className="mt-1">Leave Type</p>
        </div>
      ),
      accessor: "name",
      Cell: (row: any) => (
        <div className="flex  items-center">
          {row.cell.value}
        </div>
      ),
    },

    {
      Header: "Description",
      accessor: "description",
      Cell: (row: any) => (
        <p >
          {truncateText(row.cell.value, 50) || "--"}
        </p>
      ),
    },
    {
      Header: "Eligibility",
      accessor: "eligibilityArray",
      Cell: (row: any) => <p> {row.cell.value?.map((item) => (<span>{item}, </span>)) || "--"}</p>
    },
    {
      Header: "Time of duration (days)",
      accessor: "max_days_duration",
      Cell: (row: any) => <p> {row.cell.value || "--"} days</p>,
    },
    {
      Header: "Type",
      accessor: "paid_status",
      Cell: (row: any) => (
        <StatusTag status={row.cell.value} />
      ),
    },

    {
      Header: "",
      accessor: "action",
      Cell: (row: any) => (
        <div className="relative">
          <FaEllipsisV
            onClick={() => {
              setShowDropdown(!showDropdown);
              setRowId(row.cell.row.id);
            }}
            className="text-[#98A2B3] cursor-pointer"
          />
          {showDropdown && row.cell.row.id === rowId && (
            <FilterDropdown>
              <ul className="text-14 text-neutral-dark" ref={node}>
                <li
                  onClick={() =>
                    navigate(`/employee/leave-type-information`, {
                      state: { data: row.cell.row.original },
                    })
                  }
                  className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                >
                  <Eye size={18} />
                  View
                </li>
                {showDeleteWarn ? (
                  <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                    <div className="flex gap-3">
                      <PiTrash size={18} />
                      Are you sure?
                    </div>
                    <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                      <CustomButton isLoading={isLoading} title="Yes" handleClick={() => handleDeleteLeave(row.cell.row.original.id)} className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer" />
                      <span onClick={() => { setShowDeleteWarn(false) }} className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center">No</span>
                    </div>
                  </li>
                ) : (
                  <li onClick={() => setShowDeleteWarn(true)} className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer">
                    <PiTrash size={18} />
                    Delete
                  </li>
                )}
              </ul>
            </FilterDropdown>
          )}
        </div>
      ),
    },
  ];

  if (isFetching && !searchQuery) {
    return (<div>
      <LeaveLoader />
    </div>);
  }

  return (
    <div>
      <div className=" my-10 py-[23px]">
        {leaveTypesValue?.data?.length > 0 || searchQuery ? (
          <CustomTable
            data={leaveTypesValue?.data || []}
            meta={leaveTypesValue?.meta || {}}
            columns={columns}
            // customFilter={
            //   <div className="pb-4">
            //     <div>
            //       <h1 className="border-b border-neutral-light pl-4 py-3">Modify this view</h1>
            //       <Formik initialValues={{branch: ""}} onSubmit={() => {}}>
            //        {({setFieldValue, values}) => (
            //         <Form>
            //           <div className="flex px-4 gap-5 py-[17.5px] border-b">
            //             <p className="whitespace-nowrap">Filter by:</p>
            //             <div className="flex gap-2">
            //               <div className="">
            //                 <FormikCustomSelect
            //                   parentContainer="!h-7"
            //                   options={branches}
            //                   name="branch"
            //                   onChange={(item: { value: string; text: string }) => {
            //                       setBranchId(item.value);
            //                     setFieldValue("branch", item.text);
            //                   }}
            //                   value={values.branch}
            //                 />

            //               </div>
            //               <div className="">
            //               <div className="">
            //                 <FormikCustomCheckboxSelect
            //                 placeholder="Type of Leave"
            //                   parentContainer="!h-7"
            //                   options={[{text: "Annual Leave", value: "Annual Leave"}]}
            //                   name="leaveType"
            //                   onChange={(item: { value: string; text: string }) => {
            //                     //   setLeaveType(item.value);
            //                     setFieldValue("leaveType", item.text);
            //                   }}
            //                   // value={values.leaveType}
            //                 />
            //               </div>
            //               </div>
            //               <div className="">
            //                 <FormikCustomRadioSelect
            //                 placeholder="Status"
            //                   parentContainer="!h-7"
            //                   options={[{text: "Pending", value: "Pending"},{ text: "Approved", value: "Approved"}, {text: "Declined", value: "Declined"}]}
            //                   name="status"
            //                   onChange={(item: { value: string; text: string }) => {
            //                     //   setLeaveType(item.value);
            //                     setFieldValue("status", item.text);
            //                   }}
            //                   // value={values.leaveType}
            //                 />
            //               </div>

            //             </div>
            //           </div>
            //         </Form>
            //        )}
            //       </Formik>
            //       <p className="text-end pt-3 pr-3">Clear Filter</p>
            //     </div>
            //   </div>
            // }
            dropdowmCardClass="!w-[458px]"
            handlePageChange={(pageNumber) => setPageNumber(pageNumber)}
            handleSearch={(search) => {
              setSearchQuery(search);
              debounceSearch(search);
            }}
          // header={
          //   <div>
          //     <h1 className="font-poppins-medium text-purple-normal-active mb-4">
          //       Leave types
          //     </h1>
          //   </div>
          // }
          />
        ) : (
          <div className="flex justify-center items-center py-[120px]">
            <OrganizationEmptyState
              buttonTitle="Add new leave"
              handleClick={() => navigate("/employee/add-leave")}
            />
          </div>
        )}
      </div>

      <CustomModal
        visibility={declineModal}
        toggleVisibility={setDeclineModal}
      >
        <div>
          <h1 className="font-poppins text-purple-normal-hover font-semibold bg-purple-light px-10 py-[33px] text-24">
            Feedback
          </h1>
          <div className="mt-16 px-10">
            <label className="font-poppins-medium" htmlFor="comment">Comment</label>
            <div className="mt-4">
              <textarea className="w-full border border-[#B2BBC6] rounded-lg p-4" name="comment" id="comment" cols={30} rows={10} placeholder="Enter reason for declining leave request" />
            </div>

            <div className="flex justify-end mt-[60px] mb-[30px]">
              <CustomButton className="w-[]" size={ButtonProperties.SIZES.small} variant={ButtonProperties.VARIANT.primary.name} handleClick={() => { }} title="Done" />
            </div>
          </div>

        </div>
      </CustomModal>

    </div>
  )
}

export default AllLeaveType