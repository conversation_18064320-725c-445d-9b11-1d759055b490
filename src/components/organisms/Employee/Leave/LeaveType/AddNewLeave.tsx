import React, { useState } from "react";
import FormikCustomSelect from "../../../../atoms/CustomInput/FormikCustomSelect";
import { useNavigate } from "react-router-dom";
import { ArrowLeft2 } from "iconsax-react";
import FormikCustomInput from "../../../../atoms/CustomInput/FormikCustomInput";
import { Form, Formik } from "formik";
import * as yup from "yup";
import { ButtonProperties, errorMessages } from "../../../../shared/helpers";
import CustomButton from "../../../../atoms/CustomButton/CustomButton";
import SuccessModal from "../../../../atoms/CustomModal/SuccessModal";
import { clustarkToast } from "../../../../atoms/Toast";
import { NotificationTypes } from "../../../../shared/helpers/enums";
import { createLeaveType } from "../../../../../api/leave";
import CustomDropdownMultiSelect from "../../../../atoms/CustomInput/CustomDropdownMultiSelect";

interface AddNewLeaveProps {
  name: string;
  description: string;
  // eligibility: string | string[];
  duration: string;
  leaveType: string;
}

const addLeaveSchema = yup.object().shape({
  name: yup.string().required(errorMessages.required),
  description: yup.string().required(errorMessages.required),
  // eligibility: yup.string().required(errorMessages.required),
  duration: yup.string().required(errorMessages.required),
  leaveType: yup.string().required(errorMessages.required),
});

const AddNewLeave = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedEligibility, setSelectedEligibility] = useState([]);
  const eligibility = ["PROBATION", "FULL_TIME", "CONTRACT", "PART_TIME"];

  const initialValues = {
    name: "",
    description: "",
    // eligibility: selectedEligibility || "",
    duration: "",
    leaveType: "",
  };


  const handleSubmit = (values) => {
    if (!selectedEligibility || selectedEligibility.length === 0) {
      clustarkToast(
        NotificationTypes.ERROR,
        "Please select employment eligibility"
      );
    } else {
      setIsLoading(true);
      const payload = {
        name: values.name,
        description: values.description,
        eligibility: selectedEligibility,
        duration: values.duration,
        paidStatus: values.leaveType,
      };
      createLeaveType(payload)
        .then((res) => {
          if (res.success) {
            setShowSuccessModal(true);
          }
          setIsLoading(false);
        })
    }
  };

  return (
    <div>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 cursor-pointer w-fit"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6">
        <h1 className="font-poppins-medium text-18 rounded-tl-[10px] rounded-tr-[10px] text-purple-dark bg-purple-light pl-10 py-[33px]">
          Add Leave type
        </h1>
        <div className="bg-[#F5F5F5] pt-[15px] pb-[60px] pl-10 pr-16 rounded-bl-[10px] rounded-br-[10px]">
          <Formik<AddNewLeaveProps>
            initialValues={initialValues}
            onSubmit={handleSubmit}
            validationSchema={addLeaveSchema}
          >
            {({ values, setFieldValue }) => (
              <Form>
                <div>
                  <FormikCustomInput
                    label="Leave name *"
                    id="name"
                    name="name"
                    placeholder="enter the title of the leave"
                    type="text"
                    inputClassName="!bg-transparent"
                    maxLength={50}
                  />
                </div>
                <div className="mt-8">
                  <FormikCustomInput
                    label="Description *"
                    id="description"
                    name="description"
                    placeholder="describe the purpose of this leave"
                    type="text"
                    inputClassName="!bg-transparent"
                    maxLength={120}
                  />
                </div>
                <div className="mt-8">
                  <FormikCustomInput
                    label="Duration *"
                    id="duration"
                    name="duration"
                    placeholder="enter the number of working days e.g 22"
                    type="number"
                    inputClassName="!bg-transparent"
                  />
                </div>
                <div className="mt-8">
                  <div className="text-16 mb-4">
                    <label htmlFor="days">Employee eligibility *</label>
                  </div>
                  <CustomDropdownMultiSelect
                    options={eligibility}
                    handleSelectedItems={(item) => setSelectedEligibility(item)}
                  />
                </div>

                <div className="mt-8">
                  <FormikCustomSelect
                    label="Leave type *"
                    options={[
                      { text: "Paid", value: "PAID" },
                      { text: "Unpaid", value: "UNPAID" },
                    ]}
                    name="leaveType"
                    onChange={(item: { value: string; text: string }) => {
                      setFieldValue("leaveType", item.value);
                    }}
                    value={values.leaveType}
                  />
                </div>
                <div className="mt-[120px] flex justify-end">
                  <CustomButton
                    type="submit"
                    title="Add"
                    handleClick={() => { }}
                    isLoading={isLoading}
                    size={ButtonProperties.SIZES.small}
                    variant={ButtonProperties.VARIANT.primary.name}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
      <SuccessModal
        visibility={showSuccessModal}
        toggleVisibility={setShowSuccessModal}
        route="/employee/leave?activeTab=1&tab=LeaveType"
        text="Your new leave type has been added successfully! You can view leave details in dashboard."
      />
    </div>
  );
};

export default AddNewLeave;
