import React, { useEffect, useState } from "react";
import StatisticsCard from "../../../atoms/Cards/StatisticsCard";
import { DocumentText, Eye } from "iconsax-react";
import { FiPlus } from "react-icons/fi";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { useNavigate, useSearchParams } from "react-router-dom";
import { RiUserReceivedLine } from "react-icons/ri";
import { AiOutlineStop } from "react-icons/ai";
import AllLeaveRequests from "./LeaveRequests/AllLeaveRequests";
import AllLeaveType from "./LeaveType/AllLeaveType";
import { getLeaveAnalytics } from "../../../../api/leave";
import Loader from "../../../atoms/Loader";
import { useRecoilState } from "recoil";
import { getLeaveAnalyticsAtom } from "../../../../recoil/atom/leave";
import LeaveLoader from "../../../atoms/Skeleton/LeaveLoader";


const Leave = () => {
  const navigate = useNavigate();
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("activeTab");
  const [activeTab, setActiveTab] = useState<number>(0);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [leaveAnalytic, setLeaveAnalytic] = useRecoilState(getLeaveAnalyticsAtom)

  const fetchLeaveAnalytics = () => {
    setIsFetching(true);
    getLeaveAnalytics().then((res) => {
      if (res.success) {
        setIsFetching(false);
        setLeaveAnalytic(res.data);
      }
    });
  };

  useEffect(() => {
    fetchLeaveAnalytics();
  }, []);

  const leaveTabs = [
    {
      title: "Leave requests",
      component: <AllLeaveRequests />,
      name: "LeaveRequests"
    },
    {
      title: "Leave type",
      component: <AllLeaveType />,
      name: "LeaveType"
    },
  ];

  const leaveStats = [
    {
      title: "Total leaves",
      value: leaveAnalytic?.total_requested_leave,
      icon: <RiUserReceivedLine size={24} />,
      iconBackgroundColor: '#3730A399',
      valueText: "Requested Leave",
      cardBackgroundColor: "#EEF2FFCC",
    },
    {
      title: "Approved leaves",
      value: leaveAnalytic?.total_approved_leave,
      icon: <DocumentText size={24} />,
      iconBackgroundColor: '#B3AA0199',
      valueText: "Leave",
      cardBackgroundColor: "#FDFFE8CC",
    },
    {
      title: "Declined leaves",
      value: leaveAnalytic?.total_declined_leave,
      icon: <AiOutlineStop size={24} />,
      iconBackgroundColor: '#09778399',
      valueText: "Leave",
      cardBackgroundColor: "#EBFDFFCC",
    },
  ];

  useEffect(() => {
    setActiveTab(queryParam || 0)

  }, [queryParam, activeTab]);

  if (isFetching) {
    return (<div>
      <LeaveLoader />
    </div>);
  }



  return (
    <>
      <div>
        {/* <h1 className="font-poppins font-semibold text-24">Leave</h1> */}
        <div className=" grid grid-cols-3 gap-5 ">
          {leaveStats?.map((item, index) => (
            <div key={index}>
              <StatisticsCard
                backgroundColor={item?.cardBackgroundColor}
                key={index}
                title={item.title}
                value={item.value}
                icon={item.icon}
                iconBackgroundColor={item?.iconBackgroundColor}
                valueText={item.valueText}
              />
            </div>
          ))}
        </div>

        <div className="mt-10 px-1 bg-[#F4F4F6] flex justify-between">
          <div className="flex gap-10 py-1 px-1 justify-center items-center  bg-[#F4F4F6] rounded-md">

            {leaveTabs.map((item, index) => (
              <div
                key={index}
                onClick={() => { setActiveTab(index); navigate(`/employee/leave?activeTab=${index}&tab=${item.name}`) }}
                className={`cursor-pointer px-5 py-2 rounded  ${activeTab == index ? "bg-white" : "bg-none"}`}>
                <p className={`text-neutral-normal font-poppins-medium  px-5 text-center text-16 ${activeTab == index ? "text-purple-normal" : "text-gray-950"} `}> {item.title}</p>
              </div>
            ))}
          </div>
          <div className="flex justify-center items-center">
            <CustomButton
              className="!w-[150px] !bg-purple-normal !text-white !border-none !font-normal !font-poppins-medium shadow-md !h-10"
              isTransparent={true}
              handleClick={() => {
                navigate("/employee/add-leave");
              }}
              leftIcon={<FiPlus className="ml-3" size={20} />}
              title="Add leave type"
            />
          </div>
        </div>

        <div>
          {leaveTabs[activeTab].component}
        </div>

      </div>
    </>
  );
};

export default Leave;
