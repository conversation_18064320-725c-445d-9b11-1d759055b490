import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { FaDownload, FaUpload } from "react-icons/fa6";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import * as yup from "yup"
import { FaEllipsisV, } from "react-icons/fa";
import CustomTable from "../../../atoms/CustomTable/CustomTable";
import { clustarkToast } from "../../../atoms/Toast";
import { NotificationTypes } from "../../../shared/helpers/enums";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import { Form, Formik } from "formik";
import FormikCustomPhoneInput from "../../../atoms/CustomInput/FormikCustomPhoneInput";
import { Country, State } from "country-state-city";
import { geEmployeeDepartmentAtom, getBulkEmployeeAtom, getDetailEmployeeAtom, getEmployeeBranchAtom } from "../../../../recoil/atom/bulkEmployee";
import { useRecoilState, useRecoilValue } from "recoil";
import OrganizationEmptyState from "../../../atoms/Cards/OrganizationEmptyState";
import FormikSelectCurrency from "../../../atoms/CustomInput/FormikSelectCurrency";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import moment from "moment";
import { UserEdit } from "iconsax-react";
import FilterDropdown from "../../../atoms/Cards/FilterDropdown";
import useClickOutside from "../../../shared/hooks";
import { PiTrash } from "react-icons/pi";
import Loader from "../../../atoms/Loader";
import { AiOutlineWarning } from "react-icons/ai";
import AddBulkModal from "./AddBulkModal";
import { getAllBranchesAtom, getDepartmentsAtom } from "../../../../recoil/atom/organizationAtom";
import { createBulkStaff } from "../../../../api/staff";


const AddBulkEmployee = () => {

    let countries = Country.getAllCountries().map((country) => ({
        text: country.name,
        value: country.isoCode,
        code: country.phonecode,
    }));

    const navigate = useNavigate();
    const [showDetails, setShowDetails] = useState<boolean>(false);
    const [iserror, setIserror] = useState<boolean>(false);
    const [selectedCountry, setSelectedCountry] = useState<string>("");
    const [, setSelectedState] = useState<string>("");
    const [, setBulkEmployeeAtom] = useRecoilState(getBulkEmployeeAtom);
    const getBulkEmployeeValue = useRecoilValue(getBulkEmployeeAtom);
    const getBulkEmployeeBranchValue = useRecoilValue(getEmployeeBranchAtom);
    const getBulkEmployeeDepartmentValue = useRecoilValue(geEmployeeDepartmentAtom);
    const [, setDetailEmployeeAtom] = useRecoilState(getDetailEmployeeAtom);
    const getDetailEmployeeValue = useRecoilValue(getDetailEmployeeAtom);
    const [showDeleteWarn, setShowDeleteWarn] = useState<boolean>(false);
    const [showDropdown, setShowDropdown] = useState<boolean>(false);
    const [bulkModal, setBulkModal] = useState(false);
    const [rowId, setRowId] = useState(0);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [opendiscard, setOpendiscard] = useState<boolean>(false);
    const getEntireBranchesValue = useRecoilValue(getAllBranchesAtom);
    const getDepartments = useRecoilValue(getDepartmentsAtom);


    // const defaultCurrency = getLoggedUser.businesses[0]?.default_currency;

    let states = State.getStatesOfCountry(selectedCountry || getDetailEmployeeValue?.country).map((state) => ({
        text: state.name,
        value: state.isoCode,
    }));

    const branches = getEntireBranchesValue?.data?.map((branch) => ({
        text: branch.name,
        value: branch.id,
    })) || [];

    const departments = getDepartments?.data?.map((item) => ({
        text: item.name + " - " + item.branch.name,
        value: item.id
    }));

    const editEmployeeSchema = yup.object().shape({
        email: yup.string().email('enter a valid email').required(errorMessages.required),
        first_name: yup.string().required(errorMessages.required),
        last_name: yup.string().required(errorMessages.required),
        phone: yup.string().required(errorMessages.required),
        gender: yup.string().required(errorMessages.required),
        state: yup.string().required(errorMessages.required),
        country: yup.string().required(errorMessages.required),
        employment_type: yup.string().required(errorMessages.required),
        employmentDate: yup.string().required(errorMessages.required),
        salary: yup.string().required(errorMessages.required),
        jobTitle: yup.string().required(errorMessages.required),
        currency: yup.string().required(errorMessages.required),
        branch_id: yup.string().required(errorMessages.required),
        department_id: yup.string(),
    });

    const initialValues = {
        first_name: getDetailEmployeeValue?.first_name === "Required !" ? "" : getDetailEmployeeValue?.first_name || "",
        last_name: getDetailEmployeeValue?.last_name === "Required !" ? "" : getDetailEmployeeValue?.last_name || "",
        email: getDetailEmployeeValue?.email === "Required !" ? "" : getDetailEmployeeValue?.email || "",
        phone: getDetailEmployeeValue?.phone === "Required !" ? "" : getDetailEmployeeValue?.phone || "",
        gender: getDetailEmployeeValue?.gender === "Required !" ? "" : getDetailEmployeeValue?.gender || "",
        state: getDetailEmployeeValue?.state === "Required !" ? "" : getDetailEmployeeValue?.state || "",
        country: getDetailEmployeeValue?.country === "Required !" ? "" : getDetailEmployeeValue?.country || "",
        employment_type: getDetailEmployeeValue?.employment_type === "Required !" ? "" : getDetailEmployeeValue?.employment_type || "",
        employmentDate: getDetailEmployeeValue?.employmentDate === "Required !" ? "" : getDetailEmployeeValue?.employmentDate || "",
        endDate: getDetailEmployeeValue?.endDate === "Required !" ? "" : getDetailEmployeeValue?.endDate || "",
        salary: getDetailEmployeeValue?.salary === "Required !" ? "" : getDetailEmployeeValue?.salary || "",
        jobTitle: getDetailEmployeeValue?.jobTitle === "Required !" ? "" : getDetailEmployeeValue?.jobTitle || "",
        currency: getDetailEmployeeValue?.currency === "Required !" ? "" : getDetailEmployeeValue?.currency || "",
        branch_id: getDetailEmployeeValue?.branch_id === "Required !" ? "" : getDetailEmployeeValue?.branch_id,
        department_id: getDetailEmployeeValue?.department_id === "Required !" ? "" : getDetailEmployeeValue?.department_id,

    };

    const node = useClickOutside(() => {
        setShowDropdown(false);
        setRowId(0);
    });

    const isRequiredCheck = (data: any) => {

        if (!data || !Array.isArray(data) || data.length === 0) {
            setIserror(true)
            return;
        }

        const hasRequiredField = data.some(obj =>
            Object.values(obj).includes("Required !")
        );

        if (hasRequiredField) {
            setIserror(true)
        } else {
            setIserror(false)
        }
    }

    const discardUpload = () => {
        setBulkEmployeeAtom('');
        setDetailEmployeeAtom('');
        navigate('/employee/all-staffs')
    }

    const viewEmployee = (data: any) => {
        setShowDetails(true);
        setDetailEmployeeAtom(data)
    }

    const dateCheck = (date: any) => {
        if (date === 'Required !') {
            return <p className="text-red-800">{date}</p>;
        }
        if (date === 'Not required') {
            return <p className="text-yellow-600">{date}</p>;
        }
        if (date === '' || date === null) {
            return <p className="text-yellow-600">Not required</p>;
        }
        return <p>{moment(date).format("DD-MM-YYYY") || "--"}</p>;
    };

    const salaryCheck = (salary: any) => {
        if (salary === 'Required !') {
            return <p className="text-red-800">{salary}</p>;
        }
        if (salary === 'Not required') {
            return <p className="text-yellow-600">{salary}</p>;
        }
        return !isNaN(Number(salary)) ? Number(salary).toLocaleString() : "--";
    };

    useEffect(() => {
        isRequiredCheck(getBulkEmployeeValue)
    })

    const editEmployee = (item: any, values: any) => {

        const updatedTemplate = getBulkEmployeeValue.map((employee: any) =>
            employee.email === item.email ? { ...employee, ...values } : employee
        );

        const requiredFields = [
            'first_name',
            'last_name',
            'email',
            'phone',
            'gender',
            'state',
            'country',
            'employment_type',
            'salary',
            'jobTitle',
            'currency',
        ];

        const requiredDateFields = [
            'employmentDate',
        ];

        for (const field of requiredDateFields) {
            if (values[field] === 'Required !' || values[field] === '' || !moment(values[field], moment.ISO_8601, true).isValid()) {
                clustarkToast(NotificationTypes.ERROR, `${field.replace('_', ' ')} is required and must be a valid date`);
                return;
            }
        }

        if ((values.employment_type === 'CONTRACT' || values.employment_type === 'PROBATION') &&
            (!values.endDate || values.endDate === 'Required !' || !moment(values.endDate, moment.ISO_8601, true).isValid())) {
            clustarkToast(NotificationTypes.ERROR, "End date is required and must be a valid date for contract and probation employments");
            return;
        }

        for (const field of requiredFields) {
            if (values[field] === 'Required !') {
                clustarkToast(NotificationTypes.ERROR, `${field.replace('_', ' ')} is required`);
                return;
            }
        }

        setBulkEmployeeAtom(updatedTemplate);
        setShowDetails(false);
        clustarkToast(NotificationTypes.SUCCESS, "Employee details updated successfully");
        isRequiredCheck(updatedTemplate)

    };

    const allEmploymentType = [
        { text: 'Probation', value: 'PROBATION' },
        { text: 'Full time', value: 'FULL_TIME' },
        { text: 'Part time', value: 'PART_TIME' },
        { text: 'Contract', value: 'CONTRACT' },
    ]

    let employmentType = allEmploymentType.map((item) => ({
        text: item.text,
        value: item.value,
    }));

    const deleteEmployee = (item: any) => {
        const updatedEmployees = getBulkEmployeeValue.filter((employee: any) => employee.email !== item.email);
        setBulkEmployeeAtom(updatedEmployees);
        isRequiredCheck(getBulkEmployeeValue)
        setShowDeleteWarn(false);
        setShowDropdown(!showDropdown);
        clustarkToast(NotificationTypes.SUCCESS, "Employee deleted successfully");
    };

    const addBulkEmployee = () => {

        const updatedEmployees = getBulkEmployeeValue.map((employee: any) => {
            const updatedEmployee = { ...employee };
            for (const key in updatedEmployee) {
                if (updatedEmployee[key] === 'Not required' || updatedEmployee[key] === null) {
                    updatedEmployee[key] = '';
                }
            }

            return {
                first_name: employee.first_name,
                last_name: employee.last_name,
                phone_number: employee.phone,
                email: employee.email,
                gender: employee.gender,
                country: employee.country,
                state: employee.state,
                employment_type: employee.employment_type,
                start_date: moment(updatedEmployee.employmentDate).format('YYYY-MM-DD HH:mm:ss'),
                end_date: moment(updatedEmployee.endDate).format('YYYY-MM-DD HH:mm:ss'),
                job_title: employee.jobTitle,
                salary_currency: employee.currency,
                salary_amount: employee.salary,
                branch_id: getDetailEmployeeValue.branch_id,
                department_id: getDetailEmployeeValue.department_id,
            };
        });

        const payload = { employees: updatedEmployees };

        setIsLoading(true)

        createBulkStaff(payload).then((res) => {

            if (res?.success) {
                clustarkToast(NotificationTypes.SUCCESS, res.message);
                discardUpload()
            } else {
                setIsLoading(false);
            }

        });

    };


    const columns = [
        {
            Header: '',
            accessor: 'caution',
            Cell: (row: any) => {
                const hasRequired = Object.values(row.cell.row.original).some(value => value === "Required !");

                return hasRequired ? (
                    <div className="relative">
                        <AiOutlineWarning className="text-purple-dark-hover text-[1.1rem] cursor-pointer" />
                    </div>
                ) : null;
            },
        },
        {
            Header: 'First name',
            accessor: 'first_name',
            Cell: (row: any) => (
                <p className="whitespace-nowrap flex gap-2">
                    {row.cell?.value === 'Required !' ? <p className="text-red-800">{row.cell?.value} </p> : row.cell?.value}
                </p>
            ),
        },
        {
            Header: 'Last name',
            accessor: 'last_name',
            Cell: (row: any) => (
                <p className="whitespace-nowrap flex gap-2">
                    {row.cell?.value === 'Required !' ? <p className="text-red-800">{row.cell?.value} </p> : row.cell?.value}
                </p>
            ),

        },
        {
            Header: 'Email',
            accessor: 'email',
            Cell: (row: any) => (
                <p className="whitespace-nowrap flex gap-2">
                    {row.cell?.value === 'Required !' ? <p className="text-red-800">{row.cell?.value} </p> : row.cell?.value}
                </p>
            ),
        },
        {
            Header: 'Phone',
            accessor: 'phone',
            Cell: (row: any) => (
                <p className="whitespace-nowrap flex gap-2">
                    {row.cell?.value === 'Required !' ? <p className="text-red-800">{row.cell?.value} </p> : row.cell?.value}
                </p>
            ),
        },
        {
            Header: 'Gender',
            accessor: 'gender',
            Cell: (row: any) => (
                <p className="whitespace-nowrap flex gap-2">
                    {row.cell?.value === 'Required !' ? <p className="text-red-800">{row.cell?.value} </p> : row.cell?.value}
                </p>
            ),
        },
        {
            Header: 'Country',
            accessor: 'country',
            Cell: (row: any) => (
                <p className="whitespace-nowrap flex gap-2">
                    {row.cell?.value === 'Required !' ? <p className="text-red-800">{row.cell?.value} </p> : row.cell?.value}
                </p>
            ),

        },
        {
            Header: 'State',
            accessor: 'state',
            Cell: (row: any) => (
                <p className="whitespace-nowrap flex gap-2">
                    {row.cell?.value === 'Required !' ? <p className="text-red-800">{row.cell?.value} </p> : row.cell?.value}
                </p>
            ),

        },
        {
            Header: 'Employment type',
            accessor: 'employment_type',
            Cell: (row: any) => (
                <p className="whitespace-nowrap flex gap-2">
                    {row.cell?.value === 'Required !' ? <p className="text-red-800">{row.cell?.value} </p> : row.cell?.value}
                </p>
            ),
        },
        {
            Header: 'Employment date',
            accessor: 'employmentDate',
            Cell: (row: any) => dateCheck(row.cell?.value),
        },
        {
            Header: 'End date',
            accessor: 'endDate',
            Cell: (row: any) => dateCheck(row.cell?.value),
        },
        {
            Header: 'Job title',
            accessor: 'jobTitle',
            Cell: (row: any) => (
                <p className="whitespace-nowrap flex gap-2">
                    {row.cell?.value === 'Required !' ? <p className="text-red-800">{row.cell?.value} </p> : row.cell?.value}
                </p>
            ),
        },
        {
            Header: 'Salary',
            accessor: 'salary',
            Cell: (row: any) => {
                const currency = row.cell?.row?.original?.currency;
                return (
                    <p className="whitespace-nowrap flex gap-2">
                        {currency && (
                            <span className="text-[0.8rem] bg-purple-dark text-white px-1 rounded mb-1">
                                {currency}
                            </span>
                        )}
                        {salaryCheck(row.cell?.value)}
                    </p>
                );
            },
        },
        {
            Header: "",
            accessor: "action",
            Cell: (row: any) => (
                <div className="relative">
                    <FaEllipsisV
                        onClick={() => {
                            setShowDropdown(!showDropdown);
                            setRowId(row.cell.row.id);
                        }}
                        className="text-[#98A2B3] cursor-pointer"
                    />
                    {showDropdown && row.cell.row.id === rowId && (
                        <FilterDropdown>
                            <ul className="text-14 text-neutral-dark" ref={node}>
                                <li
                                    onClick={() => { viewEmployee(row.cell.row.original) }}
                                    className="flex gap-3 pl-2.5 border-b py-2.5 cursor-pointer"
                                >
                                    <UserEdit size={18} />
                                    Edit
                                </li>
                                {showDeleteWarn ? (
                                    <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                                        <div className="flex gap-3">
                                            <PiTrash size={18} />
                                            Are you sure?
                                        </div>
                                        <div className="grid grid-cols-2 gap-5 mt-8 ml-2 mr-4">
                                            <CustomButton
                                                title="Yes"
                                                handleClick={() => { deleteEmployee(row.cell.row.original) }
                                                }
                                                className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer hover:!text-white"
                                            />
                                            <span
                                                onClick={() => {
                                                    setShowDeleteWarn(false);
                                                }}
                                                className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center"
                                            >
                                                No
                                            </span>
                                        </div>
                                    </li>
                                ) : (
                                    <li
                                        onClick={() => setShowDeleteWarn(true)}
                                        className="flex gap-3 pl-2.5 border-b pt-2.5 pb-[14px] text-alert-text-error cursor-pointer"
                                    >
                                        <PiTrash size={18} />
                                        Delete
                                    </li>
                                )}
                            </ul>
                        </FilterDropdown>
                    )}
                </div>
            ),
        },


    ]


    if (isLoading) {
        return <div>
            <Loader />
        </div>
    };



    return (

        <div className="">
            <div className="">
                <div className="pt-10 gap-2 flex justify-between px-2 mb-10">

                    <a href="https://res.cloudinary.com/dln68igvv/raw/upload/v1738584426/BulkEmployee_1_yxw3j5.xlsx" download>
                        <CustomButton
                            handleClick={() => {}}
                            className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
                            isTransparent={true}
                            leftIcon={<FaDownload className="ml-3" size={20} />}
                            title="Download template"
                        />
                    </a>

                    <div className="relative">
                        <CustomButton
                            className="!w-[210px] !bg-white !border-none !font-normal !font-poppins-medium shadow-md"
                            isTransparent={true}
                            handleClick={() => {
                                { setBulkModal(true) }
                            }}
                            leftIcon={<FaUpload className="ml-3" size={20} />}
                            title="Upload template"
                        />
                    </div>
                </div>

                {
                    getBulkEmployeeValue ?
                        <div className="">
                            <div className="">
                                <CustomTable
                                    data={getBulkEmployeeValue || []}
                                    columns={columns}
                                    hideSearch
                                />
                            </div>
                            <div className="">
                                <div className="flex justify-end gap-5">
                                    <div className="">
                                        {
                                            !opendiscard ?
                                                <CustomButton
                                                    className="mt-[86px] text-16 !text-white !bg-alert-text-error border-none "
                                                    type="submit"
                                                    title="Discard"
                                                    handleClick={() => setOpendiscard(!opendiscard)}
                                                    size={ButtonProperties.SIZES.small}
                                                    variant={ButtonProperties.VARIANT.primary.name}
                                                /> :
                                                <ul className="text-14 text-neutral-dark flex py-[30px] mt-[30px]" ref={node}>
                                                    <li className="pl-2.5 border-b pt-2.5 pb-[14px] text-purple-normal ">
                                                        <div className="text-center flex ">
                                                            <div className="flex gap-3">
                                                                <PiTrash size={18} />
                                                                Are you sure?
                                                            </div>
                                                        </div>
                                                        <div className="grid grid-cols-2 mt-8 ml-2 mr-4 w-[180px]">
                                                            <CustomButton
                                                                title="Yes"
                                                                handleClick={discardUpload}
                                                                className="border text-center !h-0 py-2 !text-neutral-normal rounded !border-neutral-normal text-12 cursor-pointer hover:!text-white !w-[70px]"
                                                            />
                                                            <span
                                                                onClick={() => {
                                                                    setOpendiscard(false);
                                                                }}
                                                                className="text-center py-2 rounded bg-alert-text-error text-white text-12 cursor-pointer flex justify-center items-center w-[70px]"
                                                            >
                                                                No
                                                            </span>
                                                        </div>
                                                    </li>
                                                </ul>
                                        }
                                    </div>
                                    <div className="">
                                        <CustomButton
                                            className="mt-[86px] text-16"
                                            isDisabled={iserror}
                                            type="submit"
                                            title="Proceed"
                                            handleClick={addBulkEmployee}
                                            size={ButtonProperties.SIZES.small}
                                            variant={ButtonProperties.VARIANT.primary.name}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div> :
                        (
                            <div className="flex justify-center items-center py-[120px]">
                                <OrganizationEmptyState text="No record found. When there is a record, they will appear here." />
                            </div>
                        )
                }

            </div>

            <CustomModal
                visibility={showDetails}
                toggleVisibility={setShowDetails}
            >
                <div>
                    <Formik
                        initialValues={initialValues}
                        onSubmit={(values) => { editEmployee(getDetailEmployeeValue, values) }}
                        validationSchema={editEmployeeSchema}
                        enableReinitialize
                    >
                        {({ values, setFieldValue }) => (
                            <Form>
                                <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[10px] rounded-tr-[10px]">
                                    <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                                        Edit employee
                                    </h1>
                                </div>
                                <div className="px-6 py-10">
                                    <div className="grid grid-cols-2 gap-8 mt-8">
                                        <div>
                                            <FormikCustomInput
                                                label="First name *"
                                                id="first_name"
                                                name="first_name"
                                                placeholder="enter first name"
                                                type="text"
                                                inputClassName="!bg-transparent"
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomInput
                                                label="Last name *"
                                                id="last_name"
                                                name="last_name"
                                                placeholder="enter last name"
                                                type="text"
                                                inputClassName="!bg-transparent"

                                            />
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-8 mt-8">
                                        <div>
                                            <FormikCustomInput
                                                label="Email *"
                                                id="email"
                                                name="email"
                                                placeholder="enter email"
                                                type="text"
                                                inputClassName="!bg-transparent"
                                            />
                                        </div>
                                        <div >
                                            <FormikCustomSelect
                                                label="Select employment type*"
                                                placeholder="Select option"
                                                optionsParentClassName="text-12"
                                                value={values.employment_type}
                                                name="employment_type"
                                                options={employmentType}
                                                onChange={(item: { value: string; text: string }) => {
                                                    setFieldValue("employment_type", item.value);
                                                }}

                                            />
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-8 px-2 mt-8">
                                        <div>
                                            <FormikCustomSelect
                                                label="Branch *"
                                                name="branch_id"
                                                id="branch_id"
                                                optionsParentClassName="!capitalize"
                                                options={branches}
                                                value={getBulkEmployeeBranchValue}
                                                disabled
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomSelect
                                                label="Department "
                                                options={departments}
                                                name="department_id"
                                                optionsParentClassName="!capitalize"
                                                value={getBulkEmployeeDepartmentValue}
                                                disabled
                                            />
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-8 mt-8">
                                        <div>
                                            <FormikCustomPhoneInput
                                                label="Phone number *"
                                                id="phone"
                                                name="phone"
                                                placeholder="enter phone number"
                                                className="!bg-transparent"
                                                value={values.phone}
                                                onChange={(value: string) => {
                                                    setFieldValue("phone", value);
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomSelect
                                                label="Gender *"
                                                options={[
                                                    { text: "Male", value: "Male" },
                                                    { text: "Female", value: "Female" },
                                                ]}
                                                name="gender"
                                                value={values.gender}
                                                placeholder="select gender"
                                                onChange={(item: { value: string; text: string }) => {
                                                    setFieldValue("gender", item.value);
                                                }}
                                            />
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-8 mt-8">
                                        <div>
                                            <FormikCustomSelect
                                                label="Country *"
                                                options={countries}
                                                name="country"
                                                value={getDetailEmployeeValue?.country || values.country}
                                                onChange={(item: { value: string; text: string }) => {
                                                    setSelectedCountry(item.value);
                                                    setFieldValue("country", item.text);
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomSelect
                                                label="State *"
                                                options={states}
                                                name="state"
                                                onChange={(item: { value: string; text: string }) => {
                                                    setSelectedState(item.value);
                                                    setFieldValue("state", item.text);
                                                }}
                                                value={getDetailEmployeeValue?.state || values.state}
                                            />
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-8 mt-8">
                                        <div>
                                            <FormikCustomInput
                                                label="Job title *"
                                                id="jobTitle"
                                                name="jobTitle"
                                                placeholder="User Researcher"
                                                type="text"
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                        <div>
                                            <FormikSelectCurrency
                                                label="Salary *"
                                                name="salary"
                                                value={values.currency}
                                                onChange={(item: { value: string; text: string }) => {
                                                    setFieldValue("currency", item?.text);
                                                }}
                                                handleAmount={(amount) => setFieldValue("salary", amount)}
                                                amountValue={getDetailEmployeeValue.salary}
                                            />
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-8 mt-8">
                                        <div className="">
                                            <FormikCustomDate
                                                label="Employment date *"
                                                value={moment(values.employmentDate)}
                                                inputClassName="border bg-transparent"
                                                name="employmentDate"
                                                onChange={(date) => {
                                                    setFieldValue("employmentDate", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomDate
                                                label="Employment end date "
                                                value={moment(values.endDate)}
                                                inputClassName="border bg-transparent"
                                                name="endDate"
                                                onChange={(date) => {
                                                    setFieldValue("endDate", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                                                }}
                                            />
                                        </div>
                                    </div>

                                    <div className="mt-10 flex justify-end">
                                        <CustomButton
                                            type="submit"
                                            title="Update"
                                            handleClick={() => { }}
                                            size={ButtonProperties.SIZES.small}
                                            variant={ButtonProperties.VARIANT.primary.name}
                                        />
                                    </div>
                                </div>
                            </Form>
                        )}
                    </Formik>
                </div>
            </CustomModal>

            <CustomModal
                visibility={bulkModal}
                toggleVisibility={setBulkModal}
            >
                <AddBulkModal
                    toggle={setBulkModal}
                />
            </CustomModal>


        </div>

    )
}

export default AddBulkEmployee




