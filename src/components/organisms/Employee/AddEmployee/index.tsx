import { ArrowLeft2 } from 'iconsax-react'
import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom';
import BasicInformation from './BasicInformation';
import EmploymentInformation from './EmploymentInformation';
import Benefits from './Benefits';

const AddEmployee = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0)

  const addEmployeeStages = [
    {
      title: 'Basic information',
      component: <BasicInformation setActiveTab={setActiveTab} />,
    },
    {
      title: 'Employment information',
      component: <EmploymentInformation setActiveTab={setActiveTab} />,
    },
    {
      title: 'Benefits',
      component: <Benefits setActiveTab={setActiveTab} />,
    },
  ]

  return (
    <div className='font-poppins'>
      <div
        onClick={() => navigate(-1)}
        className="flex text-16 gap-1 w-fit cursor-pointer"
      >
        <ArrowLeft2 size={20} /> <p>Back</p>
      </div>
      <div className="mt-6">
        <h1 className="font-poppins-medium text-24 rounded-tl-[10px] rounded-tr-[10px] text-purple-dark bg-purple-light pl-10 py-[33px]">
          Add employee
        </h1>
        <div className='bg-[#F5F5F5] px-8 '>
          <div className='mt-12 grid grid-cols-3 gap-4'>
            {addEmployeeStages.map((item, index) => (
              <div key={index}>
                <hr className={`border-4  rounded-full ${activeTab == index ? "border-purple-normal" : "border-[#E5E5E5]" }`} />
                <p className='text-neutral-normal mt-4 text-center text-16'>{item.title}</p>
              </div>
            ))}
          </div>
          <div className='mt-[92px] pb-10'>
            {addEmployeeStages[activeTab].component}
          </div>
        </div>
       </div> 
    </div>
  )
}

export default AddEmployee