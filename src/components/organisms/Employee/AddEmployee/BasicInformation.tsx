import React, { useState } from 'react'
import { Form, Formik } from 'formik';
import FormikCustomInput from '../../../atoms/CustomInput/FormikCustomInput';
import FormikCustomPhoneInput from '../../../atoms/CustomInput/FormikCustomPhoneInput';
import FormikCustomSelect from '../../../atoms/CustomInput/FormikCustomSelect';
import CustomButton from '../../../atoms/CustomButton/CustomButton';
import { ButtonProperties, errorMessages } from '../../../shared/helpers';
import { useRecoilState, useRecoilValue } from 'recoil';
import { getCreateStaffAtom } from '../../../../recoil/atom/staff';
import * as yup from "yup";
import { Country, State } from 'country-state-city';


interface Values {
    firstName: string;
    lastName: string;
    phoneNumber: string;
    gender: string;
    email: string;
    country: string;
    state: string;
};

const createEmployeeSchema = yup.object().shape({
    firstName: yup.string().required(errorMessages.required),
    lastName: yup.string().required(errorMessages.required),
    // phoneNumber: yup.string().required(errorMessages.required).min(13),
    gender: yup.string().required(errorMessages.required),
    country: yup.string().required(errorMessages.required),
    state: yup.string().required(errorMessages.required),
    email: yup
      .string()
      .email(errorMessages.email)
      .required(errorMessages.required),
});

const BasicInformation = ({setActiveTab}) => {

    const countries = Country.getAllCountries().map((country) => ({
        text: country.name,
        value: country.isoCode,
    }));
    
    const [selectedCountry, setSelectedCountry] = useState<string>("");
    const [, setSelectedState] = useState<string>("");
    const [, setCreateStaffAtom] = useRecoilState(getCreateStaffAtom);

    const getCreateStaffValue = useRecoilValue(getCreateStaffAtom);

    const states = State.getStatesOfCountry(selectedCountry).map((state) => ({
        text: state.name,
        value: state.isoCode,
      }));

    const initialState = {
        firstName: getCreateStaffValue?.firstName ? getCreateStaffValue?.firstName : "",
        lastName: getCreateStaffValue?.lastName ? getCreateStaffValue?.lastName : "",
        phoneNumber: getCreateStaffValue?.phoneNumber ? getCreateStaffValue?.phoneNumber : "",
        gender: getCreateStaffValue?.gender ? getCreateStaffValue?.gender : "",
        email: getCreateStaffValue?.email ? getCreateStaffValue?.email : "",
        country: getCreateStaffValue?.country ? getCreateStaffValue?.country : "",
        state: getCreateStaffValue?.state ? getCreateStaffValue?.state : "",
    };

    const handleSubmit = (values) => {
      setActiveTab(1);
      setCreateStaffAtom(prevState => ({
        ...prevState,
        ...values
      }))
    };


  return (
    <div>
          <Formik<Values>
            initialValues={initialState}
            onSubmit={handleSubmit}
            validationSchema={createEmployeeSchema}
            enableReinitialize
          >
            {({ setFieldValue, values }) => (
              <Form>
                <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                  <div>
                    <FormikCustomInput
                      label="First name *"
                      id="firstName"
                      name="firstName"
                      placeholder="enter your first name"
                      type="text"
                      value={values.firstName}
                      inputClassName="bg-transparent border"
                    />
                  </div>
                  <div>
                    <FormikCustomInput
                      label="Last name *"
                      id="lastName"
                      name="lastName"
                      placeholder="enter your last name"
                      type="text"
                    inputClassName="bg-transparent border"
                    />
                  </div>
                </div>
                <div className="mt-8">
                  <FormikCustomInput
                    label="Email *"
                    id="email"
                    name="email"
                    placeholder="enter your email address"
                    type="email"
                    inputClassName="bg-transparent border"
                  />
                </div>
                <div className='grid grid-cols-2 gap-8 mt-8'>
                  <div>
                    <FormikCustomPhoneInput
                      label="Phone number"
                      name="phoneNumber"
                      id="phoneNumber"
                      value={values.phoneNumber}
                      onChange={(value: string) => {
                        setFieldValue("phoneNumber", value);
                      }}
                    />
                  </div>
                  <div>
                    <FormikCustomSelect
                      label="Gender *"
                      options={[
                        { text: "Male", value: "Male" },
                        { text: "Female", value: "Female" },
                      ]}
                      name="gender"
                      value={values.gender}
                      placeholder="Select Gender"
                      onChange={(item: { value: string; text: string }) => {
                        setFieldValue("gender", item.value);
                      }}
                    />
                  </div>
                </div>
                <div className='grid grid-cols-2 gap-8 mt-8'>
                    <div>
                        <FormikCustomSelect
                            label="Country *"
                            options={countries}
                            name="country"
                            value={selectedCountry || values.country}
                            onChange={(item: {value: string, text: string}) => {
                            
                            setSelectedCountry(item.value);
                            setFieldValue("country", item.text);
                            }}
                        />
                    </div>
                    <div>
                        <FormikCustomSelect
                            label="State *"
                            options={states}
                            name="state"
                            onChange={(item: {value: string, text: string}) => {
                            setSelectedState(item.value);
                            setFieldValue("state", item.text);
                            }}
                            value={values.state}
                        />
                    </div>
                </div>
                <div className='flex justify-end'>
                    <CustomButton
                    className="mt-[86px] text-16"
                    type="submit"
                    title="Next"
                    handleClick={() => {}}
                    size={ButtonProperties.SIZES.small}
                    variant={ButtonProperties.VARIANT.primary.name}
                    />
                </div>
              </Form>
            )}
          </Formik>
    </div>
  )
}

export default BasicInformation;