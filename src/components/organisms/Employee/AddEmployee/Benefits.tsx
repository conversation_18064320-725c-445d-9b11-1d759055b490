import React, { useState } from "react";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import { ButtonProperties } from "../../../shared/helpers";
import CustomToggle from "../../../atoms/CustomToggle/CustomToggle";
import { useRecoilState, useRecoilValue } from "recoil";
import { getCreateStaffAtom } from "../../../../recoil/atom/staff";
import { MdClose } from "react-icons/md";
import { createStaff } from "../../../../api/staff";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import moment from "moment";
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";
import { getOrganization } from "../../../../api/organization";

const Benefits = ({ setActiveTab }) => {
  const [inputValue, setInputValue] = useState("");
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [displayText, setDisplayText] = useState("");
  const [, setCreateStaffAtom] = useRecoilState(getCreateStaffAtom);
  const getCreateStaffValue = useRecoilValue(getCreateStaffAtom);
  const [selectedItems, setSelectedItems] = useState(getCreateStaffValue?.benefits || []);
  const [otherBenefit, setOtherBenefits] = useState<Array<any>>(getCreateStaffValue.otherBenefits || []);
  const {fetchOrganization} = useUpdateRecoilAtom();


  const [toggle, setToggle] = useState(
    benefitsItems.reduce((acc, item) => {
      acc[item.id] = selectedItems?.includes(item.name);
      return acc;
    }, {})
  );

  const handleKeyDown = (event) => {
    if (event.key === "Enter" && inputValue) {
      setDisplayText(inputValue);
      setOtherBenefits((prevState): any => {
        if(inputValue) {
          setCreateStaffAtom((prevStates) => ({
            ...prevStates,
            otherBenefits: [...prevState, inputValue],
          }));
          return [...prevState, inputValue];
        }
      });
      setInputValue("");
    }
  };


  const handleSelectedItems = (toggle, item) => {
    setToggle((prevStates) => ({
      ...prevStates,
      [item.id]: toggle,
    }));

    setSelectedItems((prevSelected): any => {
      if (toggle) {
        setCreateStaffAtom((prevState) => ({
          ...prevState,
          benefits: [...prevSelected, item.name],
        }));
        return [...prevSelected, item.name];
      } else {
        return prevSelected.filter((name) => name !== item.name);
      }
    });
  };

  
  const handleCreateEmployee = () => {
    setIsLoading(true);
    const benefits = !getCreateStaffValue.otherBenefits ?  getCreateStaffValue?.benefits :  [...getCreateStaffValue?.benefits, ...getCreateStaffValue?.otherBenefits].filter((item) => item !== "Others");
    const payload = {
      first_name: getCreateStaffValue.firstName,
      last_name: getCreateStaffValue.lastName,
      phone_number: getCreateStaffValue.phoneNumber,  //(optional)
      gender: getCreateStaffValue.gender,
      email: getCreateStaffValue.email,
      department_id: getCreateStaffValue.departmentId,
      country: getCreateStaffValue.country,
      state: getCreateStaffValue.state, 
      branch_id: getCreateStaffValue.branchId,
      // address: getCreateStaffValue.phoneNumber
      employment_type: getCreateStaffValue.employmentType,
      start_date: moment(getCreateStaffValue.employmentDate).format("yyyy-MM-DD HH:mm:ss"),
      end_date: getCreateStaffValue.endDate ? moment(getCreateStaffValue.endDate).format("yyyy-MM-DD HH:mm:ss") : "", // required when employment_contract is PROBATION or CONTRACT
      job_title: getCreateStaffValue.jobTitle,
      salary_currency: getCreateStaffValue.currency,
      salary_amount: getCreateStaffValue.salary,  //(optional)
      direct_report_id: getCreateStaffValue.managerId, // (optional)
      grade_level_id: getCreateStaffValue.gradeLevelId, // (optional)
      work_email: getCreateStaffValue.email,
      work_hours: getCreateStaffValue.hours,
      work_days: getCreateStaffValue.days,  //(optional)
      benefits: benefits,
    };
    
    createStaff(payload).then((res) => {
      if(res?.success) {
        setIsLoading(false);
        setShowSuccessModal(true);
        getOrganization();
      } else {
        setIsLoading(false);
      }
    })
  };

  return (
    <div>
      <div>
        {benefitsItems?.map((item, index) => (
          <div className="flex justify-between pr-28 mt-6" key={index}>
            <p>{item.name}</p>
            <CustomToggle
              isOn={toggle[item.id]}
              onToggle={(toggle) => handleSelectedItems(toggle, item)}
            />
          </div>
        ))}
      </div>
      {selectedItems?.includes("Others") && (
        <div className="mt-10">
          <p>Add other benefits</p>
          <div
            className={`border mt-6 px-3 ${
              displayText ? "pt-5" : "pt-3"
            } rounded border-[#B2BBC6]`}
          >
            <div>
              <div className="flex flex-wrap gap-4">
                {otherBenefit?.map((item, index) => (
                  <p
                    className="flex justify-between gap-2 py-1 rounded-[1px] bg-purple-light font-poppins-medium text-purple-normal px-2 text-center"
                    key={index}
                  >
                    {item}
                    <MdClose
                      className="mt-1"
                      size={10}
                      onClick={() => {
                        setOtherBenefits((prev) =>
                          prev.filter((name) => name !== item)
                        );
                      }}
                    />
                  </p>
                ))}
              </div>
            </div>
            <input
              onChange={(e) => setInputValue(e.target.value)}
              value={inputValue}
              onKeyDown={handleKeyDown}
              className="w-full bg-transparent !border-none py-4 !outline-none"
              placeholder="type here"
              type="text"
            />
          </div>
          <p className="text-14 text-neutral-light mt-3">
            Press enter to add multiple benefits.
          </p>
        </div>
      )}

      <div className="flex gap-8 justify-end">
        <CustomButton
          className="mt-[86px] text-16"
          type="button"
          title="Previous"
          isTransparent
          handleClick={() => {
            setActiveTab(1);
          }}
          size={ButtonProperties.SIZES.small}
          variant={ButtonProperties.VARIANT.primary.name}
        />
        <CustomButton
          className="mt-[86px] text-16"
          type="submit"
          title="Add"
          isLoading={isLoading}
          handleClick={handleCreateEmployee}
          size={ButtonProperties.SIZES.small}
          variant={ButtonProperties.VARIANT.primary.name}
        />
      </div>
      <SuccessModal
        visibility={showSuccessModal}
        route="/employee/all-staffs"
        handleClick={() => setCreateStaffAtom([])}
        // toggleVisibility={setShowSuccessModal}
        text="Employee has been added successfully! You can view employee details in dashboard."
      />
    </div>
  );
};

export default Benefits;

const benefitsItems = [
  {
    id: 0,
    name: "Health Benefits",
  },
  {
    id: 1,
    name: "Retirement Benefits",
  },
  {
    id: 2,
    name: "Time Off Benefits",
  },
  {
    id: 3,
    name: "Income Protection Benefits",
  },
  {
    id: 4,
    name: "Work-Life Balance Benefits",
  },
  {
    id: 5,
    name: "Professional Development Benefits",
  },
  {
    id: 6,
    name: "Employee Recognition and Rewards",
  },
  {
    id: 7,
    name: "Others",
  },
];
