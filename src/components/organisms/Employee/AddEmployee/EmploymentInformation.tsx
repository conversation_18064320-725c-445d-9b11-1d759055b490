import React, { useEffect, useRef, useState } from 'react';
import { Form, Formik } from 'formik';
import FormikCustomInput from '../../../atoms/CustomInput/FormikCustomInput';
import FormikCustomSelect from '../../../atoms/CustomInput/FormikCustomSelect';
import CustomButton from '../../../atoms/CustomButton/CustomButton';
import { ButtonProperties, errorMessages } from '../../../shared/helpers';
import { useRecoilState, useRecoilValue } from 'recoil';
import { getAllStaffAtom, getCreateStaffAtom } from '../../../../recoil/atom/staff';
import * as yup from "yup";
import { Country } from 'country-state-city';
import FormikSelectCurrency from '../../../atoms/CustomInput/FormikSelectCurrency';
import { loggedUser<PERSON>tom } from '../../../../recoil/atom/authAtom';
import useUpdateRecoilAtom from '../../../shared/hooks/updateRecoilAtom';
import { defaultBusinessAtom, getDepartmentsAtom, getGradeLevelsAtom } from '../../../../recoil/atom/organizationAtom';
import FilterDropdown from '../../../atoms/Cards/FilterDropdown';
import { ArrowDown2, ArrowUp2 } from 'iconsax-react';
import useClickOutside from '../../../shared/hooks';
import CustomCheckBox from '../../../atoms/CustomCheckBox/CustomCheckBox';
import { clustarkToast } from '../../../atoms/Toast';
import { NotificationTypes } from '../../../shared/helpers/enums';
import FormikCustomDate from '../../../atoms/CustomInput/FormikCustomDate';
import moment from 'moment';


interface Values {
    employmentDate: string;
    endDate: string;
    jobTitle: string;
    lineManager: string;
    department: string;
    gradeLevel: string;
    hours: string;
    currency: string;
    salary: string;
    tax: string;
};



const EmploymentInformation = ({setActiveTab}) => {
  const isMounted = useRef(false);
  const [, setCreateStaffAtom] = useRecoilState(getCreateStaffAtom);
  const getUser = useRecoilValue(loggedUserAtom);
  const getCreateStaffValue = useRecoilValue(getCreateStaffAtom);
  const getDepartments = useRecoilValue(getDepartmentsAtom);
  const getGradeLevel = useRecoilValue(getGradeLevelsAtom);
  const getStaff = useRecoilValue(getAllStaffAtom);
  const defaultBusiness = useRecoilValue(defaultBusinessAtom);
  const {fetchAllStaff, fetchDepartments, fetchGradeLevel} = useUpdateRecoilAtom();
  const [showDays, setShowDays] = useState(false);
  const [departmentId, setDepartmentId] = useState(getCreateStaffValue?.departmentId || "");
  const [gradeLevelId, setGradeLevelId] = useState(getCreateStaffValue?.gradeLevelId || "");
  const [managerId, setManagerId] = useState(getCreateStaffValue?.managerId || "");
  const [checked, setChecked] = useState(getCreateStaffValue?.employmentType || "");
  const [selectedDays, setSelectedDays] = useState(getCreateStaffValue.days || []);
  const days = ["Mon", "Tue", "Wed", "Thur", "Fri", "Sat", "Sun"];
  const employmentType = ["PROBATION", "FULL_TIME", "CONTRACT", "PART_TIME"];

  const createEmployeeSchema = yup.object().shape({
    employmentDate: yup.string().required(errorMessages.required),
    endDate: yup.string().test((value) => {
      if (checked === 'CONTRACT' || checked === 'PROBATION') {
        errorMessages.required;
        return !!value
      }
      return true;
    }),
    jobTitle: yup.string().required(errorMessages.required),
    department: yup.string().required(errorMessages.required),
    // hours: yup.string().required(errorMessages.required),
    salary: yup.string().required(errorMessages.required),
    // currency: yup.string().required(errorMessages.required),
    // tax: yup.string().required(errorMessages.required),
   
});

  const defaultCountry = Country.getAllCountries()
  .filter((value) => value?.name === getUser?.businesses[0]?.country)
  .map((country) => ({
    text: country?.currency,
    value: country?.isoCode,
  }));

  const departments = getDepartments?.data?.map((item) => ({
    text: item.name + " - " + item.branch.name,
    value: item.id
  }));

  const gradeLevels = getGradeLevel?.data?.map((item) => ({
    text: item?.code,
    value: item?.id
  }));


  const staffs = getStaff?.data?.map((staff) => ({
    text: staff?.user?.first_name + " " + staff?.user?.last_name,
    value: staff?.id,
  }));

  const handleSelectedDays = (day) => {
    setSelectedDays((prevState: any) => {
      const isAlreadySelected = prevState.some((item) => item === day);
      if (isAlreadySelected) {
        return prevState.filter((item) => item !== day);
      } else {
        return [...prevState, day];
      }
    });
  };

  
  const initialState = {
    employmentDate: getCreateStaffValue?.employmentDate ? getCreateStaffValue?.employmentDate : "",
    endDate: getCreateStaffValue?.endDate ? getCreateStaffValue?.endDate : "",
    jobTitle: getCreateStaffValue?.jobTitle ? getCreateStaffValue?.jobTitle : "",
    lineManager: getCreateStaffValue?.lineManager ? getCreateStaffValue?.lineManager : "",
    department: getCreateStaffValue?.department ? getCreateStaffValue?.department : "",
    hours: getCreateStaffValue?.hours ? getCreateStaffValue?.hours : "",
    gradeLevel: getCreateStaffValue?.gradeLevel ? getCreateStaffValue?.gradeLevel : "",
    salary: getCreateStaffValue?.salary ? getCreateStaffValue?.salary : "",
    currency: getCreateStaffValue?.currency ? getCreateStaffValue?.currency : defaultCountry[0]?.text || defaultBusiness?.currency,
    tax: getCreateStaffValue?.tax ? getCreateStaffValue?.tax : "",
  };

  const handleSubmit = (values) => {
    if(!checked) {
      clustarkToast(NotificationTypes.ERROR, "Please select employment type");

    // } else if(selectedDays.length === 0) {
    //   clustarkToast(NotificationTypes.ERROR, "Please select work days");
    
    } else {
      setCreateStaffAtom(prevState => ({
        ...prevState,
        ...values,
        days: selectedDays,
        gradeLevelId: gradeLevelId,
        departmentId: departmentId,
        managerId: managerId,
      }));
      setActiveTab(2);
    }
  };

  useEffect(() => {
    if(isMounted.current) return
    isMounted.current = true;
    fetchAllStaff();  fetchGradeLevel();;
  }, [isMounted]);

  useEffect(() => {
    fetchDepartments();
  }, []);

  const node = useClickOutside(() => {
    setShowDays(false);
  });

    const handleCheckboxChange = (event, item) => {
    const isChecked = event.target.checked;
    if(isChecked) {
      setChecked(item);
      setCreateStaffAtom(prevState => ({
        ...prevState,
        employmentType: item
      }))
    }
  };

  return (
    <div>
      <div className='flex gap-12'>
        {employmentType.map((item, index) => (
          <div className={`flex gap-2 text-neutral-normal ${checked === item && "text-[#546881] font-bold"}`} key={index}>
            <div>
              <CustomCheckBox checked={checked === item} onChange={(e:any) => { handleCheckboxChange(e, item)}} name="" id="" />
            </div>
            <label htmlFor="">{item}</label>
          </div>
        ))}
      </div>
      <Formik<Values>
        initialValues={initialState}
        onSubmit={handleSubmit}
        validationSchema={createEmployeeSchema}
        enableReinitialize
      >
        {({ setFieldValue, values }) => (
          <Form>
            <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-16">
              <div>
                <FormikCustomDate
                label="Employment date *"
                value={moment(values.employmentDate)}
                inputClassName="border bg-transparent"
                name="employmentDate" 
                onChange={(date) =>{
                  setFieldValue("employmentDate", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                }}
                />
              </div>
              <div>
              <FormikCustomDate
                label="End date *"
                value={moment(values.endDate)}
                inputClassName="border bg-transparent"
                name="endDate" 
                onChange={(date) =>{
                  setFieldValue("endDate", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                }}
                />
              </div>
            </div>
            <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
              <div>
                <FormikCustomSelect
                  label="Department *"
                  options={departments}
                  name="department"
                  optionsParentClassName="!capitalize"
                  value={values.department}
                  onChange={(item: { value: string; text: string }) => {
                    setFieldValue("department", item.text);
                    setDepartmentId(item.value);
                  }}
                />
              </div>
              <div>
                <FormikCustomSelect
                  label="Grade level"
                  options={gradeLevels}
                  name="gradeLevel"
                  className="!uppercase"
                  optionsParentClassName="!uppercase"
                  value={values.gradeLevel}
                  onChange={(item: { value: string; text: string }) => {
                    setFieldValue("gradeLevel", item?.text);
                    setGradeLevelId(item?.value);
                  }}
                />
              </div>
            </div>
            <div className='grid grid-cols-2 gap-8 mt-8'>
              <div>
                <FormikCustomInput
                  label="Job title *"
                  id="jobTitle"
                  name="jobTitle"
                  placeholder="User Researcher"
                  type="text"
                inputClassName="bg-transparent border"
                />
              </div>
              <div>
                <FormikCustomSelect
                  label="Line manager"
                  name="lineManager"
                  id="lineManager"
                  options={staffs}
                  value={values.lineManager}
                  onChange={(item) => {
                    setFieldValue("lineManager", item?.text);
                    setManagerId(item?.value);
                  }}
                />
              </div>
             
            </div>
           
            <div className='grid grid-cols-2 gap-8 mt-8'>
              <div>
                  <FormikCustomInput
                  label="Work hours/week"
                  id="hours"
                  name="hours"
                  type="number"
                  value={values.hours}
                  inputClassName="bg-transparent border"
                  />
              </div>
              <div ref={node}>
                <div className="text-16 mb-4">
                  <label htmlFor="days">Days</label>
                </div>
                <div className='relative'>
                  <div onClick={() => setShowDays(true)} className={`w-full px-6 flex justify-between items-center rounded h-[47px] border-[0.6px] border-[#B2BBC699]`}>
                    <div className='flex flex-wrap gap-3' onClick={() => {} } >{selectedDays.map((day, index) => (<p key={index} className='flex justify-between bg-purple-light font-poppins-medium text-purple-normal px-2 w-12 text-center'>{day}</p>))} </div>
                    {showDays ? (<ArrowUp2 size={16} className="text-neutral-dark mt-1 ml-3"/>) : (<ArrowDown2 size={16} className="text-neutral-dark mt-1 ml-3"/>)}
                  </div>
                  {showDays && (
                    <FilterDropdown className='!top-16 !right-0'>
                      <div className='flex flex-wrap gap-3 py-4 px-3'>
                        {days.map((day, index) => (
                          <p className={` text-neutral-normal w-[39px] text-center cursor-pointer text-14 rounded-[1px] ${
                            selectedDays?.some(
                              (item: any) => item === day
                            ) ? "bg-purple-normal text-white" : "border-[0.5px] border-neutral-light"
                          }`} onClick={() => handleSelectedDays(day)} key={index}>{day}</p> 
                        ))}
                      </div>
                    </FilterDropdown>
                  )}

                </div>
              </div>
            </div>
            <div className='grid grid-cols-2 gap-8 mt-8'>
              
              <div>
                <FormikSelectCurrency
                  label="Salary *"
                  // options={countries}
                  name="salary"
                  value={values.currency}
                  onChange={(item: { value: string; text: string }) => {
                    setFieldValue("currency", item?.text);
                  }}
                  handleAmount={(amount) => setFieldValue("salary", amount)}
                  amountValue={getCreateStaffValue.salary}
                />
              </div>
              <div>
                <FormikCustomInput
                  label="Tax"
                  id="tax"
                  name="tax"
                  placeholder="%"
                  pattern="[0-9]*\.?[0-9]{0,2}"
                  // value={values.tax}
                  type="number"
                  value={values.tax}
                  inputClassName="bg-transparent border placeholder:text-end"
                />
              </div>
            </div>

            <div className='flex gap-8 justify-end'>
                <CustomButton
                className="mt-[86px] text-16"
                type="button"
                title="Previous"
                handleClick={() => {setActiveTab(0)}}
                size={ButtonProperties.SIZES.small}
                isTransparent
                variant={ButtonProperties.VARIANT.secondary.name}
                />
                <CustomButton
                className="mt-[86px] text-16"
                type="submit"
                title="Next"
                handleClick={() => {}}
                size={ButtonProperties.SIZES.small}
                variant={ButtonProperties.VARIANT.primary.name}
                />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  )
}

export default EmploymentInformation;