import { Form, Formik } from "formik";
import CustomButton from "../../../atoms/CustomButton/CustomButton";
import FormikCustomInput from "../../../atoms/CustomInput/FormikCustomInput";
import FormikCustomPhoneInput from "../../../atoms/CustomInput/FormikCustomPhoneInput";
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect";
import { ButtonProperties, errorMessages } from "../../../shared/helpers";
import CustomModal from "../../../atoms/CustomModal/CustomModal";
import * as yup from "yup";
import { Country, State } from 'country-state-city';
import { getAllStaffAtom, } from "../../../../recoil/atom/staff";
import { useRecoilValue } from "recoil";
import { useEffect, useRef, useState } from "react";
import GoBack from "../../../atoms/Ui/GoBack";
import { getAllBranchesAtom, getDepartmentsAtom, getGradeLevelsAtom } from "../../../../recoil/atom/organizationAtom";
import FormikCustomDate from "../../../atoms/CustomInput/FormikCustomDate";
import moment from "moment";
import { createStaff } from "../../../../api/staff";
import { NotificationTypes } from "../../../shared/helpers/enums";
import { clustarkToast } from "../../../atoms/Toast";
import SuccessModal from "../../../atoms/CustomModal/SuccessModal";
import useUpdateRecoilAtom from "../../../shared/hooks/updateRecoilAtom";


const AddStaff = () => {

    interface Values {
        firstName: string;
        lastName: string;
        phoneNumber: string;
        gender: string;
        email: string;
        country: string;
        state: string;
        employmentDate: string;
        endDate: string;
        jobTitle: string;
        department: string;
        gradeLevel: string;
        hours: string;
        branch: string;
        employmentType: string;
        directReport: string;
    };

    const isMounted = useRef(false);
    const getDepartments = useRecoilValue(getDepartmentsAtom);
    const getGradeLevel = useRecoilValue(getGradeLevelsAtom);
    const getStaff = useRecoilValue(getAllStaffAtom);
    const getEntireBranchesValue = useRecoilValue(getAllBranchesAtom);
    const { fetchAllStaff, fetchDepartments, fetchGradeLevel, fetchEntireBranches } = useUpdateRecoilAtom();
    const [departmentId, setDepartmentId] = useState('');
    const [gradeLevelId, setGradeLevelId] = useState("");
    const [branchId, setBranchId] = useState("");
    const [directReportId, setdirectReportId] = useState("");
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedCountry, setSelectedCountry] = useState<string>("");
    const [, setSelectedState] = useState<string>("");



    const branches = getEntireBranchesValue?.data?.map((branch) => ({
        text: branch.name,
        value: branch.id,
    })) || [];

    const departments = getDepartments?.data?.map((item) => ({
        text: item.name + " - " + item.branch.name,
        value: item.id
    }));

    const gradeLevels = getGradeLevel?.data?.map((item) => ({
        text: item?.code,
        value: item?.id
    }));

    const staffs = getStaff?.data?.map((staff) => ({
        text: staff?.user?.first_name + " " + staff?.user?.last_name,
        value: staff?.id,
    }));

    const employmentTypes = [
        { text: "PROBATION", value: "PROBATION" },
        { text: "FULL_TIME", value: "FULL_TIME" },
        { text: "CONTRACT", value: "CONTRACT" },
        { text: "PART_TIME", value: "PART_TIME" },
    ];

    const createEmployeeSchema = yup.object().shape({
        firstName: yup.string().required(errorMessages.required),
        lastName: yup.string().required(errorMessages.required),
        // phoneNumber: yup.string().required(errorMessages.required).min(13),
        gender: yup.string().required(errorMessages.required),
        country: yup.string().required(errorMessages.required),
        employmentType: yup.string().required(errorMessages.required),
        employmentDate: yup.string().required(errorMessages.required),
        jobTitle: yup.string().required(errorMessages.required),
        email: yup
            .string()
            .email(errorMessages.email)
            .required(errorMessages.required),
    });


    const initialState = {
        firstName: "",
        lastName: "",
        phoneNumber: "",
        gender: "",
        email: "",
        country: "",
        state: "",
        employmentDate: "",
        endDate: "",
        jobTitle: "",
        lineManager: "",
        department: "",
        branch: "",
        gradeLevel: "",
        hours: "",
        employmentType: "",
        directReport: "",
    };

    useEffect(() => {
        if (isMounted.current) return
        isMounted.current = true;
        fetchAllStaff(); fetchGradeLevel(); fetchEntireBranches();
    }, [isMounted]);

    useEffect(() => {
        fetchDepartments(branchId);
    }, [branchId]);

    const countries = Country.getAllCountries().map((country) => ({
        text: country.name,
        value: country.isoCode,
    }));

    const states = State.getStatesOfCountry(selectedCountry).map((state) => ({
        text: state.name,
        value: state.isoCode,
    }));


    const handleSubmit = (values) => {

        const payload = {
            first_name: values.firstName,
            last_name: values.lastName,
            phone_number: values.phoneNumber,
            gender: values.gender,
            email: values.email,
            department_id: departmentId,
            country: values.country,
            state: values.state,
            employment_type: values.employmentType,
            start_date: values.employmentDate,
            end_date: values.endDate,
            job_title: values.jobTitle,
            direct_report_id: directReportId,
            grade_level_id: gradeLevelId,
        }

        if ((values.employmentType === 'PROBATION' || values.employmentType === 'CONTRACT') && values.endDate === '') {
            clustarkToast(
                NotificationTypes.ERROR,
                'End date is required'
            );
        } else {
            setIsLoading(true);
            createStaff(payload).then((res) => {
                if (res?.success) {
                    setIsLoading(false);
                    setShowSuccessModal(true);
                } else {
                    setIsLoading(false);
                }
            })
        }

    };



    return (
        <div>
            <GoBack />
            <div className="mt-6">

                <div>
                    <Formik<Values>
                        initialValues={initialState}
                        onSubmit={handleSubmit}
                        validationSchema={createEmployeeSchema}
                        enableReinitialize
                    >
                        {({ setFieldValue, values }) => (

                            <Form>
                                <div className=" py-2">
                                    <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[5px] rounded-tr-[5px] mt-10">
                                        <h1 className="font-poppins-medium text-18 text-purple-dark ">
                                            Basic information
                                        </h1>
                                    </div>

                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                                        <div>
                                            <FormikCustomInput
                                                label="First name *"
                                                id="firstName"
                                                name="firstName"
                                                placeholder="enter your first name"
                                                type="text"
                                                value={values.firstName}
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomInput
                                                label="Last name *"
                                                id="lastName"
                                                name="lastName"
                                                placeholder="enter your last name"
                                                type="text"
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                    </div>
                                    <div className="mt-8">
                                        <FormikCustomInput
                                            label="Email *"
                                            id="email"
                                            name="email"
                                            placeholder="enter your email address"
                                            type="email"
                                            inputClassName="bg-transparent border"
                                        />
                                    </div>
                                    <div className='grid grid-cols-2 gap-8 mt-8'>
                                        <div>
                                            <FormikCustomPhoneInput
                                                label="Phone number"
                                                name="phoneNumber"
                                                id="phoneNumber"
                                                value={values.phoneNumber}
                                                onChange={(value: string) => {
                                                    setFieldValue("phoneNumber", value);
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomSelect
                                                label="Gender *"
                                                options={[
                                                    { text: "Male", value: "Male" },
                                                    { text: "Female", value: "Female" },
                                                ]}
                                                name="gender"
                                                value={values.gender}
                                                placeholder="Select Gender"
                                                onChange={(item: { value: string; text: string }) => {
                                                    setFieldValue("gender", item.value);
                                                }}
                                            />
                                        </div>
                                    </div>
                                    <div className='grid grid-cols-2 gap-8 mt-8'>
                                        <div>
                                            <FormikCustomSelect
                                                label="Country *"
                                                options={countries}
                                                name="country"
                                                value={selectedCountry || values.country}
                                                onChange={(item: { value: string, text: string }) => {

                                                    setSelectedCountry(item.value);
                                                    setFieldValue("country", item.text);
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomSelect
                                                label="State"
                                                options={states}
                                                name="state"
                                                onChange={(item: { value: string, text: string }) => {
                                                    setSelectedState(item.value);
                                                    setFieldValue("state", item.text);
                                                }}
                                                value={values.state}
                                            />
                                        </div>
                                    </div>

                                </div>


                                <div className="">
                                    <div className="bg-purple-light px-10 py-[28px] flex justify-between rounded-tl-[5px] rounded-tr-[5px] mt-12">
                                        <h1 className="font-poppins-medium text-18 text-purple-dark ">
                                            Employment information
                                        </h1>
                                    </div>
                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">

                                        <div>
                                            <FormikCustomInput
                                                label="Job title *"
                                                id="jobTitle"
                                                name="jobTitle"
                                                placeholder="User Researcher"
                                                type="text"
                                                inputClassName="bg-transparent border"
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomSelect
                                                label="Direct report"
                                                name="directReport"
                                                id="directReport"
                                                options={staffs}
                                                value={values.directReport}
                                                onChange={(item) => {
                                                    setFieldValue("directReport", item?.text);
                                                    setdirectReportId(item?.value)
                                                }}
                                            />
                                        </div>
                                    </div>
                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                                        <div>
                                            <FormikCustomDate
                                                label="Employment date *"
                                                value={moment(values.employmentDate)}
                                                inputClassName="border bg-transparent"
                                                name="employmentDate"
                                                onChange={(date) => {
                                                    setFieldValue("employmentDate", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                                                }}
                                            />
                                        </div>
                                        <div>
                                            <FormikCustomDate
                                                label="End date *"
                                                value={moment(values.endDate)}
                                                inputClassName="border bg-transparent"
                                                name="endDate"
                                                onChange={(date) => {
                                                    setFieldValue("endDate", date ? moment(date).format("YYYY-MM-DD HH:mm:ss") : null)
                                                }}
                                            />
                                        </div>
                                    </div>
                                    <div className="grid tablet:grid-cols-2 gap-8 px-2 mt-8">
                                        {/* <div>
                                            <FormikCustomSelect
                                                label="Branch *"
                                                name="branch"
                                                id="branch"
                                                optionsParentClassName="!capitalize"
                                                options={branches}
                                                value={values.branch}
                                                onChange={(item) => {
                                                    setFieldValue("department", "");
                                                    setFieldValue("branch", item.text);
                                                    setBranchId(item.value);
                                                }}
                                            />
                                        </div> */}

                                        <div>
                                            <FormikCustomSelect
                                                label="Employment type *"
                                                name="employmentType"
                                                id="employmentType"
                                                optionsParentClassName="!capitalize"
                                                options={employmentTypes}
                                                value={values.employmentType}
                                                onChange={(item) => {
                                                    setFieldValue("employmentType", "");
                                                    setFieldValue("employmentType", item.text);
                                                }}
                                            />
                                        </div>

                                        <div>
                                            <FormikCustomSelect
                                                label="Department *"
                                                options={departments}
                                                name="department"
                                                optionsParentClassName="!capitalize"
                                                value={values.department}
                                                onChange={(item: { value: string; text: string }) => {
                                                    setFieldValue("department", item.text);
                                                    setDepartmentId(item.value);
                                                }}
                                            // disabled={!values.branch}
                                            />
                                        </div>
                                    </div>
                                    <div className='grid grid-cols-1 gap-8 mt-8'>
                                        <div>
                                            <FormikCustomSelect
                                                label="Grade level"
                                                options={gradeLevels}
                                                name="gradeLevel"
                                                className="!uppercase"
                                                optionsParentClassName="!uppercase"
                                                value={values.gradeLevel}
                                                onChange={(item: { value: string; text: string }) => {
                                                    setFieldValue("gradeLevel", item?.text);
                                                    setGradeLevelId(item?.value);
                                                }}
                                            />
                                        </div>
                                    </div>

                                    <div className='flex gap-8 justify-end'>
                                        <CustomButton
                                            className="mt-[86px] text-16"
                                            type="submit"
                                            title="Send Invite"
                                            handleClick={() => { }}
                                            size={ButtonProperties.SIZES.small}
                                            variant={ButtonProperties.VARIANT.primary.name}
                                            isLoading={isLoading}
                                        />
                                    </div>

                                </div>

                            </Form>
                        )}
                    </Formik>
                </div>

            </div>
            <SuccessModal
                visibility={showSuccessModal}
                text="Employee added successfully."
                route="/employee/all-staffs"
            />

        </div>
    );
}


export default AddStaff;