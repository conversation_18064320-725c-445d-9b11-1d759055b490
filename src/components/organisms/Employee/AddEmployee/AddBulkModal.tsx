import { Formik, Form } from "formik"
import FormikCustomSelect from "../../../atoms/CustomInput/FormikCustomSelect"
import CustomButton from "../../../atoms/CustomButton/CustomButton"
import { ButtonProperties } from "../../../shared/helpers"
import { defaultBranch<PERSON>tom, getAllBranchesAtom, getDepartmentsAtom } from "../../../../recoil/atom/organizationAtom"
import { useRecoilState, useRecoilValue } from "recoil"
import { useEffect, useState } from "react"
import * as yup from "yup";
import * as XLSX from "xlsx";
import { Country } from "country-state-city";
import { loggedUserAtom } from "../../../../recoil/atom/authAtom"
import { geEmployeeDepartmentAtom, getBulkEmployeeAtom, getEmployeeBranchAtom } from "../../../../recoil/atom/bulkEmployee"
import { getDepartmentsbyBranchId } from "../../../../api/organization"
import { NotificationTypes } from "../../../shared/helpers/enums"
import { clustarkToast } from "../../../atoms/Toast"



const AddBulkModal = ({ toggle }) => {

    interface Values {
        department: string;
        // branch: string;
    };



    const [, setBulkEmployeeBranchAtom] = useRecoilState(getEmployeeBranchAtom);
    const [, setBulkEmployeeDepartmentAtom] = useRecoilState(geEmployeeDepartmentAtom);

    const [, setBulkEmployeeAtom] = useRecoilState(getBulkEmployeeAtom);
    const [, setDepartmentAtom] = useRecoilState(getDepartmentsAtom);
    const getDefaultBranchValue = useRecoilValue(defaultBranchAtom);
    const [iserror, setIserror] = useState<boolean>(false);
    const [fileName, setFileName] = useState('');
    const [jsonFile, setJsonFile] = useState({});
    const [, setDragging] = useState(false);
    const getLoggedUser = useRecoilValue(loggedUserAtom);
    const getEntireBranchesValue = useRecoilValue(getAllBranchesAtom);
    const getDepartments = useRecoilValue(getDepartmentsAtom);
    const [branchId, setBranchId] = useState("");
    const [departmentId, setDepartmentId] = useState("");

    const defaultCurrency = getLoggedUser.businesses[0]?.default_currency;

    const fetchDepartments = () => {
        getDepartmentsbyBranchId(branchId).then((res) => {
            if (res.success) {
                setDepartmentAtom(res.data)
            }
        });
    }

    useEffect(() => {
        fetchDepartments()
    }, [branchId])

    const modalInitialValues = {
        department: "",
        // branch: getDefaultBranchValue.name || "",
    }

    const modalShema = yup.object().shape({
        department: yup.string()
    });

  

    const departments = getDepartments?.data?.map((item) => ({
        text: item.name + " - " + item.branch.name,
        value: item.id
    }));

    const triggerFileInput = () => {
        document.getElementById("fileInput")?.click();
    };

    const isRequiredCheck = (data: any) => {

        if (!data || !Array.isArray(data) || data.length === 0) {
            setIserror(true)
            return;
        }

        const hasRequiredField = data.some(obj =>
            Object.values(obj).includes("Required !")
        );

        if (hasRequiredField) {
            setIserror(true)
        } else {
            setIserror(false)
        }
    }

    const templateUpload = (e: React.ChangeEvent<HTMLInputElement>) => {

        const file = e.target?.files?.[0];

        if (file) {
            setFileName(file.name)
            const reader = new FileReader();
            reader.onload = (e) => {
                const data = e.target?.result;

                if (data) {
                    const workbook = XLSX.read(data, { type: "array" });
                    const sheetName = workbook.SheetNames[0];
                    const sheet = workbook.Sheets[sheetName];

                    const jsonData = XLSX.utils.sheet_to_json(sheet);
                    setJsonFile(jsonData)

                }
            };

            reader.readAsArrayBuffer(file);
        }

        e.target.value = '';

    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {

        e.preventDefault();
        e.stopPropagation();
        setDragging(false);

        const file = e.dataTransfer.files[0];

        if (!file) return;

        const validMimeTypes = [
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-excel"
        ];

        if (!validMimeTypes.includes(file.type)) {
            clustarkToast(NotificationTypes.ERROR, 'only excel files can be uploaded');
            return;
        }

        setFileName(file.name);

        const reader = new FileReader();
        reader.onload = (event) => {
            const data = event.target?.result;

            if (data) {
                const workbook = XLSX.read(data, { type: "array" });
                const sheetName = workbook.SheetNames[0];
                const sheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(sheet);
                setJsonFile(jsonData);
            }
        };

        reader.readAsArrayBuffer(file);

    };

    const updateJson = (data: any) => {

        let countries = Country.getAllCountries().map((country) => ({
            text: country.name.toUpperCase(),
            code: country.phonecode,
            isCode: country.isoCode,
            currency: country.currency,
            all: country,
        }));

        let updatedEmployees = data.map((item: any) => {
            let country = item.country ? countries.find((c) => c.text === item.country.toUpperCase()) : null;
            return {
                ...item,
                first_name: item.first_name ? `${item.first_name}` : 'Required !',
                last_name: item.last_name ? `${item.last_name}` : 'Required !',
                gender: item.gender ? `${item.gender}` : 'Required !',
                country: item.country && country ? item.country.toUpperCase() : 'Required !',
                state: item.state ? `${item.state}` : 'Required !',
                employment_type: item.employment_type ? `${item.employment_type}` : 'Required !',
                email: item.email ? `${item.email}` : 'Required !',
                phone: country && item.phone ? `+${country.code}${item.phone}` : 'Required !',
                jobTitle: item.jobTitle ? `${item.jobTitle}` : 'Required !',
                salary: item.salary ? `${item.salary}` : 'Required !',
                employmentDate: item.employmentDate && !isNaN(new Date(1900, 0, item.employmentDate - 1).getTime())
                    ? new Date(1900, 0, item.employmentDate - 1).toISOString().split("T")[0]
                    : 'Required !',
                endDate: item.endDate && !isNaN(new Date(1900, 0, item.endDate - 1).getTime())
                    ? new Date(1900, 0, item.endDate - 1).toISOString().split("T")[0]
                    : (item.employment_type === 'CONTRACT' || item.employment_type === 'PROBATION') ? 'Required !' : 'Not required',
                currency: country ? country.isCode : defaultCurrency,
                // branch_id: branchId || getDefaultBranchValue.id || 'Required !',
                department_id: departmentId || 'Required !',
            };
        });

        isRequiredCheck(updatedEmployees);
        setBulkEmployeeAtom(updatedEmployees);
    };

    const onDataParsed = () => {
       if (!jsonFile || Object.keys(jsonFile).length === 0) {
            clustarkToast(NotificationTypes.ERROR, 'No excel sheet uploaded');
        } else {
            updateJson(jsonFile);
            toggle(false);
        }
    };




    return (
        <>
            <div className="bg-purple-light px-10 py-[40px]  justify-between rounded-tl-[10px] rounded-tr-[10px]">
                <div className="">
                    <h1 className="font-poppins-medium text-24 text-purple-normal-hover ">
                        Upload bulk employees
                    </h1>
                </div>
                <div>
                    <Formik<Values>
                        initialValues={modalInitialValues}
                        onSubmit={onDataParsed}
                        validationSchema={modalShema}
                        enableReinitialize
                    >
                        {({ setFieldValue, values }) => (
                            <Form>
                                <div className="px-2 mt-8">

                                    {/* <div>
                                        <FormikCustomSelect
                                            label="Branch *"
                                            name="branch"
                                            id="branch"
                                            optionsParentClassName="!capitalize"
                                            options={branches}
                                            value={getDefaultBranchValue.name || values.branch}
                                            onChange={(item) => {
                                                setFieldValue("branch", "");
                                                setFieldValue("branch", item.text);
                                                setBranchId(item.value);
                                                setBulkEmployeeBranchAtom(item.text)
                                            }}
                                        />
                                    </div> */}
                                    <div>
                                        <FormikCustomSelect
                                            label="Department "
                                            options={departments}
                                            name="department"
                                            optionsParentClassName="!capitalize"
                                            value={values.department}
                                            onChange={(item: { value: string; text: string }) => {
                                                setFieldValue("department", item.text);
                                                setDepartmentId(item.value);
                                                setBulkEmployeeDepartmentAtom(item.text)
                                            }}
                                            // disabled={!values.branch}
                                        />
                                    </div>
                                </div>
                                <div className="border-2 border-dashed bg-[#B2BBC6] bg-opacity-10 h-[178px] rounded mt-4 flex justify-center items-center"
                                    onDragOver={(e) => {
                                        e.preventDefault();
                                        setDragging(true);
                                        e.stopPropagation();
                                    }}
                                    onDragLeave={() => setDragging(false)}
                                    onDrop={handleDrop}
                                >
                                    <div
                                    >
                                        <div className="flex justify-center items-center">
                                            <svg
                                                width="40"
                                                height="40"
                                                viewBox="0 0 40 40"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                                <path
                                                    d="M31.6654 21.6675C31.2233 21.6675 30.7994 21.8431 30.4869 22.1556C30.1743 22.4682 29.9987 22.8921 29.9987 23.3341V23.9675L27.532 21.5008C26.6611 20.6367 25.4839 20.1518 24.257 20.1518C23.0302 20.1518 21.853 20.6367 20.982 21.5008L19.8154 22.6675L15.682 18.5341C14.7989 17.6935 13.6263 17.2246 12.407 17.2246C11.1878 17.2246 10.0152 17.6935 9.13203 18.5341L6.66536 21.0008V11.6675C6.66536 11.2254 6.84096 10.8015 7.15352 10.489C7.46608 10.1764 7.89 10.0008 8.33203 10.0008H19.9987C20.4407 10.0008 20.8646 9.82521 21.1772 9.51265C21.4898 9.20009 21.6654 8.77617 21.6654 8.33414C21.6654 7.89211 21.4898 7.46819 21.1772 7.15563C20.8646 6.84307 20.4407 6.66747 19.9987 6.66747H8.33203C7.00595 6.66747 5.73418 7.19425 4.7965 8.13194C3.85882 9.06962 3.33203 10.3414 3.33203 11.6675V31.6675C3.33203 32.9936 3.85882 34.2653 4.7965 35.203C5.73418 36.1407 7.00595 36.6675 8.33203 36.6675H28.332C29.6581 36.6675 30.9299 36.1407 31.8676 35.203C32.8052 34.2653 33.332 32.9936 33.332 31.6675V23.3341C33.332 22.8921 33.1564 22.4682 32.8439 22.1556C32.5313 21.8431 32.1074 21.6675 31.6654 21.6675ZM8.33203 33.3341C7.89 33.3341 7.46608 33.1585 7.15352 32.846C6.84096 32.5334 6.66536 32.1095 6.66536 31.6675V25.7175L11.4987 20.8841C11.7436 20.6508 12.0688 20.5206 12.407 20.5206C12.7453 20.5206 13.0705 20.6508 13.3154 20.8841L18.5987 26.1675L25.7654 33.3341H8.33203ZM29.9987 31.6675C29.9963 31.9865 29.8911 32.2963 29.6987 32.5508L22.182 25.0008L23.3487 23.8341C23.4682 23.7122 23.6108 23.6153 23.7682 23.5492C23.9256 23.483 24.0946 23.4489 24.2654 23.4489C24.4361 23.4489 24.6051 23.483 24.7625 23.5492C24.9199 23.6153 25.0625 23.7122 25.182 23.8341L29.9987 28.6841V31.6675ZM37.8487 7.1508L32.8487 2.1508C32.6902 1.99907 32.5033 1.88013 32.2987 1.8008C31.8929 1.63411 31.4378 1.63411 31.032 1.8008C30.8274 1.88013 30.6405 1.99907 30.482 2.1508L25.482 7.1508C25.1682 7.46464 24.9919 7.8903 24.9919 8.33414C24.9919 8.77797 25.1682 9.20363 25.482 9.51747C25.7959 9.83131 26.2215 10.0076 26.6654 10.0076C27.1092 10.0076 27.5349 9.83131 27.8487 9.51747L29.9987 7.3508V16.6675C29.9987 17.1095 30.1743 17.5334 30.4869 17.846C30.7994 18.1585 31.2233 18.3341 31.6654 18.3341C32.1074 18.3341 32.5313 18.1585 32.8439 17.846C33.1564 17.5334 33.332 17.1095 33.332 16.6675V7.3508L35.482 9.51747C35.637 9.67368 35.8213 9.79767 36.0244 9.88229C36.2275 9.9669 36.4453 10.0105 36.6654 10.0105C36.8854 10.0105 37.1032 9.9669 37.3063 9.88229C37.5094 9.79767 37.6938 9.67368 37.8487 9.51747C38.0049 9.36253 38.1289 9.1782 38.2135 8.9751C38.2981 8.772 38.3417 8.55416 38.3417 8.33414C38.3417 8.11412 38.2981 7.89627 38.2135 7.69318C38.1289 7.49008 38.0049 7.30574 37.8487 7.1508Z"
                                                    fill="#B2BBC6"
                                                />
                                            </svg>
                                        </div>

                                        <p
                                            onClick={triggerFileInput}
                                            className="text-neutral-normal text-center mt-5 flex">
                                            <span className="font-poppins-medium text-purple-normal cursor-pointer">
                                                Click here
                                            </span>
                                            &nbsp; or Drag and Drop file to upload
                                        </p>

                                        <p className="text-purple-normal font-semibold text-center mt-5">
                                            {fileName && fileName}
                                        </p>

                                        <input
                                            id="fileInput"
                                            type="file"
                                            accept=".xlsx, .csv"
                                            onChange={templateUpload}
                                            style={{ display: "none" }}
                                        />
                                    </div>
                                </div>

                                <div className='flex justify-end'>
                                    <CustomButton
                                        className="mt-[80px] text-16"
                                        type="submit"
                                        title="Proceed"
                                        handleClick={() => { }}
                                        size={ButtonProperties.SIZES.small}
                                        variant={ButtonProperties.VARIANT.primary.name}
                                    />
                                </div>

                            </Form>
                        )}
                    </Formik >
                </div >
            </div >
        </>
    )
}

export default AddBulkModal;