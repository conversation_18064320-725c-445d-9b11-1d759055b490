import html2canvas from "html2canvas";
import jsPDF from "jspdf";

export const handleDownloadPdf = async (printRef, fileName) => {
  if (!printRef.current) return;

  const element = printRef.current;
  const canvas = await html2canvas(element);
  const data = canvas.toDataURL("image/png");

  const pdf = new jsPDF("p", "mm", "a4");
  const pdfWidth = pdf.internal.pageSize.getWidth();
  const pdfHeight = (canvas.height * pdfWidth) / canvas.width;

  pdf.addImage(data, "PNG", 0, 0, pdfWidth, pdfHeight);
  pdf.save(`${fileName}.pdf`);
};

export const handleCopyPdfLink = async (printRef) => {
  if (!printRef.current) {
    console.error("Invalid printRef: no element to generate PDF from.");
    return;
  }

  try {
    const element = printRef.current;
    const canvas = await html2canvas(element);
    const data = canvas.toDataURL("image/png");

    const pdf = new jsPDF("p", "mm", "a4");
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = (canvas.height * pdfWidth) / canvas.width;

    pdf.addImage(data, "PNG", 0, 0, pdfWidth, pdfHeight);

    const pdfBlob = pdf.output("blob");
    const pdfUrl = URL.createObjectURL(pdfBlob);

    await navigator.clipboard.writeText(pdfUrl);
    // console.log("PDF link copied to clipboard:", pdfUrl);

    return pdfUrl;
  } catch (error) {
    console.error("Failed to generate or copy PDF link:", error);
  }
};

/**
 * Generates a PDF file from the content of a provided ref.
 * @param {React.RefObject} printRef - A React ref pointing to the element to be converted to PDF.
 * @returns {Promise<Blob>} - Returns a Blob representing the generated PDF file.
 */
export const generatePdfFile = async (printRef) => {
  if (!printRef.current) {
    console.error('Invalid printRef: no element to generate PDF from.');
    return null;
  }

  try {
    // Convert the referenced element to a canvas
    const element = printRef.current;
    const canvas = await html2canvas(element, { scale: 2 }); // Reduce scale for smaller image
const data = canvas.toDataURL('image/png', 0.7);

    // Create a PDF instance
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = (canvas.height * pdfWidth) / canvas.width;

    // Add the image to the PDF
    pdf.addImage(data, 'PNG', 0, 0, pdfWidth, pdfHeight);

    // Generate a Blob object for the PDF
    const pdfBlob = pdf.output('blob');
    return pdfBlob; // Return the Blob
  } catch (error) {
    console.error('Failed to generate PDF file:', error);
    return null;
  }
};

