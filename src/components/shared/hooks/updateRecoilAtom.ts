// useFetch.ts
import { useRecoilState } from "recoil";
import {
  defaultBusinessAtom,
  getAllBranchesAtom,
  getBranchesAtom,
  getDepartmentsAtom,
  getGradeLevelsAtom,
  getOrganizationAtom,
} from "../../../recoil/atom/organizationAtom";
import {
  getBranches,
  getDefaultBusiness,
  getDepartments,
  getGradeLevels,
  getOrganization,
} from "../../../api/organization";
import { getAllStaff, getStaff, getStaffById } from "../../../api/staff";
import { getAllStaffAtom, getStaffAtom } from "../../../recoil/atom/staff";
import { getCurrentPlan } from "../../../api/subscription";
import { getCurrentPlanAtom } from "../../../recoil/atom/subscription";
import { getLeaveAnalytics } from "../../../api/leave";
import { getLeaveAnalyticsAtom } from "../../../recoil/atom/leave";
import { getIssues } from "../../../api/TaskManagement";
import { getIssuesAtom } from "../../../recoil/atom/TaskManagement";

const useUpdateRecoilAtom = () => {
  const [, setBranchesAtom] = useRecoilState(getBranchesAtom);
  const [, setOrganizationAtom] = useRecoilState(getOrganizationAtom);
  const [, seEntireBranchesAtom] = useRecoilState(getAllBranchesAtom);
  const [, setStaffAtom] = useRecoilState(getStaffAtom);
  const [, setAllStaffAtom] = useRecoilState(getAllStaffAtom);
  const [, setDepartmentAtom] = useRecoilState(getDepartmentsAtom);
  const [, setGradeLevelAtom] = useRecoilState(getGradeLevelsAtom);
  const [, setStaffByIdAtom] = useRecoilState(getGradeLevelsAtom);
  const [, setCurrentPlanAtom] = useRecoilState(getCurrentPlanAtom);
  const [, setLeaveAnalytic] = useRecoilState(getLeaveAnalyticsAtom);
  const [, setDefaultBusiness] = useRecoilState(defaultBusinessAtom);
  const [, setIssuesAtom] = useRecoilState(getIssuesAtom);

  const fetchBranches = (page?: number) => {
    getBranches({ page: page }).then((res) => {
      if (res.success) {
        setBranchesAtom(res.data);
      }
    });
  };

  const fetchEntireBranches = () => {
    getBranches({ limit: 100 }).then((res) => {
      if (res.success) {
        seEntireBranchesAtom(res.data);
      }
    });
  };

  const fetchOrganization = () => {
    getOrganization().then((res) => {
      if (res.success) {
        setOrganizationAtom(res.data);
      }
    });
  };

  const fetchStaff = () => {
    getStaff({ limit: 100 }).then((res) => {
      if (res.success) {
        setStaffAtom(res.data);
      }
    });
  };

  const fetchAllStaff = () => {
    getAllStaff({ limit: 100 }).then((res) => {
      if (res.success) {
        setAllStaffAtom(res.data);
      }
    });
  };

  const fetchStaffById = (id) => {
    getStaffById(id).then((res) => {
      if (res.success) {
        setStaffByIdAtom(res.data);
      }
    });
  };

  const fetchDepartments = (branchId?: string) => {
    getDepartments({ limit: 100 }, branchId).then((res) => {
      if (res.success) {
        setDepartmentAtom(res.data);
      }
    });
  };

  const fetchGradeLevel = () => {
    getGradeLevels({ limit: 100 }).then((res) => {
      if (res.success) {
        setGradeLevelAtom(res.data);
      }
    });
  };

  const fetchCurrentPlan = () => {
    getCurrentPlan().then((res) => {
      if (res.success) {
        setCurrentPlanAtom(res.data);
      }
    });
  };

  const fetchLeaveAnalytics = () => {
    getLeaveAnalytics().then((res) => {
      if (res.success) {
        setLeaveAnalytic(res.data);
      }
    });
  };

  const fetchDefaultBusiness = () => {
    getDefaultBusiness().then((res) => {
      if (res.success) {
        setDefaultBusiness(res.data);
      }
    });
  };

  const fetchIssues = () => {
    getIssues().then((res) => {
      if (res.success) {
        setIssuesAtom(res.data);
      }
    });
  };

  return {
    fetchBranches,
    fetchOrganization,
    fetchEntireBranches,
    fetchStaff,
    fetchAllStaff,
    fetchDepartments,
    fetchGradeLevel,
    fetchStaffById,
    fetchCurrentPlan,
    fetchLeaveAnalytics,
    fetchDefaultBusiness,
    fetchIssues,
  };
};

export default useUpdateRecoilAtom;
