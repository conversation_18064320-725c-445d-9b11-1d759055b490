import { useFlutterwave, closePaymentModal } from "flutterwave-react-v3";

export const triggerPayment = (
  { amount, currency, customizeData, customerData, meta }, handlePayment
) => {
  const config = {
    public_key: import.meta.env.VITE_FLUTTER_LIVE_KEY,
    tx_ref: Date.now().toString(),
    amount: amount,
    currency: currency,
    payment_options: "card,mobilemoney,ussd",
    customer: customerData,
    customizations: customizeData,
    meta
  };



  const handleFlutterPayment = useFlutterwave(config);
  handleFlutterPayment({
    callback: (response) => {
      closePaymentModal(); 
      handlePayment();
    },
    onClose: () => {},
  });

   return handleFlutterPayment;
};



