import moment from "moment";

// const AllRoles = ["ADMIN", "OWNER", "FINANCE", "OPERATIONS"]
export const validTypes = ["image/jpg", "image/png", "image/PNG", "image/JPEG", "image/jpeg", "image/JPG"];

export const priorityStatus = ["low", "medium", "high", "critical"];


export const genericEmailDomains = [
  'gmail.com',
  'yahoo.com',
  'hotmail.com',
  'outlook.com',
  'aol.com',
  'icloud.com',
];

export const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

export const ButtonProperties = {
    SIZES: {
      small: "small",
      medium: "medium",
      big: "big",
    },
    ICON_POSITION: {
      start: "start",
      end: "end",
    },
    VARIANT: {
      primary: {
        name: "primary",
        background: "purple-dark",
        hover: "purple-dark",
        disabled: "neutral-light",
        focused: "purple-dark",
      },
      secondary: {
        name: "secondary",
        background: "purple-dark",
        hover: "",
        disabled: "neutral-light",
        focused: "",
      },
    },
  };

  
export const errorMessages = {
  email: "Email is not valid",
  orgEmail: "Email is not valid organization email",
  maxChar: (num: number) => `This field cannot have more than ${num} characters`,
  minChar: (num: number) => `This field must be at least ${num} characters`,
  minLowerCase: (num: number) => `This field must be at least ${num} lower case character`,
  minUpperCase: (num: number) => `This field must be at least ${num} upper case character`,
  minNumber: (num: number) => `This field must be at least ${num} number`,
  minSymbol: (num: number) => `This field must be at least ${num} special character`,
  required: "This field is compulsory",
  passwordMatch: "Passwords don't match",
  positiveInteger: "The number must be greater than 0",
  integer: "No decimals allowed",
};

export const getNameInitials = (firstName: string, lastName?: string) => {
  let initials = "";
  if(firstName && lastName){

      initials = `${firstName?.charAt(0)}${lastName?.charAt(0)}` || "";
  } else  {
     initials = `${firstName?.charAt(0)}` || "";
  }
  return initials?.toUpperCase() || "";
};

export const truncateText = (text: string, maxLength: number): string => {
  if (text?.length <= maxLength) {
    return text;
  }
  return text?.slice(0, maxLength) + '...';
};

export const removeHtmlTags = (str: string): string => {
  return str?.replace(/<\/?[^>]+(>|$)/g, "");
};

export const extractImgTag = (htmlString: string): string | null => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlString, 'text/html');

  const imgElement = doc.querySelector('img');

  return imgElement ? imgElement.outerHTML : null;
};


export const calculateDaysLeft = (startDate: string, endDate: string): number => {
  const now = moment();
  const start = moment(startDate); 
  const end = moment(endDate);

  if (now.isBefore(start) || now.isAfter(end)) {
    return 0;
  }

  return end.diff(now, 'days');
};

export const currencySymbol = {
  "USD" : "$",
  "NGN": "₦",
  "NGN_PREFIX": "NGN"
}


export const paymentCurrency = {
  "USD" : "USD",
  "NGN": "NGN"
}

export const days28 = [
  {
    text: "1",
    value: "1"
  },
  {
    text: "2",
    value: "2"
  },
  {
    text: "3",
    value: "3"
  },
  {
    text: "4",
    value: "4"
  },
  {
    text: "5",
    value: "5"
  },
  {
    text: "6",
    value: "6"
  },
  {
    text: "7",
    value: "7"
  },
  {
    text: "8",
    value: "8"
  },
  {
    text: "9",
    value: "9"
  },
  {
    text: "10",
    value: "10"
  },
  {
    text: "11",
    value: "11"
  },
  {
    text: "12",
    value: "12"
  },
  {
    text: "13",
    value: "13"
  },
  {
    text: "14",
    value: "14"
  },
  {
    text: "15",
    value: "15"
  },
  {
    text: "16",
    value: "16"
  },
  {
    text: "17",
    value: "17"
  },
  {
    text: "18",
    value: "18"
  },
  {
    text: "19",
    value: "19"
  },
  {
    text: "20",
    value: "20"
  },
  {
    text: "21",
    value: "21"
  },
  {
    text: "22",
    value: "22"
  },
  {
    text: "23",
    value: "23"
  },
  {
    text: "24",
    value: "24"
  },
  {
    text: "25",
    value: "25"
  },
  {
    text: "26",
    value: "26"
  },
  {
    text: "27",
    value: "27"
  },
  {
    text: "28",
    value: "28"
  }
]

export const months = [
  {
    text: "January",
    value: "January",
  },
  {
    text: "February",
    value: "February"
  },
  {
    text: "March",
    value: "March"
  },
  {
    text: "April",
    value: "April"
  },
  {
    text: "May",
    value: "May"
  },
  {
    text: "June",
    value: "June"
  },
  {
    text: "July",
    value: "July"
  },
  {
    text: "August",
    value: "August"
  },
  {
    text: "September",
    value: "September"
  },
  {
    text: "October",
    value: "October"
  },
  {
    text: "November",
    value: "November"
  },
  {
    text: "December",
    value: "December"
  },
]
