import * as yup from "yup";
import yupPassword from "yup-password";
import { errorMessages } from ".";

yupPassword(yup); // extend yup

export const passwordSchema = yup.object().shape({
    password: yup
      .string()
      .required(errorMessages.required)
      .min(8, errorMessages.minChar(8))
      .minLowercase(1, errorMessages.minLowerCase(1))
      .minUppercase(1, errorMessages.minUpperCase(2))
      .minNumbers(1, errorMessages.minNumber(1))
      .minSymbols(1, errorMessages.minSymbol(1)),
    confirmPassword: yup
      .string()
      .required(errorMessages.required)
      .oneOf([yup.ref("password"), ""], errorMessages.passwordMatch),
  });

  export const changePasswordSchema = yup.object().shape({
    oldPassword: yup.string().required(errorMessages.required),
    newPassword: yup
      .string()
      .required(errorMessages.required)
      .min(8, errorMessages.minChar(8))
      .minLowercase(1, errorMessages.minLowerCase(1))
      .minUppercase(1, errorMessages.minUpperCase(2))
      .minNumbers(1, errorMessages.minNumber(1))
      .minSymbols(1, errorMessages.minSymbol(1)),
    confirmPassword: yup
      .string()
      .required(errorMessages.required)
      .oneOf([yup.ref("newPassword"), ""], errorMessages.passwordMatch),
  });

  export const createTeamMemberSchema = yup.object().shape({
    firstName: yup.string().required(errorMessages.required),
    lastName: yup.string().required(errorMessages.required),
    phoneNumber: yup.string().required(errorMessages.required).min(13),
    gender: yup.string().required(errorMessages.required),
    password: yup
      .string()
      .required(errorMessages.required)
      .min(8, errorMessages.minChar(8))
      .minLowercase(1, errorMessages.minLowerCase(1))
      .minUppercase(1, errorMessages.minUpperCase(2))
      .minNumbers(1, errorMessages.minNumber(1))
      .minSymbols(1, errorMessages.minSymbol(1)),
    confirmPassword: yup
      .string()
      .required(errorMessages.required)
      .oneOf([yup.ref("password"), ""], errorMessages.passwordMatch),
  });