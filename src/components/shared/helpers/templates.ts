export const newsletterTemplate = {
    counters: {
        u_row: 1,
        u_column: 1,
        u_text: 1,
        u_image: 1,
    },
    body: {
        rows: [
            {
                id: 'row-1',
                cells: [1],
                columns: [
                    {
                        id: 'column-1',
                        contents: [
                            {
                                id: 'content-1',
                                type: 'text',
                                values: {
                                    text: '<h1>Welcome to Our Newsletter!</h1>',
                                    textAlign: 'center',
                                    color: '#333333',
                                    fontSize: '24px',
                                    lineHeight: '1.5',
                                },
                            },
                        ],
                    },
                ],
            },
        ],
    },
};

export const welcomeEmailTemplate = {
    counters: {
      u_row: 5,
      u_column: 5,
      u_text: 5,
      u_image: 2,
      u_button: 1,
    },
    body: {
      rows: [
        {
          id: 'row-1',
          cells: [1],
          columns: [
            {
              id: 'column-1',
              contents: [
                {
                  id: 'content-1',
                  type: 'image',
                  values: {
                    src: {
                      url: 'https://via.placeholder.com/200x50?text=HubSpot+Logo',
                    },
                    altText: 'HubSpot Logo',
                    textAlign: 'center',
                    containerPadding: '10px',
                  },
                },
              ],
            },
          ],
        },
        {
          id: 'row-2',
          cells: [1],
          columns: [
            {
              id: 'column-2',
              contents: [
                {
                  id: 'content-2',
                  type: 'text',
                  values: {
                    text: '<h2>Hi [Name],</h2><p>I would like to personally invite you to the <strong>webinar</strong> – <strong>Why your sales team hates their CRM and how you can fix it</strong>, which takes place on <strong>Thursday, October 15 at 12:30 PM IST</strong>.</p>',
                    textAlign: 'left',
                    color: '#333333',
                    fontSize: '16px',
                    lineHeight: '1.6',
                    containerPadding: '10px',
                  },
                },
              ],
            },
          ],
        },
        {
          id: 'row-3',
          cells: [1],
          columns: [
            {
              id: 'column-3',
              contents: [
                {
                  id: 'content-3',
                  type: 'text',
                  values: {
                    text: '<p>In a world of hyper-targeting and personalization, CRM data has never been more critical to an organization. Yet, sales leaders spend 13% of their time chasing their team to update their CRM. As a sales leader, how do you fix this problem?</p><p><a href="#">Register now</a> for this webinar by HubSpot and industry experts to learn:</p><ul><li>Reasons why your sales team hates using their CRM and how to overcome it.</li><li>How to replace your legacy CRM without skipping a beat on performance.</li><li>How and why ResMed moved from Salesforce CRM to HubSpot CRM.</li><li>Live Q&A with HubSpot sales and industry experts.</li></ul>',
                    textAlign: 'left',
                    color: '#333333',
                    fontSize: '16px',
                    lineHeight: '1.6',
                    containerPadding: '10px',
                  },
                },
              ],
            },
          ],
        },
        {
          id: 'row-4',
          cells: [1],
          columns: [
            {
              id: 'column-4',
              contents: [
                {
                  id: 'content-4',
                  type: 'button',
                  values: {
                    text: 'Register Now',
                    href: '#',
                    textAlign: 'center',
                    color: '#ffffff',
                    backgroundColor: '#ff6600',
                    borderRadius: '5px',
                    containerPadding: '10px',
                    fontSize: '16px',
                    lineHeight: '1.6',
                  },
                },
              ],
            },
          ],
        },
        {
          id: 'row-5',
          cells: [2],
          columns: [
            {
              id: 'column-5',
              contents: [
                {
                  id: 'content-5',
                  type: 'text',
                  values: {
                    text: '<p>Hope you can tune in!</p>',
                    textAlign: 'center',
                    color: '#333333',
                    fontSize: '16px',
                    lineHeight: '1.6',
                    containerPadding: '10px',
                  },
                },
                {
                  id: 'content-6',
                  type: 'image',
                  values: {
                    src: {
                      url: 'https://via.placeholder.com/100?text=Avatar',
                    },
                    altText: 'Speaker Avatar',
                    textAlign: 'center',
                    containerPadding: '10px',
                  },
                },
                {
                  id: 'content-7',
                  type: 'text',
                  values: {
                    text: '<p><strong>David Fallarme</strong><br>Marketing @ HubSpot</p>',
                    textAlign: 'center',
                    color: '#333333',
                    fontSize: '14px',
                    lineHeight: '1.6',
                    containerPadding: '10px',
                  },
                },
              ],
            },
          ],
        },
      ],
    },
  };

  export const RESET_PASSWORD = {
    root: {
      type: 'EmailLayout',
      data: {
        backdropColor: '#F2F5F7',
        canvasColor: '#FFFFFF',
        textColor: '#242424',
        fontFamily: 'MODERN_SANS',
        childrenIds: [
          'block_3gpSGmkgL4nWSBQjWCjK2z',
          'block_BjpQ7DGTtvaEuYRMd7VE7w',
          'block_xyg4GWmgGbJJEDRQc76bC',
          'block_76VptLCZ47t3EkAarUufEJ',
          'block_Gtk3kDYwsJqEmQf2XGWPRc',
          'block_LACDCzUS2bsvEbmnq1KHuW',
        ],
      },
    },
    block_3gpSGmkgL4nWSBQjWCjK2z: {
      type: 'Image',
      data: {
        style: {
          padding: {
            top: 24,
            bottom: 8,
            right: 24,
            left: 24,
          },
          backgroundColor: null,
          textAlign: 'left',
        },
        props: {
          height: 24,
          url: 'https://d1iiu589g39o6c.cloudfront.net/live/platforms/platform_A9wwKSL6EV6orh6f/images/wptemplateimage_Xh1R23U9ziyct9nd/codoc.png',
          alt: '',
          linkHref: null,
          contentAlignment: 'middle',
        },
      },
    },
    block_BjpQ7DGTtvaEuYRMd7VE7w: {
      type: 'Heading',
      data: {
        style: {
          color: null,
          backgroundColor: null,
          fontFamily: null,
          fontWeight: 'bold',
          textAlign: 'left',
          padding: {
            top: 32,
            bottom: 0,
            left: 24,
            right: 24,
          },
        },
        props: {
          level: 'h3',
          text: 'Reset your password?',
        },
      },
    },
    block_xyg4GWmgGbJJEDRQc76bC: {
      type: 'Text',
      data: {
        style: {
          color: '#474849',
          backgroundColor: null,
          fontSize: 14,
          fontFamily: null,
          fontWeight: 'normal',
          textAlign: 'left',
          padding: {
            top: 8,
            bottom: 16,
            left: 24,
            right: 24,
          },
        },
        props: {
          text: `If you didn't request a reset, don't worry. You can safely ignore this email.`,
        },
      },
    },
    block_76VptLCZ47t3EkAarUufEJ: {
      type: 'Button',
      data: {
        style: {
          backgroundColor: null,
          fontSize: 14,
          fontFamily: null,
          fontWeight: 'bold',
          textAlign: 'left',
          padding: {
            top: 12,
            bottom: 32,
            right: 24,
            left: 24,
          },
        },
        props: {
          buttonBackgroundColor: '#0068FF',
          buttonStyle: 'rectangle',
          buttonTextColor: '#FFFFFF',
          fullWidth: false,
          size: 'medium',
          text: 'Change my password',
          url: 'https://example.usewaypoint.com/reset_password?token=02938409809w8r09a83wr098aw0',
        },
      },
    },
    block_Gtk3kDYwsJqEmQf2XGWPRc: {
      type: 'Divider',
      data: {
        style: {
          backgroundColor: null,
          padding: {
            top: 16,
            bottom: 16,
            left: 24,
            right: 24,
          },
        },
        props: {
          lineHeight: 1,
          lineColor: '#EEEEEE',
        },
      },
    },
    block_LACDCzUS2bsvEbmnq1KHuW: {
      type: 'Text',
      data: {
        style: {
          color: '#474849',
          backgroundColor: null,
          fontSize: 12,
          fontFamily: null,
          fontWeight: 'normal',
          textAlign: 'left',
          padding: {
            top: 4,
            bottom: 24,
            left: 24,
            right: 24,
          },
        },
        props: {
          text: 'Need help? Just reply to this email to contact support.',
        },
      },
    },
  };
  
 
  