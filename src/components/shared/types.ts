export interface SubTask {
    id: string;
    title: string;
    isCompleted: boolean;
  }
  
  export interface TeamMember {
    id: string;
    name: string;
    avatar?: string;
  }
  
  export interface Task {
    id: string;
    title: string;
    description?: string;
    subTasks?: SubTask[];
    assignedTo?: TeamMember;
    priority?: 'low' | 'medium' | 'high';
    dueDate?: Date;
  }
  
  export interface Column {
    id: string;
    title: string;
    tasks: Task[];
  }
  
  export interface BoardState {
    columns: Column[];
    teamMembers: TeamMember[];
  }