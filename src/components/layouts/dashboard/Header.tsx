import React, { useEffect, useRef } from 'react';
import FormikCustomSelect from '../../atoms/CustomInput/FormikCustomSelect';
import { Form, Formik } from 'formik';
import { useRecoilState, useRecoilValue } from 'recoil';
import { loggedUserAtom } from '../../../recoil/atom/authAtom';
import { calculateDaysLeft, getNameInitials } from '../../shared/helpers';
import {
  changeDefaultBranch,
  changeDefaultBusiness,
  clearDefaultBranch,
  getBranches,
  getBusinesses,
  getDefaultBranch,
  getDefaultBusiness,
  getOrganization,
} from '../../../api/organization';
import {
  defaultBranchAtom,
  defaultBusinessAtom,
  getAllBranchesAtom,
  getBusinessesAtom,
  getOrganizationAtom,
} from '../../../recoil/atom/organizationAtom';
import { getProfileInfoAtom } from '../../../recoil/atom/profile';
import CustomLink from '../../atoms/CustomLink/CustomLink';
import { getCurrentPlanAtom } from '../../../recoil/atom/subscription';
import { subscriptionPlans } from '../../shared/helpers/enums';
import { getCurrentPlan } from '../../../api/subscription';

const Header = () => {
  const isMounted = useRef(false);
  const getUser = useRecoilValue(loggedUserAtom);
  const getOrganizationValue = useRecoilValue(getOrganizationAtom);
  const getDefaultBranchValue = useRecoilValue(defaultBranchAtom);
  const getDefaultBusinesshValue = useRecoilValue(defaultBusinessAtom);
  const getEntireBranchesValue = useRecoilValue(getAllBranchesAtom);
  const getBusinessesValue = useRecoilValue(getBusinessesAtom);
  const [, setDefaultBranchAtom] = useRecoilState(defaultBranchAtom);
  const [, setDefaultBusinessAtom] = useRecoilState(defaultBusinessAtom);
  const [, setOrganizationAtom] = useRecoilState(getOrganizationAtom);
  const [, setEntireBranchesAtom] = useRecoilState(getAllBranchesAtom);
  const [, setBusinessesAtom] = useRecoilState(getBusinessesAtom);
  const [, setCurrentPlanAtom] = useRecoilState(getCurrentPlanAtom);
  const getUserValue = useRecoilValue(getProfileInfoAtom);
  const currentPlanValue = useRecoilValue(getCurrentPlanAtom);

  const days = calculateDaysLeft(currentPlanValue?.start_date, currentPlanValue?.expiration_date);
  const plan = currentPlanValue?.plan?.name;
  const showBanner = plan === subscriptionPlans.FREE_TRIAL || (currentPlanValue && days <= 3);

  const getEntireBranches = () => {
    getBranches({ limit: 100 }).then((res) => {
      if (res.success) {
        setEntireBranchesAtom(res.data);
      }
    });
  };

  const fetchCurrentPlan = () => {
    getCurrentPlan().then((res) => {
      if (res.success) {
        setCurrentPlanAtom(res.data);
      }
    });
  };

  const fetchGetOrganization = () => {
    getOrganization().then((res) => {
      if (res.success) {
        setOrganizationAtom(res.data);
      }
    });
  };

  const fetchBusinesses = () => {
    getBusinesses().then((res) => {
      if (res?.success) {
        setBusinessesAtom(res.data);
      }
    });
  };

  useEffect(() => {
    if (isMounted.current) return;
    isMounted.current = true;
    if (!getOrganizationValue?.businesses) {
      fetchGetOrganization();
    }
    if (!getEntireBranchesValue?.data) {
      getEntireBranches();
      fetchDefaultBranch();
      fetchBusinesses();
      fetchDefaultBusiness();
    }
    fetchCurrentPlan();
  }, [isMounted]);

  const branches =
    getEntireBranchesValue?.data?.map((branch) => ({
      text: branch.name,
      value: branch.id,
    })) || [];

  const businesses =
    getBusinessesValue?.map((item) => ({
      text: item.name + ' ' + `(${item.role})`,
      value: item.id,
    })) || [];

  const fetchDefaultBranch = () => {
    getDefaultBranch()
      .then((res) => {
        if (res?.success) {
          setDefaultBranchAtom({
            id: res.data.id,
            name: res.data.name,
          });
        } else {
          setDefaultBranchAtom({});
        }
      })
      .catch(() => setDefaultBranchAtom({}));
  };

  const fetchDefaultBusiness = () => {
    getDefaultBusiness().then((res) => {
      if (res.success) {
        setDefaultBusinessAtom({
          id: res.data.id,
          name: res.data.name,
          currency: res.data.default_currency,
        });
      }
    });
  };

  const handleChangeDefaultBusiness = (item) => {
    changeDefaultBusiness({ businessId: item.value }).then((res) => {
      if (res.success) {
        getEntireBranches();
        fetchDefaultBranch();
        setDefaultBusinessAtom({
          id: item.value,
          name: item.text,
        });
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    });
  };

  const handlecChangeDefaultBranch = (item) => {
    if (item.value) {
      changeDefaultBranch({ branchId: item.value }).then((res) => {
        if (res?.success) {
          setDefaultBranchAtom({
            id: item.value,
            name: item.text,
          });
          window.location.reload();
        }
      });
    } else {
      clearDefaultBranch().then((res) => {
        if (res.success) {
          setDefaultBranchAtom({
            id: item.value,
            name: item.text,
          });
          window.location.reload();
        }
      });
    }
  };

  return (
    <div className={'sticky top-0 z-[100] bg-white ml-[2.5px]' + (showBanner ? ' pt-3' : ' py-3')}>
      <div>
        <div className='w-full px-11 flex justify-between items-center'>
          <div>
            <h1 className='font-poppins-medium text-purple-normal text-20 capitalize'>
              Welcome Back, {getUser?.first_name}
            </h1>
            {/* <p className='font-mulish text-14 mt-2'>Here’s today’s activities and reports.</p> */}
          </div>
          <div className='flex justify-center items-center'>
            <Formik
              initialValues={{
                branch: getDefaultBranchValue?.name || '',
                organization: getDefaultBusinesshValue?.name || '',
              }}
              onSubmit={() => {}}
              enableReinitialize
            >
              {({ values, setFieldValue }) => (
                <Form className='flex justify-center items-center gap-5 mr-20'>
                  {businesses.length > 1 && (
                    <div>
                      <FormikCustomSelect
                        labelClass='!text-12'
                        parentContainer='!w-[160px] !h-[38px] text-12'
                        optionsParentClassName='text-12'
                        value={values.organization}
                        name='organization'
                        options={businesses}
                        onChange={(item: { value: string; text: string }) => {
                          setFieldValue('organization', item.value);
                          handleChangeDefaultBusiness(item);
                        }}
                      />
                    </div>
                  )}

                  {branches.length > 1 && (
                    <div>
                      <FormikCustomSelect
                        labelClass='!text-12'
                        parentContainer='!w-[160px] !h-[38px] text-12'
                        optionsParentClassName='text-12'
                        value={values.branch}
                        name='branch'
                        options={branches}
                        onChange={(item: { value: string; text: string }) => {
                          setFieldValue('branch', item.value);
                          handlecChangeDefaultBranch(item);
                        }}
                      />
                    </div>
                  )}
                </Form>
              )}
            </Formik>
            <div className='flex gap-4'>
              {/* <div className=" border border-purple-light-hover w-10 h-10 text-neutral-normal-active rounded-full flex justify-center items-center">
              <div className="relative">
                <FaRegEnvelope size={14} />
                <p className="w-[7px] h-[7px] absolute top-0 -right-0.5 rounded-full bg-alert-text-error" />
              </div>
            </div> */}
              {/* <div className=" border border-purple-light-hover w-10 h-10 text-neutral-normal-active rounded-full flex justify-center items-center">
              <div className="relative">
                <FaRegBell size={14}/>
                <p className="w-[7px] h-[7px] absolute top-0 right-0.5 rounded-full bg-alert-text-error" />
              </div>
            </div> */}
              <div className=''>
                {getUserValue?.avatar && getUser?.avatar ? (
                  <img
                    src={getUserValue?.avatar || getUser?.avatar}
                    alt='gat'
                    className='w-12 h-12 border rounded-full'
                  />
                ) : (
                  <p className='w-12 h-12 bg-purple-light-active text-purple-normal font-poppins-medium font-bold rounded-full flex justify-center items-center text-20'>
                    {getNameInitials(getUser?.first_name, getUser?.last_name)}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {plan === subscriptionPlans.FREE_TRIAL && days > 3 && (
          <div className='bg-purple-light mt-1 ml-1 py-3 flex justify-center items-center'>
            <div className='flex'>
              <p className='flex justify-center items-center'>
                You are currently on free trial plan. You have
                <span className='border border-purple-normal w-8 h-8 text-center place-content-center text-purple-normal font-semibold rounded-full ml-2 mr-2 '>
                  {' '}
                  {days}
                </span>{' '}
                days left.{' '}
              </p>
              <div className='border border-purple-normal px-3 rounded-lg ml-10 py-1'>
                <CustomLink destination='/subscription' text='Upgrade now' />
              </div>
            </div>
          </div>
        )}
        {currentPlanValue && days <= 3 && (
          <div className='bg-purple-light mt-1 ml-1 py-3 flex justify-center items-center'>
            <div className='flex'>
              <p className='flex justify-center items-center'>
                {plan ? `You are currently on ${plan} plan` : 'Your subscription is expired'}.{' '}
                <span className='border border-purple-normal w-8 h-8 text-center place-content-center text-purple-normal font-semibold rounded-full ml-2 mr-2 '>
                  {days < 0 ? 0 : days}
                </span>{' '}
                days left.{' '}
              </p>
              <div className='border border-purple-normal px-3 rounded-lg ml-10 py-1'>
                <CustomLink destination='/subscription' text='Renew now' />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Header;
