import React from 'react';
import SideNavigation from './SideNavigation';
import Header from './Header';

interface DashboardBaseLayoutProps {
  children: any;
}

const DashboardBaseLayout = ({ children }: DashboardBaseLayoutProps) => {
  return (
    <div className='justify-center items-center'>
      <div className='bg-[#F9F9F9] flex h-full min-h-screen font-poppins'>
        <SideNavigation />
        <div className='w-full ml-[270px]'>
          <Header />
          <div className='px-11 py-8 mt-2'>{children}</div>
        </div>
      </div>
    </div>
  );
};

export default DashboardBaseLayout;
