import {
  <PERSON><PERSON>own2,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>s,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>2<PERSON><PERSON>,
  SecurityCard,
  Setting2,
} from 'iconsax-react';
import React from 'react';
import SidebarLinkGroup from './SidebarLinkGroup';
import { useLocation, useNavigate } from 'react-router-dom';
import { TbUserScreen } from 'react-icons/tb';
import { HiOutlineUserPlus } from 'react-icons/hi2';
import { IoVideocamOutline } from 'react-icons/io5';
import { MdLogout, MdOutlineAccountBalanceWallet, MdOutlineSubscriptions } from 'react-icons/md';
import { AiOutlineFileDone } from 'react-icons/ai';
import { PiUsersThree, PiUsersThreeLight } from 'react-icons/pi';
import ComingSoonBadge from '../../atoms/ComingSoonBadge';
import logo from '../../../assets/images/logo.png';
import { RiRobot2Line } from 'react-icons/ri';
import { useRecoilValue } from 'recoil';
import { loggedTeamInfoAtom } from '../../../recoil/atom/authAtom';

const SideNavigation = () => {
  const location = useLocation();
  const { pathname } = location;
  const navigate = useNavigate();
  const getLoggedTeam = useRecoilValue(loggedTeamInfoAtom);
  const userRole = getLoggedTeam?.role;

  return (
    <div className='w-[270px] fixed h-screen z-[100] bg-white border-r-[0.6px] border-[#e8ebf2]'>
      <h1 className='mt-[16px] ml-14 font-poppins-medium text-20 text-purple-dark'>
        <img src={logo} alt='logo' />
      </h1>
      <div className='pt-[56px] pb-24 mx-4 h-full overflow-y-scroll hide-scrollbar'>
        {SideNavigationMenuItems.map((item: any, index: number) => (
          <div key={index}>
            {item.subMenu && item?.roles?.includes(userRole) ? (
              <SidebarLinkGroup activeCondition={pathname.includes(item.main)}>
                {(handleClick, open) => (
                  <>
                    <div className='mb-1'>
                      <div className={`flex `} onClick={() => !item.comingSoon && handleClick()}>
                        {pathname.includes(item.main) && (
                          <p className='h-[50px] w-1.5 rounded-tr-lg rounded-br-lg bg-purple-dark-active' />
                        )}
                        <div
                          className={`flex relative m-auto py-3 pl-5 w-full ml-1  cursor-pointer gap-3 text-neutral-normal  ${
                            pathname.includes(item.main)
                              ? 'bg-purple-light'
                              : ' hover:bg-purple-light'
                          }`}
                        >
                          <span
                            className={` ${
                              pathname.includes(item.main)
                                ? 'font-poppins-medium text-purple-dark'
                                : '!text-neutral-normal !font-poppins '
                            }`}
                          >
                            {item.icon}
                          </span>
                          <div
                            className={`flex ${
                              pathname.includes(item.main)
                                ? 'font-poppins-medium !text-purple-normal'
                                : 'text-neutral-normal font-poppins'
                            }`}
                          >
                            {item.name}
                            {item?.comingSoon && (
                              <div className='absolute right-2'>
                                <ComingSoonBadge />
                              </div>
                            )}
                          </div>
                          {!item?.comingSoon && (
                            <span>
                              {open ? (
                                <ArrowUp2
                                  className={`mt-[1px] ${
                                    pathname.includes(item.main) ? 'text-purple-dark' : ''
                                  }`}
                                  size={14}
                                />
                              ) : (
                                <ArrowDown2 className='mt-[1px]' size={14} />
                              )}
                            </span>
                          )}
                        </div>
                      </div>
                      <div
                        className={`relative translate transform overflow-hidden ${
                          !open && 'hidden'
                        }`}
                      >
                        <div className='ml-7 my-6 flex '>
                          <div
                            className={`pl-[30px] border-l-[3px] ${
                              item.subMenu.filter(
                                (item) =>
                                  pathname.includes(item.link) || pathname.includes(item?.main)
                              )[0]
                                ? 'border-purple-normal'
                                : 'border-neutral-normal'
                            }`}
                          >
                            {item.subMenu.map((subMenu: any, index: number) => (
                              <div
                                className={` relative ${
                                  index < item.subMenu.length - 1 ? 'mb-5' : 'mb-0'
                                }`}
                                key={index}
                              >
                                {(pathname.includes(subMenu.link) ||
                                  pathname.includes(subMenu.main)) && (
                                  <p className='w-3 h-3 rounded-full slider transition ease-in-out duration-150 bg-purple-normal absolute top-1.5 -left-[36.5px] z-10' />
                                )}
                                <div className='flex justify-between'>
                                  <p
                                    className={`${
                                      pathname.includes(subMenu.link) ||
                                      pathname.includes(subMenu.main)
                                        ? ''
                                        : '!text-neutral-normal !font-poppins'
                                    } cursor-pointer text-purple-dark font-poppins-medium`}
                                    onClick={() => !subMenu?.comingSoon && navigate(subMenu.link)}
                                  >
                                    {subMenu.name}
                                  </p>
                                  {subMenu?.comingSoon && (
                                    <div className='ml-16'>
                                      <ComingSoonBadge />
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </SidebarLinkGroup>
            ) : (
              <div className='mb-1'>
                {item?.roles?.includes(userRole) && (
                  <div className='flex'>
                    {pathname.includes(item.main) && (
                      <p className='h-[50px] w-1.5 rounded-tr-lg rounded-br-lg bg-purple-normal-active' />
                    )}
                    <div
                      onClick={() => !item.comingSoon && navigate(item.link)}
                      className={`flex relative m-auto py-3 pl-5 w-full cursor-pointer ml-1 gap-3 text-neutral-normal hover:bg-purple-light ${
                        pathname.includes(item.main)
                          ? 'bg-purple-light text-purple-dark font-poppins-medium'
                          : ''
                      }`}
                    >
                      {item.icon}
                      <div
                        className={` flex justify-between gap-5 ${
                          pathname.includes(item.main)
                            ? 'font-poppins-medium !text-purple-normal'
                            : 'text-neutral-normal font-poppins'
                        }`}
                      >
                        {item.name}
                        {item?.comingSoon && (
                          <div className='absolute right-2'>
                            <ComingSoonBadge />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
        <div
          className='py-3 pl-5 flex mt-10 cursor-pointer gap-3 text-neutral-normal'
          onClick={() => {
            localStorage.clear();
            window.location.assign('/');
          }}
        >
          <MdLogout size={18} />
          <p>Logout</p>
        </div>
      </div>
      <div></div>
    </div>
  );
};

export default SideNavigation;

const SideNavigationMenuItems = [
  {
    name: 'Overview',
    icon: <Menu size={18} />,
    link: '/dashboard',
    main: 'dashboard',
    roles: ['OWNER', 'ADMIN', 'OPERATIONS', 'FINANCE'],
  },

  // {
  //   name: 'Recruitment',
  //   icon: <HiOutlineUserPlus size={18} />,
  //   link: '/recruitment',
  //   comingSoon: true,
  //   main: 'recruitment',
  //   subMenu: [
  //     {
  //       name: 'Job Posting',
  //       link: '/recruitment/job-posting',
  //     },
  //     {
  //       name: 'Onboarding',
  //       link: '/recruitment/onboarding',
  //     },
  //     {
  //       name: 'Offboarding',
  //       link: '/recruitment/offboarding',
  //     },
  //   ],
  //   roles: ['OWNER', 'ADMIN'],
  // },

  {
    name: 'Organization',
    icon: <Data className='rotate-90' size={18} />,
    link: '/organization',
    main: 'organization',
    subMenu: [
      {
        name: 'Branches',
        link: '/organization/branches',
        main: 'branch',
      },
      {
        name: 'Department',
        link: '/organization/department',
        main: 'department',
      },
      {
        name: 'Grade level',
        link: '/organization/grade-level',
        main: 'grade',
      },
    ],
    roles: ['OWNER', 'ADMIN', 'OPERATIONS'],
  },

  {
    name: 'Employee',
    icon: <Profile2User size={18} />,
    link: '/employee',
    main: 'employee',
    subMenu: [
      {
        name: 'Employee',
        link: '/employee/all-staffs',
        main: 'staff',
      },
      {
        name: 'Leave',
        link: '/employee/leave',
        main: 'leave',
      },
      {
        name: 'Disputes',
        link: '/employee/disputes',
        main: 'disputes',
      },
      {
        name: 'Resignation',
        link: '/employee/view-resignation',
        main: 'resignation',
      },
      // {
      //   name: 'Document',
      //   link: '/employee/document',
      //   main: 'document',
      // },
    ],
    roles: ['OWNER', 'ADMIN'],
  },
  {
    name: 'Asset management',
    icon: <NoteAdd size={18} />,
    link: '/asset-management',
    main: 'asset',
    roles: ['OWNER', 'ADMIN', 'OPERATIONS'],
  },
  // {
  //   name: 'Task management',
  //   icon: <NoteText size={18} />,
  //   link: '/task-management',
  //   comingSoon: false,
  //   main: 'task',
  //   subMenu: [
  //     {
  //       name: 'Dashboard',
  //       link: '/task-management/overview',
  //       comingSoon: true,
  //     },
  //     {
  //       name: 'Projects',
  //       link: '/task-management/projects',
  //     },
  //     {
  //       name: 'Teams',
  //       link: '/task-management/teams',
  //     },
  //     {
  //       name: 'Setup',
  //       link: '/task-management/task-setup',
  //       main: 'task-setup',
  //     },
  //     {
  //       name: 'Board',
  //       link: '/task-management/project-board',
  //       main: 'board',
  //     },
  //   ],
  //   roles: ['OWNER', 'ADMIN', 'OPERATIONS'],
  // },

  // {
  //   name: 'Attendance',
  //   icon: <TbUserScreen size={18} />,
  //   link: '/attendance',
  //   main: 'attendance',
  //   comingSoon: true,
  //   subMenu: [
  //     {
  //       name: 'Daily attendance',
  //       link: '/attendance/daily-attendance',
  //     },
  //     {
  //       name: 'Monthly',
  //       link: '/attendance/monthly-attendance',
  //     },
  //     {
  //       name: 'Leave',
  //       link: '/attendance/leave',
  //     },
  //     {
  //       name: 'Shift',
  //       link: '/attendance/shift',
  //     },
  //   ],
  //   roles: ['OWNER', 'ADMIN'],
  // },
  {
    name: 'Team members',
    icon: <PiUsersThree size={18} />,
    link: '/team-members',
    main: 'team-member',
    roles: ['OWNER', 'ADMIN'],
  },

  // {
  //   name: 'Meeting',
  //   icon: <IoVideocamOutline size={18} />,
  //   link: '/meeting',
  //   comingSoon: true,
  //   main: 'meeting',
  //   roles: ['OWNER', 'ADMIN'],
  // },
  // {
  //   name: 'Chat',
  //   icon: <Messages size={18} />,
  //   link: '/chat',
  //   comingSoon: true,
  //   main: 'chat',
  //   roles: ['OWNER', 'ADMIN'],
  // },
  {
    name: 'Accounting',
    icon: <MdOutlineAccountBalanceWallet size={18} />,
    link: '/accounting',
    main: 'accounting',
    subMenu: [
      {
        name: 'Reports',
        link: '/accounting/reports',
        main: 'reports',
      },
      {
        name: 'Expense',
        link: '/accounting/expense',
        main: 'expense',
      },
      {
        name: 'Income',
        link: '/accounting/income',
        main: 'income',
      },
    ],
    roles: ['OWNER', 'ADMIN', 'OPERATIONS', 'FINANCE'],
  },
  // {
  //   name: "Inventory",
  //   icon: <MdOutlineInventory size={18} />,
  //   link: "/inventory",
  //   comingSoon: true
  // },
  {
    name: 'CRM',
    icon: <PiUsersThreeLight size={18} />,
    link: '/crm',
    main: 'crm',
    subMenu: [
      {
        name: 'Customers',
        link: '/crm/customers',
        main: 'customer',
      },
      {
        name: 'Vendors',
        link: '/crm/vendors',
        main: 'vendor',
      },
    ],
    roles: ['OWNER', 'ADMIN', 'OPERATIONS'],
  },
  {
    name: 'Subscription',
    icon: <MdOutlineSubscriptions size={18} />,
    link: '/subscription',
    main: 'subscription',
    roles: ['OWNER', 'ADMIN', 'FINANCE'],
  },
  // {
  //   name: 'Automation',
  //   icon: <RiRobot2Line size={18} />,
  //   link: '/automation',
  //   main: 'automation',
  //   comingSoon: true,
  //   roles: ['OWNER', 'ADMIN'],
  // },
  {
    name: 'Payroll',
    icon: <SecurityCard size={18} />,
    main: 'payroll',
    // comingSoon: true,
    subMenu: [
      {
        name: 'Dashboard',
        link: '/payroll/dashboard',
        main: 'payroll-dashboard',
      },
      {
        name: 'Pay Runs',
        link: '/payroll/pay-runs',
        main: 'pay-runs',
      },
      // {
      //   name: 'Salary Revision',
      //   link: '/payroll/salary-revision',
      //   main: 'salary-revision',
      // },
      // {
      //   name: 'Loans',
      //   link: '/payroll/loans',
      //   main: 'loans',
      // },
      {
        name: 'Salary Grades',
        link: '/payroll/salary-grades',
        main: 'salary-grades',
      },
      {
        name: 'Payment Transactions',
        link: '/payroll/history/paid',
        main: 'history',
      },
    ],
    roles: ['OWNER', 'ADMIN', 'FINANCE'],
  },

  {
    name: 'Account setting',
    icon: <Setting2 size={18} />,
    link: '/account-setting',
    main: 'account-setting',
    roles: ['OWNER', 'ADMIN'],
  },

  {
    name: 'Support',
    icon: <AiOutlineFileDone size={18} />,
    link: '/support',
    main: 'support',
    roles: ['OWNER', 'ADMIN', 'OPERATIONS'],
  },
];
