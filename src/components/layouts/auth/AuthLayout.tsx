import React, { ReactNode } from "react";
import logo from "../../../assets/images/logoWhite.png"
import dashboardImage from "../../../assets/images/Modal.png"

interface AuthLayoutProps {
  children: ReactNode;
}

const AuthLayout = ({ children }: AuthLayoutProps) => {
  return (
    <div className="grid tablet:grid-cols-2 h-screen overflow-y-hidden">
      <div className="bg-[#f4f4f4] flex justify-center items-center ">
        {children}
      </div>
      <div className="hidden tablet:block bg-purple-normal">
        <div className="flex justify-center items-center">
          <div>
            <div className="">
              <a href="/">
                <img
                  className="w-[150px] h-[150px] object-contain"
                  src={logo}
                  alt="Clustark Logo"
                />
              </a>
            </div>
            <div className="flex justify-center items-center">
                <img
                  className=" object-contain"
                  src={dashboardImage}
                  alt="Clustark Logo"
                />
            </div>
            <div className=" font-poppins mb-10">
              <h1 className="text-center text-24 text-white font-bold">Manage different organization with ease.</h1>
            <p className="text-white text-center text-16">HR management starts here. Everything you need is in an organized dashboard</p>
            </div>

          </div>

        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
