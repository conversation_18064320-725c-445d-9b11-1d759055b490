export interface emailProps {
  email: string;
}

export interface validateOtpProps {
  email: string;
  otp: string;
}

export interface registerCompanyProps {
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  gender: string;
  password: string;
  password_confirmation: string;
  business_name: string;
  business_address: string;
  business_country: string;
  business_state: string;
}

export interface createAccountProps {
    companyEmail?: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
    gender?: string;
    businessName?: string;
    businessAddress?: string;
    country?: string;
    state?: string;
    password?: string;
    confirmPassword?: string;
}

export interface loginProps {
  email: string;
  password: string;
}

export interface resetPasswordProps {
  email: string;
  password: string;
  password_confirmation: string;
  otp: string;
}

export interface otpProps {
    email: string;
    otp: string;
}
