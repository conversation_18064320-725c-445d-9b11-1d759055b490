export interface addBranchProps {
  name: string;
  address: string;
  country: string;
  state: string;
  branchPhoneNumber?: string;
  branchId?: string;
}

export interface addDepartmentProps {
  name: string;
  branchId?: string;
  departmentId?: string;
}

export interface addEmployeeProps {
  first_name?: string;
  last_name?: string;
  phone_number?: string;
  gender?: string;
  email?: string;
  branch_id?: number; // (optional) if not passed we'd use the default branch selected
  department_id?: number;
  country?: string;
  state?: string; // (optional)
  address?: string; // (optional)
  employment_type?: string;
  start_date?: string;
  end_date?: string; // required when employment_contract is PROBATION or CONTRACT
  job_title?: string;
  salary_currency?: string;
  salary_amount?: number;
  direct_report_id?: string; // (optional)
  grade_level_id?: string; // (optional)
  work_email?: string;
  work_hours?: number;
  work_days?: Array<string>;
  benefits?: Array<string>;
}
