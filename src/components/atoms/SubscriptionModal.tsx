import React, { useEffect } from "react";
import CustomModal from "./CustomModal/CustomModal";
import birddance from "../../assets/images/bluebird-dance.gif";
import sadbird from "../../assets/images/bluebird-bird-sad.gif";
import { useLocation, useNavigate } from "react-router-dom";
import CustomButton from "./CustomButton/CustomButton";
import { ButtonProperties, calculateDaysLeft } from "../shared/helpers";
import { Form, Formik } from "formik";
import FormikCustomSelect from "./CustomInput/FormikCustomSelect";
import { getBusinessesAtom } from "../../recoil/atom/organizationAtom";
import { useRecoilState, useRecoilValue } from "recoil";
import { getBusinesses } from "../../api/organization";
import { getCurrentPlan, startFreeTrial } from "../../api/subscription";
import { getCurrentPlanAtom } from "../../recoil/atom/subscription";
import { clustarkToast } from "./Toast";
import { NotificationTypes, subscriptionPlans } from "../shared/helpers/enums";
import Loader from "./Loader";
import { loggedUserAtom } from "../../recoil/atom/authAtom";

const SubscriptionModal = () => {
  const navigate = useNavigate();
  const [openModal, setOpenModal] = React.useState(true);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isFetching, setIsFetching] = React.useState(false);
  const location = useLocation();
  const token = localStorage.getItem("token");
  const [, setBusinessesAtom] = useRecoilState(getBusinessesAtom);
  const [, setCurrentPlanAtom] = useRecoilState(getCurrentPlanAtom);
  const getBusinessesValue = useRecoilValue(getBusinessesAtom);
  const loggedUser = useRecoilValue(loggedUserAtom);
  const currentPlanValue = useRecoilValue(getCurrentPlanAtom);
  const plan = currentPlanValue?.plan?.name;
  const days = calculateDaysLeft(currentPlanValue?.start_date, currentPlanValue?.expiration_date);

  const fetchCurrentPlan = () => {
    setIsFetching(true);
    getCurrentPlan().then((res) => {
        if(res.success) {
            setCurrentPlanAtom(res.data);
        }
        setIsFetching(false);
    })
  }

  useEffect(() => {
    if (token && location.pathname) {
      setOpenModal(true);
    } 
  }, [location.pathname]);

  const fetchBusinesses = () => {
    getBusinesses().then((res) => {
      if (res.success) {
        setBusinessesAtom(res.data);
      }
    });
  };

  const businesses =
    getBusinessesValue?.map((item) => ({
      text: item.name + " " + `(${item.role})`,
      value: item.id,
    })) || [];

    const handleSubmit = (values) => {
      setIsLoading(true);
      const payload = {
        businessId: values.organization || getBusinessesValue[0]?.id || loggedUser?.businesses[0]?.id,
      }
      startFreeTrial(payload).then((res) => {
        setIsLoading(false);
        clustarkToast(NotificationTypes.SUCCESS, res.message)
        fetchCurrentPlan();
        setOpenModal(false);
      })
    };



    useEffect(() => {
        fetchBusinesses();
        fetchCurrentPlan()
    }, []);

    if(isFetching) {
      return <div>
        <Loader/>
      </div>
    };

  return (
    <CustomModal visibility={openModal} toggleVisibility={() => {setOpenModal; navigate("/subscription")}}>
      <div className="w-full py-10">
        <div className="flex items-center justify-center">
          <img width={200} height={200} src={!currentPlanValue ? birddance : sadbird} alt="GIF" />
        </div>
        <div className="px-10">
          <h1 className="text-center text-24 mt-10 font-semibold">
            {!currentPlanValue && "Welcome onboard." }
            {currentPlanValue && days <= 0 && plan === subscriptionPlans.FREE_TRIAL && "Your free trial has expired" }
            {currentPlanValue && days <= 0 && plan !== subscriptionPlans.FREE_TRIAL && "Your subscription has expired" }

          </h1>
          <p className="text-center mt-5 text-16">
          {!currentPlanValue  && "We are glad to have you with us. You will receive a free trial plan for 30 days. Start free trial to gain access to your dashboard and all features." }
          {currentPlanValue && days <= 0 && plan === subscriptionPlans.FREE_TRIAL && "Your 30 days free trial has expired. You no longer have access to your dashboard. To gain access to your dashboard you will need to upgrade your plan to continue using ArkHR" }
          {currentPlanValue && days <= 0 && plan !== subscriptionPlans.FREE_TRIAL && "Your subscription has expired. You no longer have access to your dashboard. To gain access to your dashboard you will need to renew your subscription to continue using ArkHR" }
          </p>

          {!currentPlanValue && (
            <Formik initialValues={{organization: ""}} onSubmit={handleSubmit}>
              {({values, setFieldValue}) => (
                  <Form>
                      <div>
                      {businesses.length > 1  && (
                      <div className="mt-10">
                        <FormikCustomSelect
                          label="Select Organization"
                          placeholder="Select option"
                          optionsParentClassName="text-12"
                          value={values.organization}
                          name="organization"
                          options={businesses}
                          onChange={(item: { value: string; text: string }) => {
                            setFieldValue("organization", item.value);
                          }}
                        />
                      </div>
                    )}
                      <div className="mt-10">
                          <CustomButton
                          type="submit"
                          className="!text-16"
                          title="Start Free Trial"
                          isLoading={isLoading}
                          handleClick={() => {}}
                          variant={ButtonProperties.VARIANT.primary.name}
                          />
                      </div>
                      </div>
                  </Form>
              )}
            </Formik>
          )}

          {currentPlanValue && days <= 0 && plan === subscriptionPlans.FREE_TRIAL && (
            <div className="mt-10 text-center">
              <CustomButton
                type="button"
                className="!text-16"
                title="Upgrade Now"
                isLoading={isLoading}
                handleClick={() => {navigate("/subscription")}}
                variant={ButtonProperties.VARIANT.primary.name}
              />
            </div>
          ) }

        {currentPlanValue && days <= 0 && plan !== subscriptionPlans.FREE_TRIAL && (
            <div className="mt-10 text-center">
              <CustomButton
                type="button"
                className="!text-16"
                title="Renew Now"
                isLoading={isLoading}
                handleClick={() => {navigate("/subscription")}}
                variant={ButtonProperties.VARIANT.primary.name}
              />
            </div>
          ) }


        </div>
      </div>
    </CustomModal>
  );
};

export default SubscriptionModal;
