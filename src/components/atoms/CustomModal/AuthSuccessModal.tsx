import React from "react";
import CustomButton from "../CustomButton/CustomButton";
import { useNavigate } from "react-router-dom";
import { ButtonProperties } from "../../shared/helpers";
import { useRecoilState } from "recoil";
import { emailAtom, otpAtom } from "../../../recoil/atom/authAtom";

const AuthSuccessModal = () => {
  const navigate = useNavigate();
  const [, setOtpAtom] = useRecoilState(otpAtom);
  const [, setEmailAtom] = useRecoilState(emailAtom);


  return (
    <div className="auth-modal-bg font-poppins fixed top-0 left-0 bottom-0 z-50 w-screen h-screen flex justify-center items-center">
      <div className="bg-white min-w-[691px] h-[552px] flex justify-center items-center rounded-lg">
        <div>
          <h1 className="text-24 font-poppins-medium text-neutral-dark text-center">
            Password reset successful!
          </h1>
          <p className="mt-6 text-neutral-normal text-center">
            You can now login with your new password
          </p>
          <div className="mt-[56px]">
            <CustomButton
              handleClick={() => {
                navigate("/");
                setOtpAtom({ email: "", otp: "" });
                setEmailAtom({ email: ""})
              }}
              title="Proceed"
              size={ButtonProperties.SIZES.big}
              variant={ButtonProperties.VARIANT.primary.name}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthSuccessModal;
