import { Dialog, DialogPanel, Transition, TransitionChild } from "@headlessui/react";
import { Fragment } from "react";
import SuccessBg from "../../../assets/images/successbg.png"
import { FaX } from "react-icons/fa6";
import { MdClose } from "react-icons/md";
import { useNavigate } from "react-router-dom";

interface SuccessModalProps {
  toggleVisibility?: Function;
  visibility: boolean;
//   children: any;
  callBack?: Function;
  text?: string;
  route?: string;
  handleClick?: any;
}

const SuccessModal = ({ toggleVisibility, visibility, callBack, text, route, handleClick }: SuccessModalProps) => {
  const navigate = useNavigate();
  const closeModal = () => {
    toggleVisibility && toggleVisibility(false);
    if(route) {
      navigate(`${route ? route: ""}`); 
    }
    handleClick();
    callBack && callBack();
  };

  return (
    <>
      <Transition appear as={Fragment} show={visibility}>
        <Dialog as="div" className="relative z-[120]" onClose={closeModal}>
          <TransitionChild
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black  bg-opacity-60" />
          </TransitionChild>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <TransitionChild
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <DialogPanel className="w-full relative max-w-[495px] h-[405px] rounded-lg transform overflow-hidden bg-white text-neutral-normal text-left align-middle shadow-xl transition-all">
                  <div>
                    <div onClick={closeModal} className="absolute text-neutral-dark top-[25.5px] right-[13.5px] cursor-pointer p-1 rounded-full bg-purple-light flex justify-center items-center">
                    <MdClose size={24}/>

                    </div>
                    <img className="rounded-tl-lg rounded-tr-lg" src={SuccessBg} alt="success" />
                    <div className="mt-[51px] px-11">
                      <h1 className="text-center text-20 font-poppins-medium text-neutral-dark">Success</h1>
                      <p className="pt-5 text-center">{text}</p>
                    </div>
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default SuccessModal;

