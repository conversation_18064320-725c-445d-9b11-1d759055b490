import { SearchNormal } from 'iconsax-react';
import React, { useState } from 'react';
import { MdFilterList } from 'react-icons/md';
import { useTable } from 'react-table';
import CustomPagination from '../CustomPagination';
import FilterDropdown from '../Cards/FilterDropdown';
import CustomCheckBox from '../CustomCheckBox/CustomCheckBox';
import useClickOutside from '../../shared/hooks';
import { FaX } from 'react-icons/fa6';

interface CustomTableProps {
  columns: Array<any>;
  data: any;
  tableClass?: string;
  tHeadClass?: string;
  tBodyClass?: string;
  tdClass?: string;
  thClass?: string;
  tableHeadTrClass?: string;
  tableBodyTrClass?: string;
  filterListOptions?: Array<string>;
  handleFilter?: any;
  handlePageChange?: any;
  meta?: any;
  checkedItems?: any;
  header?: any;
  handleSearch?: any;
  customFilter?: any;
  dropdowmCardClass?: string;
  hideSearch?: boolean;
  placeholder?: any;
  isLoading?: boolean;
}

const CustomTable = ({
  columns,
  placeholder = 'Search by name or any related keywords',
  data,
  tableClass,
  tHeadClass,
  tBodyClass,
  tdClass,
  thClass,
  tableBodyTrClass,
  tableHeadTrClass,
  filterListOptions,
  handleFilter,
  hideSearch,
  meta,
  header,
  checkedItems,
  handlePageChange,
  handleSearch,
  customFilter,
  dropdowmCardClass,
  isLoading = false,
}: CustomTableProps) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [showFilterList, setShowFilterList] = useState<boolean>(false);

  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } = useTable({
    columns,
    data,
  });

  const node = useClickOutside(() => {
    setShowFilterList(false);
  });

  return (
    <>
      {header && <div className='bg-white text-[#2B2B34] font-bold mb-5'>{header}</div>}
      <div className='bg-white pb-5 pt-2 px-3'>
        {/* <hr /> */}
        <div className='relative mt-3 w-full'>
          {!hideSearch && (
            <div>
              <input
                type='text'
                className={`!border indent-10 !outline-[#EFF1F4]  placeholder:text-[#333333] text-14 w-full h-[62px] border-[#EFF1F4] bg-[#fff] `}
                placeholder={`${placeholder}`}
                value={searchTerm}
                onChange={(e) => {
                  handleSearch(e.target.value);
                  setSearchTerm(e.target.value);
                }}
              />
              <SearchNormal className='absolute top-6 ml-3' size={16} />
              {searchTerm && (
                <FaX
                  onClick={() => {
                    setSearchTerm('');
                    handleSearch();
                  }}
                  className='absolute right-32 top-6 ml-3 text-purple-normal cursor-pointer'
                  size={12}
                />
              )}
            </div>
          )}
          {(filterListOptions || customFilter) && (
            <div ref={node}>
              <div
                onClick={() => setShowFilterList(!showFilterList)}
                className='absolute mr-3 text-[#344054] right-0 top-0 cursor-pointer flex justify-center items-center text-14 gap-2 font-poppins border border-[#EFEFEF]  w-[93px] h-10 my-[11px]'
              >
                <MdFilterList size={20} />
                <span className='text-neutral-normal'>Filter</span>
              </div>
              {showFilterList && (
                <FilterDropdown className={`!top-[70px] ${dropdowmCardClass}`}>
                  {filterListOptions ? (
                    <ul className='text-14'>
                      {filterListOptions?.map((option, index) => (
                        <li key={index} className='flex gap-3 border-b px-2.5 py-2.5'>
                          {' '}
                          <CustomCheckBox
                            onChange={(e: any) =>
                              handleFilter({ cheked: e.target.checked, selectedOption: option })
                            }
                            customClass='w-4 h-4 align-middle'
                          />{' '}
                          {option}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    customFilter
                  )}
                </FilterDropdown>
              )}
            </div>
          )}
        </div>
        <div className='border mt-3'>
          <table className={`w-full ${tableClass}`} {...getTableProps()}>
            <thead className={`${tHeadClass}`}>
              {headerGroups.map((headerGroup, index) => (
                <tr
                  className={`${tableHeadTrClass}`}
                  {...headerGroup.getHeaderGroupProps()}
                  key={index}
                >
                  {headerGroup.headers.map((column, index) => (
                    <th
                      className={`text-12 text-[#42526D] font-poppins-medium text-left py-[14px] px-6 border-b-[1px] border-[#EAECF0] bg-[#EEF2FF] ${thClass}`}
                      {...column.getHeaderProps()}
                      key={index}
                    >
                      {column.render('Header')}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody className={`${tBodyClass}`} {...getTableBodyProps()}>
              {isLoading && (
                <tr>
                  <td
                    colSpan={columns.length}
                    className='text-center text-[#42526D] font-poppins-medium align-middle py-3'
                  >
                    <span className='animate-spin inline-block w-5 h-5 border-2 border-current border-t-transparent rounded-full' />
                  </td>
                </tr>
              )}
              {rows.length > 0 && (
                <>
                  {rows.map((row, index) => {
                    prepareRow(row);
                    return (
                      <tr
                        className={`font-poppins-medium text-[#42526D] ${
                          checkedItems?.some((item: any) => item?.id === row.original.id)
                            ? 'bg-purple-light'
                            : ''
                        } ${tableBodyTrClass}`}
                        {...row.getRowProps()}
                        key={index}
                      >
                        {row.cells.map((cell, index) => {
                          return (
                            <td
                              className={`first-letter:capitalize text-12 px-6 text-left py-[14px] border-b  border-[#EAECF0] text-[#42526D] ${tdClass}`}
                              {...cell.getCellProps()}
                              key={index}
                            >
                              {cell.render('Cell')}
                            </td>
                          );
                        })}
                      </tr>
                    );
                  })}
                </>
              )}

              {rows.length == 0 && !isLoading && (
                <tr>
                  <td
                    colSpan={6}
                    className='text-center text-[#42526D]  font-poppins-medium align-middle py-5'
                  >
                    No record found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          {meta && meta.last_page > 1 && (
            <div className='flex justify-center mt-9 mb-5'>
              <CustomPagination
                pageCount={Math.ceil(meta?.total / meta.per_page)}
                onChange={(pageNumber) => {
                  handlePageChange(pageNumber);
                }}
                forcePage={meta.current_page}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default CustomTable;
