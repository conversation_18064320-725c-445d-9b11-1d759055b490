import { IoIosArrowRoundBack } from "react-icons/io";

interface CustomKeyPadProps {
    amountFund: string;
    setAmountFund: React.Dispatch<React.SetStateAction<string>>;
}

const CustomNumberKeyPad = ({
    amountFund,
    setAmountFund,
}: CustomKeyPadProps) => {
    const amount = [1, 2, 3, 4, 5, 6, 7, 8, 9, '', 0, 'backspace'];

    const formatNumber = (val: string) => {
        if (!val) return '';
        const num = Number(val.replace(/,/g, ''));
        return num.toLocaleString();
    };

    const handleAmountClick = (item: number | string) => {
        setAmountFund((prev) => {
            let raw = prev.replace(/,/g, '');
            if (typeof item === 'number') {
                raw = raw === '0' ? item.toString() : raw + item.toString();
            } else if (item === 'backspace') {
                raw = raw.slice(0, -1);
            }

            return formatNumber(raw);
        });
    };

    return (
        <div>
            <input
                placeholder='Enter amount'
                className='w-full h-12 border-0 border-neutral-light rounded-md px-3 text-center font-semibold focus:border-purple-normal-active'
                name='amount'
                value={amountFund}
                type='text'
                disabled
            />
            <div className='grid grid-cols-3 mt-6 gap-2 w-[200px] mx-auto '>
                {amount.map((item, index) => (
                    <div
                        key={index}
                        className="flex rounded p-1 w-[50px] h-full items-center justify-center text-purple-dark text-[1.5rem] font-bold cursor-pointer"
                        onClick={() => handleAmountClick(item)}
                    >
                        <p>{item === 'backspace' ? <IoIosArrowRoundBack /> : item}</p>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default CustomNumberKeyPad;
