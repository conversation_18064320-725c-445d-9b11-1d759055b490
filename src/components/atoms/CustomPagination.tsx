import { useEffect, useRef, useState } from "react";
import ReactPaginate from "react-paginate";
import CustomButton from "./CustomButton/CustomButton";
import { ArrowLeft, ArrowRight } from "iconsax-react";

interface CustomPaginationProps {
  forcePage: number;
  initialPage?: number;
  pageCount: number;
  onChange: (page: number) => void;
}

const CustomPagination = ({ forcePage, initialPage, onChange, pageCount }: CustomPaginationProps) => {
  const [page, setPage] = useState<number>(0);

  const hasClickedNumber = useRef<boolean>(false);

  const handlePageChange = (page: number) => {
    hasClickedNumber.current = true;

    setPage(page);
  };

  useEffect(() => {
    if (hasClickedNumber.current) {
      onChange(page);
    }
  }, [page]);

  return (
    <ReactPaginate
      activeClassName="font-bold bg-purple-light !text-purple-normal font-poppins-medium py-2.5 rounded-lg"
      breakLabel="..."
      breakLinkClassName="px-2.5 py-2.5 cursor-disabled"
      containerClassName="flex items-center -space-x-px w-fit font-poppins text-14"
      disableInitialCallback={true}
      forcePage={forcePage - 1}
      initialPage={initialPage}
      marginPagesDisplayed={pageCount > 5 && page > 3 ? 1 : 2}
      nextLabel={<CustomButton className="!text-neutral-normal ml-[18px] !w-[90px] !h-[36px] !text-14 !font-normal !rounded-lg" isTransparent handleClick={() => {}} title="Next" icon={<ArrowRight size={20}/>} />}
      nextLinkClassName=""
      onPageChange={(data) => handlePageChange(data.selected + 1)}
      // onPageChange={(event) => setPage(event.selected)}
      pageCount={pageCount}
      pageLinkClassName="px-4 py-2.5 text-14"
      pageRangeDisplayed={0}
      previousLabel={<CustomButton className="!text-neutral-normal mr-[18px] !w-[90px] !h-[36px] !font-normal !text-14 !rounded-lg" isTransparent handleClick={() => {}} title="Back" leftIcon={<ArrowLeft size={20}/>} />}
      previousLinkClassName=""
    />
  );
};

export default CustomPagination;
