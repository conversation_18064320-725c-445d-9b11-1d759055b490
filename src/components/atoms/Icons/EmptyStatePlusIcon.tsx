import React from 'react'

const EmptyStatePlusIcon = () => {
  return (
    <svg width="119" height="114" viewBox="0 0 119 114" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="52" height="64" rx="10" fill="#B2BBC6" fillOpacity="0.9"/>
    <g filter="url(#filter0_d_1034_23013)">
    <mask id="path-2-inside-1_1034_23013" fill="white">
    <path d="M21 35C21 29.4772 25.4772 25 31 25H63C68.5228 25 73 29.4772 73 35V79C73 84.5228 68.5228 89 63 89H31C25.4772 89 21 84.5228 21 79V35Z"/>
    </mask>
    <path d="M21 35C21 29.4772 25.4772 25 31 25H63C68.5228 25 73 29.4772 73 35V79C73 84.5228 68.5228 89 63 89H31C25.4772 89 21 84.5228 21 79V35Z" fill="#EAECF0"/>
    <path d="M15 35C15 26.1634 22.1634 19 31 19H57C65.8366 19 73 26.1634 73 35C73 32.7909 68.5228 31 63 31H31C28.7909 31 27 32.7909 27 35H15ZM73 89H21H73ZM31 89C22.1634 89 15 81.8366 15 73V35C15 26.1634 22.1634 19 31 19V31C28.7909 31 27 32.7909 27 35V79C27 84.5228 28.7909 89 31 89ZM73 25V89V25Z" fill="white" mask="url(#path-2-inside-1_1034_23013)"/>
    </g>
    <path d="M56.9998 60.3332H50.3332V66.9998C50.3332 67.7332 49.7332 68.3332 48.9998 68.3332C48.2665 68.3332 47.6665 67.7332 47.6665 66.9998V60.3332H40.9998C40.2665 60.3332 39.6665 59.7332 39.6665 58.9998C39.6665 58.2665 40.2665 57.6665 40.9998 57.6665H47.6665V50.9998C47.6665 50.2665 48.2665 49.6665 48.9998 49.6665C49.7332 49.6665 50.3332 50.2665 50.3332 50.9998V57.6665H56.9998C57.7332 57.6665 58.3332 58.2665 58.3332 58.9998C58.3332 59.7332 57.7332 60.3332 56.9998 60.3332Z" fill="#B2BBC6"/>
    <defs>
    <filter id="filter0_d_1034_23013" x="17" y="0" width="102" height="114" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
    <feFlood floodOpacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dx="21"/>
    <feGaussianBlur stdDeviation="12.5"/>
    <feComposite in2="hardAlpha" operator="out"/>
    <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1034_23013"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1034_23013" result="shape"/>
    </filter>
    </defs>
    </svg>    
  )
}

export default EmptyStatePlusIcon