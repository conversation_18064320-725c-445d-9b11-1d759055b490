import { LinearProgress } from "@mui/material"

interface CustomProgressBarProps {
    value: number;
}


const CustomProgressBar: React.FC<CustomProgressBarProps> = ({ value, }) => {
    return (
        <div className="">
            <LinearProgress
                variant="determinate"
                value={value}
                sx={{
                    height: 8,
                    borderRadius: 5,
                    backgroundColor: '#eee', 
                    '& .MuiLinearProgress-bar': {
                        borderRadius: 5,
                        backgroundColor: '#3730A3',
                    },
                }}
            />
        </div>
    )
}

export default CustomProgressBar;