
import ReactQuill from "react-quill"


interface CustomQuillTextAreaProps {
    value: string;
    handleChange: (value: string) => void;
    className?: string;
    placeholder?: string;
    readOnly?: boolean;
}



const CustomQuillTextArea: React.FC<CustomQuillTextAreaProps> = ({ value, handleChange, className, placeholder, readOnly }) => {
    return (
        <ReactQuill
            value={value}
            onChange={handleChange}
            theme="snow"
            placeholder={placeholder}
            className={` focus:outline-none ${className}`}
            readOnly={readOnly}
        />
    );
};

export default CustomQuillTextArea;