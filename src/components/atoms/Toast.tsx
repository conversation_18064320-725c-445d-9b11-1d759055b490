import { toast } from "react-toastify";

import { NotificationTypes } from "../shared/helpers/enums";
import errorGreen from '../../assets/images/errorGreen.svg'
import errorRed from '../../assets/images/errorRed.svg'


export const clustarkToast = (type: NotificationTypes, text: string, autoClose?: number) => {
  /**
   * Object is to be updated with icons for the different toast type when
   * we have toast of the different types e.g success, info e.t.c
   * **/
  // const toastIcons: Record<NotificationTypes, any> = {
  //   error: "",
  //   success: "",
  //   info: "",
  // };

  toast(
    <div className={`w-full flex smallLaptop:w-[400px] gap-2 text-16 font-poppins-medium `}>
      {type === NotificationTypes.SUCCESS && <img src={errorGreen} /> }
      {type === NotificationTypes.ERROR && <img src={errorRed} /> }
      
      <span className="mt-[3px]">{text}</span>
    </div>,
    {
      type,
      icon: false,
      position: "top-center",
      hideProgressBar: true,
      autoClose: autoClose || 5000,
    }
  );
};
