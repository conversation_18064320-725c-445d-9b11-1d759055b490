import React from "react";

const Loader = () => {
  const rows = new Array(10).fill(0); // 10 rows
  const cols = new Array(5).fill(0);  // 5 columns

  return (
    <div className="p-4 animate-pulse">
      <div className="flex justify-between mb-4">
        <div className="h-4 bg-gray-300 rounded w-1/3"></div>
        <div className="h-8 bg-gray-300 rounded w-24"></div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr className="bg-blue-100">
              {cols.map((_, index) => (
                <th key={index} className="px-6 py-3">
                  <div className="h-4 bg-gray-300 rounded w-24"></div>
                </th>
              ))}
              <th className="px-6 py-3">
                <div className="h-4 bg-gray-300 rounded w-12"></div>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {rows.map((_, rowIndex) => (
              <tr key={rowIndex}>
                {cols.map((_, colIndex) => (
                  <td key={colIndex} className="px-6 py-4">
                    <div className="h-4 bg-gray-200 rounded w-full"></div>
                  </td>
                ))}
                <td className="px-6 py-4">
                  <div className="h-6 bg-gray-300 rounded w-12"></div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Loader;
