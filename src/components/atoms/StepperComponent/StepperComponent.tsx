import { ArrowRight } from "iconsax-react";
import React, { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { useRecoilValue } from "recoil";
import { createAccountAtom, otpAtom } from "../../../recoil/atom/authAtom";


interface StepProps {
  goNextStep: () => void;
  goPreviousStep: () => void;
  currentStep: number;
  isLast: boolean;
  isFirst: boolean;
  step: number;
}

interface StepperComponentProps {
  steps: Step[];
}

interface Step {
  element: (stepProps: StepProps) => JSX.Element;
}

const StepperComponent = ({ steps }: StepperComponentProps) => {
  const [currentStep, setCurrentStep] = useState<number>(1);
  // const [isFirst, setIsFirst] = useState<boolean>(true);
  const [, setIsLast] = useState<boolean>(false);
  const [searchParams, _] = useSearchParams();
  const queryParam: any = searchParams.get("step");
  const getAuthDataAtom = useRecoilValue(createAccountAtom);
  const otpDataAtom = useRecoilValue(otpAtom);

  const stepTracker = () => {
    if(parseInt(queryParam) === 2 && getAuthDataAtom.companyEmail && !otpDataAtom.otp) {
     setCurrentStep(2)
    }
    if(parseInt(queryParam) === 3 && otpDataAtom.otp) {
      setCurrentStep(3)
     }
     if(parseInt(queryParam) === 4 && getAuthDataAtom.firstName) {
      setCurrentStep(4)
     }
     if(parseInt(queryParam) === 5 && getAuthDataAtom.businessName) {
      setCurrentStep(5)
     }
  }

  useEffect(() => {
    let mounted = true;

    if (mounted) {
      stepTracker();
      checkPosition();
    }

    return () => {
      mounted = false;
    };
  }, [currentStep]);

  const goNextStep = () => {
    const nextStep = currentStep + 1;

    if (nextStep <= steps.length) {
      setCurrentStep(nextStep);
    }
  };

  const goPreviousStep = () => {
    const previousStep = currentStep - 1;

    if (previousStep >= 1) {
      setCurrentStep(previousStep);
    }
  };

  const checkPosition = () => {
    // const first = currentStep === 1;
    const last = currentStep === steps.length;
    // setIsFirst(first);
    setIsLast(last);
  };


  return (
    <div className="bg-white w-[90%] pb-20 smallLaptop:w-4/5 desktop:min-w-[636px] mx-auto relative  h-[90vh] overflow-y-scroll hide-scrollbar">
      <div className="px-8 pt-[77px] mb-[69px] flex items-center justify-between">
        {/* {isFirst ? "+" : <p className="cursor-pointer"  onClick={goPreviousStep} >-</p>} */}
        <h5 className="flex gap-2 text-[13px] text-purple-dark font-poppins-medium">Step {currentStep} <span className="flex justify-center items-center"> <ArrowRight size={14}/></span></h5>
        <div className="absolute left-[50%] flex items-center -translate-x-[50%]">
          {steps.map((_, index) => (
            <div key={index}>{currentStep == index + 1 ? <p className="w-[18px] h-3 rounded-full bg-purple-light-active mx-1"/> : <p className="w-3 h-3 mx-1 rounded-full bg-[#B2BBC65C] bg-opacity-[36%]"/>}</div>
          ))}
        </div>
      </div>
      <div>
        {steps.map((step, index) => (
          <div key={index}>
            {currentStep === index + 1 && (
              <step.element
                currentStep={currentStep}
                goNextStep={goNextStep}
                goPreviousStep={goPreviousStep}
                isFirst={index === 0}
                isLast={index === steps.length - 1}
                step={index + 1}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default StepperComponent;
