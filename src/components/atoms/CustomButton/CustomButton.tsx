import React, { Fragment, LegacyRef, useEffect, useState } from 'react';
import { ImSpinner3 } from 'react-icons/im';

import { ButtonProperties } from '../../shared/helpers';

// import Icon from "@atoms/Icons";

interface ButtonProps {
  handleClick: () => void;
  className?: string;
  type?: 'button' | 'submit' | 'reset' | undefined;
  isDisabled?: boolean;
  title?: string | React.ReactNode;
  isLoading?: boolean;
  value?: string;
  icon?: any;
  iconClass?: string;
  id?: string;
  ref?: LegacyRef<HTMLButtonElement> | undefined;
  size?: string;
  iconPosition?: string;
  variant?: string;
  isTransparent?: boolean;
  leftIcon?: any;
}

/**
 * Renders the actual content of the Button
 * @param {any} title title or text of the button
 * @param {boolean} isLoading Loading state
 * @return {React.Component} Button component
 */
const renderContent = (title: string | undefined, isLoading: boolean | undefined) => (
  <Fragment>
    {isLoading ? (
      <ImSpinner3 className='animate-spin text-inherit' />
    ) : (
      <Fragment>{title}</Fragment>
    )}
  </Fragment>
);

const CustomButton = ({
  handleClick,
  variant,
  isTransparent,
  iconPosition,
  size,
  ref,
  id,
  isDisabled,
  className,
  type,
  title,
  isLoading,
  value,
  leftIcon,
  icon,
}: // iconClass,
ButtonProps) => {
  const [background, setBackGround] = useState('');
  const [hover, setHover] = useState('');
  const [_, setDisabled] = useState('');
  const [focused, setFocused] = useState('');
  const [textColor, setTextColor] = useState('white');
  const [, setBorderColor] = useState('');
  const [iconFill, setIconFill] = useState('');
  /**
   * This displays the rendered content
   */
  const content = (
    <div className='flex items-center'>
      {!isLoading && icon && iconPosition === ButtonProperties.ICON_POSITION.start ? icon : ''}
      {renderContent(typeof title === 'string' ? title : '', isLoading)}
      {!isLoading && icon && iconPosition === ButtonProperties.ICON_POSITION.end ? icon : ''}
    </div>
  );

  const setVariantColours = (variantType: string) => {
    switch (variantType) {
      case ButtonProperties.VARIANT.primary.name:
        if (isTransparent) {
          setBackGround('transparent');
          setTextColor(`${ButtonProperties.VARIANT.primary.background}`);
          setBorderColor(`${ButtonProperties.VARIANT.primary.background}`);
          setIconFill(`${ButtonProperties.VARIANT.primary.background}`);
        } else {
          setBackGround(`${ButtonProperties.VARIANT.primary.background}`);
          setTextColor('white');
        }
        setHover(`${ButtonProperties.VARIANT.primary.hover}`);
        setDisabled(`${ButtonProperties.VARIANT.primary.disabled}`);
        setFocused(`${ButtonProperties.VARIANT.primary.focused}`);

        break;
      case ButtonProperties.VARIANT.secondary.name:
        if (isTransparent) {
          setBackGround('transparent');
          setTextColor(`${ButtonProperties.VARIANT.secondary.background}`);
          setBorderColor(`${ButtonProperties.VARIANT.secondary.background}`);
          setIconFill(`${ButtonProperties.VARIANT.secondary.background}`);
        } else {
          setBackGround(`${ButtonProperties.VARIANT.secondary.background}`);
          setTextColor('white');
        }
        setHover(`${ButtonProperties.VARIANT.secondary.hover}`);
        setDisabled(`${ButtonProperties.VARIANT.secondary.disabled}`);
        setFocused(`${ButtonProperties.VARIANT.secondary.focused}`);

        break;
    }
  };

  useEffect(() => {
    let mounted = true;

    if (mounted && variant) {
      setVariantColours(variant);
    }

    return () => {
      mounted = false;
    };
  }, [variant, isTransparent, iconFill]);

  return isLoading || isDisabled ? (
    <button
      className={`text-${textColor} text-white font-semibold font-poppins border hover:bg-purple-normal-hover ${
        isTransparent
          ? `border-purple-normal text-purple-normal font-bold hover:bg-white`
          : `border-${background}`
      }  whitespace-nowrap py-[16px] rounded-[4px] cursor-not-allowed flex justify-center items-center h-[47px] ${
        size === ButtonProperties.SIZES.small
          ? 'px-[16px]'
          : size === ButtonProperties.SIZES.medium
          ? 'tablet:w-[343px] px-[78px]'
          : size === ButtonProperties.SIZES.big
          ? 'w-full tablet:w-[569px]'
          : 'w-full'
      } bg-${background} ${isDisabled ? 'opacity-50' : ''} ${
        isTransparent
          ? `hover:text-${hover} hover:border-${hover} focus:text-${focused}`
          : `hover:bg-${hover} focus:bg-${focused}`
      } ${className}`}
      id={id}
      ref={ref}
      type={type}
      value={value}
    >
      {content}
    </button>
  ) : (
    <button
      className={`text-${textColor} text-white  gap-2 font-semibold font-poppins border hover:bg-purple-normal-hover ${
        isTransparent
          ? `border-purple-normal !text-purple-normal  font-bold hover:bg-white`
          : `border-${background} `
      }  whitespace-nowrap py-[16px] rounded-[4px] flex justify-center items-center h-[47px] cursor-pointer ${
        size === ButtonProperties.SIZES.small
          ? 'px-[16px]'
          : size === ButtonProperties.SIZES.medium
          ? 'tablet:w-[343px] px-[78px]'
          : size === ButtonProperties.SIZES.big
          ? 'w-full tablet:w-[569px]'
          : 'w-full'
      } bg-${background} ${
        isTransparent
          ? `hover:text-${hover}  hover:border-${hover} focus:text-${focused}`
          : `hover:bg-${hover} focus:bg-${focused}`
      }   ${className}`}
      id={id}
      onClick={() => handleClick()}
      ref={ref}
      type={type}
      value={value}
    >
      {leftIcon}
      {content} {icon}
    </button>
  );
};

export default CustomButton;
