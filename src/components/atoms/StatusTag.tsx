import React from 'react';

const STATUS_STYLES = {
  green: [
    'active',
    'successful',
    'resolved',
    'answered',
    'approved',
    'success',
    'assigned',
    'paid',
    'completed',
  ],
  yellow: [
    'pending',
    'pending_approval',
    'pending_payment',
    'open',
    'in_review',
    'completed',
    'medium',
  ],
  red: [
    'inactive',
    'expired',
    'declined',
    'rejected',
    'failed',
    'under_investigation',
    'unpaid',
    'high',
  ],
  grey: ['cancelled', 'damaged', 'closed', 'revoked', 'suspended', 'low'],
};

const getStatusStyle = (status: string) => {
  const normalizedStatus = status?.toLowerCase();

  if (STATUS_STYLES.green.includes(normalizedStatus)) {
    return {
      bgColor: 'bg-[#ECFDF3]',
      textColor: 'text-[#027A48]',
      dotColor: 'bg-[#027A48]',
    };
  }
  if (STATUS_STYLES.yellow.includes(normalizedStatus)) {
    return {
      bgColor: 'bg-[#FFAA33] bg-opacity-[17%]',
      textColor: 'text-[#FFA500]',
      dotColor: 'bg-[#FFA500]',
    };
  }
  if (STATUS_STYLES.red.includes(normalizedStatus)) {
    return {
      bgColor: 'bg-[#FFF2EA]',
      textColor: 'text-[#F15046]',
      dotColor: 'bg-[#F15046]',
    };
  }
  if (STATUS_STYLES.grey.includes(normalizedStatus)) {
    return {
      bgColor: 'bg-[#B2BBC62B]',
      textColor: 'text-[#868686]',
      dotColor: 'bg-[#818181]',
    };
  }

  return {
    bgColor: '',
    textColor: '',
    dotColor: '',
  };
};

const StatusTag: React.FC<{ status: string; className?: string }> = ({ status, className }) => {
  const { bgColor, textColor, dotColor } = getStatusStyle(status);

  return (
    <div>
      <p
        className={`rounded-lg capitalize font-poppins-medium text-center py-1 px-3 w-fit flex justify-center items-center ${bgColor} ${textColor} ${className}`}
      >
        <span className={`w-2 h-2 rounded-full mr-2 ${dotColor}`} />
        {status || '--'}
      </p>
    </div>
  );
};

export default StatusTag;
