import React, { useState } from "react";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import moment from "moment";
import { FaCircleXmark } from 'react-icons/fa6'

interface DateRangePickerProps {
  isOpen: boolean;
  onClose: () => void;
  handleClear?: any;
  onApply: (startDate: moment.Moment | null, endDate: moment.Moment | null) => void;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  isOpen,
  onClose,
  onApply,
  handleClear
}) => {
  const [startDate, setStartDate] = useState<moment.Moment | null>(null);
  const [endDate, setEndDate] = useState<moment.Moment | null>(null);

  const handleApply = () => {
    if (startDate && endDate) {
      onApply(startDate, endDate);
      onClose();
    }
    //  else {
    //   alert("Please select both start and end dates.");
    // }
  };

  if (!isOpen) return null;

  return (
    <div className="absolute right-10 z-50 shadow-lg">
      <div className="bg-white rounded-lg shadow-lg p-6 w-[400px]">
        <div className="flex justify-end mb-3">
          <FaCircleXmark className="cursor-pointer rounded-full p-1 text-purple-dark" onClick={() => onClose()} size={25}/>

        </div>
        <div className="flex gap-4 mt-2">
          <div>
          <label className="text text-purple-dark font-poppins-medium">Select start date</label>
            <DatePicker
              value={startDate}
              onChange={(date) => {setStartDate(date);}}
              slotProps={{
                textField: {
                  size: "small",
                  sx:{
                    "& .MuiOutlinedInput-root": {
                      "&:hover fieldset": {
                        borderColor: "#B2BBC699",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#3730A3",
                      },
                      height: "30px",
                      borderRadius: "2px",
                    },
                    "& .MuiOutlinedInput-input": {
                      border: 0,
                      borderRadius: 3,
                      textTransform: "lowercase",
                      fontFamily: "Cabin",
                      fontWeight: "bold",
                      color: "#71797E"
                    },
                    svg: {
                      color: "#3730A3"
                    }
                  },
                },

              }}
                className="!mt-1"
            />

          </div>
          <div>
            <label className="text-purple-dark font-poppins-medium">Select end date</label>    
            <DatePicker
              disabled={!startDate}
              value={endDate}
              onChange={(date) => setEndDate(date)}
              minDate={startDate}
              className="!mt-1"
              slotProps={{
                textField: {
                  size: "small",
                  sx:{
                    "& .MuiOutlinedInput-root": {
                      "&:hover fieldset": {
                        borderColor: "#B2BBC699",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#3730A3",
                      },
                      height: "30px",
                      borderRadius: "2px",
                    },
                    
                    "& .MuiOutlinedInput-input": {
                      border: 0,
                      borderRadius: 3,
                      textTransform: "lowercase",
                      fontFamily: "Cabin",
                      fontWeight: "bold",
                      color: "#71797E	"
                    },
                    svg: {
                      color: "#3730A3"
                    }
                  },
                },

              }}
            />

          </div>
          </div>
          <div className="flex mt-6 justify-end gap-4">
            <button
              className="border border-purple-normal text-purple-normal px-9 py-1 rounded"
              onClick={() => {setEndDate(null); setStartDate(null); handleClear()}}
            >
              Clear
            </button>
            <button
              className="bg-purple-normal text-white py-1 px-9 rounded"
              onClick={handleApply}
            >
              Apply
            </button>
          </div>
      </div>
    </div>
  );
};

export default DateRangePicker;
