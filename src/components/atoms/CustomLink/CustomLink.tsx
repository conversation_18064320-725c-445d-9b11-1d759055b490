import React from "react";

export interface CustomLinkProps {
  destination: string;
  text: string;
  // children: ReactNode;
  className?: string;
}

const CustomLink = ({ text, destination, className }: CustomLinkProps) => {
  return (
    <a href={destination}>
      <span className={`cursor-pointer font-poppins-medium text-purple-normal-active :hover-text-purple-normal-hover } ${className}`}>{text}</span>
    </a>
  );
};

export default CustomLink;

