import React from "react";

function CustomToggle({ onToggle,  container, isOn, toggleClassName }: any) {
  const handleToggle = () => {
    onToggle(!isOn);
  };

  return (
    <div className="flex items-center">
      <button
      type="button"
        className={`relative inline-block w-9 h-[18px] border-2 rounded-full ${container} ${isOn ? "bg-purple-normal-active" : "bg-neutral-light-active"}`}
        onClick={handleToggle}
      >
        <span
          className={`${toggleClassName} absolute left-0 top-0 h-[14px] w-[14px] mt-[0.5px] rounded-full transition-transform ${
            isOn ? "bg-white translate-x-full" : "bg-white translate-x-0"
          }`}
        />
      </button>
      {/* {showLabel && <span className="ml-3 text-HavannaGreen-primary">{isOn ? "ON" : "OFF"}</span>} */}
    </div>
  );
}

export default CustomToggle;
