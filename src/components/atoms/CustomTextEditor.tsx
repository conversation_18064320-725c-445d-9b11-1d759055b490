import React, { useRef } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

interface customEditorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
}
const CustomTextEditor = ({value, onChange, className, placeholder}: customEditorProps) => {
  const quillRef = useRef<ReactQuill | null>(null);

  
  
  

  const modules = {
    toolbar: {
      container: [
        [{ header: "1" }, { header: "2" }, { font: [] }],
        [{ size: [] }],
        ["bold", "italic", "underline", "strike", "blockquote"],
        [{ list: "ordered" }, { list: "bullet" }],
        // ["link", "image"],
        // ["clean"],
      ],
    },
  };
  return (
    <div>
      <ReactQuill
        ref={quillRef}
        className={className}
        value={value}
        onChange={onChange}
        modules={modules}
        placeholder={placeholder}
      />
    </div>
  );
};

export default CustomTextEditor;
