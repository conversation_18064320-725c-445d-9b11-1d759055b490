import { useField } from "formik";

import ErrorMessage from "../../atoms/ErrorMessage";
import { useState } from "react";
import useClickOutside from "../..//shared/hooks";
import { ArrowDown2, ArrowUp2, SearchNormal } from "iconsax-react";
import { Country } from "country-state-city";

export interface OptionType {
  text: string;
  value: string;
}

const FormikSelectCurrency = ({
  label,
  id,
  parentContainer,
  disabled,
  container,
  options,
  value,
  onChange,
  optionsParentClassName,
  placeholder,
  handleAmount,
  amountValue,
  disableCurrency,
  ...props
}: any) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [amount, setAmount] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [focusColor, setFocusColor] = useState(false);
  const [selectedItem, setSelectedItem] = useState(false);
  const [, meta] = useField(props);

  const currency = Country.getAllCountries()
    .filter(
      (value, index, self) =>
        index === self.findIndex((t) => t.currency === value.currency)
    )
    .map((country) => ({
      text: country.currency,
      value: country.isoCode,
  }));

  const node = useClickOutside(() => {
    setIsOpen(false);
    setFocusColor(false);
    setSearchTerm("");
  });
  

  const filteredOptions = currency?.filter((option) =>
    option?.text?.toLowerCase()?.includes(searchTerm.toLowerCase())
  );

  const handleSelectedItem = (option) => {
    setSelectedItem(option.text);
    setIsOpen(false);
    setSearchTerm("");
    onChange(option);
  };

  return (
    <>
      <div className="text-16 mb-4">
        <label htmlFor={id}>{label}</label>
      </div>
      <div className="relative" ref={node}>
        <div
          onClick={() => {
            setFocusColor(true);
          }}
          className={`  rounded-[4px] ${
            focusColor ? "border-purple-normal-hover" : ""
          }  ${parentContainer} ${
            disabled
              ? "bg-[#F4F4F4] !cursor-not-allowed border-[0.6px] border-[#B2BBC699]"
              : "border-[0.6px] border-[#B2BBC699]"
          } flex items-center h-[47px] !outline-none px-3 w-full ${
            meta.touched && meta.error && !amount ? "!border !border-alert-text-error" : ""
          }  `}
        >
          <div className={`!w-full flex  ${container}`}>
            <div
              onClick={() => {
                setIsOpen(true);
                setFocusColor(true);
              }}
              className="flex w-[5rem] h-[55px] "
            >
              <input
                className={`h-auto w-[4rem] !outline-none border-none bg-transparent `}
                readOnly
                type="text"
                value={selectedItem || value || placeholder }
                disabled={disableCurrency}
              />
              <div className="flex justify-center items-center border-r">
                {isOpen && !disabled ? (
                  <ArrowUp2 size={16} className="text-neutral-dark mr-3" />
                ) : (
                  <ArrowDown2
                    size={16}
                    className="text-neutral-dark mr-3"
                  />
                )}

              </div>

            </div>
            <div className="w-full">
              <input
                className={`w-full h-[55px] bg-transparent indent-6 !outline-none ${disabled
                  ? "bg-[#F4F4F4]  !cursor-not-allowed"
                  : ""}`}
                type="number"
                name="amount"
                defaultValue={amountValue}
                onChange={(e) => {handleAmount(e.target.value); setAmount(e.target.value)}}
                disabled={disabled}
              />
            </div>
          </div>
        </div>
        {isOpen && !disabled && !disableCurrency && (
          <div className="absolute top-10 z-20 border-[0.2px] border-gray-100 left-0 w-[150px] rounded-md bg-white shadow-lg ">
            <div className="relative m-1">
              <input
                type="search"
                className={`border-[0.5px] placeholder:text-12 indent-8 rounded placeholder:text-neutral-light-hover w-full h-[32px] border-[#B2BBC6B0] `}
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <SearchNormal
                className="text-neutral-light-hover absolute top-2 ml-3"
                size={16}
              />
            </div>
            <ul
              className={`max-h-[180px] overflow-y-auto pb-2 overflow-x-hidden show-scrollbar ${optionsParentClassName}`}
            >
              {placeholder && (
                <li
                  className="mt-[7px] pl-3.5 cursor-pointer hover:bg-purple-light hover:text-purple-normal py-1"
                  onClick={() => {
                    handleSelectedItem({ text: placeholder, value: "" });
                  }}
                >
                  {placeholder}
                </li>
              )}
              {filteredOptions?.map((option, index) => (
                <li
                  key={index}
                  onClick={() => {
                    handleSelectedItem(option);
                  }}
                  className={`mt-[7px] pl-3.5 cursor-pointer hover:bg-purple-light hover:text-purple-normal py-1 ${
                    value?.includes(option.text)
                      ? "bg-purple-light text-purple-normal"
                      : ""
                  }`}
                >
                  {option.text}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      {(meta.touched && meta.error && !amount && !amountValue ) && <ErrorMessage error={meta.error} />}
    </>
  );
};

export default FormikSelectCurrency;
