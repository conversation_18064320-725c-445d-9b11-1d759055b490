import React, { useEffect, useState } from "react";
import useClickOutside from "../../shared/hooks";
import { ArrowDown2, ArrowUp2 } from "iconsax-react";
import FilterDropdown from "../Cards/FilterDropdown";

interface CustomMultiSelectProps {
  options: Array<any>;
  handleSelectedItems: any;
  disabled?: boolean;
  initialSelected?: Array<any>;
}

const CustomDropdownMultiSelect = ({ options, handleSelectedItems, disabled, initialSelected }: CustomMultiSelectProps) => {
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [selectedOptions, setSelectedOptions] = useState(initialSelected || []);

  const node = useClickOutside(() => {
    setShowDropdown(false);
  });

  const handleEligibility = (day) => {
    setSelectedOptions((prevState: any) => {
      const isAlreadySelected = prevState.some((item) => item === day);
      if (isAlreadySelected) {
        return prevState.filter((item) => item !== day);
      } else {
        return [...prevState, day];
      }
    });
  };

  useEffect(() => {
    handleSelectedItems(selectedOptions); 
  }, [selectedOptions]);


  return (
    <div ref={node}>
      <div className="relative">
        <div
          onClick={() => setShowDropdown(!showDropdown)}
          className={`w-full px-6 flex justify-between items-center rounded h-[47px]   ${disabled ? "bg-[#F4F4F4]" : "border-[#B2BBC699] border-[0.6px]"}`}
        >
          <div className="flex flex-wrap gap-3" onClick={() => {}}>
            {selectedOptions.map((item, index) => (
              <p
                key={index}
                className="flex justify-between bg-purple-light font-poppins-medium text-purple-normal px-2 py-1 w-fit  text-center"
              >
                {item}
              </p>
            ))}
          </div>
          {showDropdown && !disabled ? (
            <ArrowUp2 size={16} className="text-neutral-dark mt-1 ml-3" />
          ) : (
            <ArrowDown2 size={16} className="text-neutral-dark mt-1 ml-3" />
          )}
        </div>
        {showDropdown && !disabled && (
          <FilterDropdown className="!top-16 !right-0">
            <div className="flex flex-wrap gap-3 py-4 px-3">
              {options.map((item, index) => (
                <p
                  className={` text-neutral-normal w-fit px-1 py-1 text-center cursor-pointer text-12 rounded-[1px] ${
                    selectedOptions?.some((value: any) => value === item)
                      ? "bg-purple-normal text-white"
                      : "border-[0.5px] border-neutral-light"
                  }`}
                  onClick={() => handleEligibility(item)}
                  key={index}
                >
                  {item}
                </p>
              ))}
            </div>
          </FilterDropdown>
        )}
      </div>
    </div>
  );
};

export default CustomDropdownMultiSelect;
