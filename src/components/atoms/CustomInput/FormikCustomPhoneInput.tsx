import { useField } from "formik";
import React, { useState } from "react";
import PhoneInput, { CountryData, PhoneInputProps } from "react-phone-input-2";

import ErrorMessage from "../ErrorMessage";
import "react-phone-input-2/lib/style.css";
import useClickOutside from "../../shared/hooks";

interface FormikCustomPhoneInputProps extends PhoneInputProps {
  onChange: (phoneNumber: string) => void;
  onCountryChange?: (country: string) => void;
  id: string;
  name: string;
  className?: string;
  label?: string;
  error?: string;
  disabled?: boolean;
}

const FormikCustomPhoneInput = ({
  onChange,
  id,
  disabled,
  label,
  onBlur,
  className,
  onCountryChange,
  ...otherProps
}: FormikCustomPhoneInputProps) => {
  const [, meta] = useField({ name: otherProps.name });
  const [focusColor, setFocusColor] = useState(false);

  const node = useClickOutside(() => {
    setFocusColor(false);
  });

  return (
    <>
      <div className="text-16 mb-4">
        <label htmlFor={id}>{label}</label>
      </div>
      <div
      ref={node}
      onClick={() => {
        setFocusColor(true);
      }}
        className={`rounded-[4px]  placeholder:text-neutral-light  ${focusColor ? "border-purple-normal-hover" : ""} ${className} ${
          disabled
            ? "!bg-[#F4F4F4] !cursor-not-allowed border-[0.6px] border-[#B2BBC699]"
            : "border-[0.6px] border-[#B2BBC699]"
        } flex items-center h-[47px] !outline-none w-full ${
          meta.touched && meta.error ? "!border !border-alert-text-error" : ""
        }  `}
      >
        {/* <Icon className="cursor-pointer absolute top-1/3 z-20 left-2" name="callIcon" /> */}
        <PhoneInput
          buttonStyle={{
            width: "4rem",
            paddingLeft: "0.5rem",
            background: disabled ? "#F4F4F4" :"transparent",
            border: "none",
            borderRight: "1px solid #B2BBC699",
            marginRight: "1rem",
          }}
          country={"ng"}
          countryCodeEditable={false}
          defaultErrorMessage="It doesn't works, why?"
          enableAreaCodes={true}
          enableSearch={true}
          disabled={disabled}
          inputStyle={{
            height: "45px",
            // outline: '1px solid #B2BBC699',
            width: "100%",
            background: disabled ? "#F4F4F4" :"transparent",
            textIndent: "1.5rem",
            border: "none",
            marginRight: "5rem",
            fontFamily: "inherit",
          }}
          onBlur={onBlur}
          onChange={(value: string, data: CountryData) => {
            onChange(value);
            onCountryChange && onCountryChange(data.countryCode);
          }}
          placeholder="Your Phone"
          {...otherProps}
        />
      </div>
      {meta.touched && meta.error && <ErrorMessage error={meta.error} />}


    </>
  );
};

export default FormikCustomPhoneInput;
