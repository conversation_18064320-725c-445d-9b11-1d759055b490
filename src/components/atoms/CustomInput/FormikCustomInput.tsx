import { useField } from "formik";
import React, { useState } from "react";
import ErrorMessage from "../ErrorMessage";
import { Eye, EyeSlash } from "iconsax-react";

interface InputProps {
  label: string;
  id: string;
  name: string;
  type: string;
  placeholder: string;
  disabled?: boolean;
  icon?: React.ReactNode;
  iconPosition?: "start" | "end";
  inputClassName?: string;
  container?: string;
}

const FormikCustomInput = ({
  container,
  id,
  label,
  type,
  disabled,
  icon,
  inputClassName,
  iconPosition,
  ...props
}: InputProps | any) => {
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const handleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const [field, meta] = useField(props);

  return (
    <>
      <div className={`${container} font-poppins text-neutral-dark w-full`}>
          {label && (
            <div className="text-16 mb-4">
              <label htmlFor={id}>{label}</label> 
            </div>
          )}
       <div className="relative">

        <input
          className={`rounded-[4px]  placeholder:text-neutral-light ${inputClassName} ${
            disabled ? "bg-[#F4F4F4] !cursor-not-allowed border-[0.6px] border-[#B2BBC699]" : "border-[0.6px] border-[#B2BBC699]"
          }  h-[47px] outline-8 outline-purple-normal-hover px-3 w-full ${
            meta.touched && meta.error ? "!border !border-alert-text-error" : ""
          }  `}
          id={id} 
          disabled={disabled}
          type={type === "password" && showPassword ? "text" : type}
          // type={type}
          {...field}
          {...props}
        />
        {icon && iconPosition === "end" && icon}
        <div className="absolute top-1/3 right-5">
          {type === "password" && showPassword ? (
            <Eye size={16} className="cursor-pointer" onClick={handleShowPassword} />
          ) : (
            type === "password" &&
            !showPassword && (
              <EyeSlash size={16} className="cursor-pointer" onClick={handleShowPassword} />
            )
          )}

        </div>
       </div>
      </div>
    
      {meta.touched && meta.error && <ErrorMessage error={meta.error} />}
    </>
  );
};

export default FormikCustomInput;
