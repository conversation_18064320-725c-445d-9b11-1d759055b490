import React from 'react';

interface CustomRadioButtonProps {
  name: string;
  value: string;
  checked: boolean;
  onChange: (value: string) => void;
  label: string;
  className?: string;
  labelClassName?: string;
  disabled?: boolean;
  required?: boolean;
}

const CustomRadioButton: React.FC<CustomRadioButtonProps> = ({
  name,
  value,
  checked,
  onChange,
  label,
  className = '',
  labelClassName = '',
  disabled = false,
  required
}) => {
  return (
    <label className={`flex items-center gap-2 cursor-pointer ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}>
      <input
        type="radio"
        name={name}
        value={value}
        checked={checked}
        onChange={() => !disabled && onChange(value)}
        disabled={disabled}
        className="w-4 h-4 text-purple-normal accent-purple-normal focus:ring-purple-normal focus:ring-2 cursor-pointer"
        required={required}
      />
      <span className={`text-sm text-gray-700 ${labelClassName}`}>{label}</span>
    </label>
  );
};

export default CustomRadioButton;
