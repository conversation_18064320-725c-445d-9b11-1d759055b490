import { useField } from "formik";
import React from "react";
import ErrorMessage from "../ErrorMessage";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { layouts } from "chart.js";

interface InputProps {
  label: string;
  id: string;
  name: string;
  type: string;
  placeholder: string;
  disabled?: boolean;
  icon?: React.ReactNode;
  iconPosition?: "start" | "end";
  inputClassName?: string;
  container?: string;
  height?: string;
  textSize?: string;
}

const FormikCustomDate = ({
  container,
  id,
  label,
  type,
  disabled,
  icon,
  inputClassName,
  iconPosition,
  height,
  textSize,
  sx,
  ...props
}: InputProps | any) => {



  const [field, meta] = useField(props);

  return (
    <>
      <div className={`${container} font-poppins text-neutral-dark w-full`}>
        {label && (
          <div className="text-16 mb-4">
            <label htmlFor={id}>{label}</label>
          </div>
        )}
        <div className="relative">


          <DatePicker
            {...field}
            {...props}
            className={`mt-0 w-full ${meta.touched && meta.error ? "!border !border-alert-text-error" : ""}`}
            slotProps={{
              textField: {
                size: "small",
                sx: {
                  "& .MuiOutlinedInput-root": {
                    "&:hover fieldset": {
                      borderColor: "#B2BBC699",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#3730A3",
                      borderWidth: "0.8px"
                    },
                    height: height ? height : "47px",
                    borderRadius: "4px",        
                    outline: "#3730A3",
                    fontSize: textSize,
                    fontFamily: "Cabin",
                    fontWeight: "bold",
                    color: "#71797E"
                  },
                  "& .MuiOutlinedInput-input": {
                    textTransform: "lowercase",
                  },
                },
                error: false,
              },
              popper: {
                disablePortal: true,
                sx: {
                  zIndex: 1300,
                },
              },
            }}
            disabled={disabled}
            sx={sx}
          />
        </div>
      </div>

      {meta.touched && meta.error && <ErrorMessage error={meta.error} />}
    </>
  );
};

export default FormikCustomDate;
