import ErrorMessage from '../../atoms/ErrorMessage';
import { useState } from 'react';
import useClickOutside from '../..//shared/hooks';
import { ArrowDown2, ArrowUp2, SearchNormal } from 'iconsax-react';

interface CustomSelectProps {
  label?: string;
  parentContainer?: string;
  disabled?: boolean;
  container?: string;
  options?: any[];
  value: any;
  onChange: any;
  optionsParentClassName?: string;
  placeholder?: string;
  labelClass?: string;
  className?: string;
  error?: string | null;
}

const CustomSelect = ({
  label,
  parentContainer,
  disabled,
  container,
  options,
  value,
  onChange,
  optionsParentClassName,
  placeholder,
  labelClass,
  className,
  error = null,
}: CustomSelectProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [focusColor, setFocusColor] = useState(false);
  const selectedText: string = options?.find((x) => x.value === value)?.text;

  const node = useClickOutside(() => {
    setIsOpen(false);
    setFocusColor(false);
    setSearchTerm('');
  });

  const filteredOptions = options?.filter((option) =>
    option?.text.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectedItem = (option) => {
    setIsOpen(false);
    setSearchTerm('');
    onChange(option);
  };

  return (
    <>
      {label && (
        <div className={`text-16 mb-4 text-neutral-dark ${labelClass}`}>
          <label>{label}</label>
        </div>
      )}
      <div className='relative' ref={node}>
        <div
          className={`border rounded-[4px] ${
            focusColor ? 'border-purple-normal-hover' : ''
          } ${parentContainer} ${
            disabled ? 'bg-[#F4F4F4] !cursor-not-allowed' : 'border-[0.6px] border-[#B2BBC699]'
          } flex items-center h-[47px] !outline-none px-3 w-full ${
            error ? '!border !border-alert-text-error' : ''
          }`}
        >
          <button
            className='w-full'
            onClick={() => {
              setIsOpen(!isOpen);
              setFocusColor(true);
            }}
          >
            <div className={`!w-full pr-2 flex justify-between ${container}`}>
              <input
                className={`h-auto w-full !outline-none border-none bg-transparent capitalize ${className} ${
                  selectedText ? '' : 'text-gray-400'
                }`}
                readOnly
                type='text'
                value={selectedText || placeholder}
              />
              {isOpen && !disabled ? (
                <ArrowUp2 size={16} className='text-neutral-dark ml-3' />
              ) : (
                <ArrowDown2 size={16} className='text-neutral-dark ml-3' />
              )}
            </div>
          </button>
        </div>
        {isOpen && !disabled && (
          <div className='absolute top-10 z-[100] border-[0.2px] border-gray-100 right-0 w-[250px] rounded-md bg-white shadow-lg '>
            <div className='relative m-1'>
              <input
                type='search'
                className={`border-[0.5px] placeholder:text-12 indent-8 rounded placeholder:text-neutral-light-hover w-full h-[32px] border-[#B2BBC6B0] `}
                placeholder='Search...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <SearchNormal className='text-neutral-light-hover absolute top-2 ml-3' size={16} />
            </div>
            <ul
              className={`max-h-[180px] overflow-y-auto pb-2 overflow-x-hidden show-scrollbar ${optionsParentClassName}`}
            >
              {placeholder && (
                <li className='mt-[7px] pl-3.5 cursor-pointer hover:bg-purple-light hover:text-purple-normal text-gray-400 py-1'>
                  <button
                    className='w-full text-left'
                    onClick={() => {
                      handleSelectedItem({ text: placeholder, value: null });
                    }}
                  >
                    {placeholder}
                  </button>
                </li>
              )}
              {filteredOptions?.map((option, index) => (
                <li
                  key={'opts-' + index}
                  className={`mt-[7px] pl-3.5 cursor-pointer capitalize hover:bg-purple-light hover:text-purple-normal py-1 ${
                    value === option.text ? 'bg-purple-light text-purple-normal' : ''
                  }`}
                >
                  <button
                    className='w-full text-left'
                    onClick={() => {
                      handleSelectedItem(option);
                    }}
                  >
                    {option.text}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      {error && <ErrorMessage error={error} />}
    </>
  );
};

export default CustomSelect;
