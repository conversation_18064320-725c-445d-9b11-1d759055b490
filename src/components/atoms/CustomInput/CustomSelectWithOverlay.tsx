import React, { useState } from 'react';
import useClickOutside from '../../shared/hooks';
import ErrorMessage from '../ErrorMessage';
import { BiCaretDown, BiCaretUp } from 'react-icons/bi';
import { truncateText } from '../../shared/helpers';

interface CustomSelectWithOverlayProps {
  label?: string;
  parentContainer?: string;
  disabled?: boolean;
  container?: string;
  options?: any[];
  value: any;
  onChange: any;
  optionsParentClassName?: string;
  placeholder?: string;
  labelClass?: string;
  className?: string;
  error?: string | null;
  showOverlay?: boolean;
}

const CustomSelectWithOverlay = ({
  label,
  parentContainer,
  disabled,
  container,
  options,
  value,
  onChange,
  optionsParentClassName,
  placeholder,
  labelClass,
  className,
  error = null,
  showOverlay = true,
}: CustomSelectWithOverlayProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [focusColor, setFocusColor] = useState(false);
  const selectedText: string = options?.find((x) => x.value === value)?.text;

  const node = useClickOutside(() => {
    setIsOpen(false);
    setFocusColor(false);
  });

  const handleSelectedItem = (option) => {
    setIsOpen(false);
    onChange(option);
  };

  const handleToggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      setFocusColor(!focusColor);
    }
  };

  return (
    <>
      {/* Dark Overlay */}
      {isOpen && showOverlay && (
        <div className="fixed inset-0 bg-black bg-opacity-10 z-40 transition-all duration-300 ease-in-out" />
      )}

      <div className={`relative ${parentContainer}`}>
        {label && (
          <label className={`block text-sm font-medium text-gray-700 mb-1 ${labelClass}`}>
            {label}
          </label>
        )}

        <div className="relative" ref={node}>
          <button
            type="button"
            onClick={handleToggleDropdown}
            disabled={disabled}
            className={`
              relative w-full bg-white border border-gray-300 rounded-md pl-3 pr-10 py-2 text-left cursor-pointer
              focus:outline-none focus:ring-1 focus:ring-[#3730A3] focus:border-[#3730A3]
              ${focusColor ? 'ring-1 ring-[#3730A3] border-[#3730A3]' : ''}
              ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'hover:border-neutral-gray'}
              ${error ? 'border-red-500' : ''}
              ${className}
            `}
          >
            <span className="block text-sm">
              {selectedText || placeholder || 'Select an option'}
            </span>
            <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              {isOpen ? (
                <BiCaretUp className="h-4 w-4 text-neutral-gray" />
              ) : (
                <BiCaretDown className="h-4 w-4 text-neutral-gray" />
              )}
            </span>
          </button>

          {isOpen && !disabled && (
            <div className={`
              absolute top-full right-0 mt-1 w-[8rem] bg-white border border-gray-200 rounded-md shadow-lg z-50
              max-h-60 overflow-hidden
              ${container}
            `}>
              {/* Options List */}
              <ul className={`max-h-60 overflow-y-auto hide-scrollbar py-1 ${optionsParentClassName}`}>
                {options && options.length > 0 ? (
                  options.map((option, index) => (
                    <li
                      key={index}
                      onClick={() => handleSelectedItem(option)}
                      className={`
                        cursor-pointer select-none relative py-3 px-4 text-sm transition-colors duration-150
                        ${value === option.value
                          ? 'bg-indigo-600 text-white'
                          : 'text-gray-900 hover:bg-indigo-50 hover:text-indigo-900'
                        }
                      `}
                    >
                      <span className="block truncate font-medium">{option.text}</span>
                    </li>
                  ))
                ) : (
                  <li className="cursor-default select-none relative py-3 px-4 text-sm text-gray-500">
                    No options found
                  </li>
                )}
              </ul>
            </div>
          )}
        </div>

        {error && <ErrorMessage error={error} />}
      </div>
    </>
  );
};

export default CustomSelectWithOverlay;
