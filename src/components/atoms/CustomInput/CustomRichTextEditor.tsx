import React from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
// import ReactSummernote from 'react-summernote';
// import 'react-summernote/dist/react-summernote.css'; // import styles

// // Import bootstrap(v3 or v4) dependencies
// import 'bootstrap/js/modal';
// import 'bootstrap/js/dropdown';
// import 'bootstrap/js/tooltip';
// import 'bootstrap/dist/css/bootstrap.css';

interface CustomRichTextEditorProps {
  value: string;
  onChange?: (content: string) => void;
}

const CustomRichTextEditor = ({ value, onChange }: CustomRichTextEditorProps) => {
  return (
    <div style={{ minHeight: 300 }}>
      <ReactQuill
        style={{ height: 250 }}
        theme='snow'
        value={value}
        onChange={onChange}
        formats={[
          'header',
          'font',
          'size',
          'bold',
          'italic',
          'underline',
          'strike',
          'blockquote',
          'list',
          'bullet',
          'indent',
          'link',
          'image',
          'color',
        ]}
      />
    </div>
  );
};

export default CustomRichTextEditor;
