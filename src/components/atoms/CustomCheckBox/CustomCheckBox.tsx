import React from "react";



interface CustomCheckBoxProps extends React.HTMLAttributes<HTMLInputElement> {
  label?: string;
  labelPosition?: string;
  className?: string;
  labelClassName?: string;
  shape?: string;
  size?: string;
  isDisabled?: boolean;
  checked?: boolean;
  customClass?: string;
  name?: string;
  required?: boolean;
  heightClass?: string;
  widthClass?: string;
}
const CustomCheckBox = ({ label, name, required, shape, checked, isDisabled, size, labelPosition, className, labelClassName, customClass, widthClass, heightClass, ...otherProps }: CustomCheckBoxProps) => {
  return (

      <div className={`inline ${className}`}>
        {isDisabled ? (
          <input
            className={`${customClass} pointer-events-none drop-shadow-sm border accent-purple-dark  text-[#EBEFF1] border-none focus:ring-0`}
            disabled={true}
            type="checkbox"
            {...otherProps}
          />
        ) : (
          <input
            className={`w-5 h-5 cursor-pointer rounded-[6px] accent-purple-dark ${customClass} ${widthClass} ${heightClass}`}
            type="checkbox"
            name={name}
            checked={checked}
            {...otherProps}
            required
          />
        )}
      </div>

  );
};

export default CustomCheckBox;

