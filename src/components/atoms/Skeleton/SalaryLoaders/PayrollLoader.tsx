import React from 'react';

const PayrollLoader = () => {
  return (
    <div className="p-6 space-y-8 animate-pulse">
      {/* Top bar with filters and toggle */}
      <div className="flex items-center justify-between">
        <div className="h-4 w-1/4 bg-gray-200 rounded" />
        <div className="h-10 w-20 bg-gray-200 rounded" />
      </div>

      {/* Filter chips/cards */}
      <div className="flex gap-4">
        {Array(4).fill(0).map((_, idx) => (
          <div
            key={idx}
            className="flex items-center gap-2 bg-gray-200 rounded-xl px-4 py-4 w-1/4"
          >
            <div className="w-6 h-6 rounded-full bg-gray-300" />
            <div className="flex flex-col gap-2 w-full">
              <div className="h-3 bg-gray-300 rounded w-3/4" />
              <div className="h-3 bg-gray-300 rounded w-1/2" />
            </div>
          </div>
        ))}
      </div>

      {/* Filter side bar */}
      <div className="flex gap-8">
        <div className="space-y-4 w-1/4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-10 bg-gray-200 rounded" />
          ))}
          <div className="h-4 bg-gray-200 rounded w-2/3" />
          <div className="h-4 bg-gray-200 rounded w-1/3" />
        </div>

        {/* Table content */}
        <div className="flex-1 space-y-4">
          <div className="h-10 bg-gray-300 rounded" />

          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="grid grid-cols-6 gap-4 items-center">
              {Array(5).fill(0).map((_, j) => (
                <div key={j} className="h-4 bg-gray-200 rounded w-full" />
              ))}
              <div className="h-6 w-12 bg-gray-200 rounded-full mx-auto" />
            </div>
          ))}
        </div>
      </div>

      {/* Side list with dots and lines */}
      <div className="w-1/2 space-y-4">
        <div className="h-4 bg-gray-200 rounded w-1/3 mb-2" />
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-gray-300" />
              <div className="h-3 bg-gray-300 rounded w-40" />
            </div>
            <div className="h-3 bg-gray-300 rounded w-10" />
          </div>
        ))}
      </div>
    </div>
  );
};

export default PayrollLoader;
