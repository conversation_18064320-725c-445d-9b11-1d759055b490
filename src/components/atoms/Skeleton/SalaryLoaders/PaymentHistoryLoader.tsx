import React from 'react';

const PaymentHistoryLoader = () => {
    return (
        <div className="p-6 animate-pulse">
            {/* Top filters */}
            <div className="flex gap-4 mb-6">
                <div className="h-10 w-40 bg-gray-200 rounded" />
                <div className="h-10 w-40 bg-gray-200 rounded" />
            </div>

            {/* Search bar */}
            <div className="h-4 w-1/3 bg-gray-200 rounded mb-6" />

            {/* Table headers */}
            <div className="grid grid-cols-8 gap-4 bg-gray-300 h-10 rounded mb-4" />

            {/* Table rows */}
            {Array.from({ length: 7 }).map((_, index) => (
                <div key={index} className="grid grid-cols-8 gap-4 mb-4">
                    <div className="h-4 bg-gray-200 rounded col-span-1" />
                    <div className="space-y-2 col-span-2">
                        <div className="h-4 bg-gray-200 rounded w-3/4" />
                        <div className="h-4 bg-gray-200 rounded w-2/3" />
                    </div>
                    <div className="h-4 bg-gray-200 rounded col-span-1" />
                    <div className="h-4 bg-gray-200 rounded col-span-1" />
                    <div className="h-4 bg-gray-200 rounded col-span-1" />
                    <div className="h-4 bg-gray-200 rounded col-span-1" />
                    <div className="h-6 w-12 bg-gray-200 rounded-full mx-auto col-span-1" />
                </div>
            ))}
        </div>
    );
};

export default PaymentHistoryLoader;
