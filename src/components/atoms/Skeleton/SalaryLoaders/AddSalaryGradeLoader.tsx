import React from "react";

const AddSalaryGradeLoaoder = () => {
    return (
        <div className="p-6 animate-pulse space-y-6">
            {/* Header */}
            <div className="h-4 w-1/4 bg-gray-300 rounded" />

            {/* Top rows */}
            <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                    <div className="h-4 w-3/4 bg-gray-200 rounded" />
                    <div className="h-4 w-1/2 bg-gray-200 rounded" />
                    <div className="h-4 w-2/3 bg-gray-200 rounded" />
                </div>
                <div className="space-y-2">
                    <div className="h-4 w-1/2 bg-gray-200 rounded" />
                    <div className="h-4 w-2/3 bg-gray-200 rounded" />
                </div>
            </div>

            {/* Input group with label */}
            <div className="w-1/3 h-4 bg-gray-300 rounded" />
            <div className="w-1/2 h-10 bg-gray-100 rounded" />

            {/* Radio buttons or pills */}
            <div className="flex gap-6">
                <div className="h-4 w-16 bg-gray-200 rounded-full" />
                <div className="h-4 w-16 bg-gray-200 rounded-full" />
                <div className="h-4 w-16 bg-gray-200 rounded-full" />
            </div>

            {/* Divider */}
            <div className="h-3 w-full bg-gray-200 rounded" />

            {/* Details grid */}
            <div className="grid grid-cols-2 gap-6">
                {[...Array(4)].map((_, i) => (
                    <div key={i} className="space-y-2">
                        <div className="h-4 w-1/2 bg-gray-200 rounded" />
                        <div className="h-4 w-2/3 bg-gray-200 rounded" />
                    </div>
                ))}
                <div className="space-y-2">
                    <div className="h-4 w-1/2 bg-gray-200 rounded" />
                    <div className="h-4 w-1/3 bg-gray-200 rounded" />
                </div>
            </div>

            {/* Button */}
            <div className="w-32 h-8 bg-gray-300 rounded" />

            {/* Footer rows */}
            <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                    <div className="h-4 w-2/3 bg-gray-200 rounded" />
                    <div className="h-4 w-1/2 bg-gray-200 rounded" />
                </div>
                <div className="flex gap-4 items-center">
                    <div className="h-4 w-16 bg-gray-200 rounded-full" />
                    <div className="h-4 w-16 bg-gray-200 rounded-full" />
                    <div className="h-4 w-16 bg-gray-200 rounded-full" />
                </div>
            </div>

            {/* Action buttons */}
            <div className="flex gap-4 mt-6">
                <div className="h-10 w-24 bg-gray-300 rounded" />
                <div className="h-10 w-24 bg-gray-100 rounded" />
            </div>
        </div>
    );
};

export default AddSalaryGradeLoaoder;
