import React from 'react';


const PayrollDashboardSkeleton = () => {
  return (
    <div>
      <div className='bg-white py-8 px-6 rounded-lg animate-pulse'>
        <div className='mt-4 grid grid-cols-4 gap-5'>
          {[1, 2, 3, 4].map((index) => (
            <div key={index} className='p-6 h-28 bg-gray-100 flex gap-5 rounded-lg animate-pulse '>
              <div className='flex items-start justify-between mb-2'>
                <div className=' bg-gray-200 w-14 h-14 rounded-full' />
              </div>
              <div>

              <div className=' bg-gray-200 w-24 h-6 mb-2' />
              <div className=' bg-gray-200 w-32 h-4' />
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className='mt-10 grid grid-cols-2 gap-5'>
        <div>
          <div className='rounded-lg col-span-4'>
            {/* Header */}
            <div className='bg-white px-4 py-3 flex justify-between items-center rounded-t-lg border-b'>
              <div className=' bg-gray-200 w-36 h-5' />
              <div className=' bg-gray-200 w-28 h-8 rounded-md' />
            </div>

            <div className='bg-white px-4 py-6 rounded-b-lg h-[200px]'>
              {[1, 2, 3].map((index) => (
                <div key={index} className='flex items-center py-4 border-b border-gray-100 last:border-b-0'>
                  <div className=' bg-gray-200 w-6 h-6 rounded-full' />
                  <div className='ml-4 flex-1'>
                    <div className=' bg-gray-200 w-28 h-4 mb-2' />
                    <div className=' bg-gray-200 w-40 h-3' />
                  </div>
                  <div className=' bg-gray-200 w-24 h-4' />
                </div>
              ))}
            </div>
          </div>
        </div>

        <div>
          <div>
              <div className=' bg-gray-200 w-full h-8' />
            <div className='flex justify-between items-center mb-4'>
            </div>
            <div className='space-y-2 bg-white p-6 rounded-lg'>
                <div className='w-40 h-10 bg-gray-200 rounded-md' />
                <div className='w-40 h-10 bg-gray-200 rounded-md' />
                <div className='w-40 h-10 bg-gray-200 rounded-md' />
                <div className='flex justify-between gap-5 w-4/5 mt-5'>
                <div className='w-full h-6 bg-gray-200 rounded-md' />
                <div className='w-12 h-6 bg-gray-200 rounded-md' />

                </div>
            </div>
            <div className='mt-8'>
              <div className='w-full h-10 bg-gray-200 rounded-md' />
            </div>
          </div>
        </div>
      </div>

      <div className='mt-10'>
        <div className='bg-white shadow-md rounded-lg col-span-4'>
          <div className='px-6 py-6 border-b border-gray-200'>
            <div className=' bg-gray-200 w-52 h-6 mb-4' />
          </div>
          <div className='p-6'>
            <div className='grid grid-cols-4 gap-6 mb-6 pb-4 border-b border-gray-100'>
              <div className=' bg-gray-200 w-28 h-4' />
              <div className=' bg-gray-200 w-32 h-4' />
              <div className=' bg-gray-200 w-24 h-4' />
              <div className=' bg-gray-200 w-36 h-4' />
            </div>
            {[1, 2, 3, 4, 5].map((index) => (
              <div key={index} className='grid grid-cols-4 gap-6 py-4 border-b border-gray-100 last:border-b-0'>
                <div className=' bg-gray-200 w-36 h-4' />
                <div className=' bg-gray-200 w-32 h-4' />
                <div className=' bg-gray-200 w-40 h-4' />
                <div className='flex items-center gap-2'>
                  <div className=' bg-gray-200 w-8 h-4' />
                  <div className=' bg-gray-200 w-20 h-4' />
                </div>
              </div>
            ))}
            <div className='text-center py-8'>
              <div className=' bg-gray-200 w-48 h-4 mx-auto' />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PayrollDashboardSkeleton;
