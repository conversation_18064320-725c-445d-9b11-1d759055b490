import React from 'react';

interface AccountingAnalyticsSkeletonProps {
  className?: string;
}

const AccountingAnalyticsSkeleton = ({ 
  className = ""
}: AccountingAnalyticsSkeletonProps) => {
  return (
    <div className={`p-6 animate-pulse ${className}`}>

      <div className="grid grid-cols-5 gap-8 mb-8">
        <div className="col-span-3">
          <div className="flex bg-white justify-between items-center mb-4 py-2 px-4">
            <div className="h-5 w-32 bg-gray-200 rounded"></div>
            <div className="flex items-center gap-2">
              <div className="h-8 w-20 bg-gray-200 rounded"></div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg p-6" style={{ height: '400px' }}>
            <SkeletonLineChart />
          </div>
        </div>

        {/* Stats Section - Right Side (col-span-2) */}
        <div className="col-span-2">
          {/* Stats Header */}
          <div className="flex justify-between bg-white items-center mb-4 py-2 px-4">
            <div className="h-5 w-28 bg-gray-200 rounded"></div>
            <div className="flex items-center gap-2">
              <div className="h-8 w-16 bg-gray-200 rounded"></div>
              <div className="h-8 w-16 bg-gray-200 rounded"></div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            {Array.from({ length: 8 }).map((_, index) => (
              <SkeletonStatsCard key={index} />
            ))}
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg p-6">
        <SkeletonTable />
      </div>
    </div>
  );
};


const SkeletonLineChart = () => {
  return (
    <div className="w-full h-full flex flex-col">
      {/* Chart area with line simulation */}
      <div className="flex-1 relative">
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between py-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="h-3 w-8 bg-gray-200 rounded"></div>
          ))}
        </div>
        
        {/* Chart grid and line */}
        <div className="ml-12 h-full relative">
          {Array.from({ length: 5 }).map((_, index) => (
            <div 
              key={index} 
              className="absolute w-full h-px bg-gray-200" 
              style={{ top: `${(index + 1) * 20}%` }}
            ></div>
          ))}
        </div>
      </div>
      
      <div className="flex justify-between mt-4 ml-12">
        {Array.from({ length: 12 }).map((_, index) => (
          <div key={index} className="h-3 w-6 bg-gray-200 rounded"></div>
        ))}
      </div>
    </div>
  );
};


const SkeletonStatsCard = () => {
  return (
    <div className="bg-white rounded-lg p-4 border border-gray-200">
      <div className="flex items-center justify-between mb-3">
        <div className="h-3 w-16 bg-gray-200 rounded"></div>
        <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
      </div>
      <div className="flex items-center gap-2">
        <div className="h-4 w-8 bg-gray-200 rounded"></div>
        <div className="h-5 w-12 bg-gray-200 rounded"></div>
        <div className="h-3 w-3 bg-gray-200 rounded-full"></div>
      </div>
    </div>
  );
};


const SkeletonTable = () => {
  return (
    <div className='relative'>
        <div className="h-10 w-32 absolute -top-10 right-0 bg-gray-200 rounded"></div>
      <div className="flex justify-between items-center mb-4">
        <div className="h-8 w-20 bg-gray-200 rounded"></div>
      </div>
      
      <div className="h-10 w-full bg-gray-100 rounded mb-4"></div>
      
      <div className="grid grid-cols-5 gap-4 p-4 bg-gray-100 rounded-t-lg">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="h-4 bg-gray-200 rounded"></div>
        ))}
      </div>
      
      {Array.from({ length: 5 }).map((_, rowIndex) => (
        <div key={rowIndex} className="grid grid-cols-5 gap-4 p-4 border-b border-gray-200">
          {Array.from({ length: 5 }).map((_, colIndex) => (
            <div key={colIndex} className="h-4 bg-gray-200 rounded"></div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default AccountingAnalyticsSkeleton;
