import React from "react";

const LeaveLoader: React.FC = () => {
    return (
        <div className="animate-pulse space-y-6 p-6">
            {/* Top Filters / Search Bar */}
            <div className="flex items-center space-x-4">
                <div className="h-10 w-40 bg-gray-200 rounded" />
                <div className="h-10 w-40 bg-gray-200 rounded" />
                <div className="h-10 w-10 bg-gray-200 rounded" />
                <div className="h-10 w-40 bg-gray-200 rounded ml-auto" />
            </div>

            {/* Stat Cards */}
            <div className="grid grid-cols-4 gap-4">
                {[...Array(4)].map((_, i) => (
                    <div
                        key={i}
                        className="flex items-center space-x-4 p-4 bg-gray-200 rounded-lg"
                    >
                        <div className="h-10 w-10 bg-gray-300 rounded-full" />
                        <div className="flex-1 space-y-2">
                            <div className="h-3 w-3/4 bg-gray-300 rounded" />
                            <div className="h-3 w-1/2 bg-gray-300 rounded" />
                        </div>
                    </div>
                ))}
            </div>

            {/* Table Header */}
            <div className="grid grid-cols-6 gap-4 bg-gray-100 py-4 rounded">
                {[...Array(6)].map((_, i) => (
                    <div key={i} className="h-4 w-full bg-gray-200 rounded" />
                ))}
            </div>

            {/* Table Rows */}
            {[...Array(8)].map((_, rowIndex) => (
                <div
                    key={rowIndex}
                    className="grid grid-cols-6 gap-4 py-4 items-center border-b border-gray-100"
                >
                    {[...Array(6)].map((_, colIndex) => (
                        <div
                            key={colIndex}
                            className="h-4 w-full bg-gray-200 rounded"
                        />
                    ))}
                </div>
            ))}
        </div>
    );
};

export default LeaveLoader;
