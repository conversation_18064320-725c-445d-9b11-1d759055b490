const ReportSkeleton = () => {
  return (
    <div className="animate-pulse">
      {/* Report Summary */}
      <div className="bg-white py-8 px-6 rounded-[10px]">
        <h1 className=" h-5 w-32 bg-gray-200 rounded" />
        <div className="grid grid-cols-3 gap-5 mt-2">
          <div className="bg-gray-300 h-[100px] rounded-xl px-4">
            <div className="flex gap-4 mt-6">
              <div className="h-[41px] w-[41px] bg-gray-200 rounded-full border-2 flex justify-center items-center " />
              <div>
                <p className="text-14 font-poppins w-40 h-3 rounded-md bg-gray-200"></p>
                <p className=" w-28 h-3 rounded-md bg-gray-200 mt-6" />
              </div>
            </div>
          </div>
          <div className="bg-gray-300 h-[100px] rounded-xl px-4">
            <div className="flex gap-4 mt-6">
              <div className="h-[41px] w-[41px] bg-gray-200 rounded-full border-2 flex justify-center items-center " />
              <div>
                <p className="text-14 font-poppins w-40 h-3 rounded-md bg-gray-200"></p>
                <p className=" w-28 h-3 rounded-md bg-gray-200 mt-6" />
              </div>
            </div>
          </div>
          <div className="bg-gray-300 h-[100px] rounded-xl px-4">
            <div className="flex gap-4 mt-6">
              <div className="h-[41px] w-[41px] bg-gray-200 rounded-full border-2 flex justify-center items-center " />
              <div>
                <p className="text-14 font-poppins w-40 h-3 rounded-md bg-gray-200"></p>
                <p className=" w-28 h-3 rounded-md bg-gray-200 mt-6" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-5 gap-8 mt-8">
        <div className="col-span-3">
          <div className="flex bg-white justify-between items-center mb-4 py-2 px-4">
            <div className="h-5 w-32 bg-gray-200 rounded"></div>
            <div className="flex items-center gap-2">
              <div className="h-8 w-20 bg-gray-200 rounded"></div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6" style={{ height: "300px" }}>
            <SkeletonLineChart />
          </div>
        </div>
        <div className="col-span-2">
          <div className="flex bg-white justify-between items-center mb-4 py-2 px-4">
            <div className="h-5 w-32 bg-gray-200 rounded"></div>
            <div className="flex items-center gap-2">
              <div className="h-8 w-20 bg-gray-200 rounded"></div>
              <div className="h-8 w-20 bg-gray-200 rounded"></div>
            </div>
          </div>
          <div className="bg-white mt-4 p-6">
            <div className="bg-gray-300 h-32  w-full"></div>
            <div className="bg-gray-300 h-32  w-full mt-6"></div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6 mt-8">
        <div className="grid grid-cols-2 gap-4 bg-white mt-2 px-4 py-8">
          <div className="bg-gray-200 border border-[#DCDEE64D] px-4 py-3 rounded-tl-lg rounded-bl-lg font-medium" />

          <div className="bg-gray-200 border border-[#DCDEE64D] px-4 py-3 rounded-tr-lg rounded-br-lg font-medium" />
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index}>
              {/* <div className="border border-[#DCDEE64D] px-4 py-3 rounded" /> */}
              <div className="border border-[#DCDEE64D] px-4 py-3 flex gap-4 items-center rounded">
                <span className="bg-gray-200 w-40  p-1 rounded mr-2" />
                <p className="bg-gray-200 w-[100px]"></p>
              </div>
            </div>
          ))}
        </div>
        <div className="grid grid-cols-2 gap-4 bg-white mt-2 px-4 py-8">
          <div className="bg-gray-200 border border-[#DCDEE64D] px-4 py-3 rounded-tl-lg rounded-bl-lg font-medium" />

          <div className="bg-gray-200 border border-[#DCDEE64D] px-4 py-3 rounded-tr-lg rounded-br-lg font-medium" />
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index}>
              {/* <div className="border border-[#DCDEE64D] px-4 py-3 rounded" /> */}
              <div className="border border-[#DCDEE64D] px-4 py-3 flex gap-4 items-center rounded">
                <span className="bg-gray-200 w-40  p-1 rounded mr-2" />
                <p className="bg-gray-200 w-[100px]"></p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div className="grid grid-cols-2 gap-4 bg-white mt-8 px-4 py-8">
          <div className="bg-gray-200 border border-[#DCDEE64D] px-4 py-3 rounded-tl-lg rounded-bl-lg font-medium" />

          <div className="bg-gray-200 border border-[#DCDEE64D] px-4 py-3 rounded-tr-lg rounded-br-lg font-medium" />
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index}>
              {/* <div className="border border-[#DCDEE64D] px-4 py-3 rounded" /> */}
              <div className="border border-[#DCDEE64D] px-4 py-3 flex gap-4 items-center rounded">
                <span className="bg-gray-200 w-40  p-1 rounded mr-2" />
                <p className="bg-gray-200 w-[100px]"></p>
              </div>
            </div>
          ))}
        </div>
        <div className="grid grid-cols-2 gap-4 bg-white mt-8 px-4 py-8">
          <div className="bg-gray-200 border border-[#DCDEE64D] px-4 py-3 rounded-tl-lg rounded-bl-lg font-medium" />

          <div className="bg-gray-200 border border-[#DCDEE64D] px-4 py-3 rounded-tr-lg rounded-br-lg font-medium" />
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index}>
              {/* <div className="border border-[#DCDEE64D] px-4 py-3 rounded" /> */}
              <div className="border border-[#DCDEE64D] px-4 py-3 flex gap-4 items-center rounded">
                <span className="bg-gray-200 w-40  p-1 rounded mr-2" />
                <p className="bg-gray-200 w-[100px]"></p>
              </div>
            </div>
          ))}
        </div>
      </div>x
    </div>
  );
};

export default ReportSkeleton;

const SkeletonLineChart = () => {
  return (
    <div className="w-full h-full flex flex-col">
      {/* Chart area with line simulation */}
      <div className="flex-1 relative">
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between py-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="h-3 w-8 bg-gray-200 rounded"></div>
          ))}
        </div>

        {/* Chart grid and line */}
        <div className="ml-12 h-full relative">
          {Array.from({ length: 5 }).map((_, index) => (
            <div
              key={index}
              className="absolute w-full h-px bg-gray-200"
              style={{ top: `${(index + 1) * 20}%` }}
            ></div>
          ))}
        </div>
      </div>

      <div className="flex justify-between mt-4 ml-12">
        {Array.from({ length: 12 }).map((_, index) => (
          <div key={index} className="h-3 w-6 bg-gray-200 rounded"></div>
        ))}
      </div>
    </div>
  );
};
