import React from "react";

const skeleton = "bg-gray-200 animate-pulse";

const DashboardSkeleton = () => (
  <div className="p-4 md:p-8 bg-gray-50 rounded-xl w-full space-y-8 animate-pulse">
    {/* Header bar */}
    {/* <div className="h-4 w-32 rounded mb-4 bg-gray-200" /> */}

    {/* Top summary cards */}
    <div className="grid grid-cols-4 gap-4 mb-4">
      {Array.from({ length: 4 }).map((_, i) => (
        // <div key={i} className="h-16 rounded-lg bg-gray-200" />
        <div key={i} className="bg-gray-300 h-[100px] rounded-xl px-4">
            <div className="flex gap-4 mt-6">
              <div className="h-[41px] w-[41px] bg-gray-200 rounded-full border-2 flex justify-center items-center " />
              <div>
                <p className="text-14 font-poppins w-40 h-3 rounded-md bg-gray-200"></p>
                <p className=" w-28 h-3 rounded-md bg-gray-200 mt-6" />
              </div>
            </div>
          </div>
      ))}
    </div>

    {/* Main content grid */}
    <div className="grid grid-cols-3 gap-6 mb-8">
      {/* Trend chart */}
      <div className="col-span-1 bg-white rounded-xl p-4 flex flex-col">
        <div className="h-4 w-24 rounded bg-gray-200 mb-4" />
        <div className="h-48 w-full rounded bg-gray-200" />
      </div>
      {/* Center donut and lines */}
      <div className="col-span-1 bg-white flex flex-col items-center justify-center">
        <div className="h-4 w-24 rounded bg-gray-200 mb-4" />
        <div className="w-24 h-24 rounded-full bg-gray-200 mx-auto mb-2" />
        <div className="h-3 w-32 rounded bg-gray-200 mb-2" />
        <div className="h-3 w-20 rounded bg-gray-200 mb-2" />
        <div className="h-3 w-24 rounded bg-gray-200" />
      </div>
      {/* Right list */}
      <div className="col-span-1 bg-white rounded-xl p-4 flex flex-col">
        <div className="h-4 w-24 rounded bg-gray-200 mb-4" />
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="h-3 w-full rounded bg-gray-200 mb-3" />
        ))}
      </div>
    </div>

    {/* Lower grid: tables and donuts */}
    <div className="grid grid-cols-3 gap-6 mb-8">
      {/* Table */}
      <div className="bg-white rounded-xl p-4 flex flex-col">
        <div className="h-4 w-24 rounded bg-gray-200 mb-4" />
        {Array.from({ length: 7 }).map((_, i) => (
          <div key={i} className="h-3 w-full rounded bg-gray-200 mb-3" />
        ))}
      </div>
      {/* Donut 1 */}
      <div className="bg-white rounded-xl p-4 flex flex-col items-center">
        <div className="h-4 w-24 rounded bg-gray-200 mb-4" />
        <div className="w-20 h-20 rounded-full bg-gray-200 mb-4" />
        <div className="flex gap-2 w-full justify-center">
          <div className="h-3 w-10 rounded bg-gray-200" />
          <div className="h-3 w-10 rounded bg-gray-200" />
        </div>
      </div>
      {/* Donut 2 */}
      <div className="bg-white rounded-xl p-4 flex flex-col items-center">
        <div className="h-4 w-24 rounded bg-gray-200 mb-4" />
        <div className="w-20 h-20 rounded-full bg-gray-200 mb-4" />
        <div className="flex gap-2 w-full justify-center">
          <div className="h-3 w-10 rounded bg-gray-200" />
          <div className="h-3 w-10 rounded bg-gray-200" />
        </div>
      </div>
    </div>

    {/* Full width table */}
    <div className="bg-white rounded-xl p-4">
      <div className="h-4 w-24 rounded bg-gray-200 mb-4" />
      {Array.from({ length: 7 }).map((_, i) => (
        <div key={i} className="h-3 w-full rounded bg-gray-200 mb-3" />
      ))}
    </div>
  </div>
);

export default DashboardSkeleton;