import React from 'react';

interface statisticsProps {
  icon?: React.ReactNode;
  title: string;
  parentContainer?: string;
  valueText: string;
  value: number | string;
  backgroundColor?: string;
  iconBackgroundColor?: string;
  currency?: string | React.ReactNode;
  iconBgColor?: string;
}

const StatisticsCard = ({
  icon,
  title,
  parentContainer,
  valueText,
  value,
  backgroundColor,
  currency,
  iconBackgroundColor,
}: statisticsProps) => {
  return (
    // <div className={`py-[22px] rounded-xl shadow-lg bg-gradient-to-l from-${backgroundColor} via-grey-500 to-white pl-4 ${parentContainer}`}>
    <div
      className={`py-[13px] h-[80px] rounded  shadow-md ${backgroundColor} bg-[#3730A3] text-[#3730A3] pl-4 ${parentContainer}`}
      style={{ backgroundColor: backgroundColor }}
    >
      <div className='flex gap-4'>
        <div
          className='h-[51px] w-[51px] rounded-full flex justify-center items-center  text-[#fff]'
          style={{ backgroundColor: iconBackgroundColor }}
        >
          {icon}
        </div>
        <div className='mt-0'>
          <h1 className='font-poppins-medium text-18'>{title}</h1>
          <div className='flex'>
            <p className='text-14 font-poppins'>{currency}</p>
            <p className='text-14 font-poppins'>
              {value} {valueText}
              {/* {value > 1 ? '(s)' : ''} */}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatisticsCard;
