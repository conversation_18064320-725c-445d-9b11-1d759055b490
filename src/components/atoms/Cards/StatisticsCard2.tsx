import React from 'react';

interface StatisticsProps {
  icon?: React.ReactNode;
  title: string;
  parentContainer?: string;
  valueText: string;
  value: number | string;
  backgroundColor?: string;
  currency?: string;
}

const StatisticsCard2 = ({
  icon,
  title,
  parentContainer,
  valueText,
  value,
  backgroundColor,
  currency,
}: StatisticsProps) => {
  return (
    // <div className={`py-[22px] rounded-xl shadow-lg bg-gradient-to-l from-${backgroundColor} via-grey-500 to-white pl-4 ${parentContainer}`}>
    <div
      className={`py-[22px] rounded-xl shadow-lg bg-${
        backgroundColor ?? 'purple-dark'
      } pl-4 ${parentContainer}`}
    >
      <div className='flex gap-4'>
        <div className='h-[41px] w-[41px] text-[#fff] rounded-full border-2 border-[#fff] flex justify-center items-center '>
          {icon}
        </div>
        <div className='mt-0'>
          <h1 className='font-poppins-medium text-18 text-[#fff]'>{title}</h1>
          <div className='flex'>
            <p className='text-14 font-poppins text-[#fff]'>{currency}</p>
            <p className='text-14 font-poppins text-[#fff]'>
              {value} {valueText}
              {/* {value > 1 ? '(s)' : ''} */}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatisticsCard2;
