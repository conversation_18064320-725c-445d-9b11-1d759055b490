import React from 'react';

interface trendValueProps {
    value: number | string;
    title: string;
    icon: React.ReactNode;
    currency: string;
    color: string;
    name?: string;
}

const TrendAnalyticCard = ({icon, currency, value, title, color, name}: trendValueProps) => {
  return (
    <div className={` ${color} py-5 px-4 rounded-lg shadow-lg pl-7`}>
      <div className='flex justify-between '>
          <div>
              <h1 className='font-poppins-medium text-15 text-[#36454F]'>{title}</h1>
              <p className='text-15 text-[#36454F] mt-5'><span className="text-[0.8rem] bg-purple-dark text-white px-2 py-0.5 rounded mb-1 mr-2">{ currency }</span><span className="font-black">{value?.toLocaleString()}</span> <span className='text-13 capitalize'>{name}</span></p>
          </div>
          {icon}
      </div>
    </div>
  )
}

export default TrendAnalyticCard