import React from "react";
import EmptyStatePlusIcon from "../Icons/EmptyStatePlusIcon";
import CustomButton from "../CustomButton/CustomButton";
import { ButtonProperties } from "../../shared/helpers";
import { FiPlus } from "react-icons/fi";

const OrganizationEmptyState = ({handleClick, buttonTitle, text, className}:any) => {
  return (
    <div className={`bg-white w-[690px] py-[55px] rounded-xl ${className}`}>
      <div>
        <div className="flex justify-center items-center">
          <EmptyStatePlusIcon />
        </div>
        <h1 className="text-center mt-5 text-neutral-normal-hover font-poppins-medium">Nothing to see yet </h1>
        <div className="w-1/2 m-auto">
          <p className="text-center leading-4 text-12 text-neutral-normal mt-2 font-mulish">
            {text ? text : "You haven’t added anything yet. When you do, they will appear here."}
          </p>
        </div>
        {buttonTitle && (
        <div className="flex justify-center items-center mt-6">
          <CustomButton className="!w-[250px]" isTransparent={true} handleClick={() => handleClick()} variant={ButtonProperties.VARIANT.primary.name}  leftIcon={<FiPlus className="ml-3" size={20} />} title={buttonTitle}/>
        </div>
        )}
      </div>
    </div>
  );
};

export default OrganizationEmptyState;
