import React, { ReactNode } from "react";
import { MdTrendingDown, MdTrendingUp } from "react-icons/md";

interface OverviewStatsProps {
    title: string;
    icon:  ReactNode;
    valueCount: number;
    trendValue: number;
    lastMonthValue: number;
    lastMonthTitle: string;
    cardBackgroundColor: string;
    isTrendUp: boolean;
};
 
const OverviewStatsCard = ({title, icon, cardBackgroundColor, valueCount, trendValue, lastMonthTitle, lastMonthValue, isTrendUp}: OverviewStatsProps) => {
  return (
    <div>
      <div className={`px-4 py-6 rounded-xl font-poppins ${cardBackgroundColor}`}>
        <div className="flex gap-4">
          <div className="h-[48px] w-[48px] rounded-full bg-white flex justify-center items-center">
            {icon}
          </div>
          <h1 className="font-poppins-medium text-14 flex justify-center items-center text-neutral-dark">{title}</h1>
        </div>
          <div className="flex justify-between mt-3">
            <p className="text-20 font-semibold text-neutral-dark">{valueCount}</p>
            <div className={`${isTrendUp ? "bg-[#EDF9F0] text-[#19B946]" : "bg-[#C10A0A26] text-[#C71026]"} font-poppins-medium flex gap-1 justify-center items-center p-1.5 rounded-[2px]`}>
                {isTrendUp ? <MdTrendingUp size={15}/> : <MdTrendingDown size={15}/> }
                <p className="text-12">{trendValue}%</p>
            </div>
          </div>
          <div className="mt-3">
            <p className="text-neutral-dark-active text-12">Last Month: {lastMonthValue} {lastMonthTitle}</p>
          </div>
      </div>
    </div>
  );
};

export default OverviewStatsCard;
