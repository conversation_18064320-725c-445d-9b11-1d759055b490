import React from 'react';
import { InfoCircle } from 'iconsax-react';
import Tooltip from '../Tooltip/Tooltip';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  iconBgColor: string;
  iconColor: string;
  tooltipContent: string;
  showCurrency?: boolean;
  currencyCode?: string;
  className?: string;
  valueSize?: string;
  cardBorder?: string;
}

const StatsCard = ({
  title,
  value,
  icon,
  iconBgColor,
  iconColor,
  tooltipContent,
  showCurrency = false,
  currencyCode = 'NGN',
  className = '',
  valueSize,
  cardBorder
}: StatsCardProps) => {
  return (
    <div className={`border border-[#EEF2FF] rounded-sm p-4 bg-white ${className} ${cardBorder}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">{title}</span>        
        </div>
        <div className={`w-8 h-8 ${iconBgColor} rounded-full flex items-center justify-center`}>
          <div className={iconColor}>
            {icon}
          </div>
        </div>
      </div>
      
      {showCurrency ? (
        <div className="flex items-center gap-2">
          <span className="text-xs bg-purple-light text-purpl-normal px-2 py-1 rounded font-medium">
            {currencyCode}
          </span>
          <span className={`text-xl font-bold text-gray-900 ${valueSize}`}>{value} <Tooltip content={tooltipContent}>
        <InfoCircle size={14} className="text-gray-400 hover:text-gray-600 cursor-help" />
      </Tooltip></span>
        </div>
      ) : (
        <div className={`text-xl font-bold text-gray-900 ${valueSize}`}>{value}</div>
      )}
    </div>
  );
};

export default StatsCard;
