import { FaElli<PERSON>, Fa<PERSON>en } from "react-icons/fa6";
import FilterDropdown from "./FilterDropdown";
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "iconsax-react";
import useClickOutside from "../../shared/hooks";
import { useNavigate } from "react-router-dom";
import { CiImageOn } from "react-icons/ci";
import { useRecoilValue } from "recoil";
import { loggedUserAtom } from "../../../recoil/atom/authAtom";

const InventoryItemCard = ({data}: any) => {
    const navigate = useNavigate();
    const [showDropdown, setShowDropdown] = useState(false);
    const getLoggedUser = useRecoilValue(loggedUserAtom);
  
    const defaultCurrency = getLoggedUser.businesses[0].default_currency;
  

    const node = useClickOutside(() => {
        setShowDropdown(false);
    });


  return (
    <div className="bg-white w-[261px] border-[0.4px] pb-6 rounded-3xl px-6 text-neutral-normal relative" ref={node}>
      <div>
        <div className="flex relative justify-end my-5">
            <FaEllipsis className="cursor-pointer" onClick={() => setShowDropdown(!showDropdown)} size={14}/>
                <div>
                    {showDropdown && (
                        <FilterDropdown className="right-1 w-[8rem] !min-h-3">
                            <ul className="py-2 px-2">
                                <li className="flex gap-2 text-14 cursor-pointer" onClick={() => navigate("/inventory", {state: {data: data}})}><Eye className="mt-0.5" size={14}/>View</li>
                                <li className="flex gap-2 mt-2 text-14 cursor-pointer" onClick={() => navigate("/inventory", {state: {data: data}})}><FaPen className="mt-0.5" size={14}/> Edit</li>
                            </ul>
                        </FilterDropdown>
                    )}
                </div>

        </div>
        <div className="flex justify-center items-center">
            {data?.images ? (
                <img
                className="h-[180px] w-full rounded-lg border object-cover"
                src={data?.images[0].attachment}
                />

            ) : (
                <div className="w-[100px] h-[100px]  text-neutral-normal font-poppins-medium font-bold flex justify-center items-center ">
                  <p className="text-[30px] mt-3"><CiImageOn size={100}/></p>
                </div>
            )}

        </div>
        <p className="mt-4 text-center font-poppins-medium capitalize">{data?.name} </p>
        <p className="mt-2 text-center font-poppins-medium capitalize">Quantity: {data?.quantity} </p>
        <div className="flex justify-center gap-3">

            <p className="text-center text-12 px-6 bg-[#f4f4f4] rounded-md py-1 mt-3 capitalize">Cost Price <br /> {defaultCurrency}{data?.cost_price?.toLocaleString()}</p>
            <p className="text-center text-12 px-6 bg-[#f4f4f4]  rounded-md py-1 mt-3 capitalize">Selling Price <br /> {defaultCurrency}{data?.selling_price?.toLocaleString()}</p>
        </div>
        <a href={`#`}>
            <p className="bg-purple-light text-purple-normal flex gap-2 rounded-[18px] text-12 mt-5 py-2 justify-center">Copy Link <Copy size={14} className="mt-0.5"/></p>
        </a>
      </div>
      {showDropdown && (<div className="absolute w-[261px] h-auto left-0 top-0 bottom-0 bg-black bg-opacity-40 rounded-3xl"/>)}
      
    </div>
  );
};

export default InventoryItemCard;
