import React from 'react';

interface dropdownProps {
    children: React.ReactNode;
    className?: string;
}

const FilterDropdown = ({className, children}: dropdownProps) => {
  return (
    <div className={`absolute top-6 -right-7 bg-white min-h-20 rounded drop-shadow-2xl  w-[235px] z-50 ${className}`}>
      {/* <p className='absolute -z-10 bg-white w-[49px] h-[39px] -top-[1.35px] right-4 rotate-45'/> */}
      {children}
    </div>
  )
}

export default FilterDropdown