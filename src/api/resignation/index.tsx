import { clustarkToast } from "../../components/atoms/Toast";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";
import { pageFilterProps } from "../organization";


  export async function getResignations(data? : pageFilterProps) {
    try {
      const response = await api.get(`/staff/get-staff-resignations?q=${data?.search || ""}&page=${data?.page || 1}&limit=${data?.limit || 10}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      };
     
        throw error;
    }
  };

  

  export async function UpdateResination( data: any) {
    try {
      const response = await api.put(`/staff/update-resignation-status`, data);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      };
     
      if (error.response) {
          clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
        } else {
          clustarkToast(NotificationTypes.ERROR, error.message);
        }
    }
  };

  export async function getResignationById(id: string) {
    try {
      const response = await api.get(`/staff/get-single-resignation/${id}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      };
     
        throw error;
    }
  };

 
