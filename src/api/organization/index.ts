import { clustarkToast } from "../../components/atoms/Toast";
import {
  addBranchProps,
  addDepartmentProps,
} from "../../components/schemas/organization";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";

export interface pageFilterProps {
  limit?: any;
  page?: any;
  search?: any;
}

export const noActiveSub = "You have no active subscription";

export async function addBranch(data: addBranchProps) {
  try {
    const response = await api.post("/branch/add-branch", data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function updateBranch(data: addBranchProps) {
  try {
    const response = await api.put("/branch/update-branch", data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function getBranches(data?: pageFilterProps) {
  try {
    const response = await api.get(
      `/branch/get-branches?q=${data?.search || ""}&page=${
        data?.page || 1
      }&limit=${data?.limit || 10}`
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function getBranchById(id: any) {
  try {
    const response = await api.get(`/branch/get-branch?branchId=${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function changeDefaultBranch(branchId: { branchId: string }) {
  try {
    const response = await api.put("/branch/change-default-branch", branchId);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function clearDefaultBranch() {
  try {
    const response = await api.put("/branch/clear-default-branch");
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function getDefaultBranch() {
  try {
    const response = await api.get("/branch/get-default-branch");
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function changeBranchHQ(branchId: { branchId: string }) {
  try {
    const response = await api.put("/branch/change-hq", branchId);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function getBranchManagerPermissions() {
  try {
    const response = await api.get("/business/get-branch-manager-permissions");
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function updateBranchManager(data: any) {
  try {
    const response = await api.put("/branch/update-branch-manager", data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function getOrganization() {
  try {
    const response = await api.get("/business/business-data");
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function getDepartments(
  data?: pageFilterProps,
  branch_id?: string
) {
  try {
    const response = await api.get(
      `/department/get-departments?q=${data?.search || ""}&page=${
        data?.page || 1
      }&limit=${data?.limit || 10}&branch_id=${branch_id || ""}`
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function getDepartmentsbyBranchId(branch_id?: string) {
  try {
    const response = await api.get(
      `/department/get-departments${branch_id ? `?branch_id=${branch_id}` : ""}`
    );
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }
    throw error;
  }
}

export async function getDepartmentById(departmentId: {
  departmentId: string;
}) {
  try {
    const response = await api.get(
      `/department/get-department?departmentId=${departmentId}`
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function addDepartment(data: addDepartmentProps) {
  try {
    const response = await api.post("/department/create-department", data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function updateDepartment(data: addDepartmentProps) {
  try {
    const response = await api.put("/department/update-department", data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function updateDepartmentHead(data: any) {
  try {
    const response = await api.put("/department/update-department-head", data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function getDepartmentHeadPermissions() {
  try {
    const response = await api.get("/business/get-department-head-permissions");
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

// ***************** Grade levels ********************************
export async function getGradeLevels(data?: pageFilterProps) {
  try {
    const response = await api.get(
      `/business/grade-levels?q=${data?.search || ""}&page=${
        data?.page || 1
      }&limit=${data?.limit || 10}`
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function addGradeLevel(data: any) {
  try {
    const response = await api.post("/business/create-grade-level", data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function updateGradeLevel(data: any) {
  try {
    const response = await api.put("/business/update-grade-level", data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function deleteGradeLevel(gradeLevelId: string) {
  try {
    const response = await api.delete(
      `/business/delete-grade-level?gradeLevelId=${gradeLevelId}`
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

// ******Business ********* //
export async function getBusinesses() {
  try {
    const response = await api.get("/business/get-businesses");
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function getDefaultBusiness() {
  try {
    const response = await api.get("/business/get-default-business");
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function changeDefaultBusiness(businessId: {
  businessId: string;
}) {
  try {
    const response = await api.put(
      "/business/switch-default-business",
      businessId
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function getBusinessAnalytics() {
  try {
    const response = await api.get("/business/get-business-analytics");
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    throw error;
  }
}

export async function updateBusinessAvatar(data) {
  try {
    const response = await api.put("/business/update-business-avatar", data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function deleteBusinessAvatar() {
  try {
    const response = await api.put("/business/delete-business-avatar");
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function updateBusinessInfo(data) {
  try {
    const response = await api.put(
      "/business/update-business-information",
      data
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}
