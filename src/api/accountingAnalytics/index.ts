import api from "../interceptor";

interface dateRangeFilterProps {
    from: string;
    to: string;
}

export async function getIncomeExpenseSummary() {
    try {
      const response = await api.get(`/analytics/total-income-expense-summary`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/"
        localStorage.clear();
      }
        throw error;
    }
};

export async function getIncomeChartTrend({from, to}: dateRangeFilterProps) {
    try {
      const response = await api.get(`/analytics/income-chart-trend?from=${from}&to=${to}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/"
        localStorage.clear();
      }
        throw error;
    }
};

export async function getHighestIncomeBranch({from, to}: dateRangeFilterProps) {
    try {
      const response = await api.get(`/analytics/highest-income-branches?from=${from}&to=${to}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/"
        localStorage.clear();
      }
        throw error;
    }
};

export async function getHighestExpenseBranch({from, to}: dateRangeFilterProps) {
    try {
      const response = await api.get(`/analytics/highest-expense-branches?from=${from}&to=${to}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/"
        localStorage.clear();
      }
        throw error;
    }
};

export async function getExpenseChartTrend({from, to}: dateRangeFilterProps) {
    try {
      const response = await api.get(`/analytics/expense-chart-trend?from=${from}&to=${to}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/"
        localStorage.clear();
      }
        throw error;
    }
};

export async function getDisbursementSummary({from, to}: dateRangeFilterProps) {
    try {
      const response = await api.get(`/analytics/get-disbursement-summary?from=${from}&to=${to}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/"
        localStorage.clear();
      }
        throw error;
    }
};

export async function getRevenueSUmmary({from, to}: dateRangeFilterProps) {
    try {
      const response = await api.get(`/analytics/get-revenue-summary?from=${from}&to=${to}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/"
        localStorage.clear();
      }
        throw error;
    }
};

export async function getTop10ExpenseVendor({from, to}: dateRangeFilterProps) {
  try {
    const response = await api.get(`/analytics/top-ten-highest-expense-vendor?from=${from}&to=${to}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
      localStorage.clear();
    }
      throw error;
  }
};

export async function getTop10ExpenseCategory({from, to}: dateRangeFilterProps) {
  try {
    const response = await api.get(`/analytics/top-ten-highest-expense-category?from=${from}&to=${to}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
      localStorage.clear();
    }
      throw error;
  }
};

export async function getTop10IncomeCustomers({from, to}: dateRangeFilterProps) {
  try {
    const response = await api.get(`/analytics/top-ten-income-customers?from=${from}&to=${to}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
      localStorage.clear();
    }
      throw error;
  }
};

export async function getTop10IncomeSources({from, to}: dateRangeFilterProps) {
  try {
    const response = await api.get(`/analytics/top-ten-income-sources?from=${from}&to=${to}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
      localStorage.clear();
    }
      throw error;
  }
};