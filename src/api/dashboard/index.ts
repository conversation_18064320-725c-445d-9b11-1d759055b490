
import { handleError } from "../handleError";
import api from "../interceptor";


export async function getDashboardStatistics() {
    try {
      const response = await api.get(`/misc/get-dashboard-statistics`);
      return response.data;
    } catch (error: any) {
     handleError(error)
    }
};

export async function getEmployeeStatistics() {
  try {
    const response = await api.get(`/misc/get-dashboard-employee-statistics`);
    return response.data;
  } catch (error: any) {
   handleError(error)
  }
};

export async function getEmployeeComposition() {
  try {
    const response = await api.get(`/misc/get-dashboard-employee-composition`);
    return response.data;
  } catch (error: any) {
   handleError(error)
  }
};

export async function getEmployeeReminders() {
  try {
    const response = await api.get(`/misc/get-dashboard-birthday-reminders`);
    return response.data;
  } catch (error: any) {
   handleError(error)
  }
};

export async function getEmploymentTypes() {
  try {
    const response = await api.get(`/misc/get-dashboard-top-three-employment-types`);
    return response.data;
  } catch (error: any) {
   handleError(error)
  }
};

export async function getContractReminders() {
  try {
    const response = await api.get(`/misc/get-contract-expiration-reminders`);
    return response.data;
  } catch (error: any) {
   handleError(error)
  }
};

export async function getSalaryStatistics() {
  try {
    const response = await api.get(`/misc/get-salary-statistics`);
    return response.data;
  } catch (error: any) {
   handleError(error)
  }
};