import { clustarkToast } from '../../components/atoms/Toast';
import { NotificationTypes } from '../../components/shared/helpers/enums';
import api from '../interceptor';
import { pageFilterProps } from '../organization';

export async function getCurrentPlan() {
  try {
    const response = await api.get(`/payments/get-current-plan`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = '/';
      localStorage.clear();
    }
    throw error;
  }
}

export async function getPlans() {
  try {
    const response = await api.get(`/payments/plans`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = '/';
      localStorage.clear();
    }
    throw error;
  }
}

export async function getSubscriptionHistory(data: pageFilterProps) {
  try {
    const response = await api.get(
      `/payments/subscription-history?q=${data?.search || ''}&page=${data?.page || 1}&limit=${
        data?.limit || 10
      }`
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = '/';
      localStorage.clear();
    }
    throw error;
  }
}

export async function startFreeTrial(data: any) {
  try {
    const response = await api.post(`/payments/start-free-trial`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = '/';
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          'Something went wrong'
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function cancelSubscription(businessId: string) {
  try {
    const response = await api.post(`/payments/cancel-subscription`, businessId);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = '/';
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          'Something went wrong'
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function getSubscriptionTransactionHistory(data: pageFilterProps) {
  try {
    const response = await api.get(
      `/payments/subscription-transaction-history?q=${data?.search || ''}&page=${
        data?.page || 1
      }&limit=${data?.limit || 10}`
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = '/';
      localStorage.clear();
    }
    throw error;
  }
}

export async function getCardHistory(data: pageFilterProps) {
  try {
    const response = await api.get(
      `/payments/saved-cards?q=${data?.search || ''}&page=${data?.page || 1}&limit=${
        data?.limit || 10
      }`
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = '/';
      localStorage.clear();
    }
    throw error;
  }
}
