import { clustarkToast } from "../../components/atoms/Toast";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";
import { pageFilterProps } from "../organization";

interface InventoryQueryProps {
  department_id?: string;
  branch_id?: string;
  vendor_id?: string;
  date?: string;
  expense_category_id?: string;
}

export async function getInventory(
  data?: pageFilterProps,
  payload?: InventoryQueryProps
) {
  try {
    const response = await api.get(
      `/inventory/get-inventories?page=${data?.page || 1}&limit=${
        data?.limit || 100
      }&q=${data?.search}`,
      { params: payload }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }
  }
}

export async function getSingleInventory(id: string) {
  try {
    const response = await api.get(`/inventory/get-inventory/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }
  }
}

export async function createInventory(data: any) {
  try {
    const response = await api.post(`/inventory/create-inventory`, data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }
  }
}

export async function updateInventory(data: any) {
  try {
    const response = await api.put(`/inventory/update-inventory`, data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }
  }
}

export async function deleteInventory(id: string) {
  try {
    const response = await api.delete(
      `/inventory/delete-Inventory?inventory_id=${id}`
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    if (error.response.status === 401) {
      window.location.href = "/inventory";
    }
  }
};


export async function addVariantInventory(data: any) {
  try {
    const response = await api.post(`/inventory/add-variant-to-inventory`, data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }
  }
};

export async function deleteVaraintInventory(id: string) {
  try {
    const response = await api.delete(
      `/inventory/remove-variant-from-inventory?inventory_variant_id=${id}`
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }
  }
};

export async function restockVaraintInventory(data: any) {
  try {
    const response = await api.put(
      `/inventory/re-stock-inventory`, data
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    if (error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }
  }
};
