import { clustarkToast } from "../../components/atoms/Toast";
import { addEmployeeProps } from "../../components/schemas/organization";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";
import { pageFilterProps } from "../organization";

interface StaffQueryProps {
  department_id?: string;
  branch_id?: string;
}

export async function getStaffData() {
  try {
    const response = await api.get(`/staff/get-staff-data`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function getStaff(
  data?: pageFilterProps,
  payload?: StaffQueryProps
) {
  try {
    const response = await api.get(
      `/staff/get-staffs?q=${data?.search || ""}&page=${
        data?.page || 1
      }&limit=${data?.limit || 10}`,
      {
        params: payload,
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function getAllStaff(
  data?: pageFilterProps,
  payload?: StaffQueryProps
) {
  try {
    const response = await api.get(
      `/staff/get-all-staff?page=${data?.page || 1}&limit=${
        data?.limit || 100
      }`,
      {
        params: payload,
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function getStaffById(id: any) {
  try {
    const response = await api.get(`/staff/get-staff/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function createStaff(data?: addEmployeeProps) {
  try {
    const response = await api.post(`/staff/add-staff`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function updateStaff(id: string, data?: addEmployeeProps) {
  try {
    const response = await api.put(`/staff/update-staff/${id}`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function transferStaffToBranch(data?: any) {
  try {
    const response = await api.put(`/staff/transfer-staff-to-branch`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function transferStaffToDepartment(data?: any) {
  try {
    const response = await api.put(`/staff/transfer-staff-to-department`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function getEmployeeDisputes(data?: pageFilterProps) {
  try {
    const response = await api.get(
      `/staff/get-all-disputes?q=${data?.search || ""}&page=${
        data?.page || 1
      }&limit=${data?.limit || 10}`
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401 || error.response.status === 403) {
      window.location.href = "/";
      localStorage.clear();
    }
    throw error;
  }
}

export async function changeEmployeeDisputeStatus(data: any) {
  try {
    const response = await api.put(`/staff/update-dispute-status`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401 || error.response.status === 403) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message || "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function createBulkStaff(data?: any) {
  try {
    const response = await api.post(`/staff/upload-staff-bulk-data`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function createNextOfKin(data?: any) {
  try {
    const response = await api.post(`/staff/add-staff-next-of-kin`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function updateNextOfKin(data?: any) {
  try {
    const response = await api.put(`/staff/update-staff-next-of-kin`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function createWorkExperience(data?: any) {
  try {
    const response = await api.post(`/staff/add-staff-work-experience`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function updateWorkExperience(data?: any) {
  try {
    const response = await api.put(`/staff/update-staff-work-experience`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function createEducation(data?: any) {
  try {
    const response = await api.post(`/staff/add-staff-education-info`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function updateEducation(data?: any) {
  try {
    const response = await api.put(`/staff/update-staff-education-info`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function updateBasicInformation(data?: any) {
  try {
    const response = await api.put(`/staff/update-staff-basic-info`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function updatePersonalInformation(data?: any) {
  try {
    const response = await api.put(`/staff/update-staff-personal-info`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function createDocument(data?: any) {
  try {
    const response = await api.post(`/staff/upload-staff-document`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function getEmployeeDocument(data?: pageFilterProps) {
  try {
    const response = await api.get(
      `/staff/get-all-disputes?q=${data?.search || ""}&page=${
        data?.page || 1
      }&limit=${data?.limit || 10}`
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401 || error.response.status === 403) {
      window.location.href = "/";
      localStorage.clear();
    }
    throw error;
  }
}

export async function deleteDocument(id: string) {
  try {
    const response = await api.delete(`/staff/delete-staff-document/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function getBanks() {
  try {
    const response = await api.get(`/payments/banks`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function validateBank(data?: any) {
  try {
    const response = await api.post(
      `/staff/validate-staff-account-number`,
      data
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function createBank(data?: any) {
  try {
    const response = await api.post(`/staff/add-staff-bank-info`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function switchBank(data?: any) {
  try {
    const response = await api.put(`/staff/change-bank-default`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
    }

    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}
