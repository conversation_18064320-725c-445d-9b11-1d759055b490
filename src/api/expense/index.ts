import { clustarkToast } from "../../components/atoms/Toast";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";
import { pageFilterProps } from "../organization";

interface ExpenseQueryProps {
  department_id?: string;
  branch_id?: string;
  vendor_id?: string;
  date?: string;
  expense_category_id?: string;
}

export async function getExpenseCategories() {
  try {
    const response = await api.get(`/expense/get-expense-categories`);
    return response.data;
  } catch (error: any) {
    if (error.response) {
        clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      };
     
  }
};

export async function getExpenses(data? : pageFilterProps, payload?: ExpenseQueryProps) {
  try {
    const response = await api.get(`/expense/get-expenses?page=${data?.page || 1}&limit=${data?.limit || 100}&q=${data?.search}`, {params: payload});
    return response.data;
  } catch (error: any) {
    if (error.response) {
        clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      };
     
  }
};

export async function createExpense(data: any) {
  try {
    const response = await api.post(`/expense/create-expense`, data);
    return response.data;
  } catch (error: any) {
    if (error.response) {
        clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      }
     
  }
};

export async function updateExpense(data: any) {
  try {
    const response = await api.put(`/expense/update-expense`, data);
    return response.data;
  } catch (error: any) {
    if (error.response) {
        clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      }
     
  }
};

export async function deleteExpense(id: string) {
  try {
    const response = await api.delete(`/expense/delete-expense?expense_id=${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response) {
        clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      }
     
  }
};

export async function expenseUploadReceipt(data: any) {
  try {
    const response = await api.put(`/expense/upload-receipt`, data);
    return response.data;
  } catch (error: any) {
    if (error.response) {
        clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      }
     
  }
};

