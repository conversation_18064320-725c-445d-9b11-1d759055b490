import { clustarkToast } from "../../components/atoms/Toast";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";
import { pageFilterProps } from "../organization";

export async function createLeaveType(data: any) {
  try {
    const response = await api.post(`/leave/create-leave-type`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    };

    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
};

export async function updateLeaveType(id: string, data: any) {
  try {
    const response = await api.put(`/leave/update-leave-type/${id}`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    };

    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
};

export async function getLeaveTypes(data?: pageFilterProps) {
  try {
    const response = await api.get(`/leave/get-leave-types?q=${data?.search || ""}&page=${data?.page || 1}&limit=${data?.limit || 10}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    };

    throw error;
  }
};

export async function getLeaveApplications(data?: pageFilterProps) {
  try {
    const response = await api.get(`/leave/get-leaves?q=${data?.search || ""}&page=${data?.page || 1}&limit=${data?.limit || 10}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    };

    throw error;
  }
};

export async function getLeaveAnalytics() {
  try {
    const response = await api.get(`/leave/get-leave-analytics`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    };

    throw error;
  }
};

export async function deleteLeaveType(id: string) {
  try {
    const response = await api.delete(`/leave/delete-leave-type/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    };

    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
};

export async function approveLeave(id: string, data: any) {
  try {
    const response = await api.put(`/leave/approve-leave/${id}`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    };

    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
};

export async function declineLeave(id: string, data: any) {
  try {
    const response = await api.put(`/leave/decline-leave/${id}`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    };

    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
};

export async function getLeaveById(id: string) {
  try {
    const response = await api.get(`/leave/get-leave/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    };

    throw error;
  }
};

export async function getStaffLeaveById(id: string) {
  try {
    const response = await api.get(`/staff/get-staff-leaves/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    };

    throw error;
  }
};

