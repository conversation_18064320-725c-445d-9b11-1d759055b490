import { clustarkToast } from "../../components/atoms/Toast";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";



export async function updateAvatar(data) {
  try {
    const response = await api.put(`/profile/update-profile-avatar`, data);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    };
   
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function deleteAvatar() {
  try {
    const response = await api.put(`/profile/delete-avatar`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    };
   
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function updateProfileInfo(data) {
    try {
      const response = await api.put(`/profile/update-profile-information`, data);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/"
        localStorage.clear();
      };
     
      if (error.response) {
        clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
    }
  }

  export async function updatePassword(data) {
    try {
      const response = await api.put(`/profile/change-password`, data);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/"
        localStorage.clear();
      };
     
      if (error.response) {
        clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
    }
  }

export async function getProfileInformation() {
  try {
    const response = await api.get("/profile/get-profile-information");
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    };
   
    throw error;
  }
}

