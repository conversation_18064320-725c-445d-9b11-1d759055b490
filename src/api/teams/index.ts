import { clustarkToast } from "../../components/atoms/Toast";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";
import { pageFilterProps } from "../organization";

interface inviteTeamProps {
  email: string;
  role: string;
}

interface acceptInviteProps {
  firstName: string;
  lastName: string;
  gender: string;
  password: string;
  password_confirmation: string;
  phoneNumber: string;
  token: string;
}


export async function getTeamMembers(data? : pageFilterProps) {
  try {
    const response = await api.get(`/team/get-team?q=${data?.search || ""}&page=${data?.page || 1}&limit=${data?.limit || 10}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401) {
      window.location.href = "/";
        localStorage.clear();
    }
    throw error;
  }

}

export async function getPendingInvitation(data? : pageFilterProps) {
  try {
    const response = await api.get(`/team/pending-invitations?q=${data?.search || ""}&page=${data?.page || 1}&limit=${data?.limit || 10}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/";
        localStorage.clear();
    }
    throw error;
  }
}


export async function inviteTeamMember(data : inviteTeamProps) {
  try {
    const response = await api.post(`/team/invite-member`, data);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/";
        localStorage.clear();
    }
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
};

export async function acceptTeamInvite(data : acceptInviteProps) {
  try {
    const response = await api.post(`/team/accept-invitation`, data);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401) {
      clustarkToast(NotificationTypes.ERROR, "Unauthorized access to accept this invite, please try again");
      return;
    }
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
};

export async function cancelTeamInvitation(id : string) {
  try {
    const response = await api.put(`/team/cancel-invitation?invitation_id=${id}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/";
        localStorage.clear();
    }
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
};

export async function declineTeamInvitation(token) {
  try {
    const response = await api.put(`/team/decline-invitation`, token);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401) {
      clustarkToast(NotificationTypes.ERROR, "Unauthorized access to accept this invite, please try again");
      return;
    }
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
};

export async function validateInvitationToken(token) {
  try {
    const response = await api.post(`/team/validate-invitation-token`, token);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/";
        localStorage.clear();
    }
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, "Ops.., cannot process your invitation link. Try again with a valid invitation link");
    } else {
      clustarkToast(NotificationTypes.ERROR, "Ops.., cannot process your invitation link. Try again with a valid invitation link");
    }
  }
};

export async function revokeTeamAccess(id) {
  try {
    const response = await api.put(`/team/revoke-team-access`, id);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/";
        localStorage.clear();
    }
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
};
