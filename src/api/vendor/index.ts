import { clustarkToast } from "../../components/atoms/Toast";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";
import { pageFilterProps } from "../organization";

export async function addVendor(data: any) {
    try {
      const response = await api.post(`/vendor/add-vendor`, data);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      }
      if (error.response) {
          clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
        } else {
          clustarkToast(NotificationTypes.ERROR, error.message);
        }
    }
};

export async function updateVendor(data: any) {
    try {
      const response = await api.put(`/vendor/update-vendor`, data);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      }
      if (error.response) {
          clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
        } else {
          clustarkToast(NotificationTypes.ERROR, error.message);
        }
    }
};

export async function deleteVendor(id: any) {
    try {
      const response = await api.delete(`/vendor/delete-vendor?vendorId=${id}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      }
      if (error.response) {
          clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
        } else {
          clustarkToast(NotificationTypes.ERROR, error.message);
        }
    }
};

export async function getVendors(data? : pageFilterProps, branchId?: string) {
    try {
      const response = await api.get(`/vendor/get-vendors?q=${data?.search || ""}&branchId=${branchId || ""}&page=${data?.page || 1}&limit=${data?.limit || 10}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      }
        throw error;
    }
  };