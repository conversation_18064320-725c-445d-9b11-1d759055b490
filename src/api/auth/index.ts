import { clustarkToast } from "../../components/atoms/Toast";
import {
  emailProps,
  loginProps,
  registerCompanyProps,
  resetPasswordProps,
  validateOtpProps,
} from "../../components/schemas/auth";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";

export async function validateEmail(email: emailProps) {
  try {
    const response = await api.post("/auth/validate-email", email);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function sendOtp(email: emailProps) {
  try {
    const response = await api.post("/auth/send-otp", email);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}
 
export async function validateOtp(data: validateOtpProps) {
  try {
    const response = await api.post("/auth/validate-otp", data);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function registerCompany(data: registerCompanyProps) {
  try {
    const response = await api.post("/auth/register", data);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function login(data: loginProps) {
  try {
    const response = await api.post("/auth/login", data);
    localStorage.setItem("token", response.data.data.token.token);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function forgetPassword(email: emailProps) {
  try {
    const response = await api.post("/auth/reset-password", email);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function resetPassword(data: resetPasswordProps) {
  try {
    const response = await api.post("/auth/validate-reset-password-otp", data);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}
