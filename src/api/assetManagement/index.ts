import { clustarkToast } from "../../components/atoms/Toast";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";
import { pageFilterProps } from "../organization";

 

export async function getAssetTypes() {
    try {
      const response = await api.get(`/assets/asset-types`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/"
        localStorage.clear();
      }
     
        throw error;
    }
};

export async function getAssignedRequests(data?: pageFilterProps) {
  try {
    const response = await api.get(`/assets/assigned-assets?q=${data?.search || ""}&page=${data?.page || 1}&limit=${data?.limit || 10}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
      throw error;
  }
};

export async function getAllAssets(data?: pageFilterProps) {
  try {
    const response = await api.get(`/assets/get-assets?q=${data?.search || ""}&page=${data?.page || 1}&limit=${data?.limit || 10}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
      throw error;
  }
};

export async function getUnassignedAssets() {
  try {
    const response = await api.get(`/assets/get-unassigned-assets`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
      throw error;
  }
};



export async function getAssignedAssetbyId(id: string) {
  try {
    const response = await api.get(`/assets/assigned-asset/${id}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
      throw error;
  }
};

export async function getAssetAnalytics() {
  try {
    const response = await api.get(`/assets/asset-analytics`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
      throw error;
  }
};

export async function getAssetRequests(data: pageFilterProps) {
  try {
    const response = await api.get(`/assets/asset-requests?q=${data?.search || ""}&page=${data?.page || 1}&limit=${data?.limit || 10}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
      throw error;
  }
};


export async function addNewAsset(data: any) {
  try {
    const response = await api.post(`/assets/add-asset`, data);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
    if (error.response) {
       clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong")
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
     
  }
};

export async function reassignAsset(data: any) {
  try {
    const response = await api.put(`/assets/re-assign-asset`, data);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
    if (error.response) {
       clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong")
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
      
  }
};

export async function assignAsset(data: any) {
  try {
    const response = await api.put(`/assets/assign-asset`, data);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
    if (error.response) {
       clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong")
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
      
  }
};

export async function approveAssetRequest(data: any) {
  try {
    const response = await api.put(`/assets/approve-asset-request`, data);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
    if (error.response) {
       clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong")
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
      
  }
};

export async function rejectAssetRequest(data: any) {
  try {
    const response = await api.put(`/assets/reject-asset-request`, data);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
    if (error.response) {
       clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong")
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
      
  }
};

export async function markAsReturned(data: any) {
  try {
    const response = await api.put(`/assets/mark-as-returned`, data);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
    if (error.response) {
       clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong")
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
     
  }
};

export async function updateAsset(data: any) {
  try {
    const response = await api.put(`/assets/update-asset`, data);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
   
    if (error.response) {
        clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
     
  }
};

export async function deleteAsset(id: any) {
  try {
    const response = await api.delete(`/assets/delete-asset?asset_id=${id}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/"
        localStorage.clear();
    }
   
    if (error.response) {
        clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
      
  }
};


