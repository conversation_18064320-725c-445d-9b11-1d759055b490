import { clustarkToast } from '../components/atoms/Toast';
import { NotificationTypes } from '../components/shared/helpers/enums';

export async function handleError(error: any) {
  if (error.response.status === 401) {
    window.location.href = '/';
    localStorage.clear();
  }

  if (error.response) {
    clustarkToast(
      NotificationTypes.ERROR,
      error.response.data.message || error.response.data.errors[0].message || 'Something went wrong'
    );
  } else {
    clustarkToast(NotificationTypes.ERROR, error.message);
  }
}