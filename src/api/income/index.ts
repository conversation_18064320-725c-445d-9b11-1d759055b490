import { clustarkToast } from "../../components/atoms/Toast";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";
import { pageFilterProps } from "../organization";

interface IncomeQueryProps {
    department_id?: string;
    branch_id?: string;
    customer_id?: string;
    date?: string;
    incomeSourceId?: string;
  }

export async function addIncome(data: any) {
    try {
      const response = await api.post(`/income/create-income`, data);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      };
     
      if (error.response) {
          clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
        } else {
          clustarkToast(NotificationTypes.ERROR, error.message);
        }
    }
};

export async function updateIncome(data: any) {
    try {
      const response = await api.put(`/income/update-income`, data);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      };
     
      if (error.response) {
          clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
        } else {
          clustarkToast(NotificationTypes.ERROR, error.message);
        }
    }
};

export async function deleteIncome(id: any) {
    try {
      const response = await api.delete(`/income/delete-income?incomeId=${id}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      };
     
      if (error.response) {
          clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
        } else {
          clustarkToast(NotificationTypes.ERROR, error.message);
        }
    }
};

export async function getIncomes(data? : pageFilterProps, payload?: IncomeQueryProps) {
    try {
      const response = await api.get(`/income/get-incomes?q=${data?.search || ""}&page=${data?.page || 1}&limit=${data?.limit || 10}`, {params: payload});
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      };
      if (error.response) {
        clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
      } else {
        clustarkToast(NotificationTypes.ERROR, error.message);
      }
        throw error;
    }
  };

  export async function getIncomeSources() {
    try {
      const response = await api.get(`/income/get-income-sources`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      };
     
        throw error;
    }
  };