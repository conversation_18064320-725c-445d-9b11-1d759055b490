import { clustarkToast } from "../../components/atoms/Toast";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";
import { pageFilterProps } from "../organization";

export async function addCustomer(data: any) {
    try {
      const response = await api.post(`/customer/add-customer`, data);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      }
     
      if (error.response) {
          clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
        } else {
          clustarkToast(NotificationTypes.ERROR, error.message);
        }
     
    }
};

export async function updateCustomer(data: any) {
    try {
      const response = await api.put(`/customer/update-customer`, data);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      }
     
      if (error.response) {
          clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
        } else {
          clustarkToast(NotificationTypes.ERROR, error.message);
        }
       
    }
};

export async function deleteCustomer(id: any) {
    try {
      const response = await api.delete(`/customer/delete-customer?customerId=${id}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      }
     
      if (error.response) {
          clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
        } else {
          clustarkToast(NotificationTypes.ERROR, error.message);
        }
        
    }
};

export async function getCustomers(data? : pageFilterProps, branchId?: string) {
    try {
      const response = await api.get(`/customer/get-customers?q=${data?.search || ""}&branchId=${branchId || ""}&page=${data?.page || 1}&limit=${data?.limit || 10}`);
      return response.data;
    } catch (error: any) {
      if(error.response.status === 401 ) {
        window.location.href = "/";
        localStorage.clear();
      }
     
        throw error;
    }
  };