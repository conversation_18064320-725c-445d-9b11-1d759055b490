import { clustarkToast } from "../../components/atoms/Toast";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";
import { pageFilterProps } from "../organization";

export async function createTicket(data) {
  try {
    const response = await api.post(`/ticket/create-ticket`, data);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/";
        localStorage.clear();
    }
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}

export async function getTickets(data?: pageFilterProps) {
  try {
    const response = await api.get(
      `/ticket/get-tickets?q=${data?.search || ''}&page=${
        data?.page || 1
      }&limit=${data?.limit || 10}`
    );
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/";
        localStorage.clear();
    }
    throw error;
  }
}

export async function getTicketChats(ticketId: string) {
  try {
    const response = await api.get(`/ticket/get-ticket-chats?ticketId=${ticketId}`);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/";
        localStorage.clear();
    }
    throw error;
  }
}

export async function getTicketById(ticketId: string) {
  try {
    const response = await api.get(
      `/ticket/get-ticket-chats?ticketId=${ticketId}`
    );
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/";
        localStorage.clear();
    }
    throw error;
  }
}

export async function sendTicketChat(data: any) {
  try {
    const response = await api.post(`/ticket/send-chat`, data);
    return response.data;
  } catch (error: any) {
    if(error.response.status === 401 ) {
      window.location.href = "/";
        localStorage.clear();
    }
    if (error.response) {
      clustarkToast(NotificationTypes.ERROR, error.response.data.message || error.response.data.errors[0].message  || "Something went wrong");
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
  }
}
