import { clustarkToast } from '../../components/atoms/Toast';
import { NotificationTypes } from '../../components/shared/helpers/enums';
import { handleError } from '../handleError';
import api from '../interceptor';


export async function getInfo() {
  const response = await api.get(`/payroll/info`);
  return response.data;
  //   } catch (error: any) {
  //     handleError(error);
  //   }
}

export async function setupPayroll(data: any) {
  try {
    const response = await api.post(`/payroll/setup`, data);
    clustarkToast(NotificationTypes.SUCCESS, response.data.message);
    return response;
  } catch (error: any) {
    handleError(error);
  }
}
