import { handleError } from '../handleError';
import api from '../interceptor';

interface payWithSavedCardProps {
    chargeToken: string;
    amount: string;
    type: string
}


export async function getWalletBalance() {
    try {
      const response = await api.get(`/payments/wallet-balance`);
      return response.data;
    } catch (error: any) {
      handleError(error)
    }
 };

  export async function getWalletTransactions() {
    try {
      const response = await api.get(`/payments/wallet-transactions`);
      return response.data;
    } catch (error: any) {
      handleError(error)
    }
  };

  export async function getSavedCards() {
    try {
      const response = await api.get(`/payments/saved-cards`);
      return response.data;
    } catch (error: any) {
      handleError(error)
    }
};


export async function payWithSavedCard(data: payWithSavedCardProps) {
  try {
    const response = await api.post(`/payments/charge-authorization`, data);
    // clustarkToast(NotificationTypes.SUCCESS, response.data.message);
    return response.data;
  } catch (error: any) {
    handleError(error);
  }
}

