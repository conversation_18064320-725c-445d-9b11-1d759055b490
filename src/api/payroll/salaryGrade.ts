import { clustarkToast } from '../../components/atoms/Toast';
import { NotificationTypes } from '../../components/shared/helpers/enums';
import { handleError } from '../handleError';
import api from '../interceptor';


export async function getSalaryGrades() {
  const response = await api.get(`/payroll/salary-grades`);
  return response.data;
  //   } catch (error: any) {
  //     handleError(error);
  //   }
}

export async function createSalaryGrade(data: any) {
  try {
    const response = await api.post(`/payroll/salary-grades`, data);
    clustarkToast(NotificationTypes.SUCCESS, response.data.message);
    return response;
  } catch (error: any) {
    handleError(error);
  }
}

export async function manageEmployeesOnSalaryGrade(id: number, staffIds: number[]) {
  try {
    const response = await api.post(`/payroll/salary-grades/${id}/manage-employees`, {
      staffIds,
    });
    clustarkToast(NotificationTypes.SUCCESS, response.data.message);
    return response;
  } catch (error: any) {
    handleError(error);
  }
  return false;
}

export async function deleteSalaryGrade(id: any) {
  try {
    const response = await api.delete(`/payroll/salary-grades/${id}`);
    clustarkToast(NotificationTypes.SUCCESS, response.data.message);
    return response;
  } catch (error: any) {
    handleError(error);
  }
}
