import { clustarkToast } from '../../components/atoms/Toast';
import { NotificationTypes } from '../../components/shared/helpers/enums';
import { handleError } from '../handleError';
import api from '../interceptor';

export interface pageFilterProps {
  limit?: any;
  page?: any;
  search?: any;
}


export async function getTransactions( 
  data?: pageFilterProps,
  branch_id?: string
) {
  try {
    const response = await api.get(`/payroll/transactions?q=${data?.search || ""}&limit=${data?.limit || 10}&page=${data?.page}`);
    return response.data;
  } catch (error: any) {
    handleError(error);
  }
  return {};
}

export async function getBatches() {
  try {
    const response = await api.get(`/payroll/transactions/batches`);
    return response.data;
  } catch (error: any) {
    handleError(error);
  }
  return {};
}

export async function updateBatchStatus(id: number, status: string) {
  try {
    const response = await api.post(`/payroll/transactions/${id}/update-status`, { status });
    clustarkToast(NotificationTypes.SUCCESS, response.data.message);
    return response.data;
  } catch (error: any) {
    handleError(error);
  }
  return null;
}
