import { clustarkToast } from "../../components/atoms/Toast";
import { NotificationTypes } from "../../components/shared/helpers/enums";
import api from "../interceptor";

export async function getLabels() {
  try {
    const response = await api.get(`/task/label/get-labels`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function addLabels(data) {
  try {
    const response = await api.post(`/task/label/add-label`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }

    throw error;
  }
}

export async function updateLabel(data) {
  try {
    const response = await api.put(`/task/label/update-label`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }

    throw error;
  }
}

export async function deleteLabel(data) {
  try {
    const response = await api.delete(`/task/label/delete-label`, { data });
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }

    throw error;
  }
}

export async function getStatus() {
  try {
    const response = await api.get(`/task/status/get-statuses`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function updateStatus(data) {
  try {
    const response = await api.put(`/task/status/update-status`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }

    throw error;
  }
}

export async function addStatus(data) {
  try {
    const response = await api.post(`/task/status/add-status`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }

    throw error;
  }
}

export async function TaskupdateStatus(data) {
  try {
    const response = await api.put(`/task/status/update-status`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }

    throw error;
  }
}

export async function deleteStatus(data) {
  try {
    const response = await api.delete(`/task/status/delete-status`, { data });
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }

    throw error;
  }
}

export async function addTeam(data) {
  try {
    const response = await api.post(`/task/team/create-team`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }

    throw error;
  }
}

export async function getTeam() {
  try {
    const response = await api.get(`/task/team/get-teams`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function getTeamById(id) {
  try {
    const response = await api.get(`/task/team/get-team/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function updateTeam(data) {
  try {
    const response = await api.put(`/task/team/edit-team`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function addTeamMember(data) {
  try {
    const response = await api.post(`/task/team/assign-team-member`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }

    throw error;
  }
}

export async function getTeamMember(id) {
  try {
    const response = await api.get(`/task/team/get-team-members/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function getTeamMemberByProject(id: string) {
  try {
    const response = await api.get(
      `/task/project/get-project-team-members/${id}`
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function deleteTeamMember(data) {
  try {
    const response = await api.delete(`/task/team/remove-team-member`, {
      data,
    });
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }

    throw error;
  }
}

export async function addProject(data) {
  try {
    const response = await api.post(`/task/project/add-project`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }

    throw error;
  }
}

export async function getProjects() {
  try {
    const response = await api.get(`/task/project/get-projects`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function getProjectById(id) {
  try {
    const response = await api.get(`/task/project/get-project/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }

    throw error;
  }
}

export async function updateProject(data) {
  try {
    const response = await api.put(`/task/project/update-project`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function updateProjectStatus(data) {
  try {
    const response = await api.put(`/task/project/update-project-status`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function createIssue(data) {
  try {
    const response = await api.post(`/task/issue/create-issue`, data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function updateIssue(data) {
  try {
    const response = await api.put(`/task/issue/update-issue`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function getIssues() {
  try {
    const response = await api.get(`/task/issue/get-issues`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function getSingleIssue(id: string) {
  try {
    const response = await api.get(`/task/issue/get-issue/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function createSubTask(data) {
  try {
    const response = await api.post(
      `/task/issue/add-issue-checklist`,
      data,
      {}
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function getSubtaskById(id: string) {
  try {
    const response = await api.get(`/task/issue/get-issue-checklists/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function updateSubtask(data) {
  try {
    const response = await api.put(`/task/issue/complete-checklist`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function deleteSubtaskById(id: string) {
  try {
    const response = await api.delete(`/task/issue/delete-checklist/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function assignSubtaskById(data) {
  try {
    const response = await api.put(`/task/issue/reassign-checklist`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function unAssignSubtaskById(data) {
  try {
    const response = await api.put(
      `/task/issue/unassign-staff-checklist`,
      data
    );
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function updateTaskStatusById(data) {
  try {
    const response = await api.put(`/task/issue/update-task-status`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function updateTaskLabelById(data) {
  try {
    const response = await api.put(`/task/issue/update-issue-labels`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function updateTaskPriorityById(data) {
  try {
    const response = await api.put(`/task/issue/update-task-priority`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function updateTaskStatus(data) {
  try {
    const response = await api.put(`/task/issue/update-task-status`, data);
    return response.data;
  } catch(error: any) {
    if(error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
};

export async function updateTaskLabel(data) {
  try {
    const response = await api.put(`/task/issue/update-issue-labels`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
};

export async function addTaskAssignee(data) {
  try {
    const response = await api.post(`/task/issue/add-assignee`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
};

export async function removeTaskAssignee(data) {
  try {
    const response = await api.put(`/task/issue/remove-assignee`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
};


export async function sendComment(data) {
  try {
    const response = await api.post(`/task/issue/save-comment`, data);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}

export async function getTaskComment(id: string) {
  try {
    const response = await api.get(`/task/issue/get-comments/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response.status === 401) {
      window.location.href = "/";
      localStorage.clear();
    }
    if (error.response) {
      clustarkToast(
        NotificationTypes.ERROR,
        error.response.data.message ||
          error.response.data.errors[0].message ||
          "Something went wrong"
      );
    } else {
      clustarkToast(NotificationTypes.ERROR, error.message);
    }
    throw error;
  }
}
