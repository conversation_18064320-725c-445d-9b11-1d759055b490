import React from 'react'

const SearchCategoryIcon = () => {
  return (
    <>
    <svg width="120" height="101" viewBox="0 0 120 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M70.2755 37.1659C62.8611 37.2125 55.3316 36.8491 48.2887 34.7896C41.2459 32.7302 35.017 28.8442 29.3544 24.306C25.6472 21.3519 22.2762 19.0036 17.463 19.3577C12.7569 19.6182 8.25541 21.4676 4.62483 24.6321C-1.49785 30.2793 -0.56883 40.7164 1.87316 48.0596C5.55385 59.121 16.7552 66.7997 26.2666 71.8039C37.2644 77.5908 49.3416 80.9549 61.4543 82.8839C72.0716 84.5799 85.7061 85.8193 94.899 78.5134C103.349 71.8132 105.667 56.4931 103.596 46.1492C103.093 43.0941 101.548 40.3374 99.2521 38.396C93.3152 33.8298 84.4674 36.877 77.7962 37.0354C75.363 37.0914 72.806 37.1566 70.2755 37.1659Z" fill="#F2F2F2"/>
<path d="M34.123 1V5.00708" stroke="#CFCFCF" stroke-width="0.85" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M32.2207 3.00391H36.0253" stroke="#CFCFCF" stroke-width="0.85" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M106.304 80.8711V84.8782" stroke="#CFCFCF" stroke-width="0.85" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M104.401 82.875H108.206" stroke="#CFCFCF" stroke-width="0.85" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.37685 63.5667C5.85573 63.5667 6.24393 63.1578 6.24393 62.6535C6.24393 62.1491 5.85573 61.7402 5.37685 61.7402C4.89797 61.7402 4.50977 62.1491 4.50977 62.6535C4.50977 63.1578 4.89797 63.5667 5.37685 63.5667Z" fill="#CFCFCF"/>
<path d="M79.203 5.13703C79.6819 5.13703 80.0701 4.72816 80.0701 4.22379C80.0701 3.71942 79.6819 3.31055 79.203 3.31055C78.7241 3.31055 78.3359 3.71942 78.3359 4.22379C78.3359 4.72816 78.7241 5.13703 79.203 5.13703Z" fill="#CFCFCF"/>
<path d="M55.1726 100.999C73.1061 100.999 87.6441 100.048 87.6441 98.8747C87.6441 97.7013 73.1061 96.75 55.1726 96.75C37.2391 96.75 22.7012 97.7013 22.7012 98.8747C22.7012 100.048 37.2391 100.999 55.1726 100.999Z" fill="#F2F2F2"/>
<path d="M30.1593 11.8848H77.3181C78.4914 11.8848 79.6167 12.3757 80.4463 13.2495C81.276 14.1233 81.742 15.3084 81.742 16.5442V75.6347C81.742 76.8704 81.276 78.0555 80.4463 78.9294C79.6167 79.8032 78.4914 80.2941 77.3181 80.2941H24.1163C22.943 80.2941 21.8178 79.8032 20.9881 78.9294C20.1585 78.0555 19.6924 76.8704 19.6924 75.6347V23.0207L30.1593 11.8848Z" fill="white" stroke="#BABABA" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M81.5299 40.6797H32.9023V80.8623H81.5299V40.6797Z" fill="#D2D2D2"/>
<path d="M19.6924 23.0207H28.2659C28.7689 23.0183 29.2505 22.8061 29.6053 22.4306C29.9601 22.0551 30.1593 21.5469 30.1593 21.0172V11.8848L19.6924 23.0207Z" fill="white" stroke="#BABABA" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M81.291 27.8477H33.1944C31.3228 27.8477 29.8057 29.4456 29.8057 31.4168V42.2545C29.8057 44.2257 31.3228 45.8236 33.1944 45.8236H81.291C83.1626 45.8236 84.6797 44.2257 84.6797 42.2545V31.4168C84.6797 29.4456 83.1626 27.8477 81.291 27.8477Z" fill="white" stroke="#BABABA" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M39.1495 39.971C40.7816 39.971 42.1047 38.5775 42.1047 36.8586C42.1047 35.1396 40.7816 33.7461 39.1495 33.7461C37.5174 33.7461 36.1943 35.1396 36.1943 36.8586C36.1943 38.5775 37.5174 39.971 39.1495 39.971Z" fill="#D2D2D2"/>
<path d="M48.4395 39.971C50.0716 39.971 51.3947 38.5775 51.3947 36.8586C51.3947 35.1396 50.0716 33.7461 48.4395 33.7461C46.8074 33.7461 45.4844 35.1396 45.4844 36.8586C45.4844 38.5775 46.8074 39.971 48.4395 39.971Z" fill="#D2D2D2"/>
<path d="M57.7198 39.971C59.3519 39.971 60.675 38.5775 60.675 36.8586C60.675 35.1396 59.3519 33.7461 57.7198 33.7461C56.0877 33.7461 54.7646 35.1396 54.7646 36.8586C54.7646 38.5775 56.0877 39.971 57.7198 39.971Z" fill="#D2D2D2"/>
<path d="M81.291 47.7715H33.1944C31.3228 47.7715 29.8057 49.3694 29.8057 51.3406V62.1783C29.8057 64.1495 31.3228 65.7474 33.1944 65.7474H81.291C83.1626 65.7474 84.6797 64.1495 84.6797 62.1783V51.3406C84.6797 49.3694 83.1626 47.7715 81.291 47.7715Z" fill="white" stroke="#BABABA" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M39.1495 59.9046C40.7816 59.9046 42.1047 58.5111 42.1047 56.7922C42.1047 55.0732 40.7816 53.6797 39.1495 53.6797C37.5174 53.6797 36.1943 55.0732 36.1943 56.7922C36.1943 58.5111 37.5174 59.9046 39.1495 59.9046Z" fill="#D2D2D2"/>
<path d="M48.4395 59.9046C50.0716 59.9046 51.3947 58.5111 51.3947 56.7922C51.3947 55.0732 50.0716 53.6797 48.4395 53.6797C46.8074 53.6797 45.4844 55.0732 45.4844 56.7922C45.4844 58.5111 46.8074 59.9046 48.4395 59.9046Z" fill="#D2D2D2"/>
<path d="M57.7198 59.9046C59.3519 59.9046 60.675 58.5111 60.675 56.7922C60.675 55.0732 59.3519 53.6797 57.7198 53.6797C56.0877 53.6797 54.7646 55.0732 54.7646 56.7922C54.7646 58.5111 56.0877 59.9046 57.7198 59.9046Z" fill="#D2D2D2"/>
<path d="M81.291 67.7031H33.1944C31.3228 67.7031 29.8057 69.3011 29.8057 71.2722V82.11C29.8057 84.0811 31.3228 85.6791 33.1944 85.6791H81.291C83.1626 85.6791 84.6797 84.0811 84.6797 82.11V71.2722C84.6797 69.3011 83.1626 67.7031 81.291 67.7031Z" fill="white" stroke="#BABABA" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M39.1495 79.8285C40.7816 79.8285 42.1047 78.435 42.1047 76.716C42.1047 74.997 40.7816 73.6035 39.1495 73.6035C37.5174 73.6035 36.1943 74.997 36.1943 76.716C36.1943 78.435 37.5174 79.8285 39.1495 79.8285Z" fill="#D2D2D2"/>
<path d="M48.4395 79.8285C50.0716 79.8285 51.3947 78.435 51.3947 76.716C51.3947 74.997 50.0716 73.6035 48.4395 73.6035C46.8074 73.6035 45.4844 74.997 45.4844 76.716C45.4844 78.435 46.8074 79.8285 48.4395 79.8285Z" fill="#D2D2D2"/>
<path d="M57.7198 79.8285C59.3519 79.8285 60.675 78.435 60.675 76.716C60.675 74.997 59.3519 73.6035 57.7198 73.6035C56.0877 73.6035 54.7646 74.997 54.7646 76.716C54.7646 78.435 56.0877 79.8285 57.7198 79.8285Z" fill="#D2D2D2"/>
<path d="M85.6433 50.5478C95.773 50.5478 103.985 41.8989 103.985 31.23C103.985 20.561 95.773 11.9121 85.6433 11.9121C75.5135 11.9121 67.3018 20.561 67.3018 31.23C67.3018 41.8989 75.5135 50.5478 85.6433 50.5478Z" fill="white" stroke="#BABABA" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M98.6689 45.2461L103.553 50.3901" stroke="#BABABA" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M103.006 47.8926L101.49 49.5632C100.808 50.3146 100.833 51.5062 101.547 52.2246L114.722 65.4897C115.435 66.2081 116.567 66.1812 117.249 65.4298L118.765 63.7592C119.447 63.0077 119.422 61.8162 118.708 61.0978L105.533 47.8327C104.82 47.1143 103.688 47.1411 103.006 47.8926Z" fill="white" stroke="#BABABA" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
    </>
  )
}

export default SearchCategoryIcon